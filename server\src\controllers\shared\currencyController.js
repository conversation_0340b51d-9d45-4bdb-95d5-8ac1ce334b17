const { getSupportedCurrencies, isValidCurrency } = require('../../utils/currency');
const User = require('../../models/User');

/**
 * Get all supported currencies
 */
const getSupportedCurrenciesList = async (req, res) => {
  try {
    const currencies = getSupportedCurrencies();
    
    res.json({
      success: true,
      data: {
        currencies,
        defaultCurrency: 'INR'
      }
    });
  } catch (error) {
    console.error('Get currencies error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch supported currencies'
    });
  }
};

/**
 * Update user's preferred currency
 */
const updateUserCurrency = async (req, res) => {
  try {
    const { currency } = req.body;
    
    if (!currency) {
      return res.status(400).json({
        success: false,
        message: 'Currency is required'
      });
    }
    
    if (!isValidCurrency(currency.toUpperCase())) {
      return res.status(400).json({
        success: false,
        message: 'Invalid currency code'
      });
    }
    
    const userId = req.user.userId || req.user._id;
    const user = await User.findById(userId);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    // Update user's preferred currency
    if (!user.preferences) {
      user.preferences = {};
    }
    user.preferences.currency = currency.toUpperCase();
    
    await user.save();
    
    res.json({
      success: true,
      message: 'Currency preference updated successfully',
      data: {
        currency: user.preferences.currency
      }
    });
  } catch (error) {
    console.error('Update user currency error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update currency preference'
    });
  }
};

/**
 * Get user's current currency preference
 */
const getUserCurrency = async (req, res) => {
  try {
    const userId = req.user.userId || req.user._id;
    const user = await User.findById(userId).select('preferences.currency');
    
    const currency = user?.preferences?.currency || 'INR';
    
    res.json({
      success: true,
      data: {
        currency,
        isDefault: currency === 'INR'
      }
    });
  } catch (error) {
    console.error('Get user currency error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user currency preference'
    });
  }
};

module.exports = {
  getSupportedCurrenciesList,
  updateUserCurrency,
  getUserCurrency
};
