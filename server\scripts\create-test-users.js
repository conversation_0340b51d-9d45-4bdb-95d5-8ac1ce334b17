const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import models
const User = require('../src/models/User');

const createTestUsers = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('Connected to MongoDB');

    // Password will be hashed by the User schema pre-save middleware

    // Create Admin User
    let adminUser = await User.findOne({ email: '<EMAIL>' });
    if (!adminUser) {
      adminUser = new User({
        firstName: 'Test',
        lastName: 'Admin',
        email: '<EMAIL>',
        password: 'password123', // Will be hashed by pre-save middleware
        userType: 'admin',
        isEmailVerified: true,
        status: 'active'
      });
      await adminUser.save();
      console.log('✅ Admin user created:', adminUser.email);
      console.log('   - UserType:', adminUser.userType);
      console.log('   - Status:', adminUser.status);
    } else {
      // Update existing user to ensure correct userType
      adminUser.userType = 'admin';
      adminUser.isEmailVerified = true;
      adminUser.status = 'active';
      await adminUser.save();
      console.log('✅ Admin user updated:', adminUser.email);
      console.log('   - UserType:', adminUser.userType);
      console.log('   - Status:', adminUser.status);
    }

    // Create Vendor User
    let vendorUser = await User.findOne({ email: '<EMAIL>' });
    if (!vendorUser) {
      vendorUser = new User({
        firstName: 'Test',
        lastName: 'Vendor',
        businessName: 'Test Store',
        businessType: 'retail',
        contactPerson: 'Test Vendor',
        email: '<EMAIL>',
        password: 'password123', // Will be hashed by pre-save middleware
        userType: 'vendor',
        isEmailVerified: true,
        isVendorApproved: true,
        status: 'active'
      });
      await vendorUser.save();
      console.log('✅ Vendor user created:', vendorUser.email);
      console.log('   - UserType:', vendorUser.userType);
      console.log('   - Status:', vendorUser.status);
    } else {
      // Update existing user to ensure correct userType
      vendorUser.firstName = vendorUser.firstName || 'Test';
      vendorUser.lastName = vendorUser.lastName || 'Vendor';
      vendorUser.userType = 'vendor';
      vendorUser.isEmailVerified = true;
      vendorUser.isVendorApproved = true;
      vendorUser.status = 'active';
      await vendorUser.save();
      console.log('✅ Vendor user updated:', vendorUser.email);
      console.log('   - UserType:', vendorUser.userType);
      console.log('   - Status:', vendorUser.status);
    }

    // Create Customer User
    let customerUser = await User.findOne({ email: '<EMAIL>' });
    if (!customerUser) {
      customerUser = new User({
        firstName: 'Test',
        lastName: 'Customer',
        email: '<EMAIL>',
        password: 'password123', // Will be hashed by pre-save middleware
        userType: 'customer',
        isEmailVerified: true,
        status: 'active'
      });
      await customerUser.save();
      console.log('✅ Customer user created:', customerUser.email);
    } else {
      console.log('✅ Customer user already exists:', customerUser.email);
    }

    console.log('\n🎉 Test users created successfully!');
    console.log('\nLogin credentials:');
    console.log('Admin: <EMAIL> / password123');
    console.log('Vendor: <EMAIL> / password123');
    console.log('Customer: <EMAIL> / password123');
    console.log('\nYou can now test the admin and vendor panels!');

  } catch (error) {
    console.error('❌ Error creating test users:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
};

// Run the script
createTestUsers();