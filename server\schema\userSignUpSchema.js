
const mongoose = require('mongoose');

const userSignUpSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
    },
    email: {
        type: String,
        required: true,
        unique: true,
    },
    password: {
        type: String,
        required: true,
    },
    createdAt: {
        type: Date,
        default: Date.now,
    },
});

const UserSignUp = mongoose.model('UserSignUp', userSignUpSchema);

module.exports = UserSignUp;
    