const mongoose = require('mongoose');

const deliveryChargesSchema = new mongoose.Schema({
  standard: {
    charge: {
      type: Number,
      min: 0,
      default: 0
    },
    freeAbove: {
      type: Number,
      min: 0,
      default: 0
    },
    estimatedDays: {
      type: Number,
      min: 1,
      default: 5
    }
  },
  express: {
    available: {
      type: Boolean,
      default: false
    },
    charge: {
      type: Number,
      min: 0,
      default: 0
    },
    estimatedDays: {
      type: Number,
      min: 1,
      default: 2
    }
  },
  sameDay: {
    available: {
      type: Boolean,
      default: false
    },
    charge: {
      type: Number,
      min: 0,
      default: 0
    },
    cutoffTime: {
      type: String,
      default: '12:00'
    }
  },
  locationCharges: [{
    location: {
      name: String,
      type: {
        type: String,
        enum: ['city', 'state', 'country', 'pincode'],
        default: 'city'
      }
    },
    charge: {
      type: Number,
      min: 0,
      default: 0
    },
    estimatedDays: {
      type: Number,
      min: 1,
      default: 5
    }
  }]
}, { _id: false });

module.exports = { deliveryChargesSchema };
