# Database Initialization Scripts

## Initialize Categories Script

This script initializes the "All Categories" modal data in the database with the hardcoded categories that were previously in the frontend.

### Usage

#### Method 1: Using npm script (Recommended)
```bash
cd server
npm run init:categories
```

#### Method 2: Direct execution
```bash
cd server
node scripts/initializeCategories.js
```

### What it does

1. **Connects to MongoDB** using the connection string from your `.env` file
2. **Checks for existing categories** in the database
3. **Prompts for confirmation** if categories already exist
4. **Initializes default categories** including:
   - 18 main categories with icons (displayed in 6-column grid)
   - 10 popular categories (displayed in 5-column layout)

### Categories Initialized

#### Main Categories (18 total):
1. Apparel & Accessories (ShopOutlined)
2. Consumer Electronics (MobileOutlined)
3. Sports & Entertainment (CrownOutlined)
4. Jewelry, Eyewear & Watches (HeartOutlined)
5. Shoes & Accessories (CarOutlined)
6. Home & Garden (HomeOutlined)
7. Beauty (SkinOutlined)
8. Luggage, Bags & Cases (ShoppingOutlined)
9. Packaging & Printing (PrinterOutlined)
10. Parents, Kids & Toys (BugOutlined)
11. Personal Care & Home Care (SmileOutlined)
12. Health & Medical (MedicineBoxOutlined)
13. Gifts & Crafts (GiftOutlined)
14. Furniture (TableOutlined)
15. Lights & Lighting (BulbOutlined)
16. Home Appliances (SettingOutlined)
17. Safety & Security (SafetyOutlined)
18. View All (AppstoreOutlined)

#### Popular Categories (10 total):
1. Consumer Electronics
2. Apparel & Accessories
3. Home & Garden
4. Sports & Entertainment
5. Beauty
6. Jewelry, Eyewear & Watches
7. Shoes & Accessories
8. Health & Medical
9. Furniture
10. Home Appliances

### After Running the Script

Once the script completes successfully:

1. **Frontend will automatically load** the categories from the database
2. **Admin panel access** is available at: `Admin Panel → Homepage Settings → All Categories`
3. **Full CRUD operations** are available for managing categories
4. **Real-time updates** - changes in admin panel reflect immediately in frontend

### Environment Requirements

- MongoDB connection string in `.env` file (`MONGODB_URI`)
- Node.js environment with all dependencies installed

### Error Handling

The script includes comprehensive error handling:
- Database connection failures
- Existing data detection
- User confirmation prompts
- Graceful cleanup and disconnection

### Safety Features

- **Non-destructive by default** - won't overwrite existing data without confirmation
- **Backup recommendation** - always backup your database before running scripts
- **Rollback capability** - you can manually delete the categories from admin panel if needed

### Troubleshooting

1. **Connection Issues**: Verify your `MONGODB_URI` in `.env` file
2. **Permission Issues**: Ensure your database user has write permissions
3. **Existing Data**: The script will prompt before overwriting existing categories
4. **Icon Issues**: All icons are validated against available Ant Design icons

### Manual Management

After initialization, you can:
- Add new categories through the admin panel
- Edit existing categories (name, icon, link URL, status)
- Delete categories
- Reorder categories
- Enable/disable categories
- Set custom link URLs for navigation