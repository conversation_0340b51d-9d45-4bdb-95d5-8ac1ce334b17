const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const profileController = require('../../controllers/customer/profileController');
const imageUpload = require('../../middleware/upload/imageUpload');
const authenticate = require('../../middleware/auth/authenticate');
const { requireUserType } = require('../../middleware/auth/authMiddleware');

// Apply authentication middleware to all profile routes
router.use(authenticate);
router.use(requireUserType(['customer']));

/**
 * @route   GET /api/customer/profile
 * @desc    Get current user profile
 * @access  Private (Customer)
 */
router.get('/', profileController.getProfile);

/**
 * @route   PUT /api/customer/profile
 * @desc    Update user profile
 * @access  Private (Customer)
 */
router.put('/',
  imageUpload.avatar(),
  [
    check('firstName', 'First name is required').optional().isString().trim().isLength({ min: 2, max: 50 }),
    check('lastName', 'Last name is required').optional().isString().trim().isLength({ min: 2, max: 50 }),
    check('phone').optional().isString().trim().isLength({ min: 10, max: 15 }).withMessage('Phone number should be 10-15 characters'),
    check('countryCode').optional().isString().trim().isLength({ max: 3 }).withMessage('Country code must be 3 characters or less'),
    check('address').optional().isString().trim(),
    check('city').optional().isString().trim(),
    check('state').optional().isString().trim(),
    check('zipCode').optional().isString().trim(),
    check('country').optional().isString().trim(),
    check('preferences.language').optional().isString().trim(),
    check('preferences.timezone').optional().isString().trim(),
    check('preferences.currency').optional().isString().trim()
  ],
  profileController.updateProfile
);

/**
 * @route   PUT /api/customer/profile/avatar
 * @desc    Update profile picture
 * @access  Private (Customer)
 */
router.put('/avatar',
  imageUpload.avatar(),
  profileController.updateProfilePicture
);

/**
 * @route   DELETE /api/customer/profile/avatar
 * @desc    Delete profile picture
 * @access  Private (Customer)
 */
router.delete('/avatar', profileController.deleteProfilePicture);

/**
 * @route   POST /api/customer/profile/addresses
 * @desc    Add a new address
 * @access  Private (Customer)
 */
router.post('/addresses',
  [
    check('street', 'Street address is required').notEmpty().trim(),
    check('city', 'City is required').notEmpty().trim(),
    check('state', 'State is required').notEmpty().trim(),
    check('zipCode', 'ZIP code is required').notEmpty().trim(),
    check('country', 'Country is required').notEmpty().trim(),
    check('type').optional().isIn(['home', 'work', 'other']).withMessage('Invalid address type'),
    check('isDefault').optional().isBoolean().withMessage('isDefault must be a boolean')
  ],
  profileController.addAddress
);

/**
 * @route   PUT /api/customer/profile/addresses/:addressId
 * @desc    Update an address
 * @access  Private (Customer)
 */
router.put('/addresses/:addressId',
  [
    check('street').optional().notEmpty().trim().withMessage('Street address cannot be empty'),
    check('city').optional().notEmpty().trim().withMessage('City cannot be empty'),
    check('state').optional().notEmpty().trim().withMessage('State cannot be empty'),
    check('zipCode').optional().notEmpty().trim().withMessage('ZIP code cannot be empty'),
    check('country').optional().notEmpty().trim().withMessage('Country cannot be empty'),
    check('type').optional().isIn(['home', 'work', 'other']).withMessage('Invalid address type'),
    check('isDefault').optional().isBoolean().withMessage('isDefault must be a boolean')
  ],
  profileController.updateAddress
);

/**
 * @route   DELETE /api/customer/profile/addresses/:addressId
 * @desc    Delete an address
 * @access  Private (Customer)
 */
router.delete('/addresses/:addressId', profileController.deleteAddress);

/**
 * @route   PUT /api/customer/profile/preferences
 * @desc    Update user preferences
 * @access  Private (Customer)
 */
router.put('/preferences', profileController.updatePreferences);

module.exports = router;