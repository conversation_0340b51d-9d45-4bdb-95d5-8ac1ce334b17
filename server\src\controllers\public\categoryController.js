const Category = require('../../models/Category');

/**
 * Get all active categories for public use
 */
const getCategories = async (req, res) => {
  try {
    const categories = await Category.find({ status: 'active' })
      .sort({ sortOrder: 1, name: 1 })
      .select('name slug description parent level featured')
      .lean();

    res.json({
      success: true,
      data: {
        categories
      }
    });

  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch categories',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get category tree structure
 */
const getCategoryTree = async (req, res) => {
  try {
    const categories = await Category.find({ status: 'active' })
      .sort({ sortOrder: 1, name: 1 })
      .select('name slug description parent level featured')
      .lean();

    // Build tree structure
    const categoryMap = {};
    const tree = [];

    // Create a map for quick lookup
    categories.forEach(cat => {
      categoryMap[cat._id] = { ...cat, children: [] };
    });

    // Build the tree
    categories.forEach(cat => {
      if (cat.parent) {
        if (categoryMap[cat.parent]) {
          categoryMap[cat.parent].children.push(categoryMap[cat._id]);
        }
      } else {
        tree.push(categoryMap[cat._id]);
      }
    });

    res.json({
      success: true,
      data: {
        categories: tree
      }
    });

  } catch (error) {
    console.error('Get category tree error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch category tree',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get featured categories
 */
const getFeaturedCategories = async (req, res) => {
  try {
    const categories = await Category.find({ 
      status: 'active', 
      featured: true 
    })
      .sort({ sortOrder: 1, name: 1 })
      .select('name slug description featured')
      .lean();

    res.json({
      success: true,
      data: {
        categories
      }
    });

  } catch (error) {
    console.error('Get featured categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch featured categories',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get single category by ID or slug
 */
const getCategory = async (req, res) => {
  try {
    const identifier = req.params.id;
    let category;

    // Try to find by ID first, then by slug
    if (identifier.match(/^[0-9a-fA-F]{24}$/)) {
      category = await Category.findOne({ 
        _id: identifier, 
        status: 'active' 
      });
    } else {
      category = await Category.findOne({ 
        slug: identifier, 
        status: 'active' 
      });
    }

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    // Get subcategories
    const subcategories = await Category.find({
      parent: category._id,
      status: 'active'
    })
      .sort({ sortOrder: 1, name: 1 })
      .select('name slug description')
      .lean();

    res.json({
      success: true,
      data: {
        category: {
          ...category.toObject(),
          subcategories
        }
      }
    });

  } catch (error) {
    console.error('Get category error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch category',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getCategories,
  getCategoryTree,
  getFeaturedCategories,
  getCategory
};