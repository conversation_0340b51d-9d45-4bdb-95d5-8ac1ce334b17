const User = require('../../models/User');
const Vendor = require('../../models/Vendor');

/**
 * Middleware to ensure vendor record exists for vendor users
 * Creates a basic vendor record if it doesn't exist
 * 
 * Note: For demo purposes, vendors are automatically verified to allow product creation.
 * In production, vendors would need to go through a proper verification process.
 */
const ensureVendorRecord = async (req, res, next) => {
  try {
    // Only apply to vendor users
    if (req.user.userType !== 'vendor') {
      return next();
    }

    const vendorId = req.user.userId;
    
    // Check if vendor record exists
    let vendor = await Vendor.findOne({ user: vendorId });
    
    if (!vendor) {
      // Get user details
      const user = await User.findById(vendorId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Create basic vendor record with all required fields
      vendor = await Vendor.create({
        user: vendorId,
        businessName: `${user.firstName} ${user.lastName}'s Store`,
        businessDescription: 'Welcome to my store! I\'m excited to share my products with you.',
        businessType: 'individual', // Use valid enum value
        status: 'active', // Set as active for demo purposes
        verification: {
          status: 'verified' // Set as verified for demo purposes to allow product creation
        },
        businessAddress: {
          street: user.addresses?.[0]?.street || 'Not provided',
          city: user.addresses?.[0]?.city || 'Not provided',
          state: user.addresses?.[0]?.state || 'Not provided',
          zipCode: user.addresses?.[0]?.zipCode || '00000',
          country: user.addresses?.[0]?.country || 'Not provided'
        },
        contactInfo: {
          businessPhone: user.phone || 'Not provided',
          businessEmail: user.email || '<EMAIL>'
        },
        bankDetails: {
          accountHolderName: 'Not provided',
          bankName: 'Not provided',
          accountNumber: 'Not provided',
          routingNumber: 'Not provided',
          accountType: 'checking'
        }
      });

      console.log(`Created vendor record for user ${vendorId}: ${vendor.businessName}`);
    } else {
      // Update existing vendor record if verification status is not verified (for demo purposes)
      if (vendor.verification.status !== 'verified') {
        vendor.verification.status = 'verified';
        vendor.status = 'active';
        await vendor.save();
        console.log(`Updated vendor verification status for user ${vendorId}: ${vendor.businessName}`);
      }
    }

    // Attach vendor info to request for use in controllers
    req.vendor = vendor;
    next();

  } catch (error) {
    console.error('Ensure vendor record error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initialize vendor account'
    });
  }
};

module.exports = ensureVendorRecord;
