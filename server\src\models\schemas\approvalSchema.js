const mongoose = require('mongoose');

const approvalSchema = new mongoose.Schema({
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'requires_changes'],
    default: 'pending'
  },
  submittedAt: Date,
  reviewedAt: Date,
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  rejectionReason: {
    type: String,
    trim: true,
    maxlength: [500, 'Rejection reason cannot exceed 500 characters']
  },
  adminNotes: {
    type: String,
    trim: true,
    maxlength: [1000, 'Admin notes cannot exceed 1000 characters']
  },
  changesRequested: [{
    field: {
      type: String,
      required: true
    },
    message: {
      type: String,
      required: true,
      maxlength: [200, 'Change request message cannot exceed 200 characters']
    },
    resolved: {
      type: Boolean,
      default: false
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  history: [{
    action: {
      type: String,
      enum: ['submitted', 'approved', 'rejected', 'changes_requested', 'resubmitted'],
      required: true
    },
    performedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    reason: String,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }]
}, { _id: false });

module.exports = { approvalSchema };
