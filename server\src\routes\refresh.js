const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middleware/auth/authMiddleware');
const refreshService = require('../services/refreshService');

/**
 * @route   GET /api/refresh/dashboard
 * @desc    Get dashboard statistics
 * @access  Private (Admin)
 */
router.get('/dashboard', verifyToken, async (req, res) => {
  try {
    const stats = await refreshService.getDashboardStats();
    res.json(stats);
  } catch (error) {
    console.error('Dashboard refresh error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to refresh dashboard data'
    });
  }
});

/**
 * @route   GET /api/refresh/vendor-stats/:vendorId
 * @desc    Get vendor-specific statistics
 * @access  Private (Vendor/Admin)
 */
router.get('/vendor-stats/:vendorId', verifyToken, async (req, res) => {
  try {
    const { vendorId } = req.params;
    const stats = await refreshService.getVendorStats(vendorId);
    res.json(stats);
  } catch (error) {
    console.error('Vendor stats refresh error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to refresh vendor statistics'
    });
  }
});

/**
 * @route   GET /api/refresh/orders
 * @desc    Get order updates for user
 * @access  Private
 */
router.get('/orders', verifyToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const userType = req.user.userType;
    const orders = await refreshService.getOrderUpdates(userId, userType);
    res.json(orders);
  } catch (error) {
    console.error('Orders refresh error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to refresh orders'
    });
  }
});

module.exports = router;