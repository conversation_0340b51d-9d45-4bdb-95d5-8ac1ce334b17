/**
 * Utility functions for handling image URLs
 */

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
const SERVER_BASE_URL = API_BASE_URL.replace('/api', '');

/**
 * Convert relative image URLs to absolute URLs
 * @param {string} imageUrl - The image URL from the database
 * @returns {string} - Absolute image URL
 */
export const getAbsoluteImageUrl = (imageUrl) => {
  if (!imageUrl) return null;
  
  // If it's already an absolute URL (http/https), return as is
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }
  
  // If it's a relative URL starting with /uploads, make it absolute
  if (imageUrl.startsWith('/uploads/')) {
    return `${SERVER_BASE_URL}${imageUrl}`;
  }
  
  // If it's a relative URL without leading slash, add it
  if (imageUrl.startsWith('uploads/')) {
    return `${SERVER_BASE_URL}/${imageUrl}`;
  }
  
  // For any other relative URLs, prepend the server base URL
  return `${SERVER_BASE_URL}/${imageUrl}`;
};

/**
 * Get optimized image URL with fallback
 * @param {string} imageUrl - The original image URL
 * @param {Object} options - Optimization options
 * @returns {string} - Optimized image URL
 */
export const getOptimizedImageUrl = (imageUrl, options = {}) => {
  const absoluteUrl = getAbsoluteImageUrl(imageUrl);
  
  if (!absoluteUrl) return null;
  
  // If it's a Cloudinary URL, we can add transformations
  if (absoluteUrl.includes('cloudinary.com')) {
    const { width, height, quality = 'auto', format = 'auto' } = options;
    
    // Basic Cloudinary transformations
    let transformations = [];
    
    if (width) transformations.push(`w_${width}`);
    if (height) transformations.push(`h_${height}`);
    if (quality) transformations.push(`q_${quality}`);
    if (format) transformations.push(`f_${format}`);
    
    if (transformations.length > 0) {
      // Insert transformations into Cloudinary URL
      const transformString = transformations.join(',');
      return absoluteUrl.replace('/upload/', `/upload/${transformString}/`);
    }
  }
  
  return absoluteUrl;
};

/**
 * Validate if an image URL is accessible
 * @param {string} imageUrl - The image URL to validate
 * @returns {Promise<boolean>} - Whether the image is accessible
 */
export const validateImageUrl = async (imageUrl) => {
  if (!imageUrl) return false;
  
  try {
    const absoluteUrl = getAbsoluteImageUrl(imageUrl);
    const response = await fetch(absoluteUrl, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.warn('Image validation failed:', error);
    return false;
  }
};

/**
 * Generate a local SVG placeholder image
 * @param {number} width - Image width
 * @param {number} height - Image height
 * @param {string} text - Text to display
 * @param {string} bgColor - Background color
 * @param {string} textColor - Text color
 * @returns {string} - Data URL for SVG placeholder
 */
const generateSVGPlaceholder = (width, height, text, bgColor = '#f3f4f6', textColor = '#6b7280') => {
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="${bgColor}"/>
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="${Math.min(width, height) / 10}" 
            fill="${textColor}" text-anchor="middle" dominant-baseline="central">${text}</text>
    </svg>
  `;
  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

/**
 * Get fallback image URL
 * @param {string} type - Type of fallback image (product, user, vendor, etc.)
 * @returns {string} - Fallback image URL
 */
export const getFallbackImageUrl = (type = 'product') => {
  const fallbacks = {
    product: generateSVGPlaceholder(400, 400, 'No Image', '#f3f4f6', '#6b7280'),
    user: generateSVGPlaceholder(200, 200, 'User', '#f3f4f6', '#6b7280'),
    vendor: generateSVGPlaceholder(300, 300, 'Vendor', '#f3f4f6', '#6b7280'),
    category: generateSVGPlaceholder(400, 300, 'Category', '#f3f4f6', '#6b7280')
  };
  
  return fallbacks[type] || fallbacks.product;
};

/**
 * Extract image URLs from product data
 * @param {Object} product - Product object
 * @returns {Array} - Array of processed image URLs
 */
export const getProductImageUrls = (product) => {
  if (!product || !product.images || !Array.isArray(product.images)) {
    return [];
  }
  
  return product.images
    .map(img => {
      if (typeof img === 'string') {
        return getAbsoluteImageUrl(img);
      }
      if (img && img.url) {
        return getAbsoluteImageUrl(img.url);
      }
      return null;
    })
    .filter(Boolean);
};

/**
 * Get primary product image URL
 * @param {Object} product - Product object
 * @returns {string|null} - Primary image URL or null
 */
export const getPrimaryProductImage = (product) => {
  if (!product) {
    console.warn('getPrimaryProductImage: No product provided');
    return null;
  }
  
  if (!product.images) {
    console.warn('getPrimaryProductImage: No images property on product', product.name || 'Unknown product');
    return null;
  }
  
  if (!Array.isArray(product.images)) {
    console.warn('getPrimaryProductImage: Images is not an array:', product.images);
    return null;
  }
  
  if (product.images.length === 0) {
    console.warn('getPrimaryProductImage: Images array is empty for product', product.name || 'Unknown product');
    return null;
  }
  
  // Find primary image
  const primaryImage = product.images.find(img => {
    if (typeof img === 'object' && img !== null && img.isPrimary) {
      return true;
    }
    return false;
  });
  
  if (primaryImage) {
    const url = getAbsoluteImageUrl(primaryImage.url);
    console.log('getPrimaryProductImage: Using primary image:', url);
    return url;
  }
  
  // Fallback to first image
  const firstImage = product.images[0];
  if (firstImage) {
    const url = typeof firstImage === 'string' ? firstImage : firstImage.url;
    const absoluteUrl = getAbsoluteImageUrl(url);
    console.log('getPrimaryProductImage: Using first image as fallback:', absoluteUrl);
    return absoluteUrl;
  }
  
  console.warn('getPrimaryProductImage: No usable image found for product', product.name || 'Unknown product');
  return null;
};

export default {
  getAbsoluteImageUrl,
  getOptimizedImageUrl,
  validateImageUrl,
  getFallbackImageUrl,
  getProductImageUrls,
  getPrimaryProductImage
};