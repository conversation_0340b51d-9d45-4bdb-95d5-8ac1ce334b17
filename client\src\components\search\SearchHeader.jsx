import React from 'react';
import { Button } from 'antd';
import { FilterOutlined } from '@ant-design/icons';

const SearchHeader = ({ 
  searchTerm, 
  searchResults, 
  loading, 
  showFilters, 
  onToggleFilters 
}) => {
  return (
    <div className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 sm:py-4">
        <div className="flex items-center justify-between">
          {/* Results Info */}
          <div className="flex-1 min-w-0">
            {searchTerm && (
              <span className="text-gray-600 text-sm truncate block">
                {loading ? 'Searching...' : `${searchResults.length} results for "${searchTerm}"`}
              </span>
            )}
          </div>
          
          {/* Mobile Filter Toggle */}
          <Button
            icon={<FilterOutlined />}
            onClick={onToggleFilters}
            className="lg:hidden ml-4 flex-shrink-0"
            size="small"
          >
            <span className="hidden sm:inline">Filters</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SearchHeader;
