const mongoose = require('mongoose');

const specificationsSchema = new mongoose.Schema({
  weight: {
    value: Number,
    unit: {
      type: String,
      enum: ['kg', 'g', 'lb', 'oz'],
      default: 'kg'
    }
  },
  dimensions: {
    length: Number,
    width: Number,
    height: Number,
    unit: {
      type: String,
      enum: ['cm', 'm', 'in', 'ft'],
      default: 'cm'
    }
  },
  material: String,
  color: String,
  size: String,
  features: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    value: {
      type: String,
      required: true,
      trim: true
    }
  }],
  technicalSpecs: [{
    category: {
      type: String,
      required: true,
      trim: true
    },
    specifications: [{
      name: String,
      value: String,
      unit: String
    }]
  }]
}, { _id: false });

module.exports = { specificationsSchema };
