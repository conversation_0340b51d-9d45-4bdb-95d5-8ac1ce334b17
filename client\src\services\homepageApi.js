import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'https://multi-vendor-server-1tb9.onrender.com';

// Create axios instance for public homepage API
const homepageApi = axios.create({
  baseURL: API_BASE_URL.endsWith('/api') ? `${API_BASE_URL}/public/homepage` : `${API_BASE_URL}/api/public/homepage`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Homepage APIs
export const getHomepageData = () => homepageApi.get('/data');
export const getFeaturedCategories = () => homepageApi.get('/featured-categories');

export default homepageApi;
