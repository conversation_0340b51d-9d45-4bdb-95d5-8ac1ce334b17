import React from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../hooks/useAuth";

const MobileMenu = ({ isMobileMenuOpen, onMenuClose }) => {
    const navigate = useNavigate();
    const { isAuthenticated } = useAuth();

    const handleOrdersClick = () => {
        navigate('/orders');
        onMenuClose();
    };

    if (!isMobileMenuOpen) return null;

    return (
        <div className="lg:hidden border-t border-gray-700 bg-black">
            <div className="px-2 py-3 space-y-1">
                {!isAuthenticated && (
                    <button 
                        onClick={() => {
                            navigate('/auth');
                            onMenuClose();
                        }}
                        className="block w-full text-left px-3 py-3 text-white hover:text-orange-500 hover:bg-gray-800 rounded-md active:bg-gray-700 transition-colors touch-manipulation"
                        style={{ minHeight: '44px' }}
                    >
                        Join Free
                    </button>
                )}
                <a 
                    href="#" 
                    className="block px-3 py-3 text-white hover:text-orange-500 hover:bg-gray-800 rounded-md active:bg-gray-700 transition-colors touch-manipulation"
                    style={{ minHeight: '44px' }}
                >
                    Messages
                </a>
                <button 
                    onClick={handleOrdersClick}
                    className="block w-full text-left px-3 py-3 text-white hover:text-orange-500 hover:bg-gray-800 rounded-md active:bg-gray-700 transition-colors touch-manipulation"
                    style={{ minHeight: '44px' }}
                >
                    Orders
                </button>
            </div>
        </div>
    );
};

export default MobileMenu;
