import React, { useContext } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { AuthContext } from '../contexts/AuthContext';

const VendorProtectedRoute = ({ children }) => {
  const { isAuthenticated, isLoading, userType, user } = useContext(AuthContext);
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // If not authenticated, redirect to landing page (vendors should login through main auth)
  if (!isAuthenticated) {
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  // Check if user is vendor (check both userType and user.role for compatibility)
  const isVendor = userType === 'vendor' || user?.role === 'vendor' || user?.userType === 'vendor';

  // If authenticated but not vendor, show unauthorized message and redirect
  if (!isVendor) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg max-w-md">
            <h2 className="text-xl font-bold mb-2">Access Denied</h2>
            <p className="mb-4">You don't have permission to access the vendor dashboard.</p>
            <p className="text-sm text-red-600">Only vendors can access this area.</p>
            <div className="mt-4">
              <button 
                onClick={() => window.history.back()} 
                className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded mr-2"
              >
                Go Back
              </button>
              <a 
                href="/" 
                className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded inline-block"
              >
                Home
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If authenticated and is vendor, render the protected component
  return children;
};

export default VendorProtectedRoute;