import axios from 'axios';
import { config, getVendorApiUrl, debugLog, errorLog } from '../config/environment.js';

// Create axios instance with environment-based config
const vendorApi = axios.create({
  baseURL: getVendorApiUrl(),
  timeout: config.api.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests with enhanced logging
vendorApi.interceptors.request.use(
  (requestConfig) => {
    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    if (token) {
      requestConfig.headers.Authorization = `Bearer ${token}`;
      debugLog('Request with auth token:', requestConfig.url);
    } else {
      debugLog('Request without auth token:', requestConfig.url);
    }
    
    // Log request details in debug mode
    debugLog('API Request:', {
      url: requestConfig.url,
      method: requestConfig.method,
      baseURL: requestConfig.baseURL,
      hasAuth: !!token
    });
    
    return requestConfig;
  },
  (error) => {
    errorLog('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Handle response errors with enhanced logging
vendorApi.interceptors.response.use(
  (response) => {
    debugLog('API Response success:', {
      url: response.config.url,
      status: response.status,
      dataSize: JSON.stringify(response.data).length
    });
    return response;
  },
  (error) => {
    const errorDetails = {
      url: error.config?.url,
      status: error.response?.status,
      message: error.message,
      data: error.response?.data
    };
    
    errorLog('API Response error:', errorDetails);
    
    if (error.response?.status === 401) {
      errorLog('Authentication failed, clearing tokens and redirecting');
      // Token expired or invalid
      localStorage.removeItem('authToken');
      localStorage.removeItem('authUser');
      localStorage.removeItem('authUserType');
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/auth';
    }
    
    return Promise.reject(error);
  }
);

// Products APIs
export const productsApi = {
  // Get vendor's products with pagination and filters
  getProducts: (params) => vendorApi.get('/products', { params }),
  
  // Get single product by ID
  getProduct: (id) => vendorApi.get(`/products/${id}`),
  
  // Create new product with images
  createProduct: (formData) => {
    return vendorApi.post('/products', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // Create new product (simplified, no images)
  createSimpleProduct: (data) => {
    return vendorApi.post('/products/simple', data, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
  },
  
  // Update product with optional images
  updateProduct: (id, formData) => {
    return vendorApi.put(`/products/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // Delete product
  deleteProduct: (id) => vendorApi.delete(`/products/${id}`),
  
  // Update product stock
  updateStock: (id, quantity, operation = 'set') => 
    vendorApi.patch(`/products/${id}/stock`, { quantity, operation }),
  
  // Bulk operations on products
  bulkOperation: (productIds, action) => 
    vendorApi.post('/products/bulk', { productIds, action }),
  
  // Remove product image
  removeImage: (id, imageUrl) => 
    vendorApi.delete(`/products/${id}/images`, { data: { imageUrl } }),
  
  // Get product statistics
  getStats: () => vendorApi.get('/products/stats'),

  // Submit product for approval
  submitForApproval: (id) => vendorApi.patch(`/products/${id}/submit-approval`),

  // Get product approval history
  getApprovalHistory: (id) => vendorApi.get(`/products/${id}/approval-history`),

  // Multi-currency pricing endpoints
  getPricing: (id, currency) => vendorApi.get(`/products/${id}/pricing/${currency}`),
  updatePricing: (id, currency, pricing) => 
    vendorApi.patch(`/products/${id}/pricing/${currency}`, pricing),
  getAvailableCurrencies: (id) => vendorApi.get(`/products/${id}/currencies`),
};

// Dashboard APIs
export const dashboardApi = {
  getStats: () => vendorApi.get('/dashboard/stats'),
  getAnalytics: (params) => vendorApi.get('/analytics', { params }),
};

// Orders APIs
export const ordersApi = {
  getOrders: (params) => vendorApi.get('/orders', { params }),
  getOrder: (id) => vendorApi.get(`/orders/${id}`),
  
  // Updated to support multi-vendor item status updates
  updateOrderStatus: (id, status, note, itemIds = null) => 
    vendorApi.patch(`/orders/${id}/status`, { status, note, itemIds }),
  
  // Update specific items status
  updateOrderItemStatus: (id, status, itemIds, note) => 
    vendorApi.patch(`/orders/${id}/status`, { status, itemIds, note }),
  
  updateShipping: (id, trackingNumber, carrier, shippingDate) => 
    vendorApi.patch(`/orders/${id}/shipping`, { trackingNumber, carrier, shippingDate }),
  
  // New fulfillment and shipping APIs
  createShippingTracking: (id, data) => 
    vendorApi.post(`/orders/${id}/tracking`, data),
  
  updateTrackingStatus: (trackingId, status, location, description, estimatedDelivery) => 
    vendorApi.put(`/orders/tracking/${trackingId}/status`, { 
      status, location, description, estimatedDelivery 
    }),
  
  processOrderFulfillment: (id, action, items, shippingInfo, note) => 
    vendorApi.post(`/orders/${id}/fulfill`, { 
      action, items, shippingInfo, note 
    }),
  
  // Get order analytics and fulfillment stats
  getOrderAnalytics: (period = '30d') => 
    vendorApi.get('/orders/analytics', { params: { period } }),
    
  // Get order statistics for vendor
  getOrderStats: () => vendorApi.get('/orders/stats'),
  
  // Bulk operations on orders
  bulkUpdateOrderStatus: (orderIds, status, note) => 
    vendorApi.post('/orders/bulk-status', { orderIds, status, note }),
};

// Store/Profile APIs
export const storeApi = {
  getProfile: () => vendorApi.get('/store/profile'),
  updateProfile: (data) => vendorApi.put('/store/profile', data),
  updateSettings: (data) => vendorApi.put('/store/settings', data),
  uploadLogo: (formData) => {
    return vendorApi.post('/store/logo', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  uploadBanner: (formData) => {
    return vendorApi.post('/store/banner', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  uploadAvatar: (formData) => {
    return vendorApi.post('/store/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
};

// Categories API (vendor endpoint for getting categories)
export const categoriesApi = {
  getCategories: () => vendorApi.get('/categories'),
};

export default vendorApi;