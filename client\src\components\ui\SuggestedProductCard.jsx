import React from 'react';
import { useNavigate } from 'react-router-dom';
import { getFallbackImageUrl, getPrimaryProductImage } from '../../utils/imageUtils';

const SuggestedProductCard = ({ product, onCartClick }) => {
  const navigate = useNavigate();

  const handleProductClick = () => {
    navigate(`/product/${product.id}`);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleCartClick = (e) => {
    e.stopPropagation();
    if (onCartClick) {
      onCartClick(product.id);
    }
  };

  // Generate random rating and reviews for demo
  const rating = (Math.random() * 2 + 3).toFixed(1);
  const reviewCount = Math.floor(Math.random() * 500) + 50;
  const discount = Math.floor(Math.random() * 30) + 10;

  return (
    <div
      onClick={handleProductClick}
      className="bg-white rounded-lg overflow-hidden shadow-sm border border-gray-100 hover:shadow-lg hover:border-gray-200 transition-all duration-300 transform hover:-translate-y-1 cursor-pointer group"
    >
      {/* Product Image */}
      <div className="relative aspect-square bg-gray-100 overflow-hidden">
        <img
          src={getPrimaryProductImage(product) || product.images?.[0] || getFallbackImageUrl('product')}
          alt={product.title}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          onError={(e) => {
            e.target.src = getFallbackImageUrl('product');
          }}
        />
        
        {/* Discount Badge */}
        <div className="absolute top-2 left-2">
          <span className="bg-red-500 text-white text-xs font-semibold px-2 py-1 rounded">
            -{discount}%
          </span>
        </div>

        {/* Quick Action Buttons */}
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 space-y-1">
          <button
            onClick={handleCartClick}
            className="bg-white/90 hover:bg-white p-2 rounded-full shadow-md transition-colors"
            title="Add to Cart"
          >
            <svg className="w-4 h-4 text-gray-600 hover:text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293A1 1 0 004 16h16M7 13v4a2 2 0 002 2h6a2 2 0 002-2v-4m-8 2h4" />
            </svg>
          </button>
        </div>
      </div>

      {/* Product Info */}
      <div className="p-3">
        {/* Category */}
        <p className="text-xs text-blue-600 font-medium mb-1">
          {product.category?.name || 'General'}
        </p>
        
        {/* Title */}
        <h3 className="text-sm font-medium text-gray-800 leading-tight mb-2 line-clamp-2" 
            style={{
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              minHeight: '2.5em'
            }}>
          {product.title}
        </h3>
        
        {/* Rating */}
        <div className="flex items-center mb-2">
          <div className="flex items-center">
            {Array.from({ length: 5 }).map((_, i) => (
              <svg
                key={i}
                className={`w-3 h-3 ${i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'}`}
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
              </svg>
            ))}
          </div>
          <span className="text-xs text-gray-500 ml-1">
            ({reviewCount})
          </span>
        </div>
        
        {/* Price */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <span className="text-lg font-bold text-red-600">
              ${product.price}
            </span>
            <span className="text-sm text-gray-500 line-through">
              ${(product.price * 1.3).toFixed(2)}
            </span>
          </div>
        </div>
        
        {/* Shipping Info */}
        <div className="flex items-center justify-between text-xs">
          <span className="text-green-600 font-medium">Free Shipping</span>
          <span className="text-gray-500">MOQ: 1 piece</span>
        </div>
      </div>
    </div>
  );
};

export default SuggestedProductCard;