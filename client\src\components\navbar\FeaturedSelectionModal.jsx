import React from 'react';
import { TrophyOutlined, FireOutlined, StarOutlined } from '@ant-design/icons';

const FeaturedSelectionModal = ({ showModal, onMouseEnter, onMouseLeave }) => {
    if (!showModal) return null;

    const featuredItems = [
        { icon: <StarOutlined className="text-3xl text-blue-500" />, title: 'Top Ranking' },
        { icon: <FireOutlined className="text-3xl text-red-500" />, title: 'New Arrivals' },
        { icon: <TrophyOutlined className="text-3xl text-yellow-500" />, title: 'Top Deals' },
    ];

    const links = [
        { href: "/featured/electronics", text: "Dropshipping center" },
        { href: "/featured/fashion", text: "Sample center" },
        { href: "/featured/home", text: "Fast customization" },
        { href: "/featured/all", text: "Online Trade Show" },
    ];

    return (
        <div
            className="absolute left-1/2 sm:left-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-xl z-50 navbar-hover-modal w-[95vw] sm:w-[550px] max-w-[550px] transform -translate-x-1/2 sm:translate-x-0"
            onMouseEnter={onMouseEnter}
            onMouseLeave={onMouseLeave}
        >
            <div className="p-4 flex justify-between">
                {/* Left side: 3 boxes */}
                <div className="flex-grow pr-4">
                    <div className="grid grid-cols-3 gap-3">
                        {featuredItems.map((item, index) => (
                            <div key={index} className="group cursor-pointer">
                                <div className="bg-white border border-gray-200 rounded-lg p-3 hover:shadow-md transition-all duration-200 h-full">
                                    <div className="flex flex-col items-center justify-center text-center h-full">
                                        <div className="w-10 h-10 mb-2 flex items-center justify-center">
                                            {item.icon}
                                        </div>
                                        <h4 className="text-sm font-medium text-gray-800">{item.title}</h4>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Right side: Links */}
                <div className="flex-shrink-0 border-l border-gray-200 pl-4 w-[180px]">
                    <div className="flex flex-col space-y-2">
                        {links.map((link, index) => (
                            <a key={index} href={link.href} className="text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200">
                                {link.text}
                            </a>
                        ))}
                    </div>
                </div>
            </div>

            {/* Triangle pointer */}
            <div className="absolute -top-2 left-1/2 sm:left-28 w-4 h-4 bg-white border-l border-t border-gray-200 transform rotate-45 -translate-x-1/2 sm:translate-x-0"></div>
        </div>
    );
};

export default FeaturedSelectionModal;
