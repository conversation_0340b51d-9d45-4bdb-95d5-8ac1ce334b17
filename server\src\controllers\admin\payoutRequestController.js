const Payout = require('../../models/Payout');
const Vendor = require('../../models/Vendor');
const { validationResult } = require('express-validator');

/**
 * Admin Payout Request Management Controller
 * Handles viewing, searching, and basic operations for payout requests
 */
class PayoutRequestController {

  /**
   * Get admin payout dashboard with statistics and recent activity
   */
  static async getDashboard(req, res) {
    try {
      // Get overall statistics
      const stats = await Payout.getPayoutStats();

      // Get pending payouts count
      const pendingCount = await Payout.countDocuments({ status: 'pending' });

      // Get recent payouts (last 10)
      const recentPayouts = await Payout.find()
        .populate('vendor', 'businessName contactInfo.businessEmail user')
        .populate('vendor.user', 'firstName lastName email')
        .sort({ requestDate: -1 })
        .limit(10)
        .select('requestedAmount status requestDate method vendor metadata.vendorBusinessName');

      // Calculate total amounts by status
      const totalAmounts = await PayoutRequestController._calculateTotalAmounts();

      // Get monthly trends for the last 12 months
      const monthlyTrends = await PayoutRequestController._getMonthlyTrends();

      res.json({
        success: true,
        data: {
          stats,
          pendingCount,
          recentPayouts,
          totals: totalAmounts,
          monthlyTrends
        }
      });

    } catch (error) {
      console.error('Error fetching admin payout dashboard:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  /**
   * Get all payout requests with filtering, searching, and pagination
   */
  static async getAllPayouts(req, res) {
    try {
      const {
        page = 1,
        limit = 20,
        status,
        search,
        sortBy = 'requestDate',
        sortOrder = 'desc'
      } = req.query;

      const skip = (parseInt(page) - 1) * parseInt(limit);
      const sort = { [sortBy]: sortOrder === 'asc' ? 1 : -1 };

      // Build query
      const query = await PayoutRequestController._buildSearchQuery(status, search);

      // Get payouts with populated data
      const payouts = await Payout.find(query)
        .populate({
          path: 'vendor',
          select: 'businessName contactInfo.businessEmail user',
          populate: {
            path: 'user',
            select: 'firstName lastName email'
          }
        })
        .populate('processedBy', 'firstName lastName email')
        .sort(sort)
        .skip(skip)
        .limit(parseInt(limit));

      const total = await Payout.countDocuments(query);

      res.json({
        success: true,
        data: {
          payouts,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(total / parseInt(limit)),
            totalItems: total,
            itemsPerPage: parseInt(limit)
          }
        }
      });

    } catch (error) {
      console.error('Error fetching all payouts:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  /**
   * Get pending payouts specifically for admin processing
   */
  static async getPendingPayouts(req, res) {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;

      const payouts = await Payout.getPendingPayouts(limit, page);
      const total = await Payout.countDocuments({ status: 'pending' });

      res.json({
        success: true,
        data: {
          payouts,
          pagination: {
            currentPage: page,
            totalPages: Math.ceil(total / limit),
            totalItems: total,
            itemsPerPage: limit
          }
        }
      });

    } catch (error) {
      console.error('Error fetching pending payouts:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  /**
   * Get detailed information about a specific payout request
   */
  static async getPayoutDetails(req, res) {
    try {
      const { payoutId } = req.params;

      const payout = await Payout.findById(payoutId)
        .populate({
          path: 'vendor',
          select: 'businessName contactInfo user bankDetails',
          populate: {
            path: 'user',
            select: 'firstName lastName email phone'
          }
        })
        .populate('processedBy', 'firstName lastName email role');

      if (!payout) {
        return res.status(404).json({
          success: false,
          message: 'Payout not found'
        });
      }

      res.json({
        success: true,
        data: payout
      });

    } catch (error) {
      console.error('Error fetching payout details:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  /**
   * Export payout data in various formats
   */
  static async exportPayouts(req, res) {
    try {
      const { status, startDate, endDate, format = 'json' } = req.query;
      
      // Build export query
      const query = PayoutRequestController._buildExportQuery(status, startDate, endDate);

      // Get payouts for export
      const payouts = await Payout.find(query)
        .populate({
          path: 'vendor',
          select: 'businessName contactInfo.businessEmail user',
          populate: {
            path: 'user',
            select: 'firstName lastName email'
          }
        })
        .populate('processedBy', 'firstName lastName email')
        .sort({ requestDate: -1 });

      if (format === 'csv') {
        return PayoutRequestController._exportAsCSV(res, payouts);
      }

      res.json({
        success: true,
        data: payouts
      });

    } catch (error) {
      console.error('Error exporting payouts:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Private helper methods

  /**
   * Calculate total amounts by status
   */
  static async _calculateTotalAmounts() {
    const result = await Payout.aggregate([
      {
        $group: {
          _id: null,
          totalRequested: { $sum: '$requestedAmount' },
          totalCompleted: { 
            $sum: { 
              $cond: [{ $eq: ['$status', 'completed'] }, '$requestedAmount', 0] 
            } 
          },
          totalPending: { 
            $sum: { 
              $cond: [{ $in: ['$status', ['pending', 'approved', 'processing']] }, '$requestedAmount', 0] 
            } 
          }
        }
      }
    ]);

    return result[0] || {
      totalRequested: 0,
      totalCompleted: 0,
      totalPending: 0
    };
  }

  /**
   * Get monthly payout trends for the last 12 months
   */
  static async _getMonthlyTrends() {
    const twelveMonthsAgo = new Date();
    twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);

    return await Payout.aggregate([
      {
        $match: {
          requestDate: { $gte: twelveMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$requestDate' },
            month: { $month: '$requestDate' }
          },
          count: { $sum: 1 },
          totalAmount: { $sum: '$requestedAmount' },
          completedAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'completed'] }, '$requestedAmount', 0]
            }
          }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);
  }

  /**
   * Build search query for filtering payouts
   */
  static async _buildSearchQuery(status, search) {
    let query = {};

    // Filter by status
    if (status && ['pending', 'approved', 'processing', 'completed', 'rejected', 'failed'].includes(status)) {
      query.status = status;
    }

    // Search functionality
    if (search) {
      const vendors = await Vendor.find({
        $or: [
          { businessName: { $regex: search, $options: 'i' } },
          { 'contactInfo.businessEmail': { $regex: search, $options: 'i' } }
        ]
      }).select('_id');

      const vendorIds = vendors.map(v => v._id);
      
      if (vendorIds.length > 0) {
        query.vendor = { $in: vendorIds };
      } else {
        // Search in metadata if no vendors match
        query['metadata.vendorBusinessName'] = { $regex: search, $options: 'i' };
      }
    }

    return query;
  }

  /**
   * Build query for export functionality
   */
  static _buildExportQuery(status, startDate, endDate) {
    let query = {};
    
    if (status) {
      query.status = status;
    }
    
    if (startDate || endDate) {
      query.requestDate = {};
      if (startDate) query.requestDate.$gte = new Date(startDate);
      if (endDate) query.requestDate.$lte = new Date(endDate);
    }

    return query;
  }

  /**
   * Export payouts as CSV
   */
  static _exportAsCSV(res, payouts) {
    const csvData = payouts.map(payout => ({
      'Payout ID': payout._id,
      'Vendor Business Name': payout.vendor?.businessName || payout.metadata?.vendorBusinessName,
      'Vendor Email': payout.vendor?.user?.email || payout.metadata?.vendorEmail,
      'Requested Amount': payout.requestedAmount,
      'Net Amount': payout.netAmount,
      'Method': payout.method,
      'Status': payout.status,
      'Request Date': payout.requestDate,
      'Processed Date': payout.processedDate,
      'Completed Date': payout.completedDate,
      'Transaction ID': payout.transactionId,
      'Processed By': payout.processedBy ? `${payout.processedBy.firstName} ${payout.processedBy.lastName}` : ''
    }));

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=payouts.csv');
    
    // Simple CSV conversion
    const csv = [
      Object.keys(csvData[0] || {}).join(','),
      ...csvData.map(row => Object.values(row).map(val => `"${val || ''}"`).join(','))
    ].join('\n');
    
    return res.send(csv);
  }
}

module.exports = PayoutRequestController;
