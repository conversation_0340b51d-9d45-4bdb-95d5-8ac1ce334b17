import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  DatePicker,
  Progress,
  Alert,
  Descriptions,
  Modal,
  Form,
  Input,
  Select
} from 'antd';
import {
  WalletOutlined,
  DollarOutlined,
  CalendarOutlined,
  BankOutlined,
  CreditCardOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const PayoutsManagement = () => {
  const [payouts, setPayouts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [paymentMethodModalVisible, setPaymentMethodModalVisible] = useState(false);
  const [form] = Form.useForm();

  // Sample data - replace with actual API calls
  const samplePayouts = [
    {
      id: 'PAY-001',
      amount: 1250.75,
      commission: 187.61,
      netAmount: 1063.14,
      status: 'completed',
      requestDate: '2024-03-01',
      processedDate: '2024-03-03',
      method: 'bank_transfer',
      reference: 'TXN123456789',
      period: 'Feb 2024'
    },
    {
      id: 'PAY-002',
      amount: 890.50,
      commission: 133.58,
      netAmount: 756.92,
      status: 'processing',
      requestDate: '2024-03-15',
      processedDate: null,
      method: 'paypal',
      reference: null,
      period: 'Mar 1-15, 2024'
    },
    {
      id: 'PAY-003',
      amount: 2150.25,
      commission: 322.54,
      netAmount: 1827.71,
      status: 'pending',
      requestDate: '2024-03-25',
      processedDate: null,
      method: 'bank_transfer',
      reference: null,
      period: 'Mar 16-25, 2024'
    }
  ];

  const currentBalance = {
    totalEarnings: 3456.78,
    commission: 518.51,
    availableBalance: 2938.27,
    pendingAmount: 1827.71,
    minimumPayout: 100.00
  };

  const paymentMethods = {
    bank_transfer: {
      type: 'Bank Transfer',
      accountNumber: '**** **** **** 1234',
      bankName: 'Chase Bank',
      isDefault: true
    },
    paypal: {
      type: 'PayPal',
      email: '<EMAIL>',
      isDefault: false
    }
  };

  useEffect(() => {
    fetchPayouts();
  }, []);

  const fetchPayouts = async () => {
    setLoading(true);
    try {
      // Simulate API call
      setTimeout(() => {
        setPayouts(samplePayouts);
        setLoading(false);
      }, 1000);
    } catch (error) {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'green';
      case 'processing':
        return 'blue';
      case 'pending':
        return 'orange';
      case 'failed':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined />;
      case 'processing':
        return <ClockCircleOutlined />;
      case 'pending':
        return <ClockCircleOutlined />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  const getMethodIcon = (method) => {
    switch (method) {
      case 'bank_transfer':
        return <BankOutlined />;
      case 'paypal':
        return <CreditCardOutlined />;
      default:
        return <WalletOutlined />;
    }
  };

  const columns = [
    {
      title: 'Payout ID',
      dataIndex: 'id',
      key: 'id',
      render: (id) => <strong>{id}</strong>,
    },
    {
      title: 'Period',
      dataIndex: 'period',
      key: 'period',
    },
    {
      title: 'Gross Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => `$${amount.toFixed(2)}`,
      sorter: (a, b) => a.amount - b.amount,
    },
    {
      title: 'Commission',
      dataIndex: 'commission',
      key: 'commission',
      render: (commission) => (
        <span style={{ color: '#f5222d' }}>-${commission.toFixed(2)}</span>
      ),
    },
    {
      title: 'Net Amount',
      dataIndex: 'netAmount',
      key: 'netAmount',
      render: (netAmount) => (
        <strong style={{ color: '#52c41a' }}>${netAmount.toFixed(2)}</strong>
      ),
      sorter: (a, b) => a.netAmount - b.netAmount,
    },
    {
      title: 'Method',
      dataIndex: 'method',
      key: 'method',
      render: (method) => (
        <Space>
          {getMethodIcon(method)}
          {paymentMethods[method]?.type || method}
        </Space>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Request Date',
      dataIndex: 'requestDate',
      key: 'requestDate',
      sorter: (a, b) => new Date(a.requestDate) - new Date(b.requestDate),
    },
    {
      title: 'Processed Date',
      dataIndex: 'processedDate',
      key: 'processedDate',
      render: (date) => date || 'Pending',
    },
  ];

  const totalPayouts = payouts.length;
  const completedPayouts = payouts.filter(payout => payout.status === 'completed').length;
  const totalPaidAmount = payouts
    .filter(payout => payout.status === 'completed')
    .reduce((sum, payout) => sum + payout.netAmount, 0);

  const progressToMinimum = Math.min((currentBalance.availableBalance / currentBalance.minimumPayout) * 100, 100);

  return (
    <div>
      <Title level={2}>Payouts Management</Title>
      
      {/* Current Balance Card */}
      <Card 
        title={
          <Space>
            <WalletOutlined />
            Current Balance
          </Space>
        }
        extra={
          <Button 
            type="primary" 
            icon={<SettingOutlined />}
            onClick={() => setPaymentMethodModalVisible(true)}
          >
            Payment Methods
          </Button>
        }
        style={{ marginBottom: 24 }}
      >
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="Available Balance"
              value={currentBalance.availableBalance}
              prefix={<DollarOutlined />}
              formatter={(value) => `$${value.toFixed(2)}`}
              valueStyle={{ color: '#52c41a', fontSize: '24px' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Pending Amount"
              value={currentBalance.pendingAmount}
              prefix={<ClockCircleOutlined />}
              formatter={(value) => `$${value.toFixed(2)}`}
              valueStyle={{ color: '#faad14' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Total Earnings"
              value={currentBalance.totalEarnings}
              prefix={<DollarOutlined />}
              formatter={(value) => `$${value.toFixed(2)}`}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Total Commission"
              value={currentBalance.commission}
              prefix={<DollarOutlined />}
              formatter={(value) => `$${value.toFixed(2)}`}
              valueStyle={{ color: '#f5222d' }}
            />
          </Col>
        </Row>
        
        <div style={{ marginTop: 24 }}>
          <Text strong>Progress to Minimum Payout (${currentBalance.minimumPayout})</Text>
          <Progress 
            percent={progressToMinimum} 
            strokeColor="#52c41a"
            style={{ marginTop: 8 }}
          />
          {currentBalance.availableBalance >= currentBalance.minimumPayout ? (
            <Alert
              message="You can request a payout!"
              description="Your available balance meets the minimum payout requirement."
              type="success"
              showIcon
              style={{ marginTop: 16 }}
              action={
                <Button type="primary" size="small">
                  Request Payout
                </Button>
              }
            />
          ) : (
            <Alert
              message={`$${(currentBalance.minimumPayout - currentBalance.availableBalance).toFixed(2)} more needed`}
              description="You need to reach the minimum payout amount to request a withdrawal."
              type="info"
              showIcon
              style={{ marginTop: 16 }}
            />
          )}
        </div>
      </Card>

      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="Total Payouts"
              value={totalPayouts}
              prefix={<WalletOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="Completed Payouts"
              value={completedPayouts}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="Total Paid"
              value={totalPaidAmount}
              prefix={<DollarOutlined />}
              formatter={(value) => `$${value.toFixed(2)}`}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Payment Methods */}
      <Card title="Payment Methods" style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          {Object.entries(paymentMethods).map(([key, method]) => (
            <Col span={12} key={key}>
              <Card size="small" style={{ backgroundColor: method.isDefault ? '#f6ffed' : '#fafafa' }}>
                <Space>
                  {getMethodIcon(key)}
                  <div>
                    <div style={{ fontWeight: 500 }}>{method.type}</div>
                    <div style={{ color: '#666', fontSize: '12px' }}>
                      {method.accountNumber || method.email}
                    </div>
                    {method.bankName && (
                      <div style={{ color: '#666', fontSize: '12px' }}>{method.bankName}</div>
                    )}
                  </div>
                  {method.isDefault && (
                    <Tag color="green" size="small">Default</Tag>
                  )}
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* Payouts History */}
      <Card title="Payout History">
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <RangePicker />
        </div>

        <Table
          columns={columns}
          dataSource={payouts}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} payouts`,
          }}
        />
      </Card>

      {/* Payment Methods Modal */}
      <Modal
        title="Manage Payment Methods"
        open={paymentMethodModalVisible}
        onCancel={() => setPaymentMethodModalVisible(false)}
        footer={null}
        width={600}
      >
        <div style={{ marginBottom: 16 }}>
          <Button type="primary" style={{ marginBottom: 16 }}>
            Add New Payment Method
          </Button>
        </div>
        
        {Object.entries(paymentMethods).map(([key, method]) => (
          <Card key={key} size="small" style={{ marginBottom: 16 }}>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="Type">{method.type}</Descriptions.Item>
              <Descriptions.Item label="Details">
                {method.accountNumber || method.email}
              </Descriptions.Item>
              {method.bankName && (
                <Descriptions.Item label="Bank">{method.bankName}</Descriptions.Item>
              )}
              <Descriptions.Item label="Status">
                {method.isDefault ? (
                  <Tag color="green">Default</Tag>
                ) : (
                  <Button size="small" type="link">Set as Default</Button>
                )}
              </Descriptions.Item>
            </Descriptions>
            <div style={{ marginTop: 8 }}>
              <Space>
                <Button size="small">Edit</Button>
                <Button size="small" danger>Remove</Button>
              </Space>
            </div>
          </Card>
        ))}
      </Modal>
    </div>
  );
};

export default PayoutsManagement;