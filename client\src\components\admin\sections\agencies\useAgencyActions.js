import { useState } from 'react';
import { message } from 'antd';

// This would be imported from your API service
// import { agencyApi } from '../../../../services/agencyApi';

// Mock API functions - replace these with actual API calls
const mockAgencyApi = {
  // Fetch all agencies
  getAgencies: async (pagination = {}) => {
    try {
      // Mock delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock agency data
      const mockAgencies = [
        {
          id: '1',
          _id: '1',
          businessName: 'Global Trade Agency',
          user: {
            firstName: '<PERSON>',
            lastName: 'Doe',
            email: '<EMAIL>'
          },
          status: 'active',
          verification: {
            status: 'verified'
          },
          totalVendors: 25,
          totalRevenue: 150000,
          commission: {
            rate: 10
          },
          createdAt: new Date('2024-01-15')
        },
        {
          id: '2',
          _id: '2',
          businessName: 'E-Commerce Hub Agency',
          user: {
            firstName: '<PERSON>',
            lastName: '<PERSON>',
            email: '<EMAIL>'
          },
          status: 'active',
          verification: {
            status: 'verified'
          },
          totalVendors: 18,
          totalRevenue: 95000,
          commission: {
            rate: 12
          },
          createdAt: new Date('2024-02-20')
        },
        {
          id: '3',
          _id: '3',
          businessName: 'Digital Market Solutions',
          user: {
            firstName: 'Mike',
            lastName: 'Johnson',
            email: '<EMAIL>'
          },
          status: 'pending_approval',
          verification: {
            status: 'pending'
          },
          totalVendors: 5,
          totalRevenue: 12000,
          commission: {
            rate: 8
          },
          createdAt: new Date('2024-06-10')
        }
      ];

      return {
        agencies: mockAgencies,
        pagination: {
          current: pagination.current || 1,
          pageSize: pagination.pageSize || 10,
          total: mockAgencies.length
        }
      };
    } catch (error) {
      console.error('Error fetching agencies:', error);
      throw error;
    }
  },

  // Fetch vendors for a specific agency
  getAgencyVendors: async (agencyId) => {
    try {
      // Mock delay
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Mock vendor data for the agency
      const mockVendors = [
        {
          id: '1',
          _id: '1',
          businessName: 'Tech Gadgets Store',
          user: {
            firstName: 'Alice',
            lastName: 'Wilson',
            email: '<EMAIL>'
          },
          contactInfo: {
            businessEmail: '<EMAIL>'
          },
          status: 'active',
          verification: {
            status: 'verified'
          },
          performance: {
            rating: 4.5,
            totalProducts: 120,
            totalSales: 850,
            totalRevenue: 45000
          },
          commission: {
            rate: 15
          },
          createdAt: new Date('2024-03-10')
        },
        {
          id: '2',
          _id: '2',
          businessName: 'Fashion Forward',
          user: {
            firstName: 'Bob',
            lastName: 'Brown',
            email: '<EMAIL>'
          },
          contactInfo: {
            businessEmail: '<EMAIL>'
          },
          status: 'active',
          verification: {
            status: 'verified'
          },
          performance: {
            rating: 4.2,
            totalProducts: 85,
            totalSales: 620,
            totalRevenue: 32000
          },
          commission: {
            rate: 12
          },
          createdAt: new Date('2024-04-05')
        },
        {
          id: '3',
          _id: '3',
          businessName: 'Home Essentials',
          user: {
            firstName: 'Carol',
            lastName: 'Davis',
            email: '<EMAIL>'
          },
          contactInfo: {
            businessEmail: '<EMAIL>'
          },
          status: 'active',
          verification: {
            status: 'verified'
          },
          performance: {
            rating: 4.7,
            totalProducts: 95,
            totalSales: 720,
            totalRevenue: 38000
          },
          commission: {
            rate: 10
          },
          createdAt: new Date('2024-04-20')
        }
      ];

      return mockVendors;
    } catch (error) {
      console.error('Error fetching agency vendors:', error);
      throw error;
    }
  }
};

const useAgencyActions = () => {
  const [loading, setLoading] = useState(false);

  const fetchAgencies = async (pagination = {}, filters = {}) => {
    setLoading(true);
    try {
      const result = await mockAgencyApi.getAgencies(pagination);
      return result;
    } catch (error) {
      message.error('Failed to fetch agencies');
      console.error('Error fetching agencies:', error);
      return { agencies: [], pagination: { current: 1, pageSize: 10, total: 0 } };
    } finally {
      setLoading(false);
    }
  };

  const fetchAgencyVendors = async (agencyId) => {
    setLoading(true);
    try {
      const vendors = await mockAgencyApi.getAgencyVendors(agencyId);
      return vendors;
    } catch (error) {
      message.error('Failed to fetch agency vendors');
      console.error('Error fetching agency vendors:', error);
      return [];
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    fetchAgencies,
    fetchAgencyVendors
  };
};

export { useAgencyActions };
