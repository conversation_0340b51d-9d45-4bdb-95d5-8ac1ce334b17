const { verifyToken, requireUserType, requireEmailVerification, requireVendorApproval } = require('./authMiddleware');

// Vendor-specific authentication middleware
const authenticateVendor = [
    verifyToken,
    requireUserType(['vendor']),
    requireEmailVerification,
    requireVendorApproval
];

// Vendor authentication without approval requirement (for pending vendors)
const authenticateVendorBasic = [
    verifyToken,
    requireUserType(['vendor']),
    requireEmailVerification
];

// Vendor authentication without email verification (for initial setup)
const authenticateVendorMinimal = [
    verifyToken,
    requireUserType(['vendor'])
];

module.exports = {
    authenticateVendor,
    authenticateVendorBasic,
    authenticateVendorMinimal,
    // Export individual functions for flexibility
    verifyToken,
    requireUserType,
    requireEmailVerification,
    requireVendorApproval
};