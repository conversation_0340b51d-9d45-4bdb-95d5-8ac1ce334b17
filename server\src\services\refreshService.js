/**
 * Refresh Service - Simple replacement for Socket.IO
 * Uses polling and manual refresh instead of real-time updates
 */

const { User, Vendor, Order, Product } = require('../models');

class RefreshService {
  constructor() {
    console.log('🔄 Refresh service initialized (Socket.IO replacement)');
  }

  /**
   * Get dashboard statistics for admin
   */
  async getDashboardStats() {
    try {
      const [
        totalOrders,
        totalUsers,
        totalVendors,
        totalProducts,
        recentOrders,
        pendingOrders,
        totalRevenue
      ] = await Promise.all([
        Order.countDocuments(),
        User.countDocuments({ userType: 'customer' }),
        Vendor.countDocuments(),
        Product.countDocuments(),
        Order.find().sort({ createdAt: -1 }).limit(10).populate('customer', 'firstName lastName email'),
        Order.countDocuments({ status: 'pending' }),
        Order.aggregate([
          { $match: { status: 'delivered' } },
          { $group: { _id: null, total: { $sum: '$total' } } }
        ])
      ]);

      return {
        success: true,
        data: {
          totalOrders,
          totalUsers,
          totalVendors,
          totalProducts,
          recentOrders,
          pendingOrders,
          totalRevenue: totalRevenue[0]?.total || 0,
          lastUpdated: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('Error getting dashboard stats:', error);
      return {
        success: false,
        message: 'Failed to fetch dashboard statistics'
      };
    }
  }

  /**
   * Get vendor-specific statistics
   */
  async getVendorStats(vendorId) {
    try {
      const vendor = await Vendor.findById(vendorId);
      if (!vendor) {
        return { success: false, message: 'Vendor not found' };
      }

      const [
        totalOrders,
        totalProducts,
        totalRevenue,
        pendingOrders,
        recentOrders
      ] = await Promise.all([
        Order.countDocuments({ 'items.vendor': vendorId }),
        Product.countDocuments({ vendor: vendorId }),
        Order.aggregate([
          { $match: { 'items.vendor': vendorId, status: 'delivered' } },
          { $unwind: '$items' },
          { $match: { 'items.vendor': vendorId } },
          { $group: { _id: null, total: { $sum: '$items.totalPrice' } } }
        ]),
        Order.countDocuments({ 'items.vendor': vendorId, status: 'pending' }),
        Order.find({ 'items.vendor': vendorId })
          .sort({ createdAt: -1 })
          .limit(5)
          .populate('customer', 'firstName lastName email')
      ]);

      return {
        success: true,
        data: {
          totalOrders,
          totalProducts,
          totalRevenue: totalRevenue[0]?.total || 0,
          pendingOrders,
          recentOrders,
          lastUpdated: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('Error getting vendor stats:', error);
      return {
        success: false,
        message: 'Failed to fetch vendor statistics'
      };
    }
  }

  /**
   * Get recent activities for dashboard
   */
  async getRecentActivities(limit = 20) {
    try {
      const [recentOrders, recentUsers, recentVendors] = await Promise.all([
        Order.find()
          .sort({ createdAt: -1 })
          .limit(limit)
          .populate('customer', 'firstName lastName email')
          .select('orderNumber status total createdAt customer'),
        User.find({ userType: 'customer' })
          .sort({ createdAt: -1 })
          .limit(limit)
          .select('firstName lastName email createdAt'),
        Vendor.find()
          .sort({ createdAt: -1 })
          .limit(limit)
          .populate('user', 'firstName lastName email')
          .select('businessName status createdAt user')
      ]);

      // Combine and sort all activities
      const activities = [
        ...recentOrders.map(order => ({
          type: 'order',
          title: `New Order #${order.orderNumber}`,
          description: `Order from ${order.customer?.firstName} ${order.customer?.lastName}`,
          timestamp: order.createdAt,
          data: order
        })),
        ...recentUsers.map(user => ({
          type: 'user',
          title: 'New User Registration',
          description: `${user.firstName} ${user.lastName} joined`,
          timestamp: user.createdAt,
          data: user
        })),
        ...recentVendors.map(vendor => ({
          type: 'vendor',
          title: 'New Vendor Registration',
          description: `${vendor.businessName} registered`,
          timestamp: vendor.createdAt,
          data: vendor
        }))
      ].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
       .slice(0, limit);

      return {
        success: true,
        data: activities
      };
    } catch (error) {
      console.error('Error getting recent activities:', error);
      return {
        success: false,
        message: 'Failed to fetch recent activities'
      };
    }
  }

  /**
   * Get order updates for a specific user
   */
  async getOrderUpdates(userId, userType) {
    try {
      let query = {};
      
      if (userType === 'customer') {
        query.customer = userId;
      } else if (userType === 'vendor') {
        const vendor = await Vendor.findOne({ user: userId });
        if (vendor) {
          query['items.vendor'] = vendor._id;
        }
      }
      // For admin, get all orders

      const orders = await Order.find(query)
        .sort({ updatedAt: -1 })
        .limit(50)
        .populate('customer', 'firstName lastName email')
        .select('orderNumber status total updatedAt customer items');

      return {
        success: true,
        data: orders
      };
    } catch (error) {
      console.error('Error getting order updates:', error);
      return {
        success: false,
        message: 'Failed to fetch order updates'
      };
    }
  }

  /**
   * Check for low stock products
   */
  async getLowStockAlerts(vendorId = null, threshold = 10) {
    try {
      let query = { 'inventory.quantity': { $lte: threshold } };
      
      if (vendorId) {
        query.vendor = vendorId;
      }

      const lowStockProducts = await Product.find(query)
        .populate('vendor', 'businessName')
        .select('name inventory.quantity vendor sku')
        .sort({ 'inventory.quantity': 1 });

      return {
        success: true,
        data: lowStockProducts
      };
    } catch (error) {
      console.error('Error getting low stock alerts:', error);
      return {
        success: false,
        message: 'Failed to fetch low stock alerts'
      };
    }
  }

  /**
   * Get system health status
   */
  async getSystemHealth() {
    try {
      const [
        dbStatus,
        totalUsers,
        totalOrders,
        errorLogs
      ] = await Promise.all([
        this.checkDatabaseConnection(),
        User.countDocuments(),
        Order.countDocuments(),
        this.getRecentErrors()
      ]);

      return {
        success: true,
        data: {
          database: dbStatus,
          totalUsers,
          totalOrders,
          errors: errorLogs,
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          lastChecked: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('Error getting system health:', error);
      return {
        success: false,
        message: 'Failed to fetch system health'
      };
    }
  }

  /**
   * Check database connection
   */
  async checkDatabaseConnection() {
    try {
      await User.findOne().limit(1);
      return { status: 'connected', message: 'Database connection is healthy' };
    } catch (error) {
      return { status: 'error', message: 'Database connection failed' };
    }
  }

  /**
   * Get recent errors (placeholder - you can implement error logging)
   */
  async getRecentErrors() {
    // Placeholder for error logging system
    return [];
  }
}

// Create singleton instance
const refreshService = new RefreshService();

module.exports = refreshService;