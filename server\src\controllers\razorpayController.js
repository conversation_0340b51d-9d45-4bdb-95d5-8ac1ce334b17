const Razorpay = require('razorpay');
const crypto = require('crypto');
const { Order, Product, Setting, Cart, User } = require('../models');
const shiprocketService = require('../services/shiprocketService');

// Create Razorpay Order
const createRazorpayOrder = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { items, billing, shipping } = req.body;

    console.log('Creating Razorpay order for user:', userId);
    console.log('Items to process:', items?.length || 0);

    // Get Razorpay keys
    const keyId = process.env.RAZORPAY_KEY_ID || (await Setting.findOne({ key: 'razorpay_key_id' }))?.value;
    const keySecret = process.env.RAZORPAY_KEY_SECRET || (await Setting.findOne({ key: 'razorpay_key_secret' }))?.value;
    
    if (!keyId || !keySecret) {
      return res.status(400).json({ success: false, message: 'Ra<PERSON>pay not configured' });
    }

    const razorpay = new Razorpay({ key_id: keyId, key_secret: keySecret });

    // Calculate total
    let total = 0;
    const orderItems = [];

    for (const item of items) {
      const product = await Product.findById(item.productId).populate('vendor');
      if (!product) {
        return res.status(404).json({ success: false, message: 'Product not found' });
      }

      // Use salePrice if available, otherwise use basePrice
      const unitPrice = product.pricing.salePrice || product.pricing.basePrice;

      console.log(`Product ${product.name}: basePrice=${product.pricing.basePrice}, salePrice=${product.pricing.salePrice}, unitPrice=${unitPrice}`);

      if (!unitPrice || isNaN(unitPrice)) {
        console.error(`Invalid price for product ${product.name}:`, {
          basePrice: product.pricing.basePrice,
          salePrice: product.pricing.salePrice,
          unitPrice
        });
        return res.status(400).json({
          success: false,
          message: `Invalid price for product: ${product.name}`
        });
      }

      const itemTotal = unitPrice * item.quantity;
      total += itemTotal;

      console.log(`Item total for ${product.name}: ${unitPrice} x ${item.quantity} = ${itemTotal}`);

      orderItems.push({
        product: product._id,
        vendor: product.vendor._id,
        name: product.name,
        sku: product.sku,
        quantity: item.quantity,
        unitPrice: unitPrice,
        totalPrice: itemTotal,
        status: 'pending'
      });
    }

    // Validate total before proceeding
    if (!total || isNaN(total) || total <= 0) {
      console.error('Invalid total calculated:', total);
      return res.status(400).json({
        success: false,
        message: 'Invalid cart total. Please check your cart items.'
      });
    }

    // Add shipping and tax
    const subtotal = total;
    const shipping_cost = total > 500 ? 0 : 50;
    const tax = Math.round(total * 0.18);
    const finalTotal = subtotal + shipping_cost + tax;

    console.log('Order calculations:', { subtotal, shipping_cost, tax, finalTotal });

    // Validate final calculations
    if (isNaN(finalTotal) || isNaN(tax) || isNaN(shipping_cost)) {
      console.error('Invalid calculations:', { finalTotal, tax, shipping_cost });
      return res.status(400).json({
        success: false,
        message: 'Error calculating order total. Please try again.'
      });
    }

    const orderNumber = `ORD${Date.now()}`;

    console.log('Creating order with data:', {
      orderNumber,
      customer: userId,
      itemsCount: orderItems.length,
      pricing: { subtotal, tax, shipping: shipping_cost, total: finalTotal }
    });

    // Create order
    const order = new Order({
      orderNumber,
      customer: userId,
      items: orderItems,
      billing,
      shipping: { ...shipping, cost: shipping_cost },
      payment: { method: 'razorpay', status: 'pending', currency: 'INR' },
      pricing: {
        subtotal: subtotal,
        tax: tax,
        shipping: shipping_cost,
        total: finalTotal
      },
      orderStatus: 'pending'
    });

    await order.save();
    console.log('Order created successfully:', order._id);

    // Create Razorpay order
    const razorpayOrder = await razorpay.orders.create({
      amount: finalTotal * 100, // Razorpay expects amount in paise
      currency: 'INR',
      receipt: orderNumber,
      notes: {
        order_id: order._id.toString()
      }
    });

    res.json({
      success: true,
      data: {
        order_id: order._id,
        razorpay_order_id: razorpayOrder.id,
        amount: razorpayOrder.amount,
        key: keyId
      }
    });

  } catch (error) {
    console.error('Create order error:', error);

    // Provide more specific error messages
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validationErrors
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Verify Razorpay Payment
const verifyRazorpayPayment = async (req, res) => {
  try {
    const { razorpay_payment_id, razorpay_order_id, razorpay_signature, order_id } = req.body;

    const keySecret = process.env.RAZORPAY_KEY_SECRET || (await Setting.findOne({ key: 'razorpay_key_secret' }))?.value;

    // Verify signature
    const body = razorpay_order_id + '|' + razorpay_payment_id;
    const expectedSignature = crypto.createHmac('sha256', keySecret).update(body).digest('hex');

    if (expectedSignature !== razorpay_signature) {
      return res.status(400).json({ success: false, message: 'Payment verification failed' });
    }

    // Update order
    const order = await Order.findById(order_id);
    if (!order) {
      return res.status(404).json({ success: false, message: 'Order not found' });
    }

    // Mark payment as completed
    order.payment.status = 'completed';
    order.payment.transactionId = razorpay_payment_id;
    order.payment.paidAt = new Date();
    order.orderStatus = 'confirmed';
    
    order.items.forEach(item => item.status = 'confirmed');
    await order.save();

    // Update inventory
    for (const item of order.items) {
      await Product.findByIdAndUpdate(item.product, {
        $inc: {
          'inventory.quantity': -item.quantity,
          'sales.totalSold': item.quantity,
          'sales.totalRevenue': item.totalPrice
        }
      });
    }

    // Calculate commissions
    await order.calculateCommissions();

    // Trigger Shiprocket order creation per vendor (idempotent-ish)
    try {
      const alreadyPushedToShiprocket = Array.isArray(order.shiprocketOrders) && order.shiprocketOrders.length > 0;
      if (!alreadyPushedToShiprocket) {
        const shiprocketResults = await shiprocketService.createOrdersForVendors({
          orderNumber: order.orderNumber,
          billing: order.billing,
          shipping: order.shipping,
          items: order.items,
          payment: order.payment,
          pricing: order.pricing
        });

        // Store per-vendor references for traceability (best-effort)
        order.shiprocketOrders = order.shiprocketOrders || [];
        for (const res of shiprocketResults) {
          order.shiprocketOrders.push({
            vendorId: res.vendorId,
            vendorName: undefined,
            shiprocketOrderId: String(res.response?.order_id || res.response?.orderId || ''),
            awbCode: res.response?.awb_code || res.response?.awbCode || '',
            courierName: res.response?.courier_name || res.response?.courierName || '',
            pickupLocation: res.pickupLocation || '',
            items: order.items
              .filter(i => String(i.vendor) === String(res.vendorId))
              .map(i => ({ name: i.name, quantity: i.quantity, sku: i.sku })),
            error: res.error || undefined
          });
        }
        await order.save();
        console.log('✅ Shiprocket vendor orders processed after payment verification');
      }
    } catch (shipErr) {
      console.error('Shiprocket push after payment failed:', shipErr.response?.data || shipErr.message);
      // Do not fail the verification response due to Shiprocket issues
    }

    res.json({ success: true, message: 'Payment verified', data: { orderId: order._id } });

  } catch (error) {
    console.error('Verify payment error:', error);
    res.status(500).json({ success: false, message: 'Payment verification failed' });
  }
};

/**
 * Razorpay Webhook Handler
 */
const handleRazorpayWebhook = async (req, res) => {
  try {
    const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET;
    
    console.log('📥 Webhook received:', {
      event: req.body.event,
      hasSecret: !!webhookSecret,
      hasSignature: !!req.headers['x-razorpay-signature']
    });
    
    // Verify webhook signature if secret provided
    if (webhookSecret && webhookSecret !== 'whsec_your_webhook_secret_here') {
      const signature = req.headers['x-razorpay-signature'];
      if (!signature) {
        console.log('⚠️  No signature provided in webhook');
        return res.status(400).json({ success: false, message: 'No signature provided' });
      }
      
      const body = JSON.stringify(req.body);
      const expectedSignature = crypto.createHmac('sha256', webhookSecret).update(body).digest('hex');
      
      if (signature !== expectedSignature) {
        console.log('❌ Signature verification failed');
        return res.status(400).json({ success: false, message: 'Invalid signature' });
      }
      console.log('✅ Signature verified successfully');
    } else {
      console.log('⚠️  Webhook secret not configured - skipping signature verification (development mode)');
    }

    const { event, payload } = req.body;
    console.log('Razorpay Webhook:', event);

    // Handle payment events
    if (event === 'payment.captured') {
      const payment = payload.payment.entity;
      const orderId = payment.notes?.order_id;
      
      if (orderId) {
        const order = await Order.findById(orderId);
        if (order && order.payment.status === 'pending') {
          order.payment.status = 'completed';
          order.orderStatus = 'confirmed';
          order.payment.paidAt = new Date();
          await order.save();

          // Also try to push to Shiprocket if not already pushed
          try {
            const alreadyPushedToShiprocket = Array.isArray(order.shiprocketOrders) && order.shiprocketOrders.length > 0;
            if (!alreadyPushedToShiprocket) {
              const shiprocketResults = await shiprocketService.createOrdersForVendors({
                orderNumber: order.orderNumber,
                billing: order.billing,
                shipping: order.shipping,
                items: order.items,
                payment: order.payment,
                pricing: order.pricing
              });

              order.shiprocketOrders = order.shiprocketOrders || [];
              for (const res of shiprocketResults) {
                order.shiprocketOrders.push({
                  vendorId: res.vendorId,
                  vendorName: undefined,
                  shiprocketOrderId: String(res.response?.order_id || res.response?.orderId || ''),
                  awbCode: res.response?.awb_code || res.response?.awbCode || '',
                  courierName: res.response?.courier_name || res.response?.courierName || '',
                  pickupLocation: res.pickupLocation || '',
                  items: order.items
                    .filter(i => String(i.vendor) === String(res.vendorId))
                    .map(i => ({ name: i.name, quantity: i.quantity, sku: i.sku })),
                  error: res.error || undefined
                });
              }
              await order.save();
              console.log('✅ Shiprocket vendor orders processed via webhook payment.captured');
            }
          } catch (shipErr) {
            console.error('Shiprocket push via webhook failed:', shipErr.response?.data || shipErr.message);
          }
        }
      }
    }

    res.json({ success: true });

  } catch (error) {
    console.error('Webhook error:', error);
    res.status(500).json({ success: false });
  }
};

/**
 * Get Payment Status
 */
const getPaymentStatus = async (req, res) => {
  try {
    const { orderId } = req.params;
    const order = await Order.findById(orderId).select('payment orderStatus orderNumber pricing.total');
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    res.json({
      success: true,
      data: {
        orderNumber: order.orderNumber,
        paymentStatus: order.payment.status,
        orderStatus: order.orderStatus,
        total: order.pricing.total
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to get payment status'
    });
  }
};

/**
 * Initiate payment directly from cart - for Razorpay standard checkout
 */
const initiatePaymentFromCart = async (req, res) => {
  try {
    const userId = req.user.userId;

    // Get user's cart
    const cart = await Cart.findOne({ customer: userId })
      .populate('items.product', 'name pricing inventory vendor sku')
      .populate('items.vendor', 'businessName');

    if (!cart || cart.items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Cart is empty. Please add items to proceed.'
      });
    }

    // Get user details for billing
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Prepare order data from cart
    const items = cart.items.map(item => ({
      productId: item.product._id,
      quantity: item.quantity
    }));

    // Use provided billing/shipping or default from user
    const defaultBilling = {
      firstName: user.firstName || 'Customer',
      lastName: user.lastName || '',
      email: user.email,
      phone: user.phone || '9999999999',
      address: {
        street: user.address || 'Default Address',
        city: user.city || 'Default City',
        state: user.state || 'Default State',
        zipCode: user.zipCode || '000000',
        country: user.country || 'India'
      }
    };

    const orderData = {
      items,
      billing: req.body.billing || defaultBilling,
      shipping: req.body.shipping || defaultBilling
    };

    // Create Razorpay order using existing logic
    req.body = orderData;
    await createRazorpayOrder(req, res);

  } catch (error) {
    console.error('Initiate payment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initiate payment',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  createRazorpayOrder,
  verifyRazorpayPayment,
  handleRazorpayWebhook,
  getPaymentStatus,
  initiatePaymentFromCart
};
