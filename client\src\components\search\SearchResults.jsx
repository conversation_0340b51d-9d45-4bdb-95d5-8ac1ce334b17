import React from 'react';
import { Spin, Empty } from 'antd';
import { AppstoreOutlined, BarsOutlined } from '@ant-design/icons';
import ProductItem from '../ui/ProductItem';

const SearchResults = ({ 
  loading, 
  searchTerm, 
  searchResults, 
  viewMode, 
  onViewModeChange 
}) => {
  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Spin size="large" />
        <span className="ml-3 text-gray-600">Searching products...</span>
      </div>
    );
  }

  if (searchTerm && searchResults.length === 0) {
    return (
      <div className="text-center py-12">
        <Empty
          description={
            <div>
              <p className="text-gray-500 mb-2">No products found for "{searchTerm}"</p>
              <p className="text-sm text-gray-400">Try adjusting your search terms or filters</p>
            </div>
          }
        />
      </div>
    );
  }

  if (searchResults.length > 0) {
    return (
      <div>
        {/* Results Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 gap-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-800 mb-1 sm:mb-0">
              Search Results
            </h2>
            <span className="text-sm text-gray-600">
              Showing {searchResults.length} products
            </span>
          </div>
          
          {/* View Mode Toggle */}
          <div className="flex rounded-md border w-fit">
            <button
              onClick={() => onViewModeChange('grid')}
              className={`p-2 transition-colors ${viewMode === 'grid' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
              title="Grid View"
            >
              <AppstoreOutlined />
            </button>
            <button
              onClick={() => onViewModeChange('list')}
              className={`p-2 transition-colors ${viewMode === 'list' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
              title="List View"
            >
              <BarsOutlined />
            </button>
          </div>
        </div>

        {/* Products Grid/List */}
        <div 
          className={
            viewMode === 'grid' 
              ? "grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5"
              : "flex flex-col gap-4"
          }
        >
          {searchResults.map(product => (
            <ProductItem
              key={product._id}
              product={product}
              className={viewMode === 'list' ? 'flex-row items-center' : ''}
              viewMode={viewMode}
            />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="text-center py-12">
      <div className="text-gray-500 mb-4">
        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">Start your search</h3>
      <p className="text-gray-500">
        Search for products, brands, or categories to find what you're looking for.
      </p>
    </div>
  );
};

export default SearchResults;
