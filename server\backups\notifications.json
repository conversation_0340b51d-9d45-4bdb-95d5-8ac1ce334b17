[{"_id": "68845fa63068aee0ad08d1c6", "recipient": "687f910bb3469708bd362e28", "sender": "68834d102652150cfe2e8e95", "type": "product_approved", "title": "Product Approved", "message": "Great news! Your product \"IPhone 16s\" has been approved and is now live on the platform.", "data": {"productId": "687fbcbcbe5c0e3209f5e38e", "productName": "IPhone 16s", "adminNotes": ""}, "relatedEntity": {"entityType": "Product", "entityId": "687fbcbcbe5c0e3209f5e38e"}, "status": "unread", "priority": "high", "channels": ["in_app", "email"], "deliveryStatus": {"in_app": {"sent": true, "read": false, "sentAt": "2025-07-26T04:55:03.371Z"}, "email": {"sent": true, "delivered": false, "opened": false, "sentAt": "2025-07-26T04:55:03.371Z"}, "sms": {"sent": false, "delivered": false}, "push": {"sent": false, "delivered": false, "clicked": false}}, "actionUrl": "/vendor/products/687fbcbcbe5c0e3209f5e38e", "actionText": "View Product", "metadata": {"tags": []}, "createdAt": "2025-07-26T04:55:02.957Z", "updatedAt": "2025-07-26T04:55:03.373Z", "__v": 0}, {"_id": "68845fb33068aee0ad08d1e2", "recipient": "6881e45be98a7c89412be755", "sender": "68834d102652150cfe2e8e95", "type": "product_approved", "title": "Product Approved", "message": "Great news! Your product \"Samsun\" has been approved and is now live on the platform.", "data": {"productId": "6881fd806652576061a0be95", "productName": "<PERSON><PERSON>", "adminNotes": ""}, "relatedEntity": {"entityType": "Product", "entityId": "6881fd806652576061a0be95"}, "status": "unread", "priority": "high", "channels": ["in_app", "email"], "deliveryStatus": {"in_app": {"sent": true, "read": false, "sentAt": "2025-07-26T04:55:15.346Z"}, "email": {"sent": true, "delivered": false, "opened": false, "sentAt": "2025-07-26T04:55:15.346Z"}, "sms": {"sent": false, "delivered": false}, "push": {"sent": false, "delivered": false, "clicked": false}}, "actionUrl": "/vendor/products/6881fd806652576061a0be95", "actionText": "View Product", "metadata": {"tags": []}, "createdAt": "2025-07-26T04:55:15.300Z", "updatedAt": "2025-07-26T04:55:15.347Z", "__v": 0}, {"_id": "68845fbb3068aee0ad08d1fd", "recipient": "687fd3e40bc86c687720d7ca", "sender": "68834d102652150cfe2e8e95", "type": "product_approved", "title": "Product Approved", "message": "Great news! Your product \"Sharingan\" has been approved and is now live on the platform.", "data": {"productId": "687fd9cfa82bdde976da2a1d", "productName": "Sharingan", "adminNotes": ""}, "relatedEntity": {"entityType": "Product", "entityId": "687fd9cfa82bdde976da2a1d"}, "status": "unread", "priority": "high", "channels": ["in_app", "email"], "deliveryStatus": {"in_app": {"sent": true, "read": false, "sentAt": "2025-07-26T04:55:23.150Z"}, "email": {"sent": true, "delivered": false, "opened": false, "sentAt": "2025-07-26T04:55:23.150Z"}, "sms": {"sent": false, "delivered": false}, "push": {"sent": false, "delivered": false, "clicked": false}}, "actionUrl": "/vendor/products/687fd9cfa82bdde976da2a1d", "actionText": "View Product", "metadata": {"tags": []}, "createdAt": "2025-07-26T04:55:23.146Z", "updatedAt": "2025-07-26T04:55:23.151Z", "__v": 0}]