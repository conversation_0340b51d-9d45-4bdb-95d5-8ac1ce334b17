import React, { useState } from 'react';
import {
  Button,
  Space,
  Input,
  InputNumber,
  Switch,
  Card,
  Row,
  Col,
  Popconfirm,
  message,
  Upload
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  UploadOutlined
} from '@ant-design/icons';

const ColorSelector = ({ value = [], onChange, disabled = false }) => {
  const [colors, setColors] = useState(value || []);

  const handleAddColor = () => {
    const newColor = {
      name: '',
      hexCode: '#000000',
      available: true,
      stock: 0,
      image: ''
    };
    const updatedColors = [...colors, newColor];
    setColors(updatedColors);
    onChange?.(updatedColors);
  };

  const handleColorChange = (index, field, newValue) => {
    const updatedColors = colors.map((color, i) => 
      i === index ? { ...color, [field]: newValue } : color
    );
    setColors(updatedColors);
    onChange?.(updatedColors);
  };

  const handleRemoveColor = (index) => {
    const updatedColors = colors.filter((_, i) => i !== index);
    setColors(updatedColors);
    onChange?.(updatedColors);
  };

  const handleImageUpload = (index, file) => {
    // In a real implementation, you would upload the file and get a URL
    // For now, we'll just create a preview URL
    const reader = new FileReader();
    reader.onload = (e) => {
      handleColorChange(index, 'image', e.target.result);
    };
    reader.readAsDataURL(file);
    return false; // Prevent default upload
  };

  const validateHexCode = (hex) => {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(hex);
  };

  return (
    <div>
      <Space direction="vertical" style={{ width: '100%' }}>
        {colors.map((color, index) => (
          <Card
            key={index}
            size="small"
            title={`Color ${index + 1}`}
            extra={
              <Popconfirm
                title="Are you sure you want to remove this color?"
                onConfirm={() => handleRemoveColor(index)}
                okText="Yes"
                cancelText="No"
              >
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                  size="small"
                  disabled={disabled}
                />
              </Popconfirm>
            }
            style={{ marginBottom: 8 }}
          >
            <Row gutter={16}>
              <Col span={8}>
                <div style={{ marginBottom: 8 }}>
                  <label style={{ fontSize: '12px', color: '#666' }}>Color Name *</label>
                  <Input
                    placeholder="e.g., Red, Blue, Black"
                    value={color.name}
                    onChange={(e) => handleColorChange(index, 'name', e.target.value)}
                    disabled={disabled}
                    status={!color.name ? 'error' : ''}
                  />
                </div>
              </Col>
              <Col span={6}>
                <div style={{ marginBottom: 8 }}>
                  <label style={{ fontSize: '12px', color: '#666' }}>Hex Code</label>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <Input
                      placeholder="#000000"
                      value={color.hexCode}
                      onChange={(e) => handleColorChange(index, 'hexCode', e.target.value)}
                      disabled={disabled}
                      status={!validateHexCode(color.hexCode) ? 'error' : ''}
                      style={{ flex: 1 }}
                    />
                    <div
                      style={{
                        width: 24,
                        height: 24,
                        backgroundColor: validateHexCode(color.hexCode) ? color.hexCode : '#ccc',
                        border: '1px solid #d9d9d9',
                        borderRadius: 4,
                        cursor: 'pointer'
                      }}
                      onClick={() => {
                        if (!disabled) {
                          const input = document.createElement('input');
                          input.type = 'color';
                          input.value = color.hexCode;
                          input.onchange = (e) => handleColorChange(index, 'hexCode', e.target.value);
                          input.click();
                        }
                      }}
                    />
                  </div>
                </div>
              </Col>
              <Col span={4}>
                <div style={{ marginBottom: 8 }}>
                  <label style={{ fontSize: '12px', color: '#666' }}>Stock</label>
                  <InputNumber
                    min={0}
                    value={color.stock}
                    onChange={(value) => handleColorChange(index, 'stock', value || 0)}
                    disabled={disabled}
                    style={{ width: '100%' }}
                    placeholder="0"
                  />
                </div>
              </Col>
              <Col span={3}>
                <div style={{ marginBottom: 8 }}>
                  <label style={{ fontSize: '12px', color: '#666' }}>Available</label>
                  <div>
                    <Switch
                      checked={color.available}
                      onChange={(checked) => handleColorChange(index, 'available', checked)}
                      disabled={disabled}
                      size="small"
                    />
                  </div>
                </div>
              </Col>
              <Col span={3}>
                <div style={{ marginBottom: 8 }}>
                  <label style={{ fontSize: '12px', color: '#666' }}>Image</label>
                  <Upload
                    showUploadList={false}
                    beforeUpload={(file) => handleImageUpload(index, file)}
                    disabled={disabled}
                    accept="image/*"
                  >
                    <Button
                      size="small"
                      icon={<UploadOutlined />}
                      style={{ width: '100%' }}
                      disabled={disabled}
                    >
                      {color.image ? 'Change' : 'Upload'}
                    </Button>
                  </Upload>
                  {color.image && (
                    <div style={{ marginTop: 4 }}>
                      <img
                        src={color.image}
                        alt={color.name}
                        style={{
                          width: '100%',
                          height: 30,
                          objectFit: 'cover',
                          borderRadius: 4,
                          border: '1px solid #d9d9d9'
                        }}
                      />
                    </div>
                  )}
                </div>
              </Col>
            </Row>
          </Card>
        ))}
        
        <Button
          type="dashed"
          onClick={handleAddColor}
          icon={<PlusOutlined />}
          style={{ width: '100%' }}
          disabled={disabled}
        >
          Add Color Option
        </Button>
        
        {colors.length === 0 && (
          <div style={{ 
            textAlign: 'center', 
            color: '#999', 
            padding: '20px',
            border: '1px dashed #d9d9d9',
            borderRadius: 4
          }}>
            No colors added yet. Click "Add Color Option" to start.
          </div>
        )}
      </Space>
    </div>
  );
};

export default ColorSelector;