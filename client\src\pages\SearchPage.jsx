import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useSearch } from '../contexts/SearchContext';
import { SearchHeader, SearchFilters, SearchResults } from '../components/search';

const SearchPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const {
    searchTerm,
    searchResults,
    loading,
    filters,
    setSearchTerm,
    searchProducts,
    updateFilters,
    clearSearch
  } = useSearch();

  const [viewMode, setViewMode] = useState('grid'); // grid or list
  const [showFilters, setShowFilters] = useState(false);
  const [priceRange, setPriceRange] = useState([0, 1000]);

  // Get search query from URL
  useEffect(() => {
    const query = searchParams.get('q');
    console.log('🌐 URL search param:', query, 'Current searchTerm:', searchTerm);
    if (query && query !== searchTerm) {
      console.log('🔍 Setting search term and triggering search:', query);
      setSearchTerm(query);
      searchProducts(query);
    } else if (query && query === searchTerm && searchResults.length === 0) {
      // If search term matches but no results, re-trigger search
      console.log('🔄 Re-triggering search for existing term:', query);
      searchProducts(query);
    }
  }, [searchParams, searchProducts, searchTerm, searchResults.length]);

  // Update URL when search term changes
  useEffect(() => {
    if (searchTerm) {
      setSearchParams({ q: searchTerm });
    }
  }, [searchTerm, setSearchParams]);

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    updateFilters(newFilters);
    
    // Re-search with new filters
    if (searchTerm) {
      searchProducts(searchTerm, newFilters);
    }
  };

  const handlePriceRangeChange = (range) => {
    setPriceRange(range);
    handleFilterChange('minPrice', range[0]);
    handleFilterChange('maxPrice', range[1]);
  };

  const handleClearFilters = () => {
    setPriceRange([0, 1000]);
    updateFilters({
      sortBy: 'relevance',
      minPrice: '',
      maxPrice: '',
      inStock: false
    });
    
    // Re-search with cleared filters
    if (searchTerm) {
      searchProducts(searchTerm, {
        sortBy: 'relevance',
        minPrice: '',
        maxPrice: '',
        inStock: false
      });
    }
  };

  const handleToggleFilters = () => {
    setShowFilters(!showFilters);
  };

  const handleViewModeChange = (mode) => {
    setViewMode(mode);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Search Header - only show search info and mobile filter toggle */}
      <SearchHeader 
        searchTerm={searchTerm}
        searchResults={searchResults}
        loading={loading}
        showFilters={showFilters}
        onToggleFilters={handleToggleFilters}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
        <div className="flex flex-col lg:flex-row gap-4 lg:gap-6">
          {/* Filters Sidebar - always show on desktop, toggle on mobile */}
          <div className={`w-full lg:w-64 lg:flex-shrink-0 ${showFilters ? 'block' : 'hidden lg:block'}`}>
            {/* Mobile Overlay */}
            {showFilters && (
              <div 
                className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden" 
                onClick={() => setShowFilters(false)}
              />
            )}
            
            {/* Filters Container */}
            <div className={`${
              showFilters 
                ? 'fixed top-0 right-0 h-full w-80 max-w-full bg-white z-50 overflow-y-auto lg:relative lg:top-auto lg:right-auto lg:h-auto lg:w-full lg:z-auto lg:overflow-visible' 
                : ''
            }`}>
              {/* Mobile Close Button */}
              {showFilters && (
                <div className="lg:hidden p-4 border-b">
                  <button
                    onClick={() => setShowFilters(false)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    ✕ Close Filters
                  </button>
                </div>
              )}
              
              <SearchFilters 
                filters={filters}
                onFilterChange={handleFilterChange}
                onClearFilters={handleClearFilters}
                priceRange={priceRange}
                onPriceRangeChange={handlePriceRangeChange}
              />
            </div>
          </div>

          {/* Search Results */}
          <div className="flex-1 min-w-0">
            <SearchResults 
              loading={loading}
              searchTerm={searchTerm}
              searchResults={searchResults}
              viewMode={viewMode}
              onViewModeChange={handleViewModeChange}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchPage;
