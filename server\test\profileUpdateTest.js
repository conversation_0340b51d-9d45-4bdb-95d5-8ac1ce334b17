const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:5000/api/customer';
let authToken = '';

// Test data
const testUser = {
  firstName: '<PERSON>',
  lastName: 'Doe',
  email: '<EMAIL>',
  password: 'password123',
  userType: 'customer'
};

const profileUpdateData = {
  firstName: 'John Updated',
  lastName: 'Doe Updated',
  phone: '1234567890',
  countryCode: 'US',
  address: '123 Main Street',
  city: 'New York',
  state: 'NY',
  zipCode: '10001',
  country: 'United States',
  preferences: {
    language: 'en',
    timezone: 'America/New_York',
    currency: 'USD'
  }
};

async function registerUser() {
  try {
    const response = await axios.post('http://localhost:5000/api/auth/register', testUser);
    console.log('✓ User registered successfully');
    return response.data;
  } catch (error) {
    if (error.response?.data?.message?.includes('already exists')) {
      console.log('✓ User already exists, continuing...');
      return null;
    }
    throw error;
  }
}

async function loginUser() {
  try {
    const response = await axios.post('http://localhost:5000/api/auth/login', {
      email: testUser.email,
      password: testUser.password
    });
    authToken = response.data.data.token;
    console.log('✓ User logged in successfully');
    return response.data;
  } catch (error) {
    console.error('✗ Login failed:', error.response?.data?.message || error.message);
    throw error;
  }
}

async function getProfile() {
  try {
    const response = await axios.get(`${BASE_URL}/profile`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✓ Profile retrieved successfully');
    return response.data.data;
  } catch (error) {
    console.error('✗ Get profile failed:', error.response?.data?.error || error.message);
    throw error;
  }
}

async function updateProfile() {
  try {
    const response = await axios.put(`${BASE_URL}/profile`, profileUpdateData, {
      headers: { 
        Authorization: `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });
    console.log('✓ Profile updated successfully');
    return response.data.data;
  } catch (error) {
    console.error('✗ Profile update failed:', error.response?.data?.error || error.message);
    if (error.response?.data?.errors) {
      console.error('Validation errors:', error.response.data.errors);
    }
    throw error;
  }
}

async function updatePreferences() {
  try {
    const response = await axios.put(`${BASE_URL}/profile/preferences`, {
      preferences: {
        language: 'es',
        timezone: 'Europe/Madrid',
        currency: 'EUR'
      }
    }, {
      headers: { 
        Authorization: `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });
    console.log('✓ Preferences updated successfully');
    return response.data.data;
  } catch (error) {
    console.error('✗ Preferences update failed:', error.response?.data?.error || error.message);
    throw error;
  }
}

async function runTest() {
  try {
    console.log('🚀 Starting Profile Update Test...\n');

    // Step 1: Register user
    console.log('1. Registering user...');
    await registerUser();

    // Step 2: Login user
    console.log('\n2. Logging in user...');
    await loginUser();

    // Step 3: Get current profile
    console.log('\n3. Getting current profile...');
    const currentProfile = await getProfile();
    console.log('Current profile data:', {
      firstName: currentProfile.firstName,
      lastName: currentProfile.lastName,
      phone: currentProfile.phone,
      address: currentProfile.address,
      preferences: currentProfile.preferences
    });

    // Step 4: Update profile
    console.log('\n4. Updating profile...');
    const updatedProfile = await updateProfile();
    console.log('Updated profile data:', {
      firstName: updatedProfile.firstName,
      lastName: updatedProfile.lastName,
      phone: updatedProfile.phone,
      address: updatedProfile.address,
      city: updatedProfile.city,
      state: updatedProfile.state,
      zipCode: updatedProfile.zipCode,
      country: updatedProfile.country,
      preferences: updatedProfile.preferences
    });

    // Step 5: Update preferences only
    console.log('\n5. Updating preferences...');
    const updatedPreferences = await updatePreferences();
    console.log('Updated preferences:', updatedPreferences);

    // Step 6: Get final profile
    console.log('\n6. Getting final profile...');
    const finalProfile = await getProfile();
    console.log('Final profile data:', {
      firstName: finalProfile.firstName,
      lastName: finalProfile.lastName,
      phone: finalProfile.phone,
      address: finalProfile.address,
      preferences: finalProfile.preferences
    });

    console.log('\n✅ All tests passed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
runTest();
