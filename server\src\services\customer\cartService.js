const Cart = require('../../models/Cart');
const Product = require('../../models/Product');
const User = require('../../models/User');

class CartService {
  /**
   * Get or create cart for customer
   */
  static async getOrCreateCart(customerId) {
    try {
      let cart = await Cart.findByCustomer(customerId);
      
      if (!cart) {
        cart = new Cart({ customer: customerId });
        await cart.save();
        // Populate after creation
        cart = await Cart.findByCustomer(customerId);
      }
      
      return cart;
    } catch (error) {
      throw new Error(`Error getting cart: ${error.message}`);
    }
  }

  /**
   * Validate product availability and stock
   */
  static async validateProduct(productId, quantity, variantSku = null) {
    const product = await Product.findById(productId).populate('vendor', 'businessName');
    
    if (!product) {
      throw new Error('Product not found');
    }
    
    if (product.status !== 'active') {
      throw new Error('Product is not available for purchase');
    }
    
    // Check stock availability
    const availableStock = product.inventory?.quantity || 0;
    if (product.inventory?.trackQuantity && availableStock < quantity) {
      throw new Error(`Only ${availableStock} items available in stock`);
    }
    
    // Handle variant validation
    let selectedVariant = null;
    if (variantSku && product.variants && product.variants.length > 0) {
      selectedVariant = product.variants.find(v => v.sku === variantSku);
      if (!selectedVariant) {
        throw new Error('Selected variant not found');
      }
    }
    
    return { product, selectedVariant };
  }

  /**
   * Calculate item price based on product and variant
   */
  static calculateItemPrice(product, selectedVariant = null) {
    if (selectedVariant && selectedVariant.price) {
      return selectedVariant.price;
    }
    
    return product.pricing?.salePrice || product.pricing?.basePrice || 0;
  }

  /**
   * Add single item to cart with validation
   */
  static async addItemToCart(customerId, productId, quantity = 1, variantSku = null) {
    try {
      // Validate product
      const { product, selectedVariant } = await this.validateProduct(productId, quantity, variantSku);
      
      // Calculate price
      const price = this.calculateItemPrice(product, selectedVariant);
      
      // Get or create cart
      const cart = await this.getOrCreateCart(customerId);
      
      // Add item
      await cart.addItem(productId, product.vendor._id, quantity, price, selectedVariant);
      
      // Return updated cart
      return await Cart.findByCustomer(customerId);
    } catch (error) {
      throw new Error(`Error adding item to cart: ${error.message}`);
    }
  }

  /**
   * Update cart item quantity with validation
   */
  static async updateCartItem(customerId, productId, quantity, variantSku = null) {
    try {
      const cart = await Cart.findOne({ customer: customerId });
      if (!cart) {
        throw new Error('Cart not found');
      }
      
      // If quantity > 0, validate stock
      if (quantity > 0) {
        await this.validateProduct(productId, quantity, variantSku);
      }
      
      await cart.updateItemQuantity(productId, quantity, variantSku);
      
      return await Cart.findByCustomer(customerId);
    } catch (error) {
      throw new Error(`Error updating cart item: ${error.message}`);
    }
  }

  /**
   * Remove item from cart
   */
  static async removeItemFromCart(customerId, productId, variantSku = null) {
    try {
      const cart = await Cart.findOne({ customer: customerId });
      if (!cart) {
        throw new Error('Cart not found');
      }
      
      await cart.removeItem(productId, variantSku);
      
      return await Cart.findByCustomer(customerId);
    } catch (error) {
      throw new Error(`Error removing item from cart: ${error.message}`);
    }
  }

  /**
   * Clear entire cart
   */
  static async clearCart(customerId) {
    try {
      const cart = await Cart.findOne({ customer: customerId });
      if (!cart) {
        throw new Error('Cart not found');
      }
      
      await cart.clearCart();
      
      return await Cart.findByCustomer(customerId);
    } catch (error) {
      throw new Error(`Error clearing cart: ${error.message}`);
    }
  }

  /**
   * Get cart summary
   */
  static async getCartSummary(customerId) {
    try {
      const cart = await Cart.findOne({ customer: customerId });
      
      return {
        totalItems: cart ? cart.totalItems : 0,
        totalAmount: cart ? cart.totalAmount : 0,
        vendorCount: cart ? cart.vendorCount : 0,
        itemCount: cart ? cart.items.length : 0,
        hasItems: cart ? cart.items.length > 0 : false
      };
    } catch (error) {
      throw new Error(`Error getting cart summary: ${error.message}`);
    }
  }

  /**
   * Bulk add items to cart
   */
  static async bulkAddToCart(customerId, items) {
    try {
      if (!Array.isArray(items) || items.length === 0) {
        throw new Error('Items array is required');
      }
      
      const validatedItems = [];
      
      // Validate all items first
      for (const item of items) {
        const { productId, quantity = 1, variantSku } = item;
        
        if (!productId) {
          throw new Error('Each item must have a productId');
        }
        
        const { product, selectedVariant } = await this.validateProduct(productId, quantity, variantSku);
        const price = this.calculateItemPrice(product, selectedVariant);
        
        validatedItems.push({
          productId,
          vendorId: product.vendor._id,
          quantity,
          price,
          variant: selectedVariant
        });
      }
      
      // Get or create cart
      const cart = await this.getOrCreateCart(customerId);
      
      // Bulk add items
      await cart.bulkAddItems(validatedItems);
      
      return await Cart.findByCustomer(customerId);
    } catch (error) {
      throw new Error(`Error bulk adding to cart: ${error.message}`);
    }
  }

  /**
   * Bulk update cart items
   */
  static async bulkUpdateCart(customerId, items) {
    try {
      if (!Array.isArray(items) || items.length === 0) {
        throw new Error('Items array is required');
      }
      
      const cart = await Cart.findOne({ customer: customerId });
      if (!cart) {
        throw new Error('Cart not found');
      }
      
      // Validate all updates first
      for (const item of items) {
        const { productId, quantity, variantSku } = item;
        
        if (!productId || quantity === undefined) {
          throw new Error('Each item must have productId and quantity');
        }
        
        if (quantity > 0) {
          await this.validateProduct(productId, quantity, variantSku);
        }
      }
      
      // Bulk update items
      await cart.bulkUpdateItems(items);
      
      return await Cart.findByCustomer(customerId);
    } catch (error) {
      throw new Error(`Error bulk updating cart: ${error.message}`);
    }
  }

  /**
   * Sync cart with current product prices and availability
   */
  static async syncCartPrices(customerId) {
    try {
      const cart = await Cart.findOne({ customer: customerId });
      if (!cart || cart.items.length === 0) {
        return cart;
      }
      
      let hasChanges = false;
      const itemsToRemove = [];
      
      for (let i = 0; i < cart.items.length; i++) {
        const item = cart.items[i];
        
        try {
          const product = await Product.findById(item.product);
          
          if (!product || product.status !== 'active') {
            itemsToRemove.push(i);
            hasChanges = true;
            continue;
          }
          
          // Update price if different
          const currentPrice = this.calculateItemPrice(product, item.selectedVariant);
          if (item.priceAtAdd !== currentPrice) {
            cart.items[i].priceAtAdd = currentPrice;
            hasChanges = true;
          }
          
          // Check stock availability
          const availableStock = product.inventory?.quantity || 0;
          if (product.inventory?.trackQuantity && availableStock < item.quantity) {
            if (availableStock === 0) {
              itemsToRemove.push(i);
            } else {
              cart.items[i].quantity = availableStock;
            }
            hasChanges = true;
          }
        } catch (error) {
          // If product fetch fails, remove item
          itemsToRemove.push(i);
          hasChanges = true;
        }
      }
      
      // Remove items that are no longer available (in reverse order)
      for (let i = itemsToRemove.length - 1; i >= 0; i--) {
        cart.items.splice(itemsToRemove[i], 1);
      }
      
      if (hasChanges) {
        await cart.save();
      }
      
      return await Cart.findByCustomer(customerId);
    } catch (error) {
      throw new Error(`Error syncing cart prices: ${error.message}`);
    }
  }
}

module.exports = CartService;
