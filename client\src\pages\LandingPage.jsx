import React, { useEffect } from "react";
import NewNavbar from "../components/NewNavbar";
import MainNavigationMenu from "../components/MainNavigationMenu";
import HeroSection from "../components/HeroSection"

const LandingPage = () => {
  useEffect(() => {
    // Add landing page specific body class
    document.body.classList.add('landing-page-body');

    // Cleanup on unmount
    return () => {
      document.body.classList.remove('landing-page-body');
    };
  }, []);

  return (
    <div className="min-h-screen">
      <NewNavbar noShadow={true} />
      <MainNavigationMenu />
      <div className="pt-0">
        <HeroSection />
      </div>
    </div>
  )
}

export default LandingPage;