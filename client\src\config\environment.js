// Environment-based configuration for the application
export const config = {
  // API Configuration
  api: {
    baseUrl: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
    timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 30000,
    retryAttempts: parseInt(import.meta.env.VITE_API_RETRY_ATTEMPTS) || 3,
    retryDelay: parseInt(import.meta.env.VITE_API_RETRY_DELAY) || 1000,
  },

  // Analytics Configuration
  analytics: {
    refreshInterval: parseInt(import.meta.env.VITE_ANALYTICS_REFRESH_INTERVAL) || 30000, // 30 seconds
    cacheTimeout: parseInt(import.meta.env.VITE_ANALYTICS_CACHE_TIMEOUT) || 60000, // 1 minute
    enableRealTime: import.meta.env.VITE_ANALYTICS_REAL_TIME !== 'false', // Default true
    enableDebug: import.meta.env.VITE_ANALYTICS_DEBUG === 'true', // Default false
  },

  // App Configuration
  app: {
    name: import.meta.env.VITE_APP_NAME || 'Alicartify',
    version: import.meta.env.VITE_APP_VERSION || '1.0.0',
    environment: import.meta.env.NODE_ENV || 'development',
    debugMode: import.meta.env.VITE_DEBUG_MODE === 'true',
  },

  // Dashboard Configuration
  dashboard: {
    autoRefresh: import.meta.env.VITE_DASHBOARD_AUTO_REFRESH !== 'false', // Default true
    refreshInterval: parseInt(import.meta.env.VITE_DASHBOARD_REFRESH_INTERVAL) || 60000, // 1 minute
    maxRetries: parseInt(import.meta.env.VITE_DASHBOARD_MAX_RETRIES) || 3,
    fallbackToSampleData: import.meta.env.VITE_DASHBOARD_FALLBACK_SAMPLE === 'true', // Default false
  },

  // Performance Configuration
  performance: {
    enableCaching: import.meta.env.VITE_ENABLE_CACHING !== 'false', // Default true
    enableCompression: import.meta.env.VITE_ENABLE_COMPRESSION !== 'false', // Default true
    enableLazyLoading: import.meta.env.VITE_ENABLE_LAZY_LOADING !== 'false', // Default true
  }
};

// Validation function to ensure required environment variables are present
export const validateEnvironment = () => {
  const errors = [];

  // Check required variables
  if (!config.api.baseUrl) {
    errors.push('VITE_API_URL is not configured');
  }

  if (!config.app.name) {
    errors.push('VITE_APP_NAME is not configured');
  }

  // Validate URL format
  try {
    new URL(config.api.baseUrl);
  } catch (error) {
    errors.push(`Invalid API URL format: ${config.api.baseUrl}`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Development helpers
export const isDevelopment = () => config.app.environment === 'development';
export const isProduction = () => config.app.environment === 'production';
export const isDebugMode = () => config.app.debugMode || isDevelopment();

// API URL helpers
export const getApiUrl = (endpoint = '') => {
  const baseUrl = config.api.baseUrl.replace(/\/+$/, ''); // Remove trailing slashes
  const cleanEndpoint = endpoint.replace(/^\/+/, ''); // Remove leading slashes
  return cleanEndpoint ? `${baseUrl}/${cleanEndpoint}` : baseUrl;
};

export const getVendorApiUrl = (endpoint = '') => {
  return getApiUrl(`vendor/${endpoint}`.replace(/\/+$/, ''));
};

// Logging helper that respects debug mode
export const debugLog = (...args) => {
  if (isDebugMode()) {
    console.log('[DEBUG]', ...args);
  }
};

export const errorLog = (...args) => {
  console.error('[ERROR]', ...args);
};

export const infoLog = (...args) => {
  if (isDebugMode()) {
    console.info('[INFO]', ...args);
  }
};

// Export default config
export default config;
