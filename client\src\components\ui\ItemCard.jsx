import React from "react";
import { useNavigate } from "react-router-dom";

const ItemCard = ({ 
  image = "https://os.alipayobjects.com/rmsportal/QBnOOoLaAfKPirc.png",
  price = "$1.00",
  badgeText = "Trending Now",
  alt = "product",
  productId = null
}) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (productId) {
      navigate(`/product/${productId}`);
    } else {
      // If no specific product ID, redirect to products page
      navigate('/page');
    }
  };

  return (
    <div className="flex flex-col w-full">
      <div 
        onClick={handleClick}
        className="rounded-lg overflow-hidden bg-white cursor-pointer hover:shadow-md transition-shadow duration-200"
      >
        <div className="h-24 sm:h-28 md:h-32 overflow-hidden">
          <img
            alt={alt}
            src={image}
            className="w-full h-full object-cover"
            onError={(e) => {
              // Prevent infinite error loops
              if (e.currentTarget.src.startsWith('data:')) return;
              
              // Use a data URI as fallback to avoid network requests
              e.currentTarget.src = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzlDQTNBRiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pgo8L3N2Zz4K";
            }}
          />
        </div>
        <div className="p-2 text-center">
          <div className="text-sm font-medium text-gray-900">
            {price}
          </div>
          <div className="text-xs text-gray-500">
            {badgeText}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ItemCard;
