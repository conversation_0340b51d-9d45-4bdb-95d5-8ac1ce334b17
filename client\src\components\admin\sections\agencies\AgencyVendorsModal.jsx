import React from 'react';
import {
  Modal,
  Table,
  Space,
  Tag,
  Avatar,
  Rate,
  Typography,
  Empty
} from 'antd';
import {
  ShopOutlined,
  UserOutlined,
  DollarOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

const AgencyVendorsModal = ({ visible, agency, vendors, onCancel }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'pending_approval':
        return 'orange';
      case 'suspended':
        return 'red';
      case 'inactive':
        return 'default';
      default:
        return 'default';
    }
  };

  const getVerificationStatusColor = (status) => {
    switch (status) {
      case 'verified':
        return 'green';
      case 'pending':
        return 'orange';
      case 'rejected':
        return 'red';
      case 'suspended':
        return 'red';
      default:
        return 'default';
    }
  };

  const columns = [
    {
      title: 'Vendor',
      key: 'vendor',
      render: (record) => (
        <Space>
          <Avatar icon={<ShopOutlined />} style={{ backgroundColor: '#52c41a' }} />
          <div>
            <div style={{ fontWeight: 500 }}>{record.businessName}</div>
            <div style={{ color: '#666', fontSize: '12px' }}>
              {record.user?.firstName} {record.user?.lastName}
            </div>
            <div style={{ color: '#666', fontSize: '12px' }}>
              {record.user?.email || record.contactInfo?.businessEmail}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: 'Status',
      key: 'status',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Tag color={getStatusColor(record.status)}>
            {record.status?.replace('_', ' ').toUpperCase()}
          </Tag>
          <Tag color={getVerificationStatusColor(record.verification?.status)} size="small">
            {record.verification?.status?.toUpperCase()}
          </Tag>
        </Space>
      ),
    },
    {
      title: 'Rating',
      key: 'rating',
      render: (_, record) => (
        <Space>
          <Rate disabled defaultValue={record.performance?.rating || 0} style={{ fontSize: '14px' }} />
          <span>({record.performance?.rating?.toFixed(1) || '0.0'})</span>
        </Space>
      ),
    },
    {
      title: 'Products',
      key: 'products',
      align: 'center',
      render: (_, record) => record.performance?.totalProducts || 0,
    },
    {
      title: 'Total Sales',
      key: 'sales',
      render: (_, record) => record.performance?.totalSales || 0,
    },
    {
      title: 'Revenue',
      key: 'revenue',
      render: (_, record) => (
        <Space>
          <DollarOutlined />
          {(record.performance?.totalRevenue || 0).toLocaleString()}
        </Space>
      ),
    },
    {
      title: 'Commission',
      key: 'commission',
      render: (_, record) => `${record.commission?.rate || 0}%`,
    },
    {
      title: 'Joined',
      key: 'joined',
      render: (_, record) => new Date(record.createdAt).toLocaleDateString(),
    },
  ];

  const modalTitle = (
    <Space>
      <Avatar icon={<ShopOutlined />} style={{ backgroundColor: '#1890ff' }} />
      <div>
        <Title level={4} style={{ margin: 0 }}>
          {agency?.businessName || 'Agency'} - Registered Vendors
        </Title>
        <Text type="secondary">
          Total Vendors: {vendors?.length || 0}
        </Text>
      </div>
    </Space>
  );

  return (
    <Modal
      title={modalTitle}
      open={visible}
      onCancel={onCancel}
      width={1200}
      footer={null}
      style={{ top: 20 }}
    >
      {vendors && vendors.length > 0 ? (
        <Table
          columns={columns}
          dataSource={vendors}
          rowKey={(record) => record._id || record.id}
          scroll={{ x: 1000, y: 400 }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} vendors`,
          }}
          size="small"
        />
      ) : (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <div>
              <Text type="secondary">No vendors registered under this agency</Text>
            </div>
          }
        />
      )}
    </Modal>
  );
};

export default AgencyVendorsModal;
