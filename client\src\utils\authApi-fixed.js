// Authentication API utilities - FIXED VERSION
const API_BASE_URL = import.meta.env.VITE_API_URL || 'https://multi-vendor-server-1tb9.onrender.com/api';

class AuthAPI {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        const error = new Error(data.message || 'Something went wrong');
        error.response = { data, status: response.status };
        throw error;
      }

      return data;
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }

  // Unified Authentication (handles both user and vendor)
  async login(credentials) {
    return this.makeRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  async register(userData) {
    return this.makeRequest('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  // Legacy methods for backward compatibility
  async loginUser(credentials) {
    return this.login({ ...credentials, userType: 'customer' });
  }

  async registerUser(userData) {
    return this.register({ ...userData, userType: 'customer' });
  }

  async loginVendor(credentials) {
    return this.login({ ...credentials, userType: 'vendor' });
  }

  async registerVendor(vendorData) {
    return this.register({ ...vendorData, userType: 'vendor' });
  }

  // Password Reset
  async forgotPassword(email) {
    return this.makeRequest('/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  async resetPassword(resetData) {
    return this.makeRequest('/auth/reset-password', {
      method: 'POST',
      body: JSON.stringify(resetData),
    });
  }

  // Email Verification
  async verifyEmail(token, email) {
    return this.makeRequest(`/auth/verify-email?token=${token}&email=${email}`, {
      method: 'GET',
    });
  }

  async resendVerificationEmail(email) {
    return this.makeRequest('/auth/resend-verification', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  // Token Management
  async refreshToken(refreshToken) {
    return this.makeRequest('/auth/refresh-token', {
      method: 'POST',
      body: JSON.stringify({ refreshToken }),
    });
  }

  async logout(token) {
    return this.makeRequest('/auth/logout', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  }

  // Profile Management
  async getProfile(token) {
    return this.makeRequest('/auth/profile', {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  }

  async updateProfile(token, profileData) {
    console.log('🔧 AuthAPI updateProfile called');
    console.log('Profile data:', profileData);
    console.log('Token exists:', !!token);
    
    // Ensure we explicitly set the content type to prevent text/plain issues
    return this.makeRequest('/auth/profile', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json'
      },
      body: JSON.stringify(profileData),
    });
  }

  async changePassword(token, passwordData) {
    return this.makeRequest('/auth/change-password', {
      method: 'PUT',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(passwordData),
    });
  }

  // Health Check
  async healthCheck() {
    return this.makeRequest('/auth/health', {
      method: 'GET',
    });
  }
}

// Create and export a singleton instance
const authAPI = new AuthAPI();
export { authAPI };

// Export individual methods for convenience
export const {
  login,
  register,
  loginUser,
  registerUser,
  loginVendor,
  registerVendor,
  forgotPassword,
  resetPassword,
  verifyEmail,
  resendVerificationEmail,
  refreshToken,
  logout,
  getProfile,
  updateProfile,
  healthCheck,
} = authAPI;

export default authAPI;