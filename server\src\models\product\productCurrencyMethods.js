// Multi-currency pricing methods for Product model
function addProductCurrencyMethods(productSchema) {
  // Instance method to get price in specific currency
  productSchema.methods.getPriceInCurrency = function(currency = 'INR', priceType = 'base') {
    currency = currency.toUpperCase();
    
    // Check if multi-currency pricing exists for this currency
    if (this.pricing.multiCurrency && this.pricing.multiCurrency[currency]) {
      const currencyPricing = this.pricing.multiCurrency[currency];
      if (priceType === 'sale' && currencyPricing.salePrice) {
        return currencyPricing.salePrice;
      }
      return currencyPricing.basePrice || null;
    }
    
    // Fallback to default pricing if currency matches or no multi-currency data
    if (this.pricing.currency === currency) {
      if (priceType === 'sale' && this.pricing.salePrice) {
        return this.pricing.salePrice;
      }
      return this.pricing.basePrice;
    }
    
    return null; // Currency not available
  };

  // Instance method to set price in specific currency
  productSchema.methods.setPriceInCurrency = function(currency, basePrice, salePrice = null) {
    currency = currency.toUpperCase();
    
    // Initialize multiCurrency if not exists
    if (!this.pricing.multiCurrency) {
      this.pricing.multiCurrency = {};
    }
    
    // Initialize currency object if not exists
    if (!this.pricing.multiCurrency[currency]) {
      this.pricing.multiCurrency[currency] = {};
    }
    
    this.pricing.multiCurrency[currency].basePrice = basePrice;
    if (salePrice !== null) {
      this.pricing.multiCurrency[currency].salePrice = salePrice;
    }
    
    // Update default currency if this is the main currency
    if (currency === this.pricing.currency || currency === 'INR') {
      this.pricing.basePrice = basePrice;
      if (salePrice !== null) {
        this.pricing.salePrice = salePrice;
      }
    }
    
    return this;
  };

  // Instance method to get all available currencies
  productSchema.methods.getAvailableCurrencies = function() {
    const currencies = [this.pricing.currency];
    
    if (this.pricing.multiCurrency) {
      Object.keys(this.pricing.multiCurrency).forEach(currency => {
        if (this.pricing.multiCurrency[currency].basePrice && !currencies.includes(currency)) {
          currencies.push(currency);
        }
      });
    }
    
    return currencies;
  };

  // Instance method to get current price in specific currency (with sale price preference)
  productSchema.methods.getCurrentPriceInCurrency = function(currency = 'INR') {
    const salePrice = this.getPriceInCurrency(currency, 'sale');
    if (salePrice) return salePrice;
    
    return this.getPriceInCurrency(currency, 'base');
  };
}

module.exports = { addProductCurrencyMethods };
