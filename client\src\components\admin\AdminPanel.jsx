import React, { useState } from 'react';
import AdminLayout from './AdminLayout';
import MainDashboard from './dashboard/MainDashboard';
import UsersManagement from './sections/UsersManagement';
import VendorsManagement from './sections/VendorsManagement';
import AgenciesManagement from './sections/AgenciesManagement';
import ProductsManagement from './sections/ProductsManagement';
import OrdersManagement from './sections/OrdersManagement';
import Analytics from './sections/Analytics';
import Settings from './sections/Settings';
import HomepageSettings from './sections/CategoriesManagement';

const AdminPanel = () => {
  const [activeSection, setActiveSection] = useState('dashboard');

  const renderContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return <MainDashboard />;
      case 'users':
        return <UsersManagement />;
      case 'vendors':
        return <VendorsManagement />;
      case 'agencies':
        return <AgenciesManagement />;
      case 'products':
        return <ProductsManagement />;
      case 'categories':
        return <HomepageSettings />;
      case 'orders':
        return <OrdersManagement />;
      case 'analytics':
        return <MainDashboard />;
      case 'settings':
        return <Settings />;
      default:
        return <MainDashboard />;
    }
  };

  return (
    <AdminLayout 
      activeKey={activeSection} 
      onMenuSelect={setActiveSection}
    >
      {renderContent()}
    </AdminLayout>
  );
};

export default AdminPanel;