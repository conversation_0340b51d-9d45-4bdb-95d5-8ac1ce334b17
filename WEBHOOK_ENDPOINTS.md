# 🔗 Webhook Endpoints Summary

## Your Webhook URLs

### 💳 Razorpay Webhook
```
Production: https://yourdomain.com/api/payments/razorpay/webhook
Local Dev:  http://localhost:5000/api/payments/razorpay/webhook
```

### 📦 Shiprocket Webhook  
```
Production: https://yourdomain.com/api/webhooks/shiprocket
Local Dev:  http://localhost:5000/api/webhooks/shiprocket
```

## 🚀 Quick Setup Checklist

### Razorpay Webhooks
- [ ] Go to [Razorpay Dashboard](https://dashboard.razorpay.com/) → Settings → Webhooks
- [ ] Create webhook with URL: `https://yourdomain.com/api/payments/razorpay/webhook`
- [ ] Select events: `payment.captured`, `payment.failed`, `order.paid`
- [ ] Copy webhook secret to `.env` as `RAZORPAY_WEBHOOK_SECRET`

### Shiprocket Webhooks
- [ ] Go to [Shiprocket Dashboard](https://app.shiprocket.in/) → Settings → API → Webhooks
- [ ] Add webhook with URL: `https://yourdomain.com/api/webhooks/shiprocket`
- [ ] Select all shipping events (shipped, delivered, returned, etc.)

## 🧪 Test Your Webhooks

### Test Razorpay Webhook
```bash
cd server
node test-razorpay-webhook.js
```

### Test Shiprocket Webhook
```bash
curl -X POST http://localhost:5000/api/webhooks/shiprocket \
  -H "Content-Type: application/json" \
  -d '{"order_id":"TEST123","current_status":"SHIPPED"}'
```

## 🌐 For Local Development

Use ngrok to expose your local server:
```bash
# Install ngrok
npm install -g ngrok

# Expose port 5000
ngrok http 5000

# Use the ngrok URL in webhook configurations
# Example: https://abc123.ngrok.io/api/payments/razorpay/webhook
```

## 📋 Environment Variables Needed

```env
# Razorpay
RAZORPAY_KEY_ID=rzp_test_your_key_id
RAZORPAY_KEY_SECRET=your_razorpay_secret
RAZORPAY_WEBHOOK_SECRET=whsec_your_webhook_secret

# Shiprocket  
SHIPROCKET_EMAIL=<EMAIL>
SHIPROCKET_PASSWORD=your-password
```

## ✅ Verification

Your webhooks are working when:
- ✅ Razorpay payments automatically update order status
- ✅ Shiprocket shipping updates reflect in your system
- ✅ No webhook delivery failures in respective dashboards
- ✅ Server logs show webhook events being received

## 🆘 Need Help?

1. **Check webhook delivery logs** in Razorpay/Shiprocket dashboards
2. **Verify URLs are publicly accessible** (use online tools to test)
3. **Check server logs** for webhook processing errors
4. **Test with sample payloads** using the test scripts provided

---

**Quick Links:**
- [Razorpay Webhook Setup Guide](./RAZORPAY_WEBHOOK_SETUP.md)
- [Shiprocket Webhook Setup Guide](./SHIPROCKET_WEBHOOK_SETUP.md)