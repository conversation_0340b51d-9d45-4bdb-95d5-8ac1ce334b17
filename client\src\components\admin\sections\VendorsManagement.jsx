import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Input,
  Typography
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined
} from '@ant-design/icons';

// Import sub-components
import VendorStatistics from './vendors/VendorStatistics';
import VendorTable from './vendors/VendorTable';
import VendorModal from './vendors/VendorModal';
import { useVendorActions } from './vendors/useVendorActions';

const { Title } = Typography;

const VendorsManagement = () => {
  const [vendors, setVendors] = useState([]);
  const [stats, setStats] = useState({});
  const [modalVisible, setModalVisible] = useState(false);
  const [editingVendor, setEditingVendor] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [filters, setFilters] = useState({
    status: '',
    search: ''
  });

  // Use custom hook for vendor actions
  const {
    loading,
    fetchVendors: apiVendors,
    fetchStats: apiStats,
    approveVendor,
    suspendVendor,
    deleteVendor,
    createVendor,
    updateVendor
  } = useVendorActions();

  useEffect(() => {
    loadData();
  }, [pagination.current, pagination.pageSize, filters]);

  useEffect(() => {
    setFilters(prev => ({ ...prev, search: searchText }));
  }, [searchText]);

  const loadData = async () => {
    // Fetch vendors
    const vendorsResult = await apiVendors(pagination, filters);
    setVendors(vendorsResult.vendors);
    setPagination(vendorsResult.pagination);

    // Fetch stats
    const statsResult = await apiStats();
    setStats(statsResult);
  };

  const handleTableChange = (paginationData, tableFilters, sorter) => {
    setPagination(prev => ({
      ...prev,
      current: paginationData.current,
      pageSize: paginationData.pageSize
    }));
  };

  const handleAddVendor = () => {
    setEditingVendor(null);
    setModalVisible(true);
  };

  const handleEditVendor = (vendor) => {
    setEditingVendor(vendor);
    setModalVisible(true);
  };

  const handleModalOk = async (values) => {
    let success = false;
    
    if (editingVendor) {
      success = await updateVendor(editingVendor._id || editingVendor.id, values);
    } else {
      success = await createVendor(values);
    }

    if (success) {
      setModalVisible(false);
      setEditingVendor(null);
      loadData();
    }
  };

  const handleModalCancel = () => {
    setModalVisible(false);
    setEditingVendor(null);
  };

  const handleApprove = async (vendorId) => {
    const success = await approveVendor(vendorId);
    if (success) {
      loadData();
    }
  };

  const handleSuspend = async (vendorId) => {
    const success = await suspendVendor(vendorId);
    if (success) {
      loadData();
    }
  };

  const handleDelete = async (vendorId) => {
    const success = await deleteVendor(vendorId);
    if (success) {
      loadData();
    }
  };

  // Filter vendors based on search text
  const filteredVendors = vendors.filter(vendor =>
    (vendor.businessName || '').toLowerCase().includes(searchText.toLowerCase()) ||
    (vendor.contactPerson || '').toLowerCase().includes(searchText.toLowerCase()) ||
    (vendor.email || '').toLowerCase().includes(searchText.toLowerCase())
  );

  return (
    <div style={{ 
      padding: '0 16px',
      maxWidth: '100%',
      overflow: 'hidden'
    }}>
      <Title level={2} style={{ 
        marginBottom: '16px',
        fontSize: 'clamp(1.5rem, 4vw, 2rem)'
      }}>Vendors Management</Title>
      
      {/* Statistics Cards */}
      <VendorStatistics 
        vendors={vendors} 
        stats={stats} 
        loading={loading} 
      />

      <Card>
        <div style={{ 
          marginBottom: 16, 
          display: 'flex', 
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          gap: '12px',
          flexWrap: 'wrap'
        }}>
          <Input
            placeholder="Search vendors..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ 
              flex: '1',
              minWidth: '200px',
              maxWidth: '400px'
            }}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddVendor}
            style={{
              flexShrink: 0
            }}
          >
            Add Vendor
          </Button>
        </div>

        <VendorTable
          vendors={filteredVendors}
          loading={loading}
          pagination={pagination}
          onEdit={handleEditVendor}
          onApprove={handleApprove}
          onSuspend={handleSuspend}
          onDelete={handleDelete}
          onTableChange={handleTableChange}
        />
      </Card>

      <VendorModal
        visible={modalVisible}
        editingVendor={editingVendor}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        loading={loading}
      />
    </div>
  );
};

export default VendorsManagement;
