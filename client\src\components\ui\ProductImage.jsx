import React, { useState, useImperativeHandle, forwardRef, useEffect } from 'react';
import { getAbsoluteImageUrl, getFallbackImageUrl } from '../../utils/imageUtils';

const ProductImage = forwardRef(({ 
  src, 
  alt = "Product Image", 
  className = "", 
  fallbackSrc,
  onError,
  ...props 
}, ref) => {
  // Process the source URL to ensure it's absolute
  const processedSrc = getAbsoluteImageUrl(src);
  const defaultFallback = fallbackSrc || getFallbackImageUrl('product');
  
  const [imgSrc, setImgSrc] = useState(processedSrc || defaultFallback);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useImperativeHandle(ref, () => ({
    resetImage: () => {
      setHasError(false);
      setIsLoading(true);
      const newSrc = getAbsoluteImageUrl(src);
      setImgSrc(newSrc || defaultFallback);
    }
  }));

  const handleError = (e) => {
    console.warn('Image failed to load:', imgSrc);
    console.warn('Original src prop:', src);
    console.warn('Processed src:', processedSrc);
    
    if (!hasError) {
      setHasError(true);
      setIsLoading(false);
      setImgSrc(defaultFallback);
      
      // Add some styling to fallback images
      e.target.style.backgroundColor = '#f3f4f6';
      e.target.style.color = '#6b7280';
      e.target.style.display = 'flex';
      e.target.style.alignItems = 'center';
      e.target.style.justifyContent = 'center';
    }
    
    if (onError) {
      onError(e);
    }
  };

  const handleLoad = (e) => {
    setIsLoading(false);
    setHasError(false);
    
    // Reset any error styling when image loads successfully
    e.target.style.backgroundColor = '';
    e.target.style.color = '';
    
    console.log('Image loaded successfully:', imgSrc);
  };

  // Update src when prop changes
  useEffect(() => {
    const newProcessedSrc = getAbsoluteImageUrl(src);
    
    if (newProcessedSrc && newProcessedSrc !== imgSrc && !hasError) {
      setImgSrc(newProcessedSrc);
      setIsLoading(true);
      setHasError(false);
    } else if (!newProcessedSrc && !hasError) {
      setImgSrc(defaultFallback);
      setHasError(true);
      setIsLoading(false);
    }
  }, [src, imgSrc, hasError, defaultFallback]);

  return (
    <div className="relative">
      <img
        ref={ref}
        src={imgSrc}
        alt={alt}
        className={`${className} ${isLoading ? 'opacity-50' : 'opacity-100'} transition-opacity duration-200`}
        onError={handleError}
        onLoad={handleLoad}
        {...props}
      />
      {isLoading && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-600"></div>
        </div>
      )}
    </div>
  );
});

ProductImage.displayName = 'ProductImage';

export default ProductImage;
