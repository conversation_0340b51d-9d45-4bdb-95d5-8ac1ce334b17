const mongoose = require('mongoose');

const salesSchema = new mongoose.Schema({
  totalSold: {
    type: Number,
    default: 0
  },
  totalRevenue: {
    type: Number,
    default: 0
  },
  lastSaleDate: Date,
  monthlyStats: [{
    month: {
      type: String,
      required: true
    },
    year: {
      type: Number,
      required: true
    },
    sold: {
      type: Number,
      default: 0
    },
    revenue: {
      type: Number,
      default: 0
    }
  }]
}, { _id: false });

module.exports = { salesSchema };
