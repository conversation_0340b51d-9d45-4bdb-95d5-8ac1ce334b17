const mongoose = require('mongoose');

const sizeSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  value: {
    type: String,
    required: true,
    trim: true
  },
  available: {
    type: Boolean,
    default: true
  },
  stock: {
    type: Number,
    min: 0,
    default: 0
  },
  priceModifier: {
    type: Number,
    default: 0
  }
}, { _id: false });

module.exports = { sizeSchema };
