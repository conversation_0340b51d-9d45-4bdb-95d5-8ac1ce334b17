import React, { useState, useEffect } from 'react'
import { useNavbar } from '../contexts/NavbarContext'
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "./ui/navigation-menu"
import { cn } from "@/lib/utils"
import CategoriesHoverModal from './navbar/CategoriesHoverModal'
import FeaturedSelectionModal from './navbar/FeaturedSelectionModal'
import OrderProtectionModal from './navbar/OrderProtectionModal'
import BuyCentralModal from './navbar/BuyCentralModal'

const MainNavigationMenu = () => {
  const { isMainMenuHovered, setIsMainMenuHovered } = useNavbar();
  const [isScrolled, setIsScrolled] = useState(false);
  const [showCategoriesModal, setShowCategoriesModal] = useState(false);
  const [showFeaturedModal, setShowFeaturedModal] = useState(false);
  const [showOrderProtectionModal, setShowOrderProtectionModal] = useState(false);
  const [showBuyCentralModal, setShowBuyCentralModal] = useState(false);
  const [hoverTimeout, setHoverTimeout] = useState(null);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleMouseEnter = (modalSetter) => {
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
      setHoverTimeout(null);
    }
    setIsMainMenuHovered(true);
    setShowCategoriesModal(false);
    setShowFeaturedModal(false);
    setShowOrderProtectionModal(false);
    setShowBuyCentralModal(false);

    if (modalSetter) {
      modalSetter(true);
    }
  };

  const handleMouseLeave = () => {
    const timeout = setTimeout(() => {
      setIsMainMenuHovered(false);
      setShowCategoriesModal(false);
      setShowFeaturedModal(false);
      setShowOrderProtectionModal(false);
      setShowBuyCentralModal(false);
    }, 200);
    setHoverTimeout(timeout);
  };

  useEffect(() => {
    return () => {
      if (hoverTimeout) {
        clearTimeout(hoverTimeout);
      }
    };
  }, [hoverTimeout]);

  const navItemClasses = () => cn(
    'transition-colors duration-300 px-4 py-2 text-sm font-medium rounded-md bg-transparent',
    isMainMenuHovered || isScrolled
      ? 'text-gray-700'
      : 'text-white'
  );

  const navLinkClasses = () => cn(
    'transition-colors duration-300 px-4 py-2 text-sm font-medium rounded-md bg-transparent',
    isMainMenuHovered || isScrolled
      ? 'text-gray-700'
      : 'text-white'
  );

  const textShadowStyle = (isModalActive) => ({});

  return (
    <div 
      onMouseLeave={handleMouseLeave}
      className={cn(
        'fixed left-0 w-full z-40 transition-all duration-300',
        isMainMenuHovered ? 'bg-white shadow-md' : 'bg-transparent',
        isScrolled ? '-top-20 opacity-0' : 'top-16 opacity-100'
      )}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between w-full py-2">
          <NavigationMenu>
            <NavigationMenuList className="flex items-center gap-1">
                            <div className="relative" onMouseEnter={() => handleMouseEnter(setShowCategoriesModal)} >
                <button className={navItemClasses()} style={textShadowStyle(showCategoriesModal)}>
                  All categories
                </button>
                                <CategoriesHoverModal showModal={showCategoriesModal} onMouseEnter={() => handleMouseEnter(setShowCategoriesModal)} onMouseLeave={handleMouseLeave} />
              </div>
                            <div className="relative" onMouseEnter={() => handleMouseEnter(setShowFeaturedModal)} >
                <button className={navItemClasses()} style={textShadowStyle(showFeaturedModal)}>
                  Featured selections
                </button>
                                <FeaturedSelectionModal showModal={showFeaturedModal} onMouseEnter={() => handleMouseEnter(setShowFeaturedModal)} onMouseLeave={handleMouseLeave} />
              </div>
                            <div className="relative" onMouseEnter={() => handleMouseEnter(setShowOrderProtectionModal)} >
                <button className={navItemClasses()} style={textShadowStyle(showOrderProtectionModal)}>
                  Order protections
                </button>
                                <OrderProtectionModal showModal={showOrderProtectionModal} onMouseEnter={() => handleMouseEnter(setShowOrderProtectionModal)} onMouseLeave={handleMouseLeave} />
              </div>
            </NavigationMenuList>
          </NavigationMenu>
          <div className="flex items-center gap-1">
                            <div className="relative" onMouseEnter={() => handleMouseEnter(setShowBuyCentralModal)} >
                <button className={navItemClasses()} style={textShadowStyle(showBuyCentralModal)}>
                  Buy Central
                </button>
              </div>
              <div>
                <a href="/help-center" className={navLinkClasses()} style={textShadowStyle(isMainMenuHovered)}>
                  Help Center
                </a>
              </div>
              <div>
                <a href="/ship-to" className={navLinkClasses()} style={textShadowStyle(isMainMenuHovered)}>
                  Download App
                </a>
              </div>
              <div>
                <a href="/language" className={navLinkClasses()} style={textShadowStyle(isMainMenuHovered)}>
                  Become a Vendor
                </a>
              </div>
            </div>
        </div>
      </div>
            <BuyCentralModal showModal={showBuyCentralModal} onMouseEnter={() => handleMouseEnter(setShowBuyCentralModal)} onMouseLeave={handleMouseLeave} />
    </div>
  );
}

const ListItem = React.forwardRef(({ className, title, children, ...props }, ref) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <a
          ref={ref}
          className={cn(
            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
            className
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none">{title}</div>
          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
            {children}
          </p>
        </a>
      </NavigationMenuLink>
    </li>
  )
})
ListItem.displayName = "ListItem"

export default MainNavigationMenu
