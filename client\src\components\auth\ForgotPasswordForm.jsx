import React, { useState } from 'react';
import { Form, Result, message } from 'antd';
import { MailOutlined, ArrowLeftOutlined, CheckCircleOutlined } from '@ant-design/icons';
import AuthInput from '../ui/AuthInput';
import AuthButton from '../ui/AuthButton';
import { authAPI } from '../../utils/authApi';

const ForgotPasswordForm = ({ onBackToLogin }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [email, setEmail] = useState('');

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const response = await authAPI.forgotPassword(values.email);
      if (response.success) {
        setEmail(values.email);
        setEmailSent(true);
        message.success('Password reset link sent to your email');
      } else {
        message.error(response.message || 'Failed to send reset link');
      }
    } catch (error) {
      console.error('Reset password error:', error);
      message.error(error.response?.data?.message || 'Failed to send reset link');
    } finally {
      setLoading(false);
    }
  };

  const handleResendEmail = async () => {
    setLoading(true);
    try {
      const response = await authAPI.forgotPassword(email);
      if (response.success) {
        message.success('Password reset link sent again');
      } else {
        message.error(response.message || 'Failed to resend reset link');
      }
    } catch (error) {
      console.error('Resend email error:', error);
      message.error(error.response?.data?.message || 'Failed to resend reset link');
    } finally {
      setLoading(false);
    }
  };

  if (emailSent) {
    return (
      <div className="w-full max-w-md mx-auto text-center">
        <Result
          icon={<CheckCircleOutlined className="text-green-500" />}
          title="Check Your Email"
          subTitle={
            <div className="space-y-4">
              <p className="text-gray-600">
                We've sent a password reset link to:
              </p>
              <p className="font-semibold text-gray-800">{email}</p>
              <p className="text-gray-600 text-sm">
                Click the link in the email to reset your password. 
                If you don't see it, check your spam folder.
              </p>
            </div>
          }
          extra={[
            <AuthButton
              key="resend"
              variant="secondary"
              loading={loading}
              onClick={handleResendEmail}
              className="mb-3"
            >
              Resend Email
            </AuthButton>,
            <AuthButton
              key="back"
              variant="ghost"
              icon={<ArrowLeftOutlined />}
              onClick={onBackToLogin}
              block
            >
              Back to Login
            </AuthButton>
          ]}
        />
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-800 mb-2">
          Forgot Password?
        </h2>
        <p className="text-gray-600">
          Enter your email address and we'll send you a link to reset your password.
        </p>
      </div>

      <Form
        form={form}
        onFinish={handleSubmit}
        layout="vertical"
        requiredMark={false}
        className="space-y-6"
      >
        <Form.Item
          name="email"
          rules={[
            { required: true, message: 'Please enter your email' },
            { type: 'email', message: 'Please enter a valid email' }
          ]}
        >
          <AuthInput
            type="email"
            placeholder="Enter your email address"
            icon={<MailOutlined className="text-gray-400" />}
          />
        </Form.Item>

        <Form.Item>
          <AuthButton
            type="primary"
            htmlType="submit"
            loading={loading}
            block
            variant="primary"
          >
            Send Reset Link
          </AuthButton>
        </Form.Item>

        <Form.Item>
          <AuthButton
            variant="ghost"
            icon={<ArrowLeftOutlined />}
            onClick={onBackToLogin}
            block
          >
            Back to Login
          </AuthButton>
        </Form.Item>
      </Form>
    </div>
  );
};

export default ForgotPasswordForm;