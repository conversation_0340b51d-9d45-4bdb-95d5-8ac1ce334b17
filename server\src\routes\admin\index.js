const express = require('express');
const router = express.Router();

// Import route modules
const dashboardRoutes = require('./dashboard');
const usersRoutes = require('./users');
const vendorsRoutes = require('./vendors');
const ordersRoutes = require('./orders');
const productsRoutes = require('./products');
const categoriesRoutes = require('./categories');
const analyticsRoutes = require('./analytics');
const settingsRoutes = require('./settings');
const notificationsRoutes = require('./notifications');
const homepageSettingsRoutes = require('./homepageSettings');
const profileRoutes = require('./profile');

// Mount routes
router.use('/dashboard', dashboardRoutes);
router.use('/users', usersRoutes);
router.use('/vendors', vendorsRoutes);
router.use('/orders', ordersRoutes);
router.use('/products', productsRoutes);
router.use('/categories', categoriesRoutes);
router.use('/analytics', analyticsRoutes);
router.use('/settings', settingsRoutes);
router.use('/notifications', notificationsRoutes);
router.use('/homepage-settings', homepageSettingsRoutes);
router.use('/profile', profileRoutes);

module.exports = router;