.review-reply {
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  margin-bottom: 8px;
  position: relative;
}

.review-reply:before {
  content: '';
  position: absolute;
  left: 16px;
  top: -8px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid #e8e8e8;
}

.review-reply:after {
  content: '';
  position: absolute;
  left: 17px;
  top: -6px;
  width: 0;
  height: 0;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid #fff;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.vendor-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.vendor-avatar {
  background: #f0f0f0;
}

.vendor-details {
  display: flex;
  align-items: center;
  gap: 6px;
}

.vendor-name {
  font-weight: 500;
  font-size: 14px;
  color: #333;
}

.vendor-tag {
  font-size: 11px;
  padding: 2px 6px;
  margin: 0;
  border-radius: 2px;
}

.reply-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reply-date {
  font-size: 12px;
  color: #888;
  display: flex;
  align-items: center;
  gap: 4px;
}

.reply-content {
  font-size: 13px;
  color: #555;
  line-height: 1.5;
}

.reply-content p {
  margin: 0;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .reply-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }
  
  .vendor-info {
    justify-content: flex-start;
  }
  
  .reply-meta {
    align-self: flex-end;
  }
}

/* Dark Theme */
@media (prefers-color-scheme: dark) {
  .review-reply {
    background: #2a2a2a;
    border-color: #444;
  }
  
  .review-reply:before {
    border-bottom-color: #444;
  }
  
  .review-reply:after {
    border-bottom-color: #2a2a2a;
  }
  
  .vendor-name {
    color: #ddd;
  }
  
  .reply-content {
    color: #bbb;
  }
  
  .reply-date {
    color: #aaa;
  }
}
