// Cart utility functions for validation and user-specific operations

/**
 * Validates cart item before adding/updating
 */
export const validateCartItem = (productId, quantity, product = null) => {
  const errors = [];

  if (!productId) {
    errors.push('Product ID is required');
  }

  if (!quantity || quantity <= 0) {
    errors.push('Quantity must be greater than 0');
  }

  if (quantity > 100) {
    errors.push('Maximum quantity per item is 100');
  }

  if (product) {
    if (product.status !== 'active') {
      errors.push('Product is not available for purchase');
    }

    const availableStock = product.inventory?.quantity || 0;
    if (product.inventory?.trackQuantity && availableStock < quantity) {
      errors.push(`Only ${availableStock} items available in stock`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Formats cart item for display
 */
export const formatCartItem = (item) => {
  const product = item.product;
  const selectedVariant = item.selectedVariant;
  
  return {
    id: `${product._id || product.id}${selectedVariant?.sku ? `-${selectedVariant.sku}` : ''}`,
    productId: product._id || product.id,
    name: product.name || product.title,
    image: product.images?.[0] || '/placeholder-product.jpg',
    price: selectedVariant?.price || item.priceAtAdd,
    quantity: item.quantity,
    variant: selectedVariant,
    vendor: item.vendor,
    totalPrice: (selectedVariant?.price || item.priceAtAdd) * item.quantity,
    inStock: (product.inventory?.quantity || 0) > 0,
    availableStock: product.inventory?.quantity || 0
  };
};

/**
 * Calculates cart totals
 */
export const calculateCartTotals = (items = []) => {
  const totals = items.reduce((acc, item) => {
    const itemTotal = item.priceAtAdd * item.quantity;
    acc.subtotal += itemTotal;
    acc.totalItems += item.quantity;
    
    // Track unique vendors
    if (item.vendor && !acc.vendors.has(item.vendor._id || item.vendor)) {
      acc.vendors.add(item.vendor._id || item.vendor);
    }
    
    return acc;
  }, {
    subtotal: 0,
    totalItems: 0,
    vendors: new Set()
  });

  return {
    subtotal: totals.subtotal,
    totalItems: totals.totalItems,
    vendorCount: totals.vendors.size,
    itemCount: items.length,
    // Add potential shipping/tax calculations here
    shipping: 0, // Free shipping for now
    tax: totals.subtotal * 0.1, // 10% tax example
    total: totals.subtotal + (totals.subtotal * 0.1) // subtotal + tax
  };
};

/**
 * Checks if cart belongs to current user
 */
export const isUserCart = (cart, userId) => {
  if (!cart || !userId) return false;
  return cart.customer === userId || cart.customer?._id === userId;
};

/**
 * Sanitizes cart data for storage
 */
export const sanitizeCartForStorage = (cart) => {
  if (!cart) return null;

  return {
    items: cart.items?.map(item => ({
      product: {
        _id: item.product._id || item.product.id,
        name: item.product.name,
        images: item.product.images?.[0] ? [item.product.images[0]] : []
      },
      quantity: item.quantity,
      priceAtAdd: item.priceAtAdd,
      selectedVariant: item.selectedVariant,
      vendor: item.vendor?._id || item.vendor
    })) || [],
    totalItems: cart.totalItems || 0,
    totalAmount: cart.totalAmount || 0,
    lastUpdated: cart.lastUpdated || new Date().toISOString()
  };
};

/**
 * Merges local cart with server cart (for offline support)
 */
export const mergeCartData = (serverCart, localCart) => {
  if (!localCart || !localCart.items?.length) return serverCart;
  if (!serverCart || !serverCart.items?.length) return localCart;

  // For now, prioritize server cart as it's the source of truth
  // In a more complex scenario, you might want to merge based on timestamps
  return serverCart;
};

/**
 * Formats currency for display
 */
export const formatPrice = (amount, currency = 'USD') => {
  try {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount || 0);
  } catch (error) {
    // Fallback for unsupported currencies
    return `${currency} ${(amount || 0).toFixed(2)}`;
  }
};

/**
 * Gets user-specific cart storage key
 */
export const getCartStorageKey = (userId) => {
  return `cart_${userId}`;
};

/**
 * Clears user-specific cart data
 */
export const clearUserCartData = (userId) => {
  try {
    const storageKey = getCartStorageKey(userId);
    localStorage.removeItem(storageKey);
    localStorage.removeItem('cartBackup'); // Legacy key
  } catch (error) {
    console.error('Error clearing user cart data:', error);
  }
};
