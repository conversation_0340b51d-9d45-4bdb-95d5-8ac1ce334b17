const jwt = require('jsonwebtoken');
const User = require('../models/User');
const emailService = require('../utils/emailService');
const twilioService = require('../utils/twilioService');

// Generate JWT token
const generateToken = (userId, userType) => {
    return jwt.sign(
        { userId, userType },
        process.env.JWT_SECRET,
        { expiresIn: '7d' }
    );
};

class AuthController {
    // Send OTP for phone verification
    async sendOTP(req, res) {
        try {
            const { phone, countryCode, purpose = 'verification' } = req.body;
            
            if (!phone) {
                return res.status(400).json({
                    success: false,
                    message: 'Phone number is required'
                });
            }

            // Validate phone number format
            const phoneValidation = await twilioService.validatePhoneNumber(phone, countryCode || 'US');
            if (!phoneValidation.valid) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid phone number format'
                });
            }

            const formattedPhone = phoneValidation.phoneNumber;
            
            // For login OTP, check if user exists
            if (purpose === 'login') {
                const existingUser = await User.findOne({ 
                    phone: formattedPhone.replace('+', ''),
                    status: 'active'
                }).select('+otpAttempts +lastOtpRequest');
                
                if (!existingUser) {
                    return res.status(404).json({
                        success: false,
                        message: 'No account found with this phone number'
                    });
                }
                
                // Check rate limiting
                if (existingUser.isOTPRateLimited()) {
                    return res.status(429).json({
                        success: false,
                        message: 'Too many OTP requests. Please try again in 5 minutes.'
                    });
                }
                
                // Generate and save OTP
                const otp = existingUser.generateOTP();
                existingUser.otpAttempts = (existingUser.otpAttempts || 0) + 1;
                await existingUser.save();
                
                // Send OTP via SMS
                const smsResult = await twilioService.sendOTP(phone, otp, countryCode);
                
                return res.json({
                    success: true,
                    message: 'OTP sent successfully to your phone number',
                    data: {
                        phone: twilioService.formatPhoneForDisplay(phone, countryCode),
                        otpSent: smsResult.success,
                        expiresIn: 5 // minutes
                    }
                });
            }
            
            // For signup OTP, just send without checking user existence
            // Create temporary OTP storage (you might want to use Redis for this in production)
            const otp = Math.floor(100000 + Math.random() * 900000).toString();
            const otpExpiry = Date.now() + 5 * 60 * 1000; // 5 minutes
            
            // Store OTP in session or temporary storage
            // For now, we'll create a temporary user record
            const tempUser = new User({
                firstName: 'temp',
                lastName: 'temp', 
                email: `temp_${Date.now()}@temp.com`,
                password: 'temppassword',
                phone: formattedPhone.replace('+', ''),
                countryCode: countryCode || 'US',
                status: 'pending'
            });
            
            const generatedOTP = tempUser.generateOTP();
            await tempUser.save();
            
            // Send OTP via SMS
            const smsResult = await twilioService.sendOTP(phone, generatedOTP, countryCode);
            
            res.json({
                success: true,
                message: 'OTP sent successfully to your phone number',
                data: {
                    phone: twilioService.formatPhoneForDisplay(phone, countryCode),
                    otpSent: smsResult.success,
                    expiresIn: 5, // minutes
                    tempUserId: tempUser._id // For verification
                }
            });
            
        } catch (error) {
            console.error('Send OTP error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to send OTP. Please try again.'
            });
        }
    }

    // Verify OTP
    async verifyOTP(req, res) {
        try {
            const { phone, otp, countryCode, purpose = 'verification', tempUserId } = req.body;
            
            if (!phone || !otp) {
                return res.status(400).json({
                    success: false,
                    message: 'Phone number and OTP are required'
                });
            }

            // Format phone number
            const phoneValidation = await twilioService.validatePhoneNumber(phone, countryCode || 'US');
            if (!phoneValidation.valid) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid phone number format'
                });
            }

            const formattedPhone = phoneValidation.phoneNumber;
            let user;
            
            if (purpose === 'login') {
                user = await User.findOne({ 
                    phone: formattedPhone.replace('+', ''),
                    status: 'active'
                }).select('+otpCode +otpExpires +otpAttempts');
            } else {
                // For signup verification, find temp user
                user = await User.findById(tempUserId).select('+otpCode +otpExpires +otpAttempts');
            }
            
            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'Invalid verification request'
                });
            }
            
            // Verify OTP
            const isValidOTP = user.verifyOTP(otp);
            
            if (!isValidOTP) {
                user.otpAttempts = (user.otpAttempts || 0) + 1;
                await user.save();
                
                return res.status(400).json({
                    success: false,
                    message: 'Invalid or expired OTP'
                });
            }
            
            // Clear OTP after successful verification
            user.clearOTP();
            user.isPhoneVerified = true;
            await user.save();
            
            res.json({
                success: true,
                message: 'Phone number verified successfully',
                data: {
                    phoneVerified: true,
                    userId: purpose === 'signup' ? user._id : undefined
                }
            });
            
        } catch (error) {
            console.error('Verify OTP error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to verify OTP. Please try again.'
            });
        }
    }

    // Register new user
    async register(req, res) {
        try {
            console.log('🔄 Registration attempt started');
            console.log('📋 Request body:', JSON.stringify(req.body, null, 2));
            
            const {
                firstName,
                lastName,
                email,
                password,
                userType,
                businessName,
                businessType,
                phone,
                countryCode
            } = req.body;

            // Validate required fields
            if (!firstName || !lastName || !email || !password) {
                console.log('❌ Missing required fields');
                return res.status(400).json({
                    success: false,
                    message: 'Missing required fields: firstName, lastName, email, and password are required'
                });
            }

            console.log('✅ Required fields validation passed');

            // Check if user already exists
            console.log('🔍 Checking if user exists with email:', email);
            const existingUser = await User.findByEmail(email);
            if (existingUser) {
                console.log('❌ User already exists');
                return res.status(400).json({
                    success: false,
                    message: 'User with this email already exists'
                });
            }

            console.log('✅ Email is available');

            // Create user data
            const userData = {
                firstName: firstName.trim(),
                lastName: lastName.trim(),
                email: email.toLowerCase().trim(),
                password,
                userType: userType || 'customer'
            };

            // Add phone if provided
            if (phone) {
                userData.phone = phone;
                userData.countryCode = countryCode || 'US';
            }

            // Add vendor fields if needed
            if (userType === 'vendor') {
                if (!businessName) {
                    console.log('❌ Business name required for vendor registration');
                    return res.status(400).json({
                        success: false,
                        message: 'Business name is required for vendor registration'
                    });
                }
                userData.businessName = businessName.trim();
                userData.businessType = businessType ? businessType.trim() : 'general';
                userData.contactPerson = `${firstName} ${lastName}`;
            }

            console.log('📋 User data prepared:', JSON.stringify({
                ...userData,
                password: '[HIDDEN]'
            }, null, 2));

            // Create user
            console.log('💾 Creating user in database...');
            const user = new User(userData);
            
            // Generate email verification token if email verification is enabled
            let verificationToken = null;
            if (process.env.ENABLE_EMAIL_VERIFICATION === 'true') {
                console.log('📧 Generating email verification token...');
                verificationToken = user.generateEmailVerificationToken();
            }
            
            await user.save();
            console.log('✅ User saved successfully with ID:', user._id);

            // Send verification email if enabled
            if (verificationToken) {
                try {
                    console.log('📧 Sending verification email...');
                    await emailService.sendVerificationEmail(user, verificationToken);
                    console.log('✅ Verification email sent');
                } catch (emailError) {
                    console.error('❌ Email sending failed:', emailError);
                    // Don't fail registration if email fails
                }
            }

            // Generate token
            console.log('🔑 Generating JWT token...');
            const token = generateToken(user._id, user.userType);

            // Return complete clean user data
            const userResponse = {
                id: user._id,
                firstName: user.firstName,
                lastName: user.lastName,
                name: `${user.firstName} ${user.lastName}`,
                email: user.email,
                phone: user.phone,
                countryCode: user.countryCode,
                userType: user.userType,
                isEmailVerified: user.isEmailVerified,
                businessName: user.businessName,
                businessType: user.businessType,
                contactPerson: user.contactPerson,
                isVendorApproved: user.isVendorApproved,
                avatar: user.avatar,
                status: user.status
            };

            console.log('✅ Registration completed successfully');

            res.status(201).json({
                success: true,
                message: verificationToken ? 
                    'Registration successful. Please check your email for verification.' :
                    'Registration successful.',
                data: {
                    user: userResponse,
                    token
                }
            });

        } catch (error) {
            console.error('❌ Registration error:', error);
            console.error('📋 Error stack:', error.stack);
            
            if (error.code === 11000) {
                const field = Object.keys(error.keyValue || {})[0] || 'field';
                console.log('❌ Duplicate key error for field:', field);
                return res.status(400).json({
                    success: false,
                    message: `${field} already exists`
                });
            }

            if (error.name === 'ValidationError') {
                const errors = Object.values(error.errors).map(err => err.message);
                console.log('❌ Mongoose validation errors:', errors);
                return res.status(400).json({
                    success: false,
                    message: 'Validation error',
                    errors
                });
            }

            // Database connection error
            if (error.name === 'MongoNetworkError' || error.name === 'MongooseServerSelectionError') {
                console.log('❌ Database connection error');
                return res.status(500).json({
                    success: false,
                    message: 'Database connection error. Please try again later.'
                });
            }

            res.status(500).json({
                success: false,
                message: 'Internal server error',
                ...(process.env.NODE_ENV === 'development' && {
                    error: error.message,
                    stack: error.stack
                })
            });
        }
    }

    // Login user (Step 1: Verify email/password)
    async login(req, res) {
        try {
            const { email, password, userType, otp } = req.body;

            // Find user with password and OTP fields
            const user = await User.findByEmail(email).select('+password +otpCode +otpExpires +otpAttempts');
            
            if (!user) {
                return res.status(401).json({
                    success: false,
                    message: 'Invalid email or password'
                });
            }

            // Check user type if specified
            if (userType && user.userType !== userType) {
                return res.status(403).json({
                    success: false,
                    message: `This email is registered as a ${user.userType} account.`
                });
            }

            // Check if locked
            if (user.isLocked()) {
                return res.status(423).json({
                    success: false,
                    message: 'Account is temporarily locked'
                });
            }

            // Verify password
            const isValid = await user.comparePassword(password);
            
            if (!isValid) {
                // Increment login attempts (simplified)
                user.loginAttempts = (user.loginAttempts || 0) + 1;
                if (user.loginAttempts >= 5) {
                    user.lockUntil = new Date(Date.now() + 2 * 60 * 60 * 1000); // 2 hours
                }
                await user.save();
                
                return res.status(401).json({
                    success: false,
                    message: 'Invalid email or password'
                });
            }

            // If user has phone number, require OTP verification
            if (user.phone && user.phone.trim()) {
                if (!otp) {
                    // Password is correct, now require OTP
                    // Check if OTP was already sent recently
                    const now = new Date();
                    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
                    
                    if (!user.lastOtpRequest || user.lastOtpRequest < fiveMinutesAgo) {
                        // Generate and send new OTP
                        const generatedOTP = user.generateOTP();
                        user.otpAttempts = (user.otpAttempts || 0) + 1;
                        await user.save();
                        
                        // Send OTP via SMS
                        const smsResult = await twilioService.sendOTP(user.phone, generatedOTP, user.countryCode);
                        
                        console.log('🔑 Login OTP sent to:', twilioService.formatPhoneForDisplay(user.phone, user.countryCode));
                    }
                    
                    return res.status(200).json({
                        success: false,
                        requiresOTP: true,
                        message: 'OTP has been sent to your registered phone number. Please enter the OTP to complete login.',
                        data: {
                            phone: twilioService.formatPhoneForDisplay(user.phone, user.countryCode),
                            email: user.email,
                            expiresIn: 5
                        }
                    });
                }
                
                // Verify OTP if provided
                const isValidOTP = user.verifyOTP(otp);
                
                if (!isValidOTP) {
                    user.otpAttempts = (user.otpAttempts || 0) + 1;
                    await user.save();
                    
                    return res.status(400).json({
                        success: false,
                        requiresOTP: true,
                        message: 'Invalid or expired OTP. Please try again.',
                        data: {
                            phone: twilioService.formatPhoneForDisplay(user.phone, user.countryCode),
                            email: user.email,
                            expiresIn: 5
                        }
                    });
                }
                
                // Clear OTP after successful verification
                user.clearOTP();
            }

            // Reset login attempts on success
            user.loginAttempts = 0;
            user.lockUntil = undefined;
            user.lastLogin = new Date();
            await user.save();

            // Send login alert SMS if phone is available
            if (user.phone && user.phone.trim()) {
                try {
                    await twilioService.sendLoginAlert(user.phone, user.countryCode, {
                        userType: user.userType,
                        name: `${user.firstName} ${user.lastName}`
                    });
                } catch (alertError) {
                    console.error('Failed to send login alert:', alertError);
                    // Don't fail login if alert fails
                }
            }

            // Generate token
            const token = generateToken(user._id, user.userType);

            // Return complete user data
            const userResponse = {
                id: user._id,
                firstName: user.firstName,
                lastName: user.lastName,
                name: `${user.firstName} ${user.lastName}`,
                email: user.email,
                phone: user.phone,
                countryCode: user.countryCode,
                userType: user.userType,
                isEmailVerified: user.isEmailVerified,
                isPhoneVerified: user.isPhoneVerified,
                businessName: user.businessName,
                businessType: user.businessType,
                contactPerson: user.contactPerson,
                isVendorApproved: user.isVendorApproved,
                avatar: user.avatar,
                status: user.status,
                // Address fields
                address: user.address,
                city: user.city,
                state: user.state,
                zipCode: user.zipCode,
                country: user.country,
                addresses: user.addresses,
                // Preferences
                preferences: user.preferences
            };

            res.json({
                success: true,
                message: 'Login successful',
                data: {
                    user: userResponse,
                    token
                }
            });

        } catch (error) {
            console.error('Login error:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }

    // Request password reset
    async requestPasswordReset(req, res) {
        try {
            const { email } = req.body;
            const user = await User.findByEmail(email);

            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            const resetToken = user.generatePasswordResetToken();
            await user.save();

            try {
                await emailService.sendPasswordResetEmail(user, resetToken);
                res.json({
                    success: true,
                    message: 'Password reset email sent successfully'
                });
            } catch (error) {
                console.error('Error sending password reset email:', error);
                res.status(500).json({
                    success: false,
                    message: 'Failed to send password reset email'
                });
            }
        } catch (error) {
            console.error('Request password reset error:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }

    // Reset password
    async resetPassword(req, res) {
        try {
            const { token, email, newPassword } = req.body;

            const hashedToken = require('crypto').createHash('sha256').update(token).digest('hex');
            const user = await User.findOne({
                email: email.toLowerCase(),
                resetPasswordToken: hashedToken,
                resetPasswordExpires: { $gt: Date.now() }
            });

            if (!user) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid or expired password reset token'
                });
            }

            user.password = newPassword;
            user.resetPasswordToken = undefined;
            user.resetPasswordExpires = undefined;
            await user.save();

            res.json({
                success: true,
                message: 'Password reset successful'
            });
        } catch (error) {
            console.error('Reset password error:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }

    // Get current user profile
    async getProfile(req, res) {
        try {
            const user = await User.findById(req.userId);
            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            const userResponse = {
                id: user._id,
                firstName: user.firstName,
                lastName: user.lastName,
                name: `${user.firstName} ${user.lastName}`,
                email: user.email,
                phone: user.phone,
                countryCode: user.countryCode,
                userType: user.userType,
                isEmailVerified: user.isEmailVerified,
                businessName: user.businessName,
                businessType: user.businessType,
                contactPerson: user.contactPerson,
                isVendorApproved: user.isVendorApproved,
                avatar: user.avatar,
                status: user.status
            };

            res.json({
                success: true,
                data: {
                    user: userResponse
                }
            });
        } catch (error) {
            console.error('Profile error:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }

    // Update user profile
    async updateProfile(req, res) {
        try {
            const userId = req.userId;
            const updateData = req.body;

            // Check if body is empty or undefined
            if (!updateData || typeof updateData !== 'object' || Object.keys(updateData).length === 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid or empty request body. Please provide data to update.'
                });
            }

            // Find the user
            const user = await User.findById(userId);
            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            // Validate and sanitize update data
            const allowedFields = [
                'firstName', 'lastName', 'phone', 'countryCode',
                'businessName', 'businessType', 'contactPerson',
                'dateOfBirth', 'gender', 'address', 'city', 'state', 
                'zipCode', 'country', 'preferences'
            ];

            const filteredData = {};
            for (const field of allowedFields) {
                if (updateData[field] !== undefined) {
                    filteredData[field] = updateData[field];
                }
            }

            // Handle preferences separately if it's a string (from FormData)
            if (typeof filteredData.preferences === 'string') {
                try {
                    filteredData.preferences = JSON.parse(filteredData.preferences);
                } catch (error) {
                    console.error('Error parsing preferences:', error);
                    delete filteredData.preferences;
                }
            }

            console.log('📋 Filtered update data:', JSON.stringify(filteredData, null, 2));

            // Update the user
            const updatedUser = await User.findByIdAndUpdate(
                userId,
                { $set: filteredData },
                { new: true, runValidators: true }
            );

            if (!updatedUser) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            // Return updated user data
            const userResponse = {
                id: updatedUser._id,
                firstName: updatedUser.firstName,
                lastName: updatedUser.lastName,
                name: `${updatedUser.firstName} ${updatedUser.lastName}`,
                email: updatedUser.email,
                phone: updatedUser.phone,
                countryCode: updatedUser.countryCode,
                userType: updatedUser.userType,
                isEmailVerified: updatedUser.isEmailVerified,
                businessName: updatedUser.businessName,
                businessType: updatedUser.businessType,
                contactPerson: updatedUser.contactPerson,
                isVendorApproved: updatedUser.isVendorApproved,
                avatar: updatedUser.avatar,
                status: updatedUser.status,
                dateOfBirth: updatedUser.dateOfBirth,
                gender: updatedUser.gender,
                address: updatedUser.address,
                city: updatedUser.city,
                state: updatedUser.state,
                zipCode: updatedUser.zipCode,
                country: updatedUser.country,
                preferences: updatedUser.preferences
            };

            console.log('✅ Profile update completed successfully');

            res.json({
                success: true,
                message: 'Profile updated successfully',
                data: userResponse
            });

        } catch (error) {
            console.error('❌ Profile update error:', error);
            console.error('📋 Error stack:', error.stack);
            
            if (error.name === 'ValidationError') {
                const errors = Object.values(error.errors).map(err => err.message);
                console.log('❌ Mongoose validation errors:', errors);
                return res.status(400).json({
                    success: false,
                    message: 'Validation error',
                    errors
                });
            }

            res.status(500).json({
                success: false,
                message: 'Internal server error',
                ...(process.env.NODE_ENV === 'development' && {
                    error: error.message,
                    stack: error.stack
                })
            });
        }
    }

    // Email verification
    async verifyEmail(req, res) {
        try {
            const { token, email } = req.query;
            
            // Validate required parameters
            if (!token) {
                return res.status(400).json({
                    success: false,
                    message: 'Verification token is required'
                });
            }

            if (!email) {
                return res.status(400).json({
                    success: false,
                    message: 'Email parameter is required'
                });
            }

            console.log('🔍 Email verification attempt:', { email, token: token.substring(0, 10) + '...' });
            
            const hashedToken = require('crypto')
                .createHash('sha256')
                .update(token)
                .digest('hex');

            console.log('🔍 Looking for user with hashed token:', hashedToken.substring(0, 10) + '...');

            const user = await User.findOne({
                email: email.toLowerCase(),
                emailVerificationToken: hashedToken,
                emailVerificationExpires: { $gt: Date.now() }
            });

            if (!user) {
                console.log('❌ No user found with valid token');
                
                // Check if user exists but token is invalid/expired
                const userExists = await User.findOne({ email: email.toLowerCase() });
                if (userExists) {
                    if (userExists.isEmailVerified) {
                        return res.status(400).json({
                            success: false,
                            message: 'Email is already verified'
                        });
                    } else {
                        return res.status(400).json({
                            success: false,
                            message: 'Invalid or expired verification token. Please request a new verification email.'
                        });
                    }
                }
                
                return res.status(400).json({
                    success: false,
                    message: 'Invalid verification link'
                });
            }

            console.log('✅ User found, verifying email...');

            user.isEmailVerified = true;
            user.emailVerificationToken = undefined;
            user.emailVerificationExpires = undefined;
            await user.save();

            console.log('✅ Email verified successfully for:', user.email);

            // Return complete updated user data
            const userResponse = {
                id: user._id,
                firstName: user.firstName,
                lastName: user.lastName,
                name: `${user.firstName} ${user.lastName}`,
                email: user.email,
                phone: user.phone,
                countryCode: user.countryCode,
                userType: user.userType,
                isEmailVerified: user.isEmailVerified,
                businessName: user.businessName,
                businessType: user.businessType,
                contactPerson: user.contactPerson,
                isVendorApproved: user.isVendorApproved,
                avatar: user.avatar,
                status: user.status
            };

            res.json({
                success: true,
                message: 'Email verified successfully',
                data: {
                    user: userResponse
                }
            });

        } catch (error) {
            console.error('❌ Email verification error:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }

    // Resend verification email
    async resendVerificationEmail(req, res) {
        try {
            const { email } = req.body;

            // Find user by email
            const user = await User.findByEmail(email);
            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found with this email address'
                });
            }

            // Check if email is already verified
            if (user.isEmailVerified) {
                return res.status(400).json({
                    success: false,
                    message: 'Email is already verified'
                });
            }

            // Generate new verification token
            const verificationToken = user.generateEmailVerificationToken();
            await user.save();

            // Send verification email
            try {
                await emailService.sendVerificationEmail(user, verificationToken);
                
                res.json({
                    success: true,
                    message: 'Verification email sent successfully. Please check your inbox.'
                });
            } catch (emailError) {
                console.error('Email sending failed:', emailError);
                res.status(500).json({
                    success: false,
                    message: 'Failed to send verification email. Please try again later.'
                });
            }

        } catch (error) {
            console.error('Resend verification email error:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }

    // Logout user (simple token invalidation)
    async logout(req, res) {
        try {
            // In a simple JWT implementation, logout is handled client-side
            // by removing the token from storage
            res.json({
                success: true,
                message: 'Logged out successfully'
            });
        } catch (error) {
            console.error('Logout error:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }
}

module.exports = new AuthController();
