import React from 'react';
import { Table, Rate, Avatar, Button, Tag, Typography, Space, Tooltip, Empty, Card } from 'antd';
import {
  UserOutlined,
  SendOutlined,
  EditOutlined,
  DeleteOutlined,
  LikeOutlined,
  DislikeOutlined
} from '@ant-design/icons';

const { Text } = Typography;

const ReviewsTable = ({
  reviews = [],
  loading = false,
  pagination,
  onTableChange,
  onReply,
  onEditReply,
  onDeleteReply
}) => {
  // Format date helper
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Table columns configuration
  const columns = [
    {
      title: 'Product',
      dataIndex: ['product', 'name'],
      key: 'product',
      width: 150,
      render: (name, record) => (
        <div className="flex flex-col gap-1">
          <Text strong className="text-sm text-gray-900 leading-tight">
            {name || 'Unknown Product'}
          </Text>
          <Text type="secondary" className="text-xs text-gray-500">
            SKU: {record.product?.sku || 'N/A'}
          </Text>
        </div>
      )
    },
    {
      title: 'Customer',
      key: 'customer',
      width: 120,
      render: (_, record) => (
        <div className="flex items-center gap-3">
          <Avatar
            src={record.customer?.avatar}
            icon={<UserOutlined />}
            size="default"
          />
          <div className="flex flex-col gap-1">
            <Text className="text-sm font-medium text-gray-900">
              {record.customer?.firstName} {record.customer?.lastName}
            </Text>
            {record.customer?.isVerified && (
              <Tag color="green" size="small">Verified Purchase</Tag>
            )}
          </div>
        </div>
      )
    },
    {
      title: 'Rating',
      dataIndex: 'rating',
      key: 'rating',
      width: 100,
      align: 'center',
      render: (rating) => (
        <div className="flex flex-col items-center gap-1">
          <Rate disabled value={rating} size="small" />
          <Text className="text-xs text-gray-500">{rating}/5</Text>
        </div>
      ),
      sorter: (a, b) => a.rating - b.rating
    },
    {
      title: 'Review',
      dataIndex: 'comment',
      key: 'comment',
      width: 250,
      render: (comment) => (
        <div className="max-w-xs">
          <Text className="text-sm text-gray-700 leading-relaxed break-words">
            {comment && comment.length > 100 ? `${comment.substring(0, 100)}...` : comment}
          </Text>
        </div>
      )
    },
    {
      title: 'Helpful',
      key: 'helpful',
      width: 80,
      align: 'center',
      render: (_, record) => (
        <div className="flex justify-center">
          <Space direction="vertical" size="small">
            <div className="flex items-center gap-1 text-xs">
              <LikeOutlined className="text-green-500" />
              <Text className="text-xs">{record.helpfulCount || 0}</Text>
            </div>
            <div className="flex items-center gap-1 text-xs">
              <DislikeOutlined className="text-red-500" />
              <Text className="text-xs">{record.unhelpfulCount || 0}</Text>
            </div>
          </Space>
        </div>
      )
    },
    {
      title: 'Date',
      dataIndex: 'createdAt',
      key: 'date',
      width: 100,
      render: (date) => (
        <Text className="date-text">{formatDate(date)}</Text>
      ),
      sorter: (a, b) => new Date(a.createdAt) - new Date(b.createdAt)
    },
    {
      title: 'Status',
      key: 'status',
      width: 100,
      align: 'center',
      render: (_, record) => {
        const hasReply = record.replies && record.replies.length > 0;
        return (
          <Tag color={hasReply ? 'green' : 'orange'}>
            {hasReply ? 'Replied' : 'No Reply'}
          </Tag>
        );
      }
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      align: 'center',
      render: (_, record) => {
        const hasReply = record.replies && record.replies.length > 0;
        const reply = hasReply ? record.replies[0] : null;

        return (
          <Space size="small">
            {!hasReply ? (
              <Tooltip title="Reply to review">
                <Button
                  type="primary"
                  size="small"
                  icon={<SendOutlined />}
                  onClick={() => onReply(record)}
                >
                  Reply
                </Button>
              </Tooltip>
            ) : (
              <Space size="small">
                <Tooltip title="Edit reply">
                  <Button
                    type="default"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => onEditReply(reply)}
                  >
                    Edit Reply
                  </Button>
                </Tooltip>
                <Tooltip title="Delete reply">
                  <Button
                    type="default"
                    danger
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={() => onDeleteReply(reply)}
                  />
                </Tooltip>
              </Space>
            )}
          </Space>
        );
      }
    }
  ];

  return (
    <Card className="rounded-xl shadow-sm border border-gray-200">
      {reviews.length === 0 && !loading ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="Your products haven't received any reviews yet."
          className="py-16 text-center text-gray-500"
        />
      ) : (
        <Table
          columns={columns}
          dataSource={reviews}
          rowKey="_id"
          loading={loading}
          pagination={{
            current: pagination?.current || 1,
            pageSize: pagination?.pageSize || 10,
            total: pagination?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} reviews`,
            pageSizeOptions: ['10', '20', '50']
          }}
          onChange={onTableChange}
          scroll={{ x: 1200 }}
          className="rounded-lg overflow-hidden"
        />
      )}
    </Card>
  );
};

export default ReviewsTable;
