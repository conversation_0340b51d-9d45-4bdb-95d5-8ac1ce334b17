import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import Analytics from '../Analytics';
import { dashboardApi } from '../../../../services/vendorApi';

// Mock the vendor API
vi.mock('../../../../services/vendorApi', () => ({
  dashboardApi: {
    getStats: vi.fn(),
    getAnalytics: vi.fn(),
  },
}));

// Mock Chart.js
vi.mock('react-chartjs-2', () => ({
  Line: ({ data, options }) => <div data-testid="line-chart">Line Chart</div>,
  Bar: ({ data, options }) => <div data-testid="bar-chart">Bar Chart</div>,
  Doughnut: ({ data, options }) => <div data-testid="doughnut-chart">Doughnut Chart</div>,
}));

// Mock dayjs
vi.mock('dayjs', () => {
  const mockDayjs = vi.fn(() => ({
    format: vi.fn(() => 'Jan 01'),
  }));
  mockDayjs.extend = vi.fn();
  return mockDayjs;
});

describe('Vendor Analytics Component', () => {
  const mockDashboardData = {
    products: {
      totalProducts: 10,
      activeProducts: 8,
      averageRating: 4.5,
    },
    orders: {
      totalOrders: 25,
      totalRevenue: 50000,
      averageOrderValue: 2000,
      deliveredOrders: 20,
    },
    today: {
      todayOrders: 3,
      todayRevenue: 6000,
    },
    recentOrders: [],
    topProducts: [],
  };

  const mockAnalyticsData = {
    analytics: [
      { _id: '2024-01-01', revenue: 5000, orders: 5 },
      { _id: '2024-01-02', revenue: 7000, orders: 7 },
      { _id: '2024-01-03', revenue: 6000, orders: 6 },
    ],
    period: '30d',
    type: 'revenue',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock successful API responses
    dashboardApi.getStats.mockResolvedValue({
      data: {
        success: true,
        data: mockDashboardData,
      },
    });

    dashboardApi.getAnalytics.mockResolvedValue({
      data: {
        success: true,
        data: mockAnalyticsData,
      },
    });
  });

  test('renders loading state initially', () => {
    render(<Analytics />);
    expect(screen.getByText('Loading vendor analytics...')).toBeInTheDocument();
  });

  test('renders analytics data after loading', async () => {
    render(<Analytics />);

    await waitFor(() => {
      expect(screen.getByText('Vendor Analytics')).toBeInTheDocument();
    });

    // Check if statistics cards are rendered
    expect(screen.getByText('Total Products')).toBeInTheDocument();
    expect(screen.getByText('10')).toBeInTheDocument();
    expect(screen.getByText('Active Products')).toBeInTheDocument();
    expect(screen.getByText('8')).toBeInTheDocument();
    expect(screen.getByText('Total Orders')).toBeInTheDocument();
    expect(screen.getByText('25')).toBeInTheDocument();
  });

  test('handles API errors gracefully', async () => {
    dashboardApi.getStats.mockRejectedValue(new Error('API Error'));
    dashboardApi.getAnalytics.mockRejectedValue(new Error('Analytics Error'));

    render(<Analytics />);

    await waitFor(() => {
      expect(screen.getByText('Vendor Analytics')).toBeInTheDocument();
    });

    // Should show fallback data (zeros)
    expect(screen.getByText('0')).toBeInTheDocument();
  });

  test('handles period change', async () => {
    render(<Analytics />);

    await waitFor(() => {
      expect(screen.getByText('Vendor Analytics')).toBeInTheDocument();
    });

    // Find and click the period selector
    const periodSelect = screen.getByDisplayValue('Last 30 days');
    fireEvent.mouseDown(periodSelect);
    
    const sevenDaysOption = screen.getByText('Last 7 days');
    fireEvent.click(sevenDaysOption);

    // Verify analytics API was called with new period
    await waitFor(() => {
      expect(dashboardApi.getAnalytics).toHaveBeenCalledWith({ period: '7d', type: 'revenue' });
    });
  });

  test('handles refresh functionality', async () => {
    render(<Analytics />);

    await waitFor(() => {
      expect(screen.getByText('Vendor Analytics')).toBeInTheDocument();
    });

    // Find and click refresh button
    const refreshButton = screen.getByRole('img', { name: /reload/i });
    fireEvent.click(refreshButton);

    // Verify APIs were called again
    await waitFor(() => {
      expect(dashboardApi.getStats).toHaveBeenCalledTimes(2);
      expect(dashboardApi.getAnalytics).toHaveBeenCalledTimes(2);
    });
  });

  test('displays error alert when API fails', async () => {
    dashboardApi.getStats.mockRejectedValue(new Error('Network Error'));

    render(<Analytics />);

    await waitFor(() => {
      expect(screen.getByText('Data Loading Issue')).toBeInTheDocument();
      expect(screen.getByText(/Failed to load dashboard data/)).toBeInTheDocument();
    });
  });

  test('shows no data message when charts have no data', async () => {
    dashboardApi.getAnalytics.mockResolvedValue({
      data: {
        success: true,
        data: { analytics: [], period: '30d', type: 'revenue' },
      },
    });

    render(<Analytics />);

    await waitFor(() => {
      expect(screen.getByText('No revenue data available')).toBeInTheDocument();
      expect(screen.getByText('No order data available')).toBeInTheDocument();
    });
  });
});
