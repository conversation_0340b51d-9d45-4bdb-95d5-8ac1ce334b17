import React from 'react';
import { <PERSON>, Button, Divider, Typography, Space, Tag, Image } from 'antd';
import { ShoppingCartOutlined, SafetyOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { getPrimaryProductImage } from '../../utils/imageUtils';

const { Title, Text } = Typography;

const OrderSummary = ({ 
  cart, 
  totals, 
  selectedAddress, 
  selectedPaymentMethod, 
  onPlaceOrder, 
  isPlacingOrder 
}) => {
  // Enhanced address validation to handle both user profile and separate address objects
  const hasValidAddress = selectedAddress && 
    selectedAddress.address && 
    selectedAddress.city && 
    selectedAddress.state && 
    selectedAddress.zipCode;

  // Debug logging for address validation
  console.log('OrderSummary Debug:', {
    selectedAddress,
    hasValidAddress,
    addressFields: selectedAddress ? {
      address: !!selectedAddress.address,
      city: !!selectedAddress.city,
      state: !!selectedAddress.state,
      zipCode: !!selectedAddress.zipCode
    } : null
  });

  return (
    <Card className="sticky top-4" title="Order Summary">
      {/* Product Details */}
      <div className="mb-4">
        <Title level={5}>
          <ShoppingCartOutlined className="mr-2" />
          Items ({cart?.totalItems || 0})
        </Title>
        <div className="max-h-64 overflow-y-auto">
          {cart?.items?.map((item, index) => {
            const productImage = getPrimaryProductImage(item.product) || 
              'https://via.placeholder.com/60x60?text=No+Image';
            const itemPrice = item.selectedVariant?.price || item.priceAtAdd;
            
            return (
              <div key={`${item.product._id}-${index}`} className="flex items-center py-2 border-b border-gray-100 last:border-b-0">
                <Image
                  src={productImage}
                  alt={item.product.name}
                  width={50}
                  height={50}
                  className="rounded object-cover"
                  preview={false}
                />
                <div className="ml-3 flex-grow">
                  <Text strong className="text-sm block">{item.product.name}</Text>
                  <Space size="small">
                    <Text type="secondary" className="text-xs">Qty: {item.quantity}</Text>
                    <Text className="text-xs">₹{itemPrice}</Text>
                  </Space>
                  {item.selectedVariant && (
                    <div className="mt-1">
                      {Object.entries(item.selectedVariant.attributes || {}).map(([key, value]) => (
                        <Tag key={key} size="small" color="blue">
                          {key}: {value}
                        </Tag>
                      ))}
                    </div>
                  )}
                </div>
                <Text strong className="text-sm">
                  ₹{(itemPrice * item.quantity).toFixed(0)}
                </Text>
              </div>
            );
          })}
        </div>
      </div>

      <Divider />

      {/* Delivery Address */}
      {hasValidAddress && (
        <div className="mb-4">
          <Title level={5}>Delivery Address</Title>
          <div className="bg-blue-50 p-3 rounded border">
            <Text className="block font-medium">{selectedAddress.address}</Text>
            <Text type="secondary" className="block">
              {selectedAddress.city}, {selectedAddress.state} - {selectedAddress.zipCode}
            </Text>
            <Text type="secondary">{selectedAddress.country}</Text>
          </div>
        </div>
      )}

      {/* Payment Method */}
      <div className="mb-4">
        <Title level={5}>Payment Method</Title>
        <Text>{selectedPaymentMethod === 'cod' ? 'Cash on Delivery' : 'Online Payment'}</Text>
      </div>

      <Divider />

      {/* Price Breakdown */}
      <div className="space-y-2 mb-4">
        <div className="flex justify-between">
          <Text>Price ({cart?.totalItems || 0} items)</Text>
          <Text>₹{totals.subtotal.toFixed(0)}</Text>
        </div>
        <div className="flex justify-between">
          <Text>Delivery Fee</Text>
          <Text>₹{totals.protectPromiseFee.toFixed(0)}</Text>
        </div>
        <Divider className="my-2" />
        <div className="flex justify-between">
          <Text strong className="text-lg">Total Amount</Text>
          <Text strong className="text-lg">₹{totals.total.toFixed(0)}</Text>
        </div>
      </div>

      {/* Trust Badge */}
      <div className="bg-green-50 p-3 rounded mb-4">
        <Space align="start">
          <SafetyOutlined className="text-green-600 mt-1" />
          <div>
            <Text strong className="block text-green-800">Safe & Secure</Text>
            <Text type="secondary" className="text-xs">
              100% Authentic products. Easy returns.
            </Text>
          </div>
        </Space>
      </div>

      {/* Place Order Button */}
      <Button 
        type="primary"
        size="large"
        block
        loading={isPlacingOrder}
        disabled={!hasValidAddress}
        onClick={onPlaceOrder}
        style={{ backgroundColor: '#ff6b35', borderColor: '#ff6b35', height: '48px' }}
        icon={!isPlacingOrder && <CheckCircleOutlined />}
      >
        {isPlacingOrder ? 'Placing Order...' : 'PLACE ORDER'}
      </Button>
      
      {!hasValidAddress && (
        <Text type="danger" className="block text-center mt-2 text-xs">
          Please complete your delivery address to place order
        </Text>
      )}
    </Card>
  );
};

export default OrderSummary;
