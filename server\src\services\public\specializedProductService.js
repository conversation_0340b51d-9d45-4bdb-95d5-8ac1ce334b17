const Product = require('../../models/Product');
const Category = require('../../models/Category');
const Vendor = require('../../models/Vendor');

/**
 * Helper function to add currency filter to existing filter
 */
const addCurrencyFilter = (baseFilter, userCurrency) => {
  if (!userCurrency || userCurrency === 'INR') {
    return baseFilter;
  }
  
  const currencyConditions = [
    // Products with default currency matching user's currency
    { 'pricing.currency': userCurrency },
    // Products with multi-currency pricing for user's currency
    { [`pricing.multiCurrency.${userCurrency}.basePrice`]: { $exists: true, $ne: null } }
  ];
  
  // If baseFilter already has $and, add to it
  if (baseFilter.$and) {
    return {
      ...baseFilter,
      $and: [
        ...baseFilter.$and,
        { $or: currencyConditions }
      ]
    };
  }
  
  // If baseFilter has $or but not $and, wrap both in $and
  if (baseFilter.$or) {
    const { $or, ...restFilter } = baseFilter;
    return {
      ...restFilter,
      $and: [
        { $or },
        { $or: currencyConditions }
      ]
    };
  }
  
  // Default case - simple filter
  return {
    ...baseFilter,
    $and: [
      baseFilter,
      { $or: currencyConditions }
    ]
  };
};

/**
 * Get featured products
 */
const getFeaturedProducts = async (limit = 8, userCurrency = null) => {
  const baseFilter = {
    status: 'active',
    visibility: 'public',
    featured: true
  };
  
  const filter = addCurrencyFilter(baseFilter, userCurrency);
  
  return await Product.find(filter)
    .populate('vendor', 'businessName')
    .populate('category', 'name slug')
    .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating sales.totalSold featured inventory')
    .sort({ 'sales.totalSold': -1, createdAt: -1 })
    .limit(limit)
    .lean();
};

/**
 * Get new arrival products
 */
const getNewArrivals = async (limit = 8, days = 30, userCurrency = null) => {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  const baseFilter = {
    status: 'active',
    visibility: 'public',
    createdAt: { $gte: startDate }
  };
  
  const filter = addCurrencyFilter(baseFilter, userCurrency);

  return await Product.find(filter)
    .populate('vendor', 'businessName')
    .populate('category', 'name slug')
    .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating createdAt featured inventory')
    .sort({ createdAt: -1 })
    .limit(limit)
    .lean();
};

/**
 * Get best selling products
 */
const getBestSellingProducts = async (limit = 8, userCurrency = null) => {
  const baseFilter = {
    status: 'active',
    visibility: 'public',
    'sales.totalSold': { $gt: 0 }
  };
  
  const filter = addCurrencyFilter(baseFilter, userCurrency);
  
  return await Product.find(filter)
    .populate('vendor', 'businessName')
    .populate('category', 'name slug')
    .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating sales.totalSold featured inventory')
    .sort({ 'sales.totalSold': -1 })
    .limit(limit)
    .lean();
};

/**
 * Get budget-friendly products (price < 2000)
 */
const getBudgetFriendlyProducts = async (limit = 8, userCurrency = null) => {
  const baseFilter = {
    status: 'active',
    visibility: 'public',
    $or: [
      { 'pricing.salePrice': { $lt: 2000, $gt: 0 } },
      {
        'pricing.salePrice': { $exists: false },
        'pricing.basePrice': { $lt: 2000, $gt: 0 }
      },
      {
        'pricing.salePrice': 0,
        'pricing.basePrice': { $lt: 2000, $gt: 0 }
      }
    ]
  };

  const filter = addCurrencyFilter(baseFilter, userCurrency);

  return await Product.find(filter)
    .populate('vendor', 'businessName')
    .populate('category', 'name slug')
    .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating createdAt featured inventory')
    .sort({ createdAt: -1 })
    .limit(limit)
    .lean();
};

/**
 * Get products by category with descendant categories
 */
const getProductsByCategory = async (categoryId, queryParams, userCurrency = null) => {
  const page = parseInt(queryParams.page) || 1;
  const limit = parseInt(queryParams.limit) || 12;
  const skip = (page - 1) * limit;
  const sortBy = queryParams.sortBy || 'createdAt';
  const sortOrder = queryParams.sortOrder === 'asc' ? 1 : -1;

  // Verify category exists
  const category = await Category.findOne({ 
    _id: categoryId, 
    status: 'active' 
  });

  if (!category) {
    throw new Error('Category not found');
  }

  // Get all descendant categories
  const descendants = await category.getDescendants();
  const categoryIds = [categoryId, ...descendants.map(d => d._id)];

  // Build sort object
  const sort = {};
  if (sortBy === 'price') {
    sort['pricing.basePrice'] = sortOrder;
  } else if (sortBy === 'rating') {
    sort['reviews.averageRating'] = sortOrder;
  } else if (sortBy === 'popularity') {
    sort['sales.totalSold'] = sortOrder;
  } else {
    sort[sortBy] = sortOrder;
  }

  // Build base filter
  const baseFilter = {
    category: { $in: categoryIds },
    status: 'active',
    visibility: 'public'
  };
  
  const filter = addCurrencyFilter(baseFilter, userCurrency);

  const [products, totalProducts] = await Promise.all([
    Product.find(filter)
      .populate('vendor', 'businessName')
      .populate('category', 'name slug')
      .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating')
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean(),
    Product.countDocuments(filter)
  ]);

  const totalPages = Math.ceil(totalProducts / limit);

  return {
    category: {
      _id: category._id,
      name: category.name,
      slug: category.slug,
      description: category.description
    },
    products,
    pagination: {
      currentPage: page,
      totalPages,
      totalProducts,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
      limit
    }
  };
};

/**
 * Get products by vendor
 */
const getProductsByVendor = async (vendorId, queryParams, userCurrency = null) => {
  const page = parseInt(queryParams.page) || 1;
  const limit = parseInt(queryParams.limit) || 12;
  const skip = (page - 1) * limit;

  // Verify vendor exists and is active
  const vendor = await Vendor.findOne({ 
    _id: vendorId, 
    status: 'active',
    'verification.status': 'verified'
  }).populate('user', 'firstName lastName');

  if (!vendor) {
    throw new Error('Vendor not found');
  }

  // Build base filter
  const baseFilter = {
    vendor: vendorId,
    status: 'active',
    visibility: 'public'
  };
  
  const filter = addCurrencyFilter(baseFilter, userCurrency);

  const [products, totalProducts] = await Promise.all([
    Product.find(filter)
      .populate('category', 'name slug')
      .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean(),
    Product.countDocuments(filter)
  ]);

  const totalPages = Math.ceil(totalProducts / limit);

  return {
    vendor: {
      _id: vendor._id,
      businessName: vendor.businessName,
      businessDescription: vendor.businessDescription,
      logo: vendor.logo,
      performance: vendor.performance
    },
    products,
    pagination: {
      currentPage: page,
      totalPages,
      totalProducts,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
      limit
    }
  };
};

/**
 * Search products
 */
const searchProducts = async (query, queryParams, userCurrency = null) => {
  const page = parseInt(queryParams.page) || 1;
  const limit = parseInt(queryParams.limit) || 12;
  const skip = (page - 1) * limit;

  if (!query || query.trim().length < 2) {
    throw new Error('Search query must be at least 2 characters long');
  }

  // Build search filter
  const baseFilter = {
    status: 'active',
    visibility: 'public',
    $or: [
      { name: { $regex: query, $options: 'i' } },
      { description: { $regex: query, $options: 'i' } },
      { tags: { $in: [new RegExp(query, 'i')] } },
      { brand: { $regex: query, $options: 'i' } }
    ]
  };
  
  const filter = addCurrencyFilter(baseFilter, userCurrency);

  const [products, totalProducts] = await Promise.all([
    Product.find(filter)
      .populate('vendor', 'businessName')
      .populate('category', 'name slug')
      .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating featured inventory')
      .sort({ 'sales.totalSold': -1, 'reviews.averageRating': -1 })
      .skip(skip)
      .limit(limit)
      .lean(),
    Product.countDocuments(filter)
  ]);

  const totalPages = Math.ceil(totalProducts / limit);

  return {
    query,
    products,
    pagination: {
      currentPage: page,
      totalPages,
      totalProducts,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
      limit
    }
  };
};

module.exports = {
  getFeaturedProducts,
  getNewArrivals,
  getBestSellingProducts,
  getBudgetFriendlyProducts,
  getProductsByCategory,
  getProductsByVendor,
  searchProducts
};
