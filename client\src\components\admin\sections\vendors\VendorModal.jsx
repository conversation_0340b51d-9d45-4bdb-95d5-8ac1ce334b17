import React from 'react';
import { Modal, Form, Input, Select, Row, Col } from 'antd';

const { Option } = Select;
const { TextArea } = Input;

const VendorModal = ({
  visible,
  onOk,
  onCancel,
  editingVendor,
  loading
}) => {
  const [form] = Form.useForm();

  // Set form values when editing vendor changes
  React.useEffect(() => {
    if (editingVendor) {
      form.setFieldsValue(editingVendor);
    } else {
      form.resetFields();
    }
  }, [editingVendor, form]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      await onOk(values);
      form.resetFields();
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };
  return (
    <Modal
      title={editingVendor ? 'Edit Vendor' : 'Add New Vendor'}
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={800}
      confirmLoading={loading}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          status: 'pending',
          commission: 15
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="businessName"
              label="Business Name"
              rules={[{ required: true, message: 'Please enter business name' }]}
            >
              <Input placeholder="Enter business name" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="contactPerson"
              label="Contact Person"
              rules={[{ required: true, message: 'Please enter contact person' }]}
            >
              <Input placeholder="Enter contact person name" />
            </Form.Item>
          </Col>
        </Row>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="email"
              label="Email"
              rules={[
                { required: true, message: 'Please enter email' },
                { type: 'email', message: 'Please enter valid email' }
              ]}
            >
              <Input placeholder="Enter email address" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="phone"
              label="Phone"
              rules={[{ required: true, message: 'Please enter phone number' }]}
            >
              <Input placeholder="Enter phone number" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="status"
              label="Status"
              rules={[{ required: true, message: 'Please select status' }]}
            >
              <Select placeholder="Select status">
                <Option value="pending">Pending</Option>
                <Option value="approved">Approved</Option>
                <Option value="suspended">Suspended</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="commission"
              label="Commission (%)"
              rules={[{ required: true, message: 'Please enter commission rate' }]}
            >
              <Input type="number" placeholder="Enter commission percentage" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="description"
          label="Description"
        >
          <TextArea rows={3} placeholder="Enter business description" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default VendorModal;
