import React from 'react';
import { Table, Card, List, Space, Tag, Button } from 'antd';
import useResponsive from '../../hooks/useResponsive';

const ResponsiveTable = ({ 
  columns, 
  dataSource, 
  renderMobileItem, 
  loading = false,
  pagination = true,
  ...tableProps 
}) => {
  const { isMobile } = useResponsive();

  // Default mobile item renderer if not provided
  const defaultMobileRenderer = (item, index) => (
    <List.Item key={item.id || index}>
      <Card size="small" style={{ width: '100%' }}>
        {columns.map((col, colIndex) => {
          if (col.dataIndex && col.title) {
            const value = item[col.dataIndex];
            const renderedValue = col.render ? col.render(value, item, index) : value;
            
            return (
              <div key={colIndex} style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                marginBottom: '8px',
                fontSize: '12px'
              }}>
                <span style={{ fontWeight: 500, color: '#666' }}>{col.title}:</span>
                <span>{renderedValue}</span>
              </div>
            );
          }
          return null;
        })}
        {/* Render actions if present */}
        {columns.find(col => col.key === 'actions') && (
          <div style={{ marginTop: '12px', textAlign: 'right' }}>
            {columns.find(col => col.key === 'actions').render(null, item, index)}
          </div>
        )}
      </Card>
    </List.Item>
  );

  if (isMobile) {
    return (
      <List
        loading={loading}
        dataSource={dataSource}
        renderItem={renderMobileItem || defaultMobileRenderer}
        pagination={pagination ? {
          pageSize: 5,
          size: 'small',
          showSizeChanger: false,
          showQuickJumper: false,
        } : false}
        {...tableProps}
      />
    );
  }

  return (
    <Table
      columns={columns}
      dataSource={dataSource}
      loading={loading}
      pagination={pagination ? {
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) =>
          `${range[0]}-${range[1]} of ${total} items`,
      } : false}
      scroll={{ x: 'max-content' }}
      {...tableProps}
    />
  );
};

export default ResponsiveTable;