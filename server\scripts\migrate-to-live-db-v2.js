const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Database URIs - Note: Added database name to the Atlas URI
const LOCAL_DB_URI = 'mongodb://localhost:27017/multi-vendor-ecommerce';
const PRODUCTION_DB_URI = 'mongodb+srv://itsprogresspulse:<EMAIL>/multi-vendor-ecommerce?retryWrites=true&w=majority&appName=Cluster0&ssl=true';

console.log('🔧 Database Migration Setup:');
console.log('📍 Local:', LOCAL_DB_URI);
console.log('📍 Production:', PRODUCTION_DB_URI.replace(/\/\/.*@/, '//***:***@'));

class DatabaseMigrator {
    constructor() {
        this.localConnection = null;
        this.productionConnection = null;
        this.backupDir = path.join(__dirname, '../backups');
    }

    async createBackupDirectory() {
        if (!fs.existsSync(this.backupDir)) {
            fs.mkdirSync(this.backupDir, { recursive: true });
            console.log(`✅ Created backup directory: ${this.backupDir}`);
        }
    }

    async connectToLocalDB() {
        try {
            this.localConnection = await mongoose.createConnection(LOCAL_DB_URI, {
                serverSelectionTimeoutMS: 10000,
                socketTimeoutMS: 45000,
            });
            
            // Wait for connection to be ready
            await new Promise((resolve, reject) => {
                this.localConnection.once('open', resolve);
                this.localConnection.once('error', reject);
                setTimeout(() => reject(new Error('Connection timeout')), 15000);
            });
            
            console.log('✅ Connected to local MongoDB');
            return this.localConnection;
        } catch (error) {
            console.error('❌ Failed to connect to local MongoDB:', error.message);
            throw error;
        }
    }

    async connectToProductionDB() {
        try {
            console.log('🔗 Connecting to production Atlas...');
            
            // Try different connection approaches
            const connectionOptions = [
                {
                    name: 'Standard SSL',
                    options: {
                        serverSelectionTimeoutMS: 30000,
                        socketTimeoutMS: 45000,
                        connectTimeoutMS: 30000,
                        maxPoolSize: 10,
                        ssl: true,
                        sslValidate: true,
                        retryWrites: true,
                        w: 'majority'
                    }
                },
                {
                    name: 'Relaxed SSL',
                    options: {
                        serverSelectionTimeoutMS: 30000,
                        socketTimeoutMS: 45000,
                        connectTimeoutMS: 30000,
                        maxPoolSize: 10,
                        ssl: true,
                        sslValidate: false,
                        retryWrites: true,
                        w: 'majority'
                    }
                },
                {
                    name: 'Basic Connection',
                    options: {
                        serverSelectionTimeoutMS: 30000,
                        socketTimeoutMS: 45000,
                        connectTimeoutMS: 30000,
                        retryWrites: true,
                        w: 'majority'
                    }
                }
            ];

            for (const { name, options } of connectionOptions) {
                try {
                    console.log(`🔄 Trying ${name} connection...`);
                    
                    this.productionConnection = await mongoose.createConnection(PRODUCTION_DB_URI, options);
                    
                    // Wait for connection to be ready
                    await new Promise((resolve, reject) => {
                        this.productionConnection.once('open', resolve);
                        this.productionConnection.once('error', reject);
                        setTimeout(() => reject(new Error('Connection timeout')), 30000);
                    });
                    
                    console.log(`✅ Connected to production MongoDB Atlas using ${name}`);
                    return this.productionConnection;
                } catch (error) {
                    console.log(`❌ ${name} failed: ${error.message}`);
                    if (this.productionConnection) {
                        try {
                            await this.productionConnection.close();
                        } catch (closeError) {
                            // Ignore close errors
                        }
                        this.productionConnection = null;
                    }
                }
            }
            
            throw new Error('All connection methods failed');
            
        } catch (error) {
            console.error('❌ Failed to connect to production MongoDB:', error.message);
            console.error('💡 Troubleshooting tips:');
            console.error('   - Check if your IP address is whitelisted in MongoDB Atlas');
            console.error('   - Verify username and password are correct');
            console.error('   - Ensure network connectivity to Atlas');
            console.error('   - Check if the cluster is running and accessible');
            throw error;
        }
    }

    async getCollectionNames() {
        try {
            // Ensure connection is ready
            if (!this.localConnection || !this.localConnection.db) {
                throw new Error('Local connection not ready');
            }
            
            const collections = await this.localConnection.db.listCollections().toArray();
            const actualCollections = collections.map(col => col.name);
            console.log('📋 Collections found in local database:', actualCollections.join(', '));
            return actualCollections;
        } catch (error) {
            console.error('❌ Failed to get collection names:', error.message);
            throw error;
        }
    }

    async exportCollection(collectionName) {
        try {
            const collection = this.localConnection.db.collection(collectionName);
            const documents = await collection.find({}).toArray();
            
            if (documents.length === 0) {
                console.log(`⚠️  Collection '${collectionName}' is empty, skipping...`);
                return null;
            }

            const backupFile = path.join(this.backupDir, `${collectionName}.json`);
            fs.writeFileSync(backupFile, JSON.stringify(documents, null, 2));
            
            console.log(`✅ Exported ${documents.length} documents from '${collectionName}'`);
            return documents;
        } catch (error) {
            console.error(`❌ Failed to export collection '${collectionName}':`, error.message);
            return null;
        }
    }

    async importCollection(collectionName, documents) {
        try {
            if (!documents || documents.length === 0) {
                console.log(`⚠️  No documents to import for collection '${collectionName}'`);
                return;
            }

            const collection = this.productionConnection.db.collection(collectionName);
            
            // Check if collection exists and has data
            const existingCount = await collection.countDocuments();
            if (existingCount > 0) {
                console.log(`⚠️  Collection '${collectionName}' already has ${existingCount} documents in production`);
                console.log(`🗑️  Clearing existing documents from '${collectionName}'...`);
                await collection.deleteMany({});
            }

            // Insert documents in batches to avoid memory issues
            const batchSize = 1000;
            let imported = 0;

            for (let i = 0; i < documents.length; i += batchSize) {
                const batch = documents.slice(i, i + batchSize);
                await collection.insertMany(batch, { ordered: false });
                imported += batch.length;
                console.log(`📥 Imported ${imported}/${documents.length} documents to '${collectionName}'`);
            }

            console.log(`✅ Successfully imported ${imported} documents to '${collectionName}'`);
        } catch (error) {
            console.error(`❌ Failed to import collection '${collectionName}':`, error.message);
            throw error;
        }
    }

    async promptUser(question) {
        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        return new Promise((resolve) => {
            rl.question(question, (answer) => {
                rl.close();
                resolve(answer);
            });
        });
    }

    async migrateData() {
        try {
            console.log('🚀 Starting database migration...\n');

            // Create backup directory
            await this.createBackupDirectory();

            // Connect to databases
            await this.connectToLocalDB();
            await this.connectToProductionDB();

            // Get all collections from local database
            const collections = await this.getCollectionNames();
            console.log(`📋 Found ${collections.length} collections to migrate`);

            // Ask for confirmation
            console.log(`\n⚠️  This will migrate data from local MongoDB to production Atlas database.`);
            console.log(`📍 Local: ${LOCAL_DB_URI}`);
            console.log(`📍 Production: ${PRODUCTION_DB_URI.replace(/\/\/.*@/, '//***:***@')}`);
            console.log(`\n📊 Collections to migrate: ${collections.join(', ')}`);
            
            const confirm = await this.promptUser('\nDo you want to continue? (y/n): ');
            if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
                console.log('❌ Migration cancelled by user');
                return;
            }

            // Export and import each collection
            for (const collectionName of collections) {
                console.log(`\n📦 Processing collection: ${collectionName}`);
                
                // Export from local
                const documents = await this.exportCollection(collectionName);
                
                // Import to production
                if (documents) {
                    await this.importCollection(collectionName, documents);
                }
            }

            console.log('\n🎉 Migration completed successfully!');
            console.log(`📁 Backup files saved in: ${this.backupDir}`);

        } catch (error) {
            console.error('\n❌ Migration failed:', error.message);
            throw error;
        } finally {
            // Close connections
            if (this.localConnection) {
                await this.localConnection.close();
                console.log('🔌 Closed local database connection');
            }
            if (this.productionConnection) {
                await this.productionConnection.close();
                console.log('🔌 Closed production database connection');
            }
        }
    }

    async verifyMigration() {
        try {
            console.log('\n🔍 Verifying migration...');
            
            await this.connectToLocalDB();
            await this.connectToProductionDB();

            const collections = await this.getCollectionNames();
            
            console.log('\n📊 Migration Verification Results:');
            for (const collectionName of collections) {
                const localCount = await this.localConnection.db.collection(collectionName).countDocuments();
                const prodCount = await this.productionConnection.db.collection(collectionName).countDocuments();
                
                if (localCount === prodCount) {
                    console.log(`✅ ${collectionName}: ${localCount} documents (matched)`);
                } else {
                    console.log(`⚠️  ${collectionName}: Local=${localCount}, Production=${prodCount} (mismatch)`);
                }
            }

        } catch (error) {
            console.error('❌ Verification failed:', error.message);
        } finally {
            if (this.localConnection) await this.localConnection.close();
            if (this.productionConnection) await this.productionConnection.close();
        }
    }

    async testConnections() {
        try {
            console.log('🔍 Testing database connections...\n');
            
            // Test local connection
            console.log('Testing local MongoDB...');
            await this.connectToLocalDB();
            const localCollections = await this.getCollectionNames();
            console.log(`✅ Local: ${localCollections.length} collections found`);
            
            // Count total documents in local
            let totalLocalDocs = 0;
            for (const collection of localCollections) {
                const count = await this.localConnection.db.collection(collection).countDocuments();
                totalLocalDocs += count;
                console.log(`   - ${collection}: ${count} documents`);
            }
            console.log(`📊 Total local documents: ${totalLocalDocs}`);
            
            await this.localConnection.close();
            
            // Test production connection
            console.log('\nTesting production Atlas...');
            await this.connectToProductionDB();
            const prodCollections = await this.productionConnection.db.listCollections().toArray();
            console.log(`✅ Production: ${prodCollections.length} collections found`);
            
            if (prodCollections.length > 0) {
                let totalProdDocs = 0;
                for (const collection of prodCollections) {
                    const count = await this.productionConnection.db.collection(collection.name).countDocuments();
                    totalProdDocs += count;
                    console.log(`   - ${collection.name}: ${count} documents`);
                }
                console.log(`📊 Total production documents: ${totalProdDocs}`);
            } else {
                console.log('📊 Production database is empty');
            }
            
            await this.productionConnection.close();
            
            console.log('\n🎉 Both connections successful!');
            console.log('✅ Ready for migration!');
            
        } catch (error) {
            console.error('❌ Connection test failed:', error.message);
        }
    }
}

// CLI interface
async function main() {
    const migrator = new DatabaseMigrator();
    
    const args = process.argv.slice(2);
    const command = args[0];

    try {
        switch (command) {
            case 'migrate':
                await migrator.migrateData();
                break;
            case 'verify':
                await migrator.verifyMigration();
                break;
            case 'test':
                await migrator.testConnections();
                break;
            default:
                console.log(`
🚀 Database Migration Tool for Live Database

Usage:
  node migrate-to-live-db-v2.js migrate  - Migrate all data from local to production
  node migrate-to-live-db-v2.js verify   - Verify migration by comparing document counts
  node migrate-to-live-db-v2.js test     - Test both database connections

Examples:
  node scripts/migrate-to-live-db-v2.js test
  node scripts/migrate-to-live-db-v2.js migrate
  node scripts/migrate-to-live-db-v2.js verify
                `);
        }
    } catch (error) {
        console.error('❌ Operation failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = DatabaseMigrator;