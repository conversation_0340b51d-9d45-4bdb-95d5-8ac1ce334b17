import axios from 'axios';

const BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const reviewAPI = axios.create({
  baseURL: BASE_URL.endsWith('/api') ? `${BASE_URL}/reviews` : `${BASE_URL}/reviews`,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
reviewAPI.interceptors.request.use(
  (config) => {
    // Try multiple token keys to ensure compatibility
    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Handle responses and errors
reviewAPI.interceptors.response.use(
  (response) => response.data,
  (error) => {
    console.error('Review API Error:', {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message,
      url: error.config?.url,
      method: error.config?.method
    });
    
    let message = 'Something went wrong';
    
    if (error.response?.data?.message) {
      message = error.response.data.message;
    } else if (error.response?.status === 401) {
      message = 'Please log in to continue';
    } else if (error.response?.status === 403) {
      message = 'You don\'t have permission to perform this action';
    } else if (error.response?.status === 404) {
      message = 'Resource not found';
    } else if (error.message) {
      message = error.message;
    }
    
    return Promise.reject(new Error(message));
  }
);

export const reviewService = {
  // Customer methods
  async createReview(reviewData) {
    const response = await reviewAPI.post('/', reviewData);
    return response.data;
  },

  async getProductReviews(productId, params = {}) {
    const response = await reviewAPI.get(`/product/${productId}`, { params });
    return response.data;
  },

  async getMyReviews(params = {}) {
    const response = await reviewAPI.get('/my-reviews', { params });
    return response.data;
  },

  async canReviewProduct(productId) {
    const response = await reviewAPI.get(`/can-review/${productId}`);
    return response.data;
  },

  // Vendor methods
  async getVendorReviews(params = {}) {
    const response = await reviewAPI.get('/vendor/my-reviews', { params });
    return response.data;
  },

  async replyToReview(reviewId, message) {
    const response = await reviewAPI.post(`/${reviewId}/reply`, { message });
    return response.data;
  },

  async updateReply(replyId, message) {
    const response = await reviewAPI.put(`/reply/${replyId}`, { message });
    return response.data;
  },

  async deleteReply(replyId) {
    const response = await reviewAPI.delete(`/reply/${replyId}`);
    return response.data;
  }
};

export default reviewService;
