import React, { useState, useRef, useEffect } from 'react';
import { SearchOutlined } from "@ant-design/icons";
import { useNavigate } from 'react-router-dom';
import { useSearch } from '../contexts/SearchContext';

const SearchWithTabs = () => {
    const [activeTab, setActiveTab] = useState('products');
    const [searchInput, setSearchInput] = useState('');
    const searchInputRef = useRef(null);
    const navigate = useNavigate();
    const { 
        suggestions, 
        showSuggestions, 
        setShowSuggestions, 
        getSuggestions,
        setSearchTerm
    } = useSearch();

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (searchInputRef.current && !searchInputRef.current.contains(event.target)) {
                setShowSuggestions(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleSearchInput = (value) => {
        setSearchInput(value);
        if (value.trim().length >= 2) {
            getSuggestions(value);
            setShowSuggestions(true);
        } else {
            setShowSuggestions(false);
        }
    };

    const handleSearch = () => {
        if (searchInput.trim()) {
            const searchQuery = searchInput.trim();
            setSearchTerm(searchQuery);
            const searchPath = activeTab === 'products' 
                ? `/search?q=${encodeURIComponent(searchQuery)}`
                : `/suppliers?q=${encodeURIComponent(searchQuery)}`;
            navigate(searchPath);
            setShowSuggestions(false);
        }
    };

    const handleSuggestionClick = (suggestion) => {
        const searchValue = suggestion.title || suggestion.name || suggestion;
        setSearchInput(searchValue);
        setSearchTerm(searchValue);
        const searchPath = activeTab === 'products'
            ? `/search?q=${encodeURIComponent(searchValue)}`
            : `/suppliers?q=${encodeURIComponent(searchValue)}`;
        navigate(searchPath);
        setShowSuggestions(false);
    };

    return (
        <div className="w-full max-w-4xl mx-auto my-8">
            <div className="bg-[#f48c06] rounded-lg p-6 shadow-lg">
                <h2 className="text-2xl font-bold text-white mb-4">What are you looking for?</h2>
                
                {/* Tabs */}
                <div className="flex mb-4 border-b border-orange-300">
                    <button
                        className={`px-6 py-2 font-medium text-sm ${activeTab === 'products' 
                            ? 'text-white border-b-2 border-white' 
                            : 'text-orange-100 hover:text-white'}`}
                        onClick={() => setActiveTab('products')}
                    >
                        Products
                    </button>
                    <button
                        className={`px-6 py-2 font-medium text-sm ${activeTab === 'suppliers' 
                            ? 'text-white border-b-2 border-white' 
                            : 'text-orange-100 hover:text-white'}`}
                        onClick={() => setActiveTab('suppliers')}
                    >
                        Regional Suppliers
                    </button>
                </div>

                {/* Search Bar */}
                <div className="relative" ref={searchInputRef}>
                    <div className="flex rounded-md shadow-sm">
                        <input
                            type="text"
                            placeholder={`Search ${activeTab === 'products' ? 'products' : 'regional suppliers'}...`}
                            className="flex-1 min-w-0 block w-full px-4 py-3 text-base rounded-l-md border-0 focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
                            value={searchInput}
                            onChange={(e) => handleSearchInput(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                        />
                        <button
                            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-r-md text-white bg-orange-700 hover:bg-orange-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                            onClick={handleSearch}
                        >
                            <SearchOutlined className="mr-2" />
                            Search
                        </button>
                    </div>

                    {/* Suggestions Dropdown */}
                    {showSuggestions && suggestions.length > 0 && (
                        <div className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
                            {suggestions.slice(0, 6).map((suggestion, index) => (
                                <button
                                    key={index}
                                    onClick={() => handleSuggestionClick(suggestion)}
                                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                                >
                                    <SearchOutlined className="mr-3 text-gray-400" />
                                    <span>{suggestion.title || suggestion.name || suggestion}</span>
                                </button>
                            ))}
                        </div>
                    )}
                </div>

                {/* Popular Searches */}
                <div className="mt-4">
                    <p className="text-sm text-orange-100">
                        Popular: 
                        <button 
                            className="ml-2 text-white hover:underline"
                            onClick={() => setSearchInput('Electronics')}
                        >
                            Electronics
                        </button>
                        <span className="mx-2">|</span>
                        <button 
                            className="text-white hover:underline"
                            onClick={() => setSearchInput('Home & Living')}
                        >
                            Home & Living
                        </button>
                    </p>
                </div>
            </div>
        </div>
    );
};

export default SearchWithTabs;
