const mongoose = require('mongoose');

const cartItemSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  quantity: {
    type: Number,
    required: true,
    min: 1,
    default: 1
  },
  selectedVariant: {
    sku: String,
    attributes: [{
      name: String,
      value: String
    }],
    price: Number
  },
  priceAtAdd: {
    type: Number,
    required: true
  },
  vendor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vendor',
    required: true
  },
  addedAt: {
    type: Date,
    default: Date.now
  }
}, { _id: false });

const cartSchema = new mongoose.Schema({
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  items: {
    type: [cartItemSchema],
    validate: {
      validator: function(items) {
        return items.length <= 50; // Maximum 50 different items in cart
      },
      message: 'Cart cannot contain more than 50 different items'
    }
  },
  totalItems: {
    type: Number,
    default: 0,
    validate: {
      validator: function(totalItems) {
        return totalItems <= 100; // Maximum 100 total items
      },
      message: 'Cart cannot contain more than 100 total items'
    }
  },
  totalAmount: {
    type: Number,
    default: 0
  },
  lastUpdated: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Indexes (customer index is automatically created by unique: true)
cartSchema.index({ 'items.product': 1 });

// Update totals before saving
cartSchema.pre('save', function(next) {
  this.totalItems = this.items.reduce((total, item) => total + item.quantity, 0);
  this.totalAmount = this.items.reduce((total, item) => {
    const price = item.selectedVariant?.price || item.priceAtAdd;
    return total + (price * item.quantity);
  }, 0);
  this.lastUpdated = new Date();
  next();
});

// Virtual for vendors in cart
cartSchema.virtual('vendorCount').get(function() {
  const vendors = new Set();
  this.items.forEach(item => vendors.add(item.vendor.toString()));
  return vendors.size;
});

// Helper function for consistent variant matching
function isVariantMatch(itemVariant, compareVariant) {
  const itemSku = itemVariant?.sku;
  const compareSku = compareVariant?.sku || compareVariant; // Handle both object and string
  
  if (!itemSku && !compareSku) {
    // Both are null/undefined - match
    return true;
  } else if (itemSku && compareSku) {
    // Both exist - compare them
    return itemSku === compareSku;
  } else {
    // One is null, one is not - no match
    return false;
  }
}

// Instance methods
cartSchema.methods.addItem = function(productId, vendorId, quantity, price, variant = null) {
  const existingItemIndex = this.items.findIndex(item => {
    const productMatch = item.product.toString() === productId.toString();
    const variantMatch = isVariantMatch(item.selectedVariant, variant);
    return productMatch && variantMatch;
  });

  if (existingItemIndex > -1) {
    this.items[existingItemIndex].quantity += quantity;
  } else {
    this.items.push({
      product: productId,
      vendor: vendorId,
      quantity,
      priceAtAdd: price,
      selectedVariant: variant
    });
  }
  
  return this.save();
};

cartSchema.methods.updateItemQuantity = function(productId, quantity, variantSku = null) {
  const itemIndex = this.items.findIndex(item => {
    const productMatch = item.product.toString() === productId.toString();
    const variantMatch = isVariantMatch(item.selectedVariant, variantSku);
    return productMatch && variantMatch;
  });

  if (itemIndex > -1) {
    if (quantity <= 0) {
      this.items.splice(itemIndex, 1);
    } else {
      this.items[itemIndex].quantity = quantity;
    }
    return this.save();
  }
  throw new Error('Item not found in cart');
};

cartSchema.methods.removeItem = function(productId, variantSku = null) {
  this.items = this.items.filter(item => {
    const productMatch = item.product.toString() === productId.toString();
    const variantMatch = isVariantMatch(item.selectedVariant, variantSku);
    // Return false to remove items that match both product and variant
    return !(productMatch && variantMatch);
  });
  return this.save();
};

cartSchema.methods.clearCart = function() {
  this.items = [];
  return this.save();
};

// Bulk operations
cartSchema.methods.bulkAddItems = function(itemsToAdd) {
  for (const itemData of itemsToAdd) {
    const { productId, vendorId, quantity, price, variant = null } = itemData;
    const existingItemIndex = this.items.findIndex(item => {
      const productMatch = item.product.toString() === productId.toString();
      const variantMatch = isVariantMatch(item.selectedVariant, variant);
      return productMatch && variantMatch;
    });

    if (existingItemIndex > -1) {
      this.items[existingItemIndex].quantity += quantity;
    } else {
      this.items.push({
        product: productId,
        vendor: vendorId,
        quantity,
        priceAtAdd: price,
        selectedVariant: variant
      });
    }
  }
  
  return this.save();
};

cartSchema.methods.bulkUpdateItems = function(itemsToUpdate) {
  for (const updateData of itemsToUpdate) {
    const { productId, quantity, variantSku = null } = updateData;
    const itemIndex = this.items.findIndex(item => {
      const productMatch = item.product.toString() === productId.toString();
      const variantMatch = isVariantMatch(item.selectedVariant, variantSku);
      return productMatch && variantMatch;
    });

    if (itemIndex > -1) {
      if (quantity <= 0) {
        this.items.splice(itemIndex, 1);
      } else {
        this.items[itemIndex].quantity = quantity;
      }
    }
  }
  
  return this.save();
};

// Static methods
cartSchema.statics.findByCustomer = function(customerId) {
  return this.findOne({ customer: customerId })
    .populate('items.product', 'name pricing images inventory.quantity status sku')
    .populate('items.vendor', 'businessName email _id');
};

// Static method to get or create cart for a user
cartSchema.statics.getOrCreateForCustomer = async function(customerId) {
  let cart = await this.findByCustomer(customerId);
  
  if (!cart) {
    cart = new this({ customer: customerId });
    await cart.save();
    // Re-fetch with populated fields
    cart = await this.findByCustomer(customerId);
  }
  
  return cart;
};

// Static method to find cart with currency transformation
cartSchema.statics.findByCustomerWithCurrency = function(customerId, userCurrency = 'INR') {
  return this.findOne({ customer: customerId })
    .populate('items.product', 'name pricing images inventory.quantity status')
    .populate('items.vendor', 'businessName email')
    .then(cart => {
      if (!cart) return cart;
      
      // Transform pricing for each item
      const { transformCartItemPricing } = require('../utils/helpers/pricingHelper');
      const { formatCurrency } = require('../utils/currency');
      
      const transformedCart = cart.toObject();
      transformedCart.items = transformedCart.items.map(item => 
        transformCartItemPricing(item, userCurrency)
      );
      
      // Calculate display totals
      const totalAmount = transformedCart.items.reduce((sum, item) => 
        sum + (item.displayPrice ? item.displayPrice.totalPrice : item.priceAtAdd * item.quantity), 0
      );
      
      transformedCart.displayTotals = {
        totalAmount,
        formattedTotalAmount: formatCurrency(totalAmount, userCurrency),
        currency: userCurrency,
        totalItems: transformedCart.totalItems
      };
      
      return transformedCart;
    });
};

module.exports = mongoose.model('Cart', cartSchema);
