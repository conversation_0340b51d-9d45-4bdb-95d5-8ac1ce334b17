const User = require('../../models/User') || require('../../../schema/userSchema');
const emailService = require('../../utils/emailService');
const crypto = require('crypto');

/**
 * @desc    Request email change (sends verification to new email)
 * @route   POST /api/admin/profile/change-email
 * @access  Private (Admin)
 */
const changeEmail = async (req, res) => {
  try {
    const { newEmail, currentPassword } = req.body;

    // Get admin with password field
    const admin = await User.findById(req.user.id).select('+password');
    
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: 'Admin account not found'
      });
    }

    // Verify current password
    const isPasswordValid = await admin.comparePassword(currentPassword);
    if (!isPasswordValid) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }

    // Check if new email is the same as current
    if (newEmail.toLowerCase() === admin.email.toLowerCase()) {
      return res.status(400).json({
        success: false,
        message: 'New email cannot be the same as current email'
      });
    }

    // Check if new email is already in use
    const existingUser = await User.findOne({ 
      email: newEmail.toLowerCase(),
      _id: { $ne: admin._id } // Exclude current user
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'This email address is already registered'
      });
    }

    // Generate email change token
    const emailChangeToken = crypto.randomBytes(32).toString('hex');
    const hashedToken = crypto.createHash('sha256').update(emailChangeToken).digest('hex');

    // Store email change request data
    admin.emailChange = {
      newEmail: newEmail.toLowerCase(),
      token: hashedToken,
      tokenExpires: Date.now() + 30 * 60 * 1000, // 30 minutes
      requestedAt: new Date()
    };

    await admin.save();

    // Send verification email to new email address
    const emailResult = await sendEmailChangeVerification(admin, newEmail, emailChangeToken);

    if (!emailResult.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to send verification email. Please try again.'
      });
    }

    res.json({
      success: true,
      message: `Verification email sent to ${newEmail}. Please check your email and click the verification link to complete the email change.`,
      data: {
        newEmail: newEmail,
        expiresIn: '30 minutes'
      }
    });
  } catch (error) {
    console.error('Change email error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while processing email change request'
    });
  }
};

/**
 * @desc    Verify and complete email change
 * @route   POST /api/admin/profile/verify-email-change
 * @access  Public (token-based)
 */
const verifyEmailChange = async (req, res) => {
  try {
    const { token, email } = req.body;

    // Hash the provided token
    const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

    // Find admin with matching email change token
    const admin = await User.findOne({
      'emailChange.token': hashedToken,
      'emailChange.newEmail': email.toLowerCase(),
      'emailChange.tokenExpires': { $gt: Date.now() }
    });

    if (!admin) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired verification token'
      });
    }

    // Double-check that the new email is still available
    const existingUser = await User.findOne({ 
      email: admin.emailChange.newEmail,
      _id: { $ne: admin._id }
    });

    if (existingUser) {
      // Clear the email change request
      admin.emailChange = undefined;
      await admin.save();

      return res.status(400).json({
        success: false,
        message: 'This email address is no longer available'
      });
    }

    // Store old email for notification
    const oldEmail = admin.email;
    const newEmailAddress = admin.emailChange.newEmail;

    // Update the email
    admin.email = admin.emailChange.newEmail;
    admin.emailVerification.isVerified = true; // Keep verified status
    admin.emailVerification.verifiedAt = new Date();
    
    // Clear email change request
    admin.emailChange = undefined;

    await admin.save();

    // Send confirmation email to old email address
    await sendEmailChangeConfirmation(oldEmail, newEmailAddress, admin);

    res.json({
      success: true,
      message: 'Email address successfully changed',
      data: {
        newEmail: admin.email,
        changedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Verify email change error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while verifying email change'
    });
  }
};

/**
 * Send email change verification email
 */
async function sendEmailChangeVerification(admin, newEmail, token) {
  const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin/verify-email-change?token=${token}&email=${encodeURIComponent(newEmail)}`;
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Verify Email Change</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .button { display: inline-block; background: #3b82f6; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
            .warning { background: #fef3cd; border: 1px solid #fecaca; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔐 Admin Email Change Verification</h1>
            </div>
            <div class="content">
                <h2>Hello ${admin.firstName || 'Admin'}!</h2>
                <p>We received a request to change your admin email address from <strong>${admin.email}</strong> to <strong>${newEmail}</strong>.</p>
                
                <div class="warning">
                    <strong>🛡️ Security Notice:</strong> If you didn't request this email change, please ignore this email and consider changing your password as a precaution.
                </div>
                
                <p>To complete the email change, click the button below:</p>
                
                <div style="text-align: center;">
                    <a href="${verificationUrl}" class="button">Verify New Email Address</a>
                </div>
                
                <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; color: #666;">${verificationUrl}</p>
                
                <p><strong>This verification link will expire in 30 minutes for security reasons.</strong></p>
                
                <p>For your security:</p>
                <ul>
                    <li>This link can only be used once</li>
                    <li>The change will take effect immediately after verification</li>
                    <li>You'll receive a confirmation at your old email address</li>
                </ul>
                
                <p>Best regards,<br>The ${process.env.APP_NAME || 'Alicartify'} Team</p>
            </div>
            <div class="footer">
                <p>© ${new Date().getFullYear()} ${process.env.APP_NAME || 'Alicartify'}. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
  `;

  return await emailService.sendEmail(
    newEmail,
    'Verify Your New Email Address - Admin Account',
    html
  );
}

/**
 * Send email change confirmation to old email
 */
async function sendEmailChangeConfirmation(oldEmail, newEmail, admin) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Address Changed</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
            .info-box { background: #dbeafe; border: 1px solid #3b82f6; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>✅ Email Address Successfully Changed</h1>
            </div>
            <div class="content">
                <h2>Hello ${admin.firstName || 'Admin'}!</h2>
                <p>Your admin email address has been successfully changed.</p>
                
                <div class="info-box">
                    <strong>📧 Email Change Details:</strong><br>
                    <strong>Old Email:</strong> ${oldEmail}<br>
                    <strong>New Email:</strong> ${newEmail}<br>
                    <strong>Changed At:</strong> ${new Date().toLocaleString()}
                </div>
                
                <p><strong>What this means:</strong></p>
                <ul>
                    <li>Your login email is now ${newEmail}</li>
                    <li>All future notifications will be sent to your new email</li>
                    <li>This old email address is no longer associated with your admin account</li>
                </ul>
                
                <p><strong>🔐 Security Notice:</strong> If you didn't make this change, please contact the system administrator immediately.</p>
                
                <p>Best regards,<br>The ${process.env.APP_NAME || 'Alicartify'} Team</p>
            </div>
            <div class="footer">
                <p>© ${new Date().getFullYear()} ${process.env.APP_NAME || 'Alicartify'}. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
  `;

  return await emailService.sendEmail(
    oldEmail,
    'Your Admin Email Address Has Been Changed',
    html
  );
}

module.exports = {
  changeEmail,
  verifyEmailChange
};
