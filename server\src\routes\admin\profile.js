const express = require('express');
const router = express.Router();
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');
const {
  getProfile,
  updateProfile,
  changePassword,
  updateProfilePicture,
  deleteProfilePicture
} = require('../../controllers/admin/profileController');

const {
  changeEmail,
  verifyEmailChange
} = require('../../controllers/admin/emailController');
const { body } = require('express-validator');
const { validateRequest } = require('../../middleware/validation');
const imageUpload = require('../../middleware/upload/imageUpload');

// Apply authentication middleware to all routes
router.use(verifyToken);
router.use(requireUserType(['admin']));

/**
 * @route   GET /api/admin/profile
 * @desc    Get admin profile
 * @access  Private (Admin)
 */
router.get('/', getProfile);

/**
 * @route   PUT /api/admin/profile
 * @desc    Update admin profile (basic info only)
 * @access  Private (Admin)
 */
router.put('/', [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters'),
  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters'),
  body('phone')
    .optional()
    .trim()
    .matches(/^\+?[\d\s-()]+$/)
    .withMessage('Please provide a valid phone number'),
  body('countryCode')
    .optional()
    .trim()
    .isLength({ min: 2, max: 3 })
    .withMessage('Country code must be 2-3 characters'),
  body('address')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Address cannot exceed 200 characters'),
  body('city')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('City cannot exceed 100 characters'),
  body('state')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('State cannot exceed 100 characters'),
  body('zipCode')
    .optional()
    .trim()
    .isLength({ max: 20 })
    .withMessage('Zip code cannot exceed 20 characters'),
  body('country')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Country cannot exceed 100 characters')
], validateRequest, updateProfile);

/**
 * @route   POST /api/admin/profile/change-email
 * @desc    Request email change (sends verification to new email)
 * @access  Private (Admin)
 */
router.post('/change-email', [
  body('newEmail')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required for security verification')
], validateRequest, changeEmail);

/**
 * @route   POST /api/admin/profile/verify-email-change
 * @desc    Verify and complete email change
 * @access  Public (token-based verification)
 */
router.post('/verify-email-change', [
  body('token')
    .notEmpty()
    .withMessage('Verification token is required'),
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail()
], validateRequest, verifyEmailChange);

/**
 * @route   POST /api/admin/profile/change-password
 * @desc    Change admin password
 * @access  Private (Admin)
 */
router.post('/change-password', [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('New password must contain at least one lowercase letter, one uppercase letter, one number, and one special character'),
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('Password confirmation does not match new password');
      }
      return true;
    })
], validateRequest, changePassword);

/**
 * @route   PUT /api/admin/profile/avatar
 * @desc    Update admin profile picture
 * @access  Private (Admin)
 */
router.put('/avatar', imageUpload.avatar(), updateProfilePicture);

/**
 * @route   DELETE /api/admin/profile/avatar
 * @desc    Delete admin profile picture
 * @access  Private (Admin)
 */
router.delete('/avatar', deleteProfilePicture);

module.exports = router;
