const express = require('express');
const router = express.Router();

// Import middleware
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');
const ensureVendorRecord = require('../../middleware/vendor/ensureVendorRecord');
const imageUpload = require('../../middleware/upload/imageUpload');
const { addUserCurrency } = require('../../middleware/currency');

// Import validation middleware
const {
  validateCreateProduct,
  validateUpdateProduct,
  validateProductList,
  validateProductId,
  validateBulkOperation,
  validateStockUpdate,
  validateProductImages,
  validateColorImageUpload,
  validateColorImageRemoval,
  validateColorImage
} = require('../../middleware/validation/productValidation');

// Import controller
const {
  createProduct,
  getProducts,
  getProduct,
  updateProduct,
  deleteProduct,
  updateStock,
  bulkOperation,
  removeImage,
  getProductStats,
  submitForApproval,
  getApprovalHistory,
  updateMultiCurrencyPricing,
  getMultiCurrencyPricing,
  uploadColorImage,
  removeColorImage,
  getProductColors,
  createTestProduct
} = require('../../controllers/vendor/productController');

// Apply authentication middleware to all routes
router.use(verifyToken);
router.use(requireUserType(['vendor']));
router.use(ensureVendorRecord);
router.use(addUserCurrency);
// Note: Not requiring vendor approval for basic product management
// TODO: Temporarily commented out vendor approval check for testing
// router.use(requireVendorApproval);

/**
 * @route   GET /api/vendor/products/stats
 * @desc    Get vendor product statistics
 * @access  Private (Vendor)
 */
router.get('/stats', getProductStats);

/**
 * @route   GET /api/vendor/products
 * @desc    Get vendor's products with pagination and filters
 * @access  Private (Vendor)
 */
router.get('/', validateProductList, getProducts);

/**
 * @route   GET /api/vendor/products/:id
 * @desc    Get single product by ID
 * @access  Private (Vendor)
 */
router.get('/:id', validateProductId, getProduct);

/**
 * @route   POST /api/vendor/products
 * @desc    Create a new product
 * @access  Private (Vendor)
 */
router.post(
  '/',
  imageUpload.product(8), // Allow up to 8 product images
  validateProductImages,
  validateCreateProduct,
  createProduct
);

/**
 * @route   POST /api/vendor/products/simple
 * @desc    Create a new product (simplified for testing)
 * @access  Private (Vendor)
 */
router.post(
  '/simple',
  validateCreateProduct,
  createProduct
);

/**
 * @route   PUT /api/vendor/products/:id
 * @desc    Update product
 * @access  Private (Vendor)
 */
router.put(
  '/:id',
  imageUpload.product(8), // Allow up to 8 additional product images
  validateProductImages,
  validateUpdateProduct,
  updateProduct
);

/**
 * @route   DELETE /api/vendor/products/:id
 * @desc    Delete product
 * @access  Private (Vendor)
 */
router.delete('/:id', validateProductId, deleteProduct);

/**
 * @route   PATCH /api/vendor/products/:id/stock
 * @desc    Update product stock
 * @access  Private (Vendor)
 */
router.patch('/:id/stock', validateStockUpdate, updateStock);

/**
 * @route   POST /api/vendor/products/bulk
 * @desc    Bulk operations on products (activate, deactivate, delete, archive)
 * @access  Private (Vendor)
 */
router.post('/bulk', validateBulkOperation, bulkOperation);

/**
 * @route   DELETE /api/vendor/products/:id/images
 * @desc    Remove product image
 * @access  Private (Vendor)
 */
router.delete('/:id/images', validateProductId, removeImage);

/**
 * @route   PATCH /api/vendor/products/:id/submit-approval
 * @desc    Submit product for approval
 * @access  Private (Vendor)
 */
router.patch('/:id/submit-approval', validateProductId, submitForApproval);

/**
 * @route   GET /api/vendor/products/:id/approval-history
 * @desc    Get product approval history
 * @access  Private (Vendor)
 */
router.get('/:id/approval-history', validateProductId, getApprovalHistory);

/**
 * @route   GET /api/vendor/products/:id/pricing
 * @desc    Get product pricing in all available currencies
 * @access  Private (Vendor)
 */
router.get('/:id/pricing', validateProductId, getMultiCurrencyPricing);

/**
 * @route   PATCH /api/vendor/products/:id/pricing
 * @desc    Update multi-currency pricing for a product
 * @access  Private (Vendor)
 */
router.patch('/:id/pricing', validateProductId, updateMultiCurrencyPricing);

/**
 * @route   GET /api/vendor/products/:id/colors
 * @desc    Get all colors with their images for a product
 * @access  Private (Vendor)
 */
router.get('/:id/colors', validateProductId, getProductColors);

/**
 * @route   POST /api/vendor/products/:id/colors/upload-image
 * @desc    Upload color-specific image for a product
 * @access  Private (Vendor)
 */
router.post(
  '/:id/colors/upload-image',
  imageUpload.single('colorImage', 'productImages'), // Single image upload for color
  validateColorImage,
  validateColorImageUpload,
  uploadColorImage
);

/**
 * @route   DELETE /api/vendor/products/:id/colors/remove-image
 * @desc    Remove color-specific image from a product
 * @access  Private (Vendor)
 */
router.delete('/:id/colors/remove-image', validateColorImageRemoval, removeColorImage);

module.exports = router;
