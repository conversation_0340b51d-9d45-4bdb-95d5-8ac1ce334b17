const mongoose = require('mongoose');

const shippingOptionsSchema = new mongoose.Schema({
  processingDays: {
    type: Number,
    min: 0,
    max: 30,
    default: 1,
    required: true
  },
  shippingDays: {
    type: Number,
    min: 1,
    max: 30,
    default: 3,
    required: true
  },
  expeditedShipping: {
    available: {
      type: Boolean,
      default: false
    },
    days: {
      type: Number,
      min: 1,
      max: 7
    },
    additionalCost: {
      type: Number,
      min: 0,
      default: 0
    }
  },
  freeShippingThreshold: {
    type: Number,
    min: 0,
    default: 0
  },
  shippingRegions: [{
    type: String,
    enum: ['domestic', 'international', 'local'],
    default: 'domestic'
  }]
}, { _id: false });

module.exports = { shippingOptionsSchema };
