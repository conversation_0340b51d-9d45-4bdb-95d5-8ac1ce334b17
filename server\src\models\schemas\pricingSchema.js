const mongoose = require('mongoose');

const pricingSchema = new mongoose.Schema({
  basePrice: {
    type: Number,
    required: [true, 'Base price is required'],
    min: [0, 'Price cannot be negative']
  },
  salePrice: {
    type: Number,
    min: [0, 'Sale price cannot be negative']
  },
  costPrice: {
    type: Number,
    min: [0, 'Cost price cannot be negative']
  },
  currency: {
    type: String,
    default: 'INR',
    uppercase: true
  },
  // Multi-currency pricing support - flexible schema
  multiCurrency: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  taxClass: {
    type: String,
    enum: ['standard', 'reduced', 'zero', 'exempt'],
    default: 'standard'
  },
  taxRate: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  }
}, { _id: false });

module.exports = { pricingSchema };
