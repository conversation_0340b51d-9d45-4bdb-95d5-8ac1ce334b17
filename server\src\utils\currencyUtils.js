const { formatCurrency, getCurrencySymbol, getCurrencyName, isValidCurrency } = require('./currency');

/**
 * Format price for a specific currency
 * @param {number} amount - The amount to format
 * @param {string} currency - The currency code (default: INR)
 * @param {boolean} showSymbol - Whether to show currency symbol (default: true)
 * @returns {string} - Formatted price string
 */
function formatPriceForCurrency(amount, currency = 'INR', showSymbol = true) {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return showSymbol ? `${getCurrencySymbol(currency)} 0.00` : '0.00';
  }
  
  return formatCurrency(amount, currency, showSymbol);
}

/**
 * Convert price between currencies (basic implementation)
 * Note: This is a placeholder for actual currency conversion
 * In a real application, you would use exchange rate APIs
 * @param {number} amount - Amount to convert
 * @param {string} fromCurrency - Source currency
 * @param {string} toCurrency - Target currency
 * @returns {number} - Converted amount
 */
function convertCurrency(amount, fromCurrency, toCurrency) {
  // Basic conversion rates (placeholder - use real exchange rates in production)
  const exchangeRates = {
    INR: { USD: 0.012, EUR: 0.011, GBP: 0.0095, JPY: 1.8 },
    USD: { INR: 83.0, EUR: 0.92, GBP: 0.79, JPY: 150 },
    EUR: { INR: 90.0, USD: 1.09, GBP: 0.86, JPY: 163 },
    GBP: { INR: 105.0, USD: 1.27, EUR: 1.16, JPY: 190 },
    JPY: { INR: 0.55, USD: 0.0067, EUR: 0.0061, GBP: 0.0053 }
  };
  
  if (fromCurrency === toCurrency) {
    return amount;
  }
  
  if (exchangeRates[fromCurrency] && exchangeRates[fromCurrency][toCurrency]) {
    return amount * exchangeRates[fromCurrency][toCurrency];
  }
  
  // If direct conversion not available, convert through USD
  if (fromCurrency !== 'USD' && toCurrency !== 'USD') {
    const usdAmount = convertCurrency(amount, fromCurrency, 'USD');
    return convertCurrency(usdAmount, 'USD', toCurrency);
  }
  
  return amount; // Fallback to original amount
}

/**
 * Get price range string for products with variants
 * @param {number} minPrice - Minimum price
 * @param {number} maxPrice - Maximum price
 * @param {string} currency - Currency code
 * @returns {string} - Formatted price range
 */
function formatPriceRange(minPrice, maxPrice, currency = 'INR') {
  if (minPrice === maxPrice) {
    return formatPriceForCurrency(minPrice, currency);
  }
  
  const formattedMin = formatPriceForCurrency(minPrice, currency);
  const formattedMax = formatPriceForCurrency(maxPrice, currency);
  
  return `${formattedMin} - ${formattedMax}`;
}

/**
 * Calculate discount percentage
 * @param {number} originalPrice - Original price
 * @param {number} salePrice - Sale price
 * @returns {number} - Discount percentage
 */
function calculateDiscountPercentage(originalPrice, salePrice) {
  if (!originalPrice || !salePrice || salePrice >= originalPrice) {
    return 0;
  }
  
  return Math.round(((originalPrice - salePrice) / originalPrice) * 100);
}

/**
 * Format discount display
 * @param {number} originalPrice - Original price
 * @param {number} salePrice - Sale price
 * @param {string} currency - Currency code
 * @returns {Object} - Formatted discount information
 */
function formatDiscountDisplay(originalPrice, salePrice, currency = 'INR') {
  const discountPercentage = calculateDiscountPercentage(originalPrice, salePrice);
  const discountAmount = originalPrice - salePrice;
  
  return {
    hasDiscount: discountPercentage > 0,
    discountPercentage,
    discountAmount,
    formattedDiscountAmount: formatPriceForCurrency(discountAmount, currency),
    formattedOriginalPrice: formatPriceForCurrency(originalPrice, currency),
    formattedSalePrice: formatPriceForCurrency(salePrice, currency)
  };
}

module.exports = {
  formatPriceForCurrency,
  convertCurrency,
  formatPriceRange,
  calculateDiscountPercentage,
  formatDiscountDisplay
};