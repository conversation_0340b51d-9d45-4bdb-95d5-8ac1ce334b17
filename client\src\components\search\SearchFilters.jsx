import React from 'react';
import { Select, Slider, Checkbox, Button, InputNumber } from 'antd';
import { ClearOutlined } from '@ant-design/icons';
import { useCurrency } from '../../contexts/CurrencyContext';

const { Option } = Select;

const SearchFilters = ({ 
  filters, 
  onFilterChange, 
  onClearFilters, 
  priceRange, 
  onPriceRangeChange 
}) => {
  const { currentCurrency, getCurrencySymbol } = useCurrency();
  
  const sortOptions = [
    { value: 'relevance', label: 'Best Match' },
    { value: 'price-low-high', label: 'Price: Low to High' },
    { value: 'price-high-low', label: 'Price: High to Low' },
    { value: 'rating', label: 'Top Rated' },
    { value: 'newest', label: 'Newest First' }
  ];


  // Dynamic price range based on current values
  const maxPriceRange = Math.max(1000, priceRange[1] + 100);
  
  const handlePriceInputChange = (index, value) => {
    const newRange = [...priceRange];
    if (value !== null && value >= 0) {
      newRange[index] = value;
      // Ensure min is not greater than max
      if (index === 0 && value > newRange[1]) {
        newRange[1] = value;
      } else if (index === 1 && value < newRange[0]) {
        newRange[0] = value;
      }
      onPriceRangeChange(newRange);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-4 sticky top-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Filters</h3>
        <Button
          type="text"
          size="small"
          icon={<ClearOutlined />}
          onClick={onClearFilters}
        >
          Clear All
        </Button>
      </div>

      {/* Sort By */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Sort By
        </label>
        <Select
          value={filters.sortBy}
          onChange={(value) => onFilterChange('sortBy', value)}
          className="w-full"
        >
          {sortOptions.map(option => (
            <Option key={option.value} value={option.value}>
              {option.label}
            </Option>
          ))}
        </Select>
      </div>


      {/* Price Range */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Price Range
        </label>
        <Slider
          range
          min={0}
          max={maxPriceRange}
          value={priceRange}
          onChange={onPriceRangeChange}
          className="mb-3"
        />
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center">
            <span className="text-sm text-gray-600 mr-1">{getCurrencySymbol()}</span>
            <InputNumber
              min={0}
              max={priceRange[1]}
              value={priceRange[0]}
              onChange={(value) => handlePriceInputChange(0, value)}
              size="small"
              className="w-20"
            />
          </div>
          <span className="text-gray-400">-</span>
          <div className="flex items-center">
            <span className="text-sm text-gray-600 mr-1">{getCurrencySymbol()}</span>
            <InputNumber
              min={priceRange[0]}
              max={maxPriceRange}
              value={priceRange[1]}
              onChange={(value) => handlePriceInputChange(1, value)}
              size="small"
              className="w-20"
            />
          </div>
        </div>
      </div>

      {/* In Stock Only */}
      <div className="mb-4">
        <Checkbox
          checked={filters.inStock}
          onChange={(e) => onFilterChange('inStock', e.target.checked)}
        >
          In Stock Only
        </Checkbox>
      </div>
    </div>
  );
};

export default SearchFilters;
