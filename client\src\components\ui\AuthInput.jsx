import React, { useState } from 'react';
import { Input } from 'antd';
import { EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';

const AuthInput = ({ 
  type = 'text', 
  placeholder, 
  value, 
  onChange, 
  error, 
  icon, 
  className = "",
  ...props 
}) => {
  const [focused, setFocused] = useState(false);

  const inputProps = {
    size: 'large',
    placeholder,
    value,
    onChange,
    onFocus: () => setFocused(true),
    onBlur: () => setFocused(false),
    className: `auth-input ${focused ? 'focused' : ''} ${error ? 'error' : ''} ${className}`,
    prefix: icon,
    style: {
      borderRadius: '8px',
      border: error ? '2px solid #ff4d4f' : focused ? '2px solid #ff6b35' : '1px solid #d9d9d9',
      boxShadow: focused ? '0 0 0 2px rgba(255, 107, 53, 0.1)' : 'none',
      transition: 'all 0.3s ease',
    },
    ...props
  };

  if (type === 'password') {
    return (
      <div className="w-full">
        <Input.Password
          {...inputProps}
          iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
        />
        {error && <div className="text-red-500 text-sm mt-1 ml-1">{error}</div>}
      </div>
    );
  }

  return (
    <div className="w-full">
      <Input {...inputProps} />
      {error && <div className="text-red-500 text-sm mt-1 ml-1">{error}</div>}
    </div>
  );
};

export default AuthInput;