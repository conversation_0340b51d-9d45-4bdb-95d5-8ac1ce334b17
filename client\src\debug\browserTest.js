// Paste this in browser console to test currency filtering
// Make sure to run this after the app has loaded

console.log('=== BROWSER CURRENCY FILTER TEST ===');

// Test product structure from console image
const testProduct = {
  _id: "test123",
  name: "<PERSON><PERSON>",
  description: "This is demo",
  brand: "Samsung",
  sku: "SMSNG",
  pricing: {
    basePrice: 45000,
    salePrice: 4400,
    currency: "INR"
  },
  multiCurrency: {
    INR: {
      basePrice: 45000,
      salePrice: 4400
    },
    USD: {
      basePrice: 5400,
      salePrice: 4144
    }
  },
  inventory: {
    quantity: 10
  }
};

// Test the current currency
console.log('Current currency from localStorage:', localStorage.getItem('selectedCurrency'));

// Test currency filtering function
function testCurrencyFilter(product, currency) {
  console.log(`\n🧪 Testing currency filter for ${currency}`);
  console.log('Product:', product);
  
  // Check multiCurrency object format (what we expect from server)
  if (product.multiCurrency && typeof product.multiCurrency === 'object') {
    console.log('✅ Has multiCurrency object:', product.multiCurrency);
    const hasObjectCurrency = product.multiCurrency.hasOwnProperty(currency);
    console.log(`Object format has ${currency}:`, hasObjectCurrency);
    return hasObjectCurrency;
  }
  
  // Check multiCurrencyPricing array format
  if (product.multiCurrencyPricing && Array.isArray(product.multiCurrencyPricing)) {
    console.log('✅ Has multiCurrencyPricing array:', product.multiCurrencyPricing);
    const hasArrayCurrency = product.multiCurrencyPricing.some(pricing => pricing.currency === currency);
    console.log(`Array format has ${currency}:`, hasArrayCurrency);
    return hasArrayCurrency;
  }
  
  // Fallback for INR
  if (product.pricing && (product.pricing.basePrice || product.pricing.salePrice)) {
    console.log('✅ Has basic pricing:', product.pricing);
    const isINR = currency === 'INR';
    console.log(`Currency is INR (${currency}):`, isINR);
    return isINR;
  }
  
  console.log('❌ Product filtered out');
  return false;
}

// Test with different currencies
const currencies = ['INR', 'USD', 'EUR'];
currencies.forEach(currency => {
  const result = testCurrencyFilter(testProduct, currency);
  console.log(`Result for ${currency}:`, result);
});

// Test with actual search results if available
if (window.searchResults) {
  console.log('\n📊 Testing with actual search results:', window.searchResults);
  currencies.forEach(currency => {
    const filtered = window.searchResults.filter(product => testCurrencyFilter(product, currency));
    console.log(`${currency} filtered results:`, filtered.length, 'out of', window.searchResults.length);
  });
} else {
  console.log('⚠️ No search results found in window.searchResults');
}

// Test with React context if available
if (window.React && window.ReactDOM) {
  console.log('\n⚛️ Checking React components...');
  // Try to access the search context
  const searchContexts = document.querySelectorAll('[data-testid="search-results"], .search-results, [class*="search"]');
  console.log('Found search-related elements:', searchContexts.length);
} else {
  console.log('⚠️ React not found in window');
}

console.log('\n✅ Browser test complete. Check console output above for details.');

// Export for global access
window.testCurrencyFilter = testCurrencyFilter;
window.testProduct = testProduct;
