const Payout = require('../../models/Payout');
const Vendor = require('../../models/Vendor');
const { validationResult } = require('express-validator');

// Get vendor payout dashboard data
const getPayoutDashboard = async (req, res) => {
  try {
    const vendorId = req.vendor._id;

    // Get vendor with commission data
    const vendor = await Vendor.findById(vendorId).select('commission businessName');
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Get recent payouts
    const recentPayouts = await Payout.find({ vendor: vendorId })
      .sort({ requestDate: -1 })
      .limit(10)
      .select('requestedAmount status requestDate completedDate method transactionId');

    // Calculate payout statistics
    const payoutStats = await Payout.aggregate([
      { $match: { vendor: vendorId } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$requestedAmount' }
        }
      }
    ]);

    const stats = {
      pending: { count: 0, amount: 0 },
      approved: { count: 0, amount: 0 },
      processing: { count: 0, amount: 0 },
      completed: { count: 0, amount: 0 },
      rejected: { count: 0, amount: 0 },
      failed: { count: 0, amount: 0 }
    };

    payoutStats.forEach(stat => {
      if (stats[stat._id]) {
        stats[stat._id] = {
          count: stat.count,
          amount: stat.totalAmount
        };
      }
    });

    // Calculate total earned this month
    const currentDate = new Date();
    const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const monthlyEarnings = await Payout.aggregate([
      {
        $match: {
          vendor: vendorId,
          status: 'completed',
          completedDate: { $gte: startOfMonth }
        }
      },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$requestedAmount' }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        commission: {
          totalEarned: vendor.commission.totalEarned,
          totalPaid: vendor.commission.totalPaid,
          pendingAmount: vendor.commission.pendingAmount,
          lastPayoutDate: vendor.commission.lastPayoutDate
        },
        stats,
        recentPayouts,
        monthlyEarnings: monthlyEarnings[0]?.totalAmount || 0,
        minimumPayout: 50 // Minimum payout amount
      }
    });

  } catch (error) {
    console.error('Error fetching payout dashboard:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

// Request a new payout
const requestPayout = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const vendorId = req.vendor._id;
    const { amount, method, bankDetails, paypalEmail } = req.body;

    // Get vendor data
    const vendor = await Vendor.findById(vendorId).populate('user', 'firstName lastName email');
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Check if vendor has sufficient balance
    if (vendor.commission.pendingAmount < amount) {
      return res.status(400).json({
        success: false,
        message: `Insufficient balance. Available amount: $${vendor.commission.pendingAmount.toFixed(2)}`
      });
    }

    // Check minimum payout amount
    const minimumPayout = 50;
    if (amount < minimumPayout) {
      return res.status(400).json({
        success: false,
        message: `Minimum payout amount is $${minimumPayout}`
      });
    }

    // Check if there's already a pending payout
    const existingPendingPayout = await Payout.findOne({
      vendor: vendorId,
      status: { $in: ['pending', 'approved', 'processing'] }
    });

    if (existingPendingPayout) {
      return res.status(400).json({
        success: false,
        message: 'You already have a pending payout request. Please wait for it to be processed.'
      });
    }

    // Validate method-specific requirements
    if (method === 'bank_transfer' && !bankDetails) {
      return res.status(400).json({
        success: false,
        message: 'Bank details are required for bank transfer'
      });
    }

    if (method === 'paypal' && !paypalEmail) {
      return res.status(400).json({
        success: false,
        message: 'PayPal email is required for PayPal payout'
      });
    }

    // Create payout request
    const payoutData = {
      vendor: vendorId,
      requestedAmount: amount,
      availableAmount: vendor.commission.pendingAmount,
      method,
      netAmount: amount, // Will be calculated in pre-save hook
      metadata: {
        vendorBusinessName: vendor.businessName,
        vendorEmail: vendor.user.email,
        requestIpAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    };

    // Add method-specific details
    if (method === 'bank_transfer') {
      payoutData.bankDetails = {
        accountHolderName: bankDetails.accountHolderName,
        bankName: bankDetails.bankName,
        accountNumber: bankDetails.accountNumber,
        routingNumber: bankDetails.routingNumber,
        accountType: bankDetails.accountType
      };
    } else if (method === 'paypal') {
      payoutData.paypalEmail = paypalEmail;
    }

    const payout = new Payout(payoutData);
    await payout.save();

    // Update vendor's pending amount (reserve the requested amount)
    vendor.commission.pendingAmount -= amount;
    await vendor.save();

    res.status(201).json({
      success: true,
      message: 'Payout request submitted successfully',
      data: {
        payoutId: payout._id,
        requestedAmount: payout.requestedAmount,
        method: payout.method,
        status: payout.status,
        requestDate: payout.requestDate
      }
    });

  } catch (error) {
    console.error('Error creating payout request:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

// Get all payouts for vendor
const getPayouts = async (req, res) => {
  try {
    const vendorId = req.vendor._id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const status = req.query.status;

    const skip = (page - 1) * limit;
    let query = { vendor: vendorId };

    if (status && ['pending', 'approved', 'processing', 'completed', 'rejected', 'failed'].includes(status)) {
      query.status = status;
    }

    const payouts = await Payout.find(query)
      .sort({ requestDate: -1 })
      .skip(skip)
      .limit(limit)
      .select('requestedAmount status requestDate processedDate completedDate method transactionId adminNotes rejectionReason processingFee netAmount');

    const total = await Payout.countDocuments(query);

    res.json({
      success: true,
      data: {
        payouts,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(total / limit),
          totalItems: total,
          itemsPerPage: limit
        }
      }
    });

  } catch (error) {
    console.error('Error fetching payouts:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

// Get single payout details
const getPayoutDetails = async (req, res) => {
  try {
    const { payoutId } = req.params;
    const vendorId = req.vendor._id;

    const payout = await Payout.findOne({
      _id: payoutId,
      vendor: vendorId
    }).populate('processedBy', 'firstName lastName email role');

    if (!payout) {
      return res.status(404).json({
        success: false,
        message: 'Payout not found'
      });
    }

    res.json({
      success: true,
      data: payout
    });

  } catch (error) {
    console.error('Error fetching payout details:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

// Cancel pending payout
const cancelPayout = async (req, res) => {
  try {
    const { payoutId } = req.params;
    const vendorId = req.vendor._id;

    const payout = await Payout.findOne({
      _id: payoutId,
      vendor: vendorId,
      status: 'pending'
    });

    if (!payout) {
      return res.status(404).json({
        success: false,
        message: 'Pending payout not found'
      });
    }

    // Delete the payout request
    await Payout.findByIdAndDelete(payoutId);

    // Restore the amount back to vendor's pending balance
    const vendor = await Vendor.findById(vendorId);
    vendor.commission.pendingAmount += payout.requestedAmount;
    await vendor.save();

    res.json({
      success: true,
      message: 'Payout request cancelled successfully'
    });

  } catch (error) {
    console.error('Error cancelling payout:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

// Get payout methods
const getPayoutMethods = async (req, res) => {
  try {
    const vendorId = req.vendor._id;
    
    // Get vendor bank details
    const vendor = await Vendor.findById(vendorId).select('bankDetails');

    const methods = [
      {
        id: 'bank_transfer',
        name: 'Bank Transfer',
        description: 'Direct transfer to your bank account',
        processingTime: '2-3 business days',
        fee: 0,
        available: !!vendor.bankDetails.accountNumber,
        requirements: vendor.bankDetails.accountNumber ? null : 'Please update your bank details in settings'
      },
      {
        id: 'paypal',
        name: 'PayPal',
        description: 'Transfer to your PayPal account',
        processingTime: '1-2 business days',
        fee: 2.9, // percentage
        available: true,
        requirements: 'Valid PayPal email required'
      },
      {
        id: 'stripe',
        name: 'Stripe',
        description: 'Transfer via Stripe',
        processingTime: '1-2 business days',
        fee: 0.25, // fixed fee
        available: false,
        requirements: 'Coming soon'
      },
      {
        id: 'check',
        name: 'Physical Check',
        description: 'Mailed physical check',
        processingTime: '5-7 business days',
        fee: 2.5, // fixed fee
        available: true,
        requirements: 'Valid business address required'
      }
    ];

    res.json({
      success: true,
      data: methods
    });

  } catch (error) {
    console.error('Error fetching payout methods:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

module.exports = {
  getPayoutDashboard,
  requestPayout,
  getPayouts,
  getPayoutDetails,
  cancelPayout,
  getPayoutMethods
};
