import React from 'react';
import {
  Modal,
  Form,
  Select,
  Input,
  Space,
  Button,
  Image,
  Tag,
  Typography
} from 'antd';

const { Option } = Select;
const { TextArea } = Input;
const { Text } = Typography;

const ProductApprovalModal = ({
  visible,
  selectedProduct,
  approvalAction,
  form,
  loading,
  onSubmit,
  onCancel
}) => {
  return (
    <Modal
      title={`${approvalAction?.charAt(0).toUpperCase() + approvalAction?.slice(1).replace('_', ' ')} Product`}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={600}
    >
      {selectedProduct && (
        <div>
          <div style={{ 
            marginBottom: '16px', 
            padding: '12px', 
            backgroundColor: '#f5f5f5', 
            borderRadius: '6px' 
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <Image
                width={60}
                height={60}
                src={selectedProduct.images?.[0]?.url || '/placeholder-product.png'}
                alt={selectedProduct.name}
                style={{ borderRadius: '4px', objectFit: 'cover' }}
                fallback="/placeholder-product.png"
              />
              <div>
                <div style={{ fontWeight: 500, fontSize: '16px' }}>
                  {selectedProduct.name}
                </div>
                <Text type="secondary">
                  by {selectedProduct.vendor?.businessName}
                </Text>
                <div style={{ marginTop: '4px' }}>
                  <Tag color="blue" size="small">
                    {selectedProduct.category?.name}
                  </Tag>
                  <Text type="secondary" style={{ fontSize: '12px', marginLeft: '8px' }}>
                    SKU: {selectedProduct.sku}
                  </Text>
                </div>
              </div>
            </div>
          </div>

          <Form
            form={form}
            layout="vertical"
            onFinish={onSubmit}
          >
            {approvalAction === 'reject' && (
              <Form.Item
                name="reason"
                label="Rejection Reason"
                rules={[{ required: true, message: 'Please provide a rejection reason' }]}
              >
                <Select placeholder="Select rejection reason">
                  <Option value="incomplete_information">Incomplete Information</Option>
                  <Option value="poor_quality_images">Poor Quality Images</Option>
                  <Option value="inappropriate_content">Inappropriate Content</Option>
                  <Option value="pricing_issues">Pricing Issues</Option>
                  <Option value="category_mismatch">Category Mismatch</Option>
                  <Option value="duplicate_product">Duplicate Product</Option>
                  <Option value="policy_violation">Policy Violation</Option>
                  <Option value="other">Other</Option>
                </Select>
              </Form.Item>
            )}

            {approvalAction === 'request_changes' && (
              <Form.Item
                name="changes"
                label="Required Changes"
                rules={[{ required: true, message: 'Please specify required changes' }]}
              >
                <Select
                  mode="tags"
                  placeholder="Add change requests"
                  style={{ width: '100%' }}
                >
                  <Option value="Update product description">Update product description</Option>
                  <Option value="Add more product images">Add more product images</Option>
                  <Option value="Correct pricing information">Correct pricing information</Option>
                  <Option value="Update category selection">Update category selection</Option>
                  <Option value="Add product specifications">Add product specifications</Option>
                  <Option value="Improve image quality">Improve image quality</Option>
                </Select>
              </Form.Item>
            )}

            <Form.Item
              name="notes"
              label="Additional Notes"
            >
              <TextArea
                rows={4}
                placeholder={`Add any additional notes for the ${approvalAction}...`}
              />
            </Form.Item>

            <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
              <Space>
                <Button onClick={onCancel}>
                  Cancel
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  danger={approvalAction === 'reject'}
                >
                  {approvalAction === 'approve' && 'Approve Product'}
                  {approvalAction === 'reject' && 'Reject Product'}
                  {approvalAction === 'request_changes' && 'Request Changes'}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </div>
      )}
    </Modal>
  );
};

export default ProductApprovalModal;
