import React, { useState, useEffect, useRef } from 'react';
import { Form, message, Button } from 'antd';
import { PhoneOutlined, ClockCircleOutlined, ReloadOutlined } from '@ant-design/icons';

const OTPVerificationForm = ({ 
  phone, 
  countryCode, 
  purpose = 'verification', 
  onVerify, 
  onResend,
  tempUserId,
  initialTimer = 300, // 5 minutes in seconds
  onBack
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [resending, setResending] = useState(false);
  const [timer, setTimer] = useState(initialTimer);
  const [canResend, setCanResend] = useState(false);
  const [otpValues, setOtpValues] = useState(['', '', '', '', '', '']);
  const inputRefs = useRef([]);

  // Timer countdown effect
  useEffect(() => {
    let interval;
    if (timer > 0) {
      interval = setInterval(() => {
        setTimer(prev => prev - 1);
      }, 1000);
    } else {
      setCanResend(true);
    }
    return () => clearInterval(interval);
  }, [timer]);

  // Format timer display
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Handle OTP input change
  const handleOtpChange = (index, value) => {
    if (value && !/^\d$/.test(value)) return;
    
    const newValues = [...otpValues];
    newValues[index] = value;
    setOtpValues(newValues);

    // Auto focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // Auto submit when all fields are filled
    if (newValues.every(val => val !== '') && newValues.join('').length === 6) {
      handleSubmit({ otp: newValues.join('') });
    }
  };

  // Handle backspace in OTP inputs
  const handleKeyDown = (index, e) => {
    if (e.key === 'Backspace' && !otpValues[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  // Handle paste
  const handlePaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');
    const digits = pastedData.replace(/\D/g, '').slice(0, 6);
    
    if (digits.length === 6) {
      const newValues = digits.split('');
      setOtpValues(newValues);
      // Auto submit pasted OTP
      handleSubmit({ otp: digits });
    }
  };

  const handleSubmit = async (values) => {
    const otp = values.otp || otpValues.join('');
    
    if (otp.length !== 6) {
      message.error('Please enter complete 6-digit OTP');
      return;
    }

    setLoading(true);
    try {
      if (onVerify) {
        const result = await onVerify({
          phone,
          otp,
          countryCode,
          purpose,
          tempUserId
        });
        
        if (result.success) {
          message.success('Phone number verified successfully!');
        } else {
          message.error(result.message || 'OTP verification failed');
          // Clear OTP inputs on error
          setOtpValues(['', '', '', '', '', '']);
          inputRefs.current[0]?.focus();
        }
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      message.error('Failed to verify OTP. Please try again.');
      // Clear OTP inputs on error
      setOtpValues(['', '', '', '', '', '']);
      inputRefs.current[0]?.focus();
    } finally {
      setLoading(false);
    }
  };

  const handleResend = async () => {
    setResending(true);
    try {
      if (onResend) {
        const result = await onResend({
          phone,
          countryCode,
          purpose
        });
        
        if (result.success) {
          message.success('OTP has been resent to your phone number');
          setTimer(initialTimer);
          setCanResend(false);
          // Clear current OTP inputs
          setOtpValues(['', '', '', '', '', '']);
          inputRefs.current[0]?.focus();
        } else {
          message.error(result.message || 'Failed to resend OTP');
        }
      }
    } catch (error) {
      console.error('Resend OTP error:', error);
      message.error('Failed to resend OTP. Please try again.');
    } finally {
      setResending(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="text-center mb-8">
        <div className="mx-auto w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-4">
          <PhoneOutlined className="text-2xl text-orange-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Verify Your Phone Number
        </h2>
        <p className="text-gray-600 mb-2">
          We've sent a 6-digit verification code to
        </p>
        <p className="text-gray-800 font-medium">
          {phone}
        </p>
        <p className="text-sm text-gray-500 mt-2">
          Enter the code below to continue
        </p>
      </div>

      <Form
        form={form}
        onFinish={handleSubmit}
        layout="vertical"
        className="space-y-6"
      >
        {/* OTP Input Fields */}
        <div className="mb-6">
          <div className="flex justify-center space-x-3 mb-4">
            {otpValues.map((value, index) => (
              <input
                key={index}
                ref={ref => inputRefs.current[index] = ref}
                type="text"
                maxLength="1"
                value={value}
                onChange={(e) => handleOtpChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                onPaste={index === 0 ? handlePaste : undefined}
                className="w-12 h-12 text-center text-lg font-semibold border-2 border-gray-300 rounded-lg focus:border-orange-500 focus:outline-none transition-colors"
                autoFocus={index === 0}
              />
            ))}
          </div>
          
          {/* Timer */}
          <div className="text-center text-sm text-gray-500 mb-4">
            {timer > 0 ? (
              <div className="flex items-center justify-center">
                <ClockCircleOutlined className="mr-1" />
                Code expires in {formatTime(timer)}
              </div>
            ) : (
              <div className="text-red-500">
                Code has expired. Please request a new one.
              </div>
            )}
          </div>
        </div>

        {/* Verify Button */}
        <Form.Item>
          <button
            type="button"
            onClick={() => handleSubmit({ otp: otpValues.join('') })}
            disabled={loading || otpValues.join('').length !== 6}
            className="w-full bg-gradient-to-r from-orange-500 to-orange-600 text-white py-3 px-4 rounded-lg font-medium hover:from-orange-600 hover:to-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Verifying...
              </div>
            ) : (
              'Verify OTP'
            )}
          </button>
        </Form.Item>

        {/* Resend Section */}
        <div className="text-center space-y-3">
          <p className="text-gray-600 text-sm">
            Didn't receive the code?
          </p>
          
          <div className="space-y-2">
            <Button
              type="link"
              onClick={handleResend}
              disabled={!canResend || resending}
              icon={<ReloadOutlined spin={resending} />}
              className="text-orange-600 hover:text-orange-700 p-0 h-auto font-medium disabled:text-gray-400"
            >
              {resending ? 'Sending...' : 'Resend OTP'}
            </Button>
            
            {onBack && (
              <div>
                <Button
                  type="link"
                  onClick={onBack}
                  className="text-gray-500 hover:text-gray-700 p-0 h-auto"
                >
                  ← Back to {purpose === 'login' ? 'Login' : 'Registration'}
                </Button>
              </div>
            )}
          </div>
        </div>
      </Form>

      {/* Help Text */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <div className="text-xs text-gray-600 space-y-1">
          <p>• The verification code is valid for 5 minutes</p>
          <p>• Make sure you have good network connectivity</p>
          <p>• Check your SMS inbox for the code</p>
          <p>• You can request a new code after the current one expires</p>
        </div>
      </div>
    </div>
  );
};

export default OTPVerificationForm;
