.vendor-reviews-card {
  margin: 20px 0;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
}

.review-stats {
  margin-bottom: 24px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 8px;
}

.review-stats .ant-statistic {
  text-align: center;
}

.review-stats .ant-statistic-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.review-stats .ant-statistic-content {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.vendor-review-item {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 12px;
}

.vendor-review-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.customer-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.customer-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.customer-name {
  font-weight: 500;
  font-size: 15px;
  color: #333;
}

.review-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.review-date {
  font-size: 12px;
  color: #888;
}

.product-info {
  text-align: right;
}

.product-name {
  font-size: 13px;
  color: #666;
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
}

.review-comment {
  margin: 16px 0;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}

.review-comment p {
  margin: 0;
  font-style: italic;
  color: #555;
  line-height: 1.5;
}

.reply-actions {
  margin-top: 16px;
}

.existing-reply {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: #e6f7ff;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #91d5ff;
}

.reply-content {
  flex: 1;
}

.reply-content strong {
  color: #1890ff;
  font-size: 13px;
}

.reply-content p {
  margin: 8px 0 4px 0;
  color: #333;
  font-size: 14px;
}

.reply-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 11px;
  color: #666;
}

.reply-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.reply-btn {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border: none;
  border-radius: 6px;
}

.reply-btn:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
}

.reply-form,
.edit-form {
  margin-top: 12px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.reply-form-actions,
.edit-form-actions {
  margin-bottom: 0;
  display: flex;
  gap: 8px;
}

.cancel-btn {
  color: #666;
  border-color: #d9d9d9;
}

.cancel-btn:hover {
  color: #333;
  border-color: #bfbfbf;
}

.loading-container {
  text-align: center;
  padding: 40px;
}

.loading-container p {
  margin-top: 12px;
  color: #666;
}

.empty-reviews {
  text-align: center;
  padding: 40px;
  color: #999;
}

.pagination-container {
  margin-top: 24px;
  text-align: center;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .review-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .product-info {
    text-align: left;
  }
  
  .existing-reply {
    flex-direction: column;
    gap: 12px;
  }
  
  .reply-buttons {
    flex-direction: row;
    justify-content: flex-end;
  }
  
  .review-stats {
    padding: 12px;
  }
  
  .review-stats .ant-col {
    margin-bottom: 16px;
  }
  
  .reply-form-actions,
  .edit-form-actions {
    flex-direction: column;
  }
  
  .reply-form-actions .ant-btn,
  .edit-form-actions .ant-btn {
    width: 100%;
  }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
  .vendor-reviews-card {
    background: #1f1f1f;
    border-color: #333;
  }
  
  .review-stats {
    background: #262626;
  }
  
  .review-stats .ant-statistic-title {
    color: #bfbfbf;
  }
  
  .review-stats .ant-statistic-content {
    color: #fff;
  }
  
  .vendor-review-item {
    background: #2a2a2a;
    border-bottom-color: #333;
  }
  
  .customer-name {
    color: #fff;
  }
  
  .review-date {
    color: #bfbfbf;
  }
  
  .product-name {
    background: #333;
    color: #bfbfbf;
  }
  
  .review-comment {
    background: #333;
    border-left-color: #40a9ff;
  }
  
  .review-comment p {
    color: #bfbfbf;
  }
  
  .existing-reply {
    background: #1a3a52;
    border-color: #2a5880;
  }
  
  .reply-content p {
    color: #fff;
  }
  
  .reply-meta {
    color: #bfbfbf;
  }
  
  .reply-form,
  .edit-form {
    background: #333;
    border-color: #555;
  }
  
  .loading-container p,
  .empty-reviews {
    color: #bfbfbf;
  }
}
