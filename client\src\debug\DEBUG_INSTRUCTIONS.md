# Currency Filter Debug Instructions

## Quick Debug Steps

1. **Add the debug component temporarily to your SearchPage.jsx:**

```jsx
// Add this import at the top of SearchPage.jsx
import CurrencyDebugComponent from '../debug/CurrencyDebugComponent';

// Add this component inside the return statement, right after the main div:
<div className="min-h-screen bg-gray-50">
  <CurrencyDebugComponent />
  {/* ... rest of your existing code */}
```

2. **Or add it to your main App.jsx:**

```jsx
// Add this import at the top of App.jsx
import CurrencyDebugComponent from './debug/CurrencyDebugComponent';

// Add this component somewhere in your JSX:
<CurrencyDebugComponent />
```

3. **Open browser console and search for "samsun"**

4. **Change currency using the header dropdown**

5. **Watch the console output and the debug box in bottom-right corner**

## What to Look For

- Check if "Current Currency" changes when you select USD
- Check if "Test Product Filter Result" shows 1 for both INR and USD
- Check if the search re-runs when currency changes (look for "🔄 Currency changed" message)
- Check if "Results" count changes after currency change

## Expected Behavior

- For INR: Should show the product (1 result)
- For USD: Should show the product (1 result) 
- The debug box should update immediately when currency changes
- Console should show currency filtering logs

## If Still Not Working

Check in console if:
1. The product has `multiCurrency` object with both INR and USD
2. The filtering function is being called
3. The search is re-running when currency changes

Remove the debug component after testing by removing the import and component usage.
