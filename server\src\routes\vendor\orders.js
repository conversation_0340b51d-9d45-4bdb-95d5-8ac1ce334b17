const express = require('express');
const router = express.Router();
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');
const {
  getOrders,
  getOrder,
  updateOrderStatus,
  updateShipping,
  createShippingTracking,
  updateTrackingStatus,
  processOrderFulfillment,
  getOrderAnalytics
} = require('../../controllers/vendor/orderController');

// Apply authentication middleware to all routes
router.use(verifyToken);
router.use(requireUserType(['vendor']));

/**
 * @route   GET /api/vendor/orders
 * @desc    Get vendor's orders with pagination and filters
 * @access  Private (Vendor)
 */
router.get('/', getOrders);

/**
 * @route   GET /api/vendor/orders/analytics
 * @desc    Get vendor order analytics and fulfillment stats
 * @access  Private (Vendor)
 */
router.get('/analytics', getOrderAnalytics);

/**
 * @route   GET /api/vendor/orders/:id
 * @desc    Get single order by ID
 * @access  Private (Vendor)
 */
router.get('/:id', getOrder);

/**
 * @route   PATCH /api/vendor/orders/:id/status
 * @desc    Update order status
 * @access  Private (Vendor)
 */
router.patch('/:id/status', updateOrderStatus);

/**
 * @route   PATCH /api/vendor/orders/:id/shipping
 * @desc    Update shipping information
 * @access  Private (Vendor)
 */
router.patch('/:id/shipping', updateShipping);

/**
 * @route   POST /api/vendor/orders/:id/tracking
 * @desc    Create shipping tracking for order
 * @access  Private (Vendor)
 */
router.post('/:id/tracking', createShippingTracking);

/**
 * @route   PUT /api/vendor/orders/tracking/:trackingId/status
 * @desc    Update tracking status
 * @access  Private (Vendor)
 */
router.put('/tracking/:trackingId/status', updateTrackingStatus);

/**
 * @route   POST /api/vendor/orders/:id/fulfill
 * @desc    Process order fulfillment (confirm, process, ship, deliver)
 * @access  Private (Vendor)
 */
router.post('/:id/fulfill', processOrderFulfillment);

module.exports = router;
