import React from 'react';

const PaymentOptions = ({ selectedMethod, onMethodSelect, totalAmount }) => {
  const paymentMethods = [
    {
      id: 'cod',
      name: 'Cash on Delivery',
      description: 'Pay when you receive your order',
      icon: '💰',
    },
    {
      id: 'upi',
      name: 'UPI',
      description: 'Pay using UPI apps like Paytm, PhonePe, Google Pay',
      icon: '📱',
    },
    {
      id: 'card',
      name: 'Credit/Debit Cards',
      description: 'Visa, Mastercard, Rupay and more',
      icon: '💳',
    },
    {
      id: 'netbanking',
      name: 'Net Banking',
      description: 'All major banks supported',
      icon: '🏦',
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">Payment Options</h2>
      </div>
      
      <div className="px-6 py-4">
        <div className="space-y-3">
          {paymentMethods.map((method) => (
            <div key={method.id} className="border rounded-lg">
              <div 
                className={`p-4 cursor-pointer transition-colors ${
                  selectedMethod === method.id 
                    ? 'bg-blue-50 border-blue-500' 
                    : 'bg-white hover:bg-gray-50'
                }`}
                onClick={() => onMethodSelect(method.id)}
              >
                <div className="flex items-start">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value={method.id}
                    checked={selectedMethod === method.id}
                    onChange={() => onMethodSelect(method.id)}
                    className="mt-1 mr-3"
                  />
                  <div className="flex-grow">
                    <div className="flex items-center">
                      <span className="text-2xl mr-3">{method.icon}</span>
                      <div>
                        <h3 className="font-medium text-gray-900">{method.name}</h3>
                        <p className="text-sm text-gray-600">{method.description}</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Additional details for selected method */}
                {selectedMethod === method.id && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    {method.id === 'cod' && (
                      <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
                        <p className="text-sm text-yellow-800">
                          <strong>Note:</strong> You can pay ₹{totalAmount.toFixed(0)} in cash when your order is delivered.
                        </p>
                      </div>
                    )}
                    
                    {method.id === 'upi' && (
                      <div className="space-y-2">
                        <p className="text-sm text-gray-600">Choose your preferred UPI app:</p>
                        <div className="flex gap-2 flex-wrap">
                          {['Paytm', 'PhonePe', 'Google Pay', 'Amazon Pay'].map((app) => (
                            <button key={app} className="px-3 py-1 border rounded text-sm hover:bg-gray-50">
                              {app}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {method.id === 'card' && (
                      <div className="space-y-3">
                        <div className="grid grid-cols-1 gap-3">
                          <input
                            type="text"
                            placeholder="Card Number"
                            className="w-full p-2 border rounded"
                          />
                          <div className="grid grid-cols-2 gap-2">
                            <input
                              type="text"
                              placeholder="MM/YY"
                              className="p-2 border rounded"
                            />
                            <input
                              type="text"
                              placeholder="CVV"
                              className="p-2 border rounded"
                            />
                          </div>
                          <input
                            type="text"
                            placeholder="Cardholder Name"
                            className="w-full p-2 border rounded"
                          />
                        </div>
                      </div>
                    )}
                    
                    {method.id === 'netbanking' && (
                      <div>
                        <select className="w-full p-2 border rounded">
                          <option value="">Select your bank</option>
                          <option value="sbi">State Bank of India</option>
                          <option value="hdfc">HDFC Bank</option>
                          <option value="icici">ICICI Bank</option>
                          <option value="axis">Axis Bank</option>
                          <option value="kotak">Kotak Mahindra Bank</option>
                          <option value="other">Other Banks</option>
                        </select>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
        
        {/* Security Note */}
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded">
          <div className="flex items-center text-sm text-green-800">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
            </svg>
            Your payment information is secure and encrypted
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentOptions;
