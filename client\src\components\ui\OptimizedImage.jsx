import React, { useState, useEffect } from 'react';
import { getFallbackImageUrl, getAbsoluteImageUrl } from '../../utils/imageUtils';

/**
 * OptimizedImage component with automatic fallback handling
 * @param {Object} props - Component props
 * @param {string} props.src - Image source URL
 * @param {string} props.alt - Alt text for the image
 * @param {string} props.className - CSS classes
 * @param {string} props.fallbackType - Type of fallback image (product, user, vendor, category)
 * @param {number} props.width - Image width
 * @param {number} props.height - Image height
 * @param {Object} props.style - Inline styles
 * @param {Function} props.onLoad - Callback when image loads
 * @param {Function} props.onError - Callback when image fails to load
 * @param {boolean} props.lazy - Enable lazy loading
 * @returns {JSX.Element} - Optimized image component
 */
const OptimizedImage = ({
  src,
  alt = '',
  className = '',
  fallbackType = 'product',
  width,
  height,
  style = {},
  onLoad,
  onError,
  lazy = true,
  ...props
}) => {
  const [imageSrc, setImageSrc] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    setIsLoading(true);
    setHasError(false);

    if (!src) {
      // No source provided, use fallback immediately
      setImageSrc(getFallbackImageUrl(fallbackType));
      setIsLoading(false);
      return;
    }

    // Try to load the provided image
    const img = new Image();
    
    img.onload = () => {
      setImageSrc(getAbsoluteImageUrl(src) || src);
      setIsLoading(false);
      if (onLoad) onLoad();
    };
    
    img.onerror = () => {
      console.warn('Failed to load image:', src);
      setImageSrc(getFallbackImageUrl(fallbackType));
      setIsLoading(false);
      setHasError(true);
      if (onError) onError();
    };
    
    // Start loading the image
    img.src = getAbsoluteImageUrl(src) || src;
  }, [src, fallbackType, onLoad, onError]);

  const imageProps = {
    alt,
    className: `${className} ${isLoading ? 'opacity-50' : 'opacity-100'} transition-opacity duration-200`,
    style: {
      ...style,
      ...(width && { width }),
      ...(height && { height }),
    },
    ...(lazy && { loading: 'lazy' }),
    ...props,
  };

  if (isLoading) {
    return (
      <div 
        className={`${className} bg-gray-200 animate-pulse flex items-center justify-center`}
        style={{
          ...style,
          ...(width && { width }),
          ...(height && { height }),
          minHeight: height || '200px',
        }}
      >
        <svg 
          className="w-8 h-8 text-gray-400" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" 
          />
        </svg>
      </div>
    );
  }

  return (
    <img
      {...imageProps}
      src={imageSrc}
      onError={() => {
        if (!hasError) {
          console.warn('Image failed to load, using fallback:', src);
          setImageSrc(getFallbackImageUrl(fallbackType));
          setHasError(true);
          if (onError) onError();
        }
      }}
    />
  );
};

export default OptimizedImage;
