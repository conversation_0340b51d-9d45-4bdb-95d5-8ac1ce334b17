require('dotenv').config();
const mongoose = require('mongoose');
const HomepageSettings = require('../src/models/HomepageSettings');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

// Force reset categories with full default data
const resetCategories = async () => {
  try {
    console.log('🔄 Force resetting categories...');

    // Get or create homepage settings
    let settings = await HomepageSettings.getSettings();
    console.log('📊 Settings found:', !!settings);

    // Force overwrite with complete default categories
    settings.allCategoriesModal = {
      mainCategories: [
        { name: 'Apparel & Accessories', icon: 'ShopOutlined', isActive: true, sortOrder: 0, linkUrl: '' },
        { name: 'Consumer Electronics', icon: 'MobileOutlined', isActive: true, sortOrder: 1, linkUrl: '' },
        { name: 'Sports & Entertainment', icon: 'CrownOutlined', isActive: true, sortOrder: 2, linkUrl: '' },
        { name: 'Jewelry, Eyewear & Watches', icon: 'HeartOutlined', isActive: true, sortOrder: 3, linkUrl: '' },
        { name: 'Shoes & Accessories', icon: 'CarOutlined', isActive: true, sortOrder: 4, linkUrl: '' },
        { name: 'Home & Garden', icon: 'HomeOutlined', isActive: true, sortOrder: 5, linkUrl: '' },
        { name: 'Beauty', icon: 'SkinOutlined', isActive: true, sortOrder: 6, linkUrl: '' },
        { name: 'Luggage, Bags & Cases', icon: 'ShoppingOutlined', isActive: true, sortOrder: 7, linkUrl: '' },
        { name: 'Packaging & Printing', icon: 'PrinterOutlined', isActive: true, sortOrder: 8, linkUrl: '' },
        { name: 'Parents, Kids & Toys', icon: 'BugOutlined', isActive: true, sortOrder: 9, linkUrl: '' },
        { name: 'Personal Care & Home Care', icon: 'SmileOutlined', isActive: true, sortOrder: 10, linkUrl: '' },
        { name: 'Health & Medical', icon: 'MedicineBoxOutlined', isActive: true, sortOrder: 11, linkUrl: '' },
        { name: 'Gifts & Crafts', icon: 'GiftOutlined', isActive: true, sortOrder: 12, linkUrl: '' },
        { name: 'Furniture', icon: 'TableOutlined', isActive: true, sortOrder: 13, linkUrl: '' },
        { name: 'Lights & Lighting', icon: 'BulbOutlined', isActive: true, sortOrder: 14, linkUrl: '' },
        { name: 'Home Appliances', icon: 'SettingOutlined', isActive: true, sortOrder: 15, linkUrl: '' },
        { name: 'Safety & Security', icon: 'SafetyOutlined', isActive: true, sortOrder: 16, linkUrl: '' },
        { name: 'View All', icon: 'AppstoreOutlined', isActive: true, sortOrder: 17, linkUrl: '' }
      ],
      popularCategories: [
        { name: 'Consumer Electronics', isActive: true, sortOrder: 0, linkUrl: '' },
        { name: 'Apparel & Accessories', isActive: true, sortOrder: 1, linkUrl: '' },
        { name: 'Home & Garden', isActive: true, sortOrder: 2, linkUrl: '' },
        { name: 'Sports & Entertainment', isActive: true, sortOrder: 3, linkUrl: '' },
        { name: 'Beauty', isActive: true, sortOrder: 4, linkUrl: '' },
        { name: 'Jewelry, Eyewear & Watches', isActive: true, sortOrder: 5, linkUrl: '' },
        { name: 'Shoes & Accessories', isActive: true, sortOrder: 6, linkUrl: '' },
        { name: 'Health & Medical', isActive: true, sortOrder: 7, linkUrl: '' },
        { name: 'Furniture', isActive: true, sortOrder: 8, linkUrl: '' },
        { name: 'Home Appliances', isActive: true, sortOrder: 9, linkUrl: '' }
      ]
    };

    // Save the updated settings
    await settings.save();

    console.log('✅ Categories reset successfully!');
    console.log(`📊 Created ${settings.allCategoriesModal.mainCategories.length} main categories`);
    console.log(`📊 Created ${settings.allCategoriesModal.popularCategories.length} popular categories`);

    // Display the categories
    console.log('\n📋 Main Categories:');
    settings.allCategoriesModal.mainCategories.forEach((cat, index) => {
      console.log(`  ${index + 1}. ${cat.name} (${cat.icon})`);
    });

    console.log('\n📋 Popular Categories:');
    settings.allCategoriesModal.popularCategories.forEach((cat, index) => {
      console.log(`  ${index + 1}. ${cat.name}`);
    });

    console.log('\n🎉 All categories have been reset and saved to the database!');
    console.log('🔗 You can now manage them through the admin panel at: Admin Panel → Homepage Settings → All Categories');

  } catch (error) {
    console.error('�� Error resetting categories:', error);
    throw error;
  }
};

// Main execution
const main = async () => {
  try {
    await connectDB();
    await resetCategories();
    console.log('\n✅ Script completed successfully!');
  } catch (error) {
    console.error('\n❌ Script failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
};

// Run the script
if (require.main === module) {
  main();
}

module.exports = { resetCategories, connectDB };