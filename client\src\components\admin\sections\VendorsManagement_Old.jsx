import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Input,
  Typography,
  Form
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined
} from '@ant-design/icons';

// Import sub-components
import VendorStatistics from './vendors/VendorStatistics';
import VendorTable from './vendors/VendorTable';
import VendorModal from './vendors/VendorModal';
import { useVendorActions } from './vendors/useVendorActions';

const { Title } = Typography;

const VendorsManagement = () => {
  const [vendors, setVendors] = useState([]);
  const [stats, setStats] = useState({});
  const [modalVisible, setModalVisible] = useState(false);
  const [editingVendor, setEditingVendor] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [filters, setFilters] = useState({
    status: '',
    search: searchText
  });

  const [form] = Form.useForm();
  
  // Use custom hook for vendor actions
  const {
    loading,
    fetchVendors: apiVendors,
    fetchStats: apiStats,
    approveVendor,
    suspendVendor,
    deleteVendor,
    createVendor,
    updateVendor
  } = useVendorActions();

  useEffect(() => {
    loadData();
  }, [pagination.current, pagination.pageSize, filters]);

  const loadData = async () => {
    // Fetch vendors
    const vendorsResult = await apiVendors(pagination, filters);
    setVendors(vendorsResult.vendors);
    setPagination(vendorsResult.pagination);

    // Fetch stats
    const statsResult = await apiStats();
    setStats(statsResult);
  };

  const fetchVendors = async () => {
    try {
      setLoading(true);
      
      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        ...filters
      };

      const response = await vendorsApi.getVendors(params);
      if (response.data.success) {
        setVendors(response.data.data.vendors || []);
        setPagination(prev => ({
          ...prev,
          total: response.data.data.pagination?.totalVendors || 0
        }));
      } else {
        setVendors([]);
        message.error(response.data.message || 'Failed to fetch vendors');
      }
    } catch (error) {
      console.error('Failed to fetch vendors:', error);
      setVendors([]);
      message.error('Failed to fetch vendors. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchPendingVendors = async () => {
    try {
      const response = await vendorsApi.getPendingVerification();
      if (response.data.success) {
        setPendingVendors(response.data.data.vendors);
      }
    } catch (error) {
      console.error('Failed to fetch pending vendors:', error);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await vendorsApi.getStatistics();
      if (response.data.success) {
        setStats(response.data.data);
      }
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  const handleVerifyVendor = async (vendorId) => {
    try {
      const values = await verificationForm.validateFields();
      const response = await vendorsApi.verifyVendor(vendorId, values);

      if (response.data.success) {
        message.success('Vendor verified successfully');
        setVerificationModalVisible(false);
        verificationForm.resetFields();
        fetchVendors();
        fetchPendingVendors();
        fetchStats();
      }
    } catch (error) {
      message.error(error.response?.data?.message || 'Failed to verify vendor');
    }
  };

  const handleRejectVendor = async (vendorId) => {
    try {
      const values = await verificationForm.validateFields();
      const response = await vendorsApi.rejectVerification(vendorId, values);

      if (response.data.success) {
        message.success('Vendor verification rejected');
        setVerificationModalVisible(false);
        verificationForm.resetFields();
        fetchVendors();
        fetchPendingVendors();
        fetchStats();
      }
    } catch (error) {
      message.error(error.response?.data?.message || 'Failed to reject vendor');
    }
  };

  const handleProcessPayout = async (vendorId) => {
    try {
      const values = await payoutForm.validateFields();
      const response = await vendorsApi.processPayout(vendorId, values);

      if (response.data.success) {
        message.success('Payout processed successfully');
        setPayoutModalVisible(false);
        payoutForm.resetFields();
        fetchVendors();
      }
    } catch (error) {
      message.error(error.response?.data?.message || 'Failed to process payout');
    }
  };

  const handleUpdateCommission = async (vendorId, commissionData) => {
    try {
      const response = await vendorsApi.updateCommission(vendorId, commissionData);

      if (response.data.success) {
        message.success('Commission updated successfully');
        fetchVendors();
      }
    } catch (error) {
      message.error(error.response?.data?.message || 'Failed to update commission');
    }
  };

  const handleVerificationAction = (vendor, action) => {
    setSelectedVendor(vendor);
    setVerificationModalVisible(true);

    if (action === 'verify') {
      verificationForm.setFieldsValue({ notes: '' });
    } else if (action === 'reject') {
      verificationForm.setFieldsValue({ reason: '', notes: '' });
    }
  };

  const handlePayoutAction = (vendor) => {
    setSelectedVendor(vendor);
    setPayoutModalVisible(true);
    payoutForm.setFieldsValue({
      amount: vendor.commission?.pendingAmount || 0,
      method: 'bank_transfer'
    });
  };

  const handleAddVendor = () => {
    setEditingVendor(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditVendor = (vendor) => {
    setEditingVendor(vendor);
    form.setFieldsValue(vendor);
    setModalVisible(true);
  };

  const handleDeleteVendor = async (vendorId) => {
    try {
      setVendors(vendors.filter(vendor => vendor.id !== vendorId));
      message.success('Vendor deleted successfully');
    } catch (error) {
      message.error('Failed to delete vendor');
    }
  };

  const handleApproveVendor = async (vendorId) => {
    try {
      setVendors(vendors.map(vendor =>
        vendor.id === vendorId
          ? { ...vendor, status: 'approved' }
          : vendor
      ));
      message.success('Vendor approved successfully');
    } catch (error) {
      message.error('Failed to approve vendor');
    }
  };

  const handleSuspendVendor = async (vendorId) => {
    try {
      setVendors(vendors.map(vendor =>
        vendor.id === vendorId
          ? { ...vendor, status: 'suspended' }
          : vendor
      ));
      message.success('Vendor suspended successfully');
    } catch (error) {
      message.error('Failed to suspend vendor');
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingVendor) {
        setVendors(vendors.map(vendor =>
          vendor.id === editingVendor.id
            ? { ...vendor, ...values }
            : vendor
        ));
        message.success('Vendor updated successfully');
      } else {
        const newVendor = {
          id: Date.now(),
          ...values,
          joinDate: new Date().toISOString().split('T')[0],
          rating: 0,
          totalSales: 0,
          productsCount: 0,
          status: 'pending'
        };
        setVendors([...vendors, newVendor]);
        message.success('Vendor added successfully');
      }
      
      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved':
        return 'green';
      case 'pending':
        return 'orange';
      case 'suspended':
        return 'red';
      default:
        return 'default';
    }
  };

  const getVerificationStatusColor = (status) => {
    const colors = {
      pending: 'processing',
      verified: 'success',
      rejected: 'error',
      suspended: 'warning'
    };
    return colors[status] || 'default';
  };

  const filteredVendors = vendors.filter(vendor =>
    vendor.businessName.toLowerCase().includes(searchText.toLowerCase()) ||
    vendor.contactPerson.toLowerCase().includes(searchText.toLowerCase()) ||
    vendor.email.toLowerCase().includes(searchText.toLowerCase())
  );

  const columns = [
    {
      title: 'Vendor',
      dataIndex: 'businessName',
      key: 'businessName',
      render: (text, record) => (
        <Space>
          <Avatar icon={<ShopOutlined />} style={{ backgroundColor: '#52c41a' }} />
          <div>
            <div style={{ fontWeight: 500 }}>{text}</div>
            <div style={{ color: '#666', fontSize: '12px' }}>{record.contactPerson}</div>
            <div style={{ color: '#666', fontSize: '12px' }}>{record.email}</div>
          </div>
        </Space>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Rating',
      dataIndex: 'rating',
      key: 'rating',
      render: (rating) => (
        <Space>
          <Rate disabled defaultValue={rating} style={{ fontSize: '14px' }} />
          <span>({rating})</span>
        </Space>
      ),
    },
    {
      title: 'Products',
      dataIndex: 'productsCount',
      key: 'productsCount',
      align: 'center',
    },
    {
      title: 'Total Sales',
      dataIndex: 'totalSales',
      key: 'totalSales',
      render: (amount) => `$${amount.toLocaleString()}`,
    },
    {
      title: 'Commission',
      dataIndex: 'commission',
      key: 'commission',
      render: (commission) => `${commission}%`,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Space>
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditVendor(record)}
            >
              Edit
            </Button>
            {record.status === 'pending' && (
              <Button
                type="primary"
                size="small"
                icon={<CheckCircleOutlined />}
                onClick={() => handleApproveVendor(record.id)}
                style={{ backgroundColor: '#52c41a' }}
              >
                Approve
              </Button>
            )}
            {record.status === 'approved' && (
              <Button
                size="small"
                icon={<CloseCircleOutlined />}
                onClick={() => handleSuspendVendor(record.id)}
                danger
              >
                Suspend
              </Button>
            )}
          </Space>
          <Popconfirm
            title="Are you sure you want to delete this vendor?"
            onConfirm={() => handleDeleteVendor(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              type="primary"
              danger
              size="small"
              icon={<DeleteOutlined />}
              block
            >
              Delete
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const totalVendors = vendors.length;
  const approvedVendors = vendors.filter(vendor => vendor.status === 'approved').length;
  const pendingVendorsCount = vendors.filter(vendor => vendor.status === 'pending').length;
  const totalSales = vendors.reduce((sum, vendor) => sum + vendor.totalSales, 0);

  return (
    <div>
      <Title level={2}>Vendors Management</Title>
      
      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Vendors"
              value={totalVendors}
              prefix={<ShopOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Approved"
              value={approvedVendors}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Pending Approval"
              value={pendingVendorsCount}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Sales"
              value={totalSales}
              prefix={<DollarOutlined />}
              formatter={(value) => `$${value.toLocaleString()}`}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Input
            placeholder="Search vendors..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 300 }}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddVendor}
          >
            Add Vendor
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={filteredVendors}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} vendors`,
          }}
        />
      </Card>

      <Modal
        title={editingVendor ? 'Edit Vendor' : 'Add New Vendor'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'pending',
            commission: 15
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="businessName"
                label="Business Name"
                rules={[{ required: true, message: 'Please enter business name' }]}
              >
                <Input placeholder="Enter business name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="contactPerson"
                label="Contact Person"
                rules={[{ required: true, message: 'Please enter contact person' }]}
              >
                <Input placeholder="Enter contact person name" />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="email"
                label="Email"
                rules={[
                  { required: true, message: 'Please enter email' },
                  { type: 'email', message: 'Please enter valid email' }
                ]}
              >
                <Input placeholder="Enter email address" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="Phone"
                rules={[{ required: true, message: 'Please enter phone number' }]}
              >
                <Input placeholder="Enter phone number" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="status"
                label="Status"
                rules={[{ required: true, message: 'Please select status' }]}
              >
                <Select placeholder="Select status">
                  <Option value="pending">Pending</Option>
                  <Option value="approved">Approved</Option>
                  <Option value="suspended">Suspended</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="commission"
                label="Commission (%)"
                rules={[{ required: true, message: 'Please enter commission rate' }]}
              >
                <Input type="number" placeholder="Enter commission percentage" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="Description"
          >
            <TextArea rows={3} placeholder="Enter business description" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default VendorsManagement;