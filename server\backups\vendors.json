[{"_id": "687f6b2317a3a8ee2a1506f6", "user": "687f693f07ebe86aa767a6c6", "businessName": "<PERSON>'s Store", "businessDescription": "Welcome to my store! I'm excited to share my products with you.", "businessType": "individual", "logo": null, "banner": null, "businessAddress": {"street": "Not provided", "city": "Not provided", "state": "Not provided", "zipCode": "00000", "country": "Not provided"}, "contactInfo": {"businessPhone": "+***********", "businessEmail": "<EMAIL>"}, "bankDetails": {"accountHolderName": "Not provided", "bankName": "Not provided", "accountNumber": "Not provided", "routingNumber": "Not provided", "accountType": "checking"}, "verification": {"status": "verified", "documents": []}, "subscription": {"features": {"maxProducts": 100, "maxImages": 5, "analyticsAccess": false, "prioritySupport": false, "customBranding": false}, "plan": "basic", "status": "active", "startDate": "2025-07-22T10:42:43.114Z"}, "performance": {"rating": 0, "totalReviews": 0, "totalSales": 0, "totalRevenue": 0, "totalProducts": 1, "totalOrders": 0, "responseTime": 24, "fulfillmentRate": 100, "qualityScore": 75, "customerSatisfaction": 85, "monthlyStats": []}, "commission": "1", "onboarding": {"status": "not_started", "currentStep": "profile_setup", "progress": 0, "completedSteps": []}, "settings": {"businessHours": {"monday": {"closed": false}, "tuesday": {"closed": false}, "wednesday": {"closed": false}, "thursday": {"closed": false}, "friday": {"closed": false}, "saturday": {"closed": false}, "sunday": {"closed": true}}, "notifications": {"newOrders": true, "lowStock": true, "reviews": true, "messages": true}, "autoAcceptOrders": false, "processingTime": 2}, "status": "active", "createdAt": "2025-07-22T10:42:43.125Z", "updatedAt": "2025-07-26T05:23:05.971Z", "__v": 0}, {"_id": "687f937a691beb8bded8297f", "user": "687f90d4b3469708bd362e25", "businessName": "Test Vendor's Store", "businessDescription": "Welcome to my store! I'm excited to share my products with you.", "businessType": "individual", "logo": null, "banner": null, "businessAddress": {"street": "Not provided", "city": "Not provided", "state": "Not provided", "zipCode": "00000", "country": "Not provided"}, "contactInfo": {"businessPhone": "Not provided", "businessEmail": "<EMAIL>"}, "bankDetails": {"accountHolderName": "Not provided", "bankName": "Not provided", "accountNumber": "Not provided", "routingNumber": "Not provided", "accountType": "checking"}, "verification": {"status": "verified", "documents": []}, "subscription": {"features": {"maxProducts": 100, "maxImages": 5, "analyticsAccess": false, "prioritySupport": false, "customBranding": false}, "plan": "basic", "status": "active", "startDate": "2025-07-22T13:34:50.712Z"}, "performance": {"rating": 0, "totalReviews": 0, "totalSales": 0, "totalRevenue": 0, "totalProducts": 0, "totalOrders": 0, "responseTime": 24, "fulfillmentRate": 100, "qualityScore": 75, "customerSatisfaction": 85, "monthlyStats": []}, "commission": {"rate": 20}, "onboarding": {"status": "not_started", "currentStep": "profile_setup", "progress": 0, "completedSteps": []}, "settings": {"businessHours": {"monday": {"closed": false}, "tuesday": {"closed": false}, "wednesday": {"closed": false}, "thursday": {"closed": false}, "friday": {"closed": false}, "saturday": {"closed": false}, "sunday": {"closed": true}}, "notifications": {"newOrders": true, "lowStock": true, "reviews": true, "messages": true}, "autoAcceptOrders": false, "processingTime": 2}, "status": "active", "createdAt": "2025-07-22T13:34:50.715Z", "updatedAt": "2025-07-26T05:28:52.434Z", "__v": 0}, {"_id": "687f9423691beb8bded829a1", "user": "687f910bb3469708bd362e28", "businessName": "<PERSON>'s Store", "businessDescription": "Welcome to my store! I'm excited to share my products with you.", "businessType": "individual", "logo": null, "banner": null, "businessAddress": {"street": "Not provided", "city": "Not provided", "state": "Not provided", "zipCode": "00000", "country": "Not provided"}, "contactInfo": {"businessPhone": "+***********", "businessEmail": "<EMAIL>"}, "bankDetails": {"accountHolderName": "Not provided", "bankName": "Not provided", "accountNumber": "Not provided", "routingNumber": "Not provided", "accountType": "checking"}, "verification": {"status": "verified", "documents": []}, "subscription": {"features": {"maxProducts": 100, "maxImages": 5, "analyticsAccess": false, "prioritySupport": false, "customBranding": false}, "plan": "basic", "status": "active", "startDate": "2025-07-22T13:37:39.047Z"}, "performance": {"rating": 0, "totalReviews": 0, "totalSales": 0, "totalRevenue": 0, "totalProducts": 7, "totalOrders": 0, "responseTime": 24, "fulfillmentRate": 100, "qualityScore": 75, "customerSatisfaction": 85, "monthlyStats": []}, "commission": {"rate": 18}, "onboarding": {"status": "not_started", "currentStep": "profile_setup", "progress": 0, "completedSteps": []}, "settings": {"businessHours": {"monday": {"closed": false}, "tuesday": {"closed": false}, "wednesday": {"closed": false}, "thursday": {"closed": false}, "friday": {"closed": false}, "saturday": {"closed": false}, "sunday": {"closed": true}}, "notifications": {"newOrders": true, "lowStock": true, "reviews": true, "messages": true}, "autoAcceptOrders": false, "processingTime": 2}, "status": "active", "createdAt": "2025-07-22T13:37:39.057Z", "updatedAt": "2025-07-26T05:29:21.742Z", "__v": 0}, {"_id": "687fd3fa0bc86c687720d7e5", "user": "687fd3e40bc86c687720d7ca", "businessName": "<PERSON>'s Store", "businessDescription": "Welcome to my store! I'm excited to share my products with you.", "businessType": "individual", "logo": null, "banner": null, "businessAddress": {"street": "Not provided", "city": "Not provided", "state": "Not provided", "zipCode": "00000", "country": "Not provided"}, "contactInfo": {"businessPhone": "+***********", "businessEmail": "<EMAIL>"}, "bankDetails": {"accountHolderName": "Not provided", "bankName": "Not provided", "accountNumber": "Not provided", "routingNumber": "Not provided", "accountType": "checking"}, "verification": {"status": "verified", "documents": []}, "subscription": {"features": {"maxProducts": 100, "maxImages": 5, "analyticsAccess": false, "prioritySupport": false, "customBranding": false}, "plan": "basic", "status": "active", "startDate": "2025-07-22T18:10:02.495Z"}, "performance": {"rating": 0, "totalReviews": 0, "totalSales": 0, "totalRevenue": 0, "totalProducts": 2, "totalOrders": 0, "responseTime": 24, "fulfillmentRate": 100, "qualityScore": 75, "customerSatisfaction": 85, "monthlyStats": []}, "commission": {"rate": 15, "type": "percentage", "fixedAmount": 0, "totalEarned": 30000, "totalPaid": 15000, "pendingAmount": 15000, "payoutHistory": []}, "onboarding": {"status": "not_started", "currentStep": "profile_setup", "progress": 0, "completedSteps": []}, "settings": {"businessHours": {"monday": {"closed": false}, "tuesday": {"closed": false}, "wednesday": {"closed": false}, "thursday": {"closed": false}, "friday": {"closed": false}, "saturday": {"closed": false}, "sunday": {"closed": true}}, "notifications": {"newOrders": true, "lowStock": true, "reviews": true, "messages": true}, "autoAcceptOrders": false, "processingTime": 2}, "status": "active", "createdAt": "2025-07-22T18:10:02.499Z", "updatedAt": "2025-07-25T15:42:18.695Z", "__v": 0}, {"_id": "6881fd066652576061a0be6e", "user": "6881e45be98a7c89412be755", "businessName": "Guns", "businessDescription": "Testing data persistence across requests", "businessType": "individual", "logo": "https://res.cloudinary.com/alicartify/image/upload/v1753432368/ecommerce/vendors/logos/*************-oqx9u2aohq.jpg", "banner": null, "businessAddress": {"street": "123 Updated Test Street", "city": "Test City", "state": "TC", "zipCode": "12345", "country": "United States"}, "contactInfo": {"businessPhone": "******-0123", "businessEmail": "<EMAIL>", "website": "https://www.updatedtestbusiness.com", "socialMedia": {}}, "bankDetails": {"accountHolderName": "Not provided", "bankName": "Not provided", "accountNumber": "Not provided", "routingNumber": "Not provided", "accountType": "checking"}, "verification": {"status": "verified", "documents": []}, "subscription": {"features": {"maxProducts": 100, "maxImages": 5, "analyticsAccess": false, "prioritySupport": false, "customBranding": false}, "plan": "basic", "status": "active", "startDate": "2025-07-24T09:29:42.061Z"}, "performance": {"rating": 0, "totalReviews": 0, "totalSales": 0, "totalRevenue": 0, "totalProducts": 1, "totalOrders": 0, "responseTime": 24, "fulfillmentRate": 100, "qualityScore": 75, "customerSatisfaction": 85, "monthlyStats": []}, "commission": {"rate": 15, "type": "percentage", "fixedAmount": 0, "totalEarned": 30000, "totalPaid": 15000, "pendingAmount": 15000, "payoutHistory": []}, "onboarding": {"status": "not_started", "currentStep": "profile_setup", "progress": 0, "completedSteps": []}, "settings": {"autoAcceptOrders": false, "processingTime": 3, "currency": "INR", "returnPolicy": "Updated 30-day return policy", "shippingPolicy": "Updated free shipping on orders over $50", "businessHours": {"monday": {"closed": false}, "tuesday": {"closed": false}, "wednesday": {"closed": false}, "thursday": {"closed": false}, "friday": {"closed": false}, "saturday": {"closed": false}, "sunday": {"closed": true}}, "notifications": {"newOrders": true, "lowStock": true, "reviews": true, "messages": true}}, "status": "active", "createdAt": "2025-07-24T09:29:42.070Z", "updatedAt": "2025-07-25T15:43:13.147Z", "__v": 0, "businessRegistrationNumber": "BRN987654321", "taxId": "TAX123456789"}]