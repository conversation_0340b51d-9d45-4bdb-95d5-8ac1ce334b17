const { Notification, User } = require('../../models');
const NotificationService = require('../../services/notificationService');

/**
 * Get all notifications for admin
 */
const getNotifications = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    const { status, type } = req.query;

    const query = {};

    if (status && status !== 'all') {
      query.status = status;
    }

    if (type) {
      query.type = type;
    }

    const notifications = await Notification.find(query)
      .populate('recipient', 'firstName lastName email role')
      .populate('sender', 'firstName lastName email role')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    const totalNotifications = await Notification.countDocuments(query);
    const totalPages = Math.ceil(totalNotifications / limit);

    res.json({
      success: true,
      data: {
        notifications,
        pagination: {
          currentPage: page,
          totalPages,
          totalNotifications,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
          limit
        }
      }
    });

  } catch (error) {
    console.error('Get notifications error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch notifications',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get notification statistics
 */
const getNotificationStats = async (req, res) => {
  try {
    const stats = await Notification.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    const typeStats = await Notification.aggregate([
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 }
        }
      }
    ]);

    const recentStats = await Notification.aggregate([
      {
        $match: {
          createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 }
        }
      }
    ]);

    const result = {
      statusStats: {
        unread: 0,
        read: 0,
        archived: 0,
        total: 0
      },
      typeStats: {},
      recentStats: {},
      totalNotifications: 0
    };

    stats.forEach(stat => {
      result.statusStats[stat._id] = stat.count;
      result.statusStats.total += stat.count;
      result.totalNotifications += stat.count;
    });

    typeStats.forEach(stat => {
      result.typeStats[stat._id] = stat.count;
    });

    recentStats.forEach(stat => {
      result.recentStats[stat._id] = stat.count;
    });

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Get notification stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch notification statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Send system announcement
 */
const sendSystemAnnouncement = async (req, res) => {
  try {
    const { title, message, recipients, priority = 'medium', channels = ['in_app'] } = req.body;
    const adminId = req.user.userId;

    if (!title || !message) {
      return res.status(400).json({
        success: false,
        message: 'Title and message are required'
      });
    }

    let recipientUsers = [];

    if (recipients === 'all') {
      recipientUsers = await User.find({ role: { $ne: 'admin' } }).select('_id');
    } else if (recipients === 'vendors') {
      recipientUsers = await User.find({ role: 'vendor' }).select('_id');
    } else if (recipients === 'customers') {
      recipientUsers = await User.find({ role: 'customer' }).select('_id');
    } else if (Array.isArray(recipients)) {
      recipientUsers = await User.find({ _id: { $in: recipients } }).select('_id');
    } else {
      return res.status(400).json({
        success: false,
        message: 'Invalid recipients parameter'
      });
    }

    const notifications = recipientUsers.map(user => ({
      recipient: user._id,
      sender: adminId,
      type: 'system_announcement',
      title,
      message,
      priority,
      channels,
      data: {
        isSystemAnnouncement: true
      }
    }));

    const createdNotifications = await Promise.all(
      notifications.map(notif => NotificationService.createNotification(notif))
    );

    res.json({
      success: true,
      message: `System announcement sent to ${createdNotifications.length} users`,
      data: {
        sentCount: createdNotifications.length,
        recipients: recipients
      }
    });

  } catch (error) {
    console.error('Send system announcement error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send system announcement',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Mark notification as read
 */
const markAsRead = async (req, res) => {
  try {
    const { id } = req.params;

    const notification = await Notification.findById(id);
    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found'
      });
    }

    await notification.markAsRead();

    res.json({
      success: true,
      message: 'Notification marked as read',
      data: { notification }
    });

  } catch (error) {
    console.error('Mark notification as read error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark notification as read',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Archive notification
 */
const archiveNotification = async (req, res) => {
  try {
    const { id } = req.params;

    const notification = await Notification.findById(id);
    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found'
      });
    }

    await notification.archive();

    res.json({
      success: true,
      message: 'Notification archived',
      data: { notification }
    });

  } catch (error) {
    console.error('Archive notification error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to archive notification',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Delete notification
 */
const deleteNotification = async (req, res) => {
  try {
    const { id } = req.params;

    const notification = await Notification.findByIdAndDelete(id);
    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found'
      });
    }

    res.json({
      success: true,
      message: 'Notification deleted successfully'
    });

  } catch (error) {
    console.error('Delete notification error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete notification',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Bulk operations on notifications
 */
const bulkOperation = async (req, res) => {
  try {
    const { operation, notificationIds } = req.body;

    if (!operation || !Array.isArray(notificationIds) || notificationIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Operation and notification IDs are required'
      });
    }

    let result;

    switch (operation) {
      case 'mark_read':
        result = await Notification.updateMany(
          { _id: { $in: notificationIds } },
          {
            status: 'read',
            'deliveryStatus.in_app.read': true,
            'deliveryStatus.in_app.readAt': new Date()
          }
        );
        break;
      case 'archive':
        result = await Notification.updateMany(
          { _id: { $in: notificationIds } },
          { status: 'archived' }
        );
        break;
      case 'delete':
        result = await Notification.deleteMany({ _id: { $in: notificationIds } });
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid operation'
        });
    }

    res.json({
      success: true,
      message: `Bulk ${operation} completed successfully`,
      data: {
        modifiedCount: result.modifiedCount || result.deletedCount,
        operation
      }
    });

  } catch (error) {
    console.error('Bulk notification operation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to perform bulk operation',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getNotifications,
  getNotificationStats,
  sendSystemAnnouncement,
  markAsRead,
  archiveNotification,
  deleteNotification,
  bulkOperation
};