import React from 'react';
import { Card, Row, Col, Table, Badge, Typography, Button } from 'antd';
import { EyeOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

const { Text } = Typography;

const RecentActivityTables = ({ recentOrders = [], recentUsers = [] }) => {
  // Order table columns
  const orderColumns = [
    {
      title: 'Order #',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      render: (text) => <Text strong>{text}</Text>,
      responsive: ['sm']
    },
    {
      title: 'Customer',
      dataIndex: 'customer',
      key: 'customer',
      render: (customer) => customer ? `${customer.firstName} ${customer.lastName}` : 'N/A',
      responsive: ['md']
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Badge 
          status={status === 'delivered' ? 'success' : status === 'cancelled' ? 'error' : 'processing'} 
          text={status?.charAt(0).toUpperCase() + status?.slice(1)} 
        />
      )
    },
    {
      title: 'Total',
      dataIndex: ['pricing', 'total'],
      key: 'total',
      render: (total) => `$${total?.toFixed(2) || '0.00'}`,
      align: 'right'
    },
    {
      title: 'Date',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => dayjs(date).format('MMM DD, YYYY'),
      responsive: ['lg']
    }
  ];

  // User table columns
  const userColumns = [
    {
      title: 'Name',
      key: 'name',
      render: (record) => `${record.firstName || ''} ${record.lastName || ''}`.trim() || 'N/A'
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      responsive: ['md']
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      render: (role) => (
        <Badge 
          color={role === 'admin' ? 'red' : role === 'vendor' ? 'blue' : 'green'} 
          text={role?.charAt(0).toUpperCase() + role?.slice(1)} 
        />
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Badge 
          status={status === 'active' ? 'success' : 'error'} 
          text={status?.charAt(0).toUpperCase() + status?.slice(1)} 
        />
      ),
      responsive: ['sm']
    },
    {
      title: 'Joined',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => dayjs(date).format('MMM DD, YYYY'),
      responsive: ['lg']
    }
  ];

  return (
    <Row gutter={[16, 16]}>
      <Col xs={24} lg={12}>
        <Card
          title="Recent Orders"
          extra={
            <Button 
              type="link" 
              icon={<EyeOutlined />}
              size="small"
              onClick={() => {
                // Navigate to orders page
                window.location.href = '/admin/orders';
              }}
            >
              View All
            </Button>
          }
          style={{ 
            borderRadius: '12px', 
            border: 'none',
            boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
            height: '100%'
          }}
        >
          <Table
            dataSource={recentOrders}
            columns={orderColumns}
            pagination={false}
            size="small"
            rowKey="_id"
            locale={{
              emptyText: 'No recent orders'
            }}
            scroll={{ x: 400 }}
          />
        </Card>
      </Col>

      <Col xs={24} lg={12}>
        <Card
          title="Recent Users"
          extra={
            <Button 
              type="link" 
              icon={<EyeOutlined />}
              size="small"
              onClick={() => {
                // Navigate to users page
                window.location.href = '/admin/users';
              }}
            >
              View All
            </Button>
          }
          style={{ 
            borderRadius: '12px', 
            border: 'none',
            boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
            height: '100%'
          }}
        >
          <Table
            dataSource={recentUsers}
            columns={userColumns}
            pagination={false}
            size="small"
            rowKey="_id"
            locale={{
              emptyText: 'No recent users'
            }}
            scroll={{ x: 400 }}
          />
        </Card>
      </Col>
    </Row>
  );
};

export default RecentActivityTables;
