import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Select,
  Typography,
  Row,
  Col,
  Divider,
  message,
  Upload,
  Avatar,
  Space,
  Tabs,
  InputNumber,
  Spin
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  ShopOutlined,
  UserOutlined,
  SecurityScanOutlined,
  CameraOutlined
} from '@ant-design/icons';
import vendorApi, { storeApi } from '../../../services/vendorApi';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const Settings = () => {
  const [businessForm] = Form.useForm();
  const [profileForm] = Form.useForm();
  const [securityForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [profileLoading, setProfileLoading] = useState(true);
  const [vendorProfile, setVendorProfile] = useState(null);

  useEffect(() => {
    fetchVendorProfile();
  }, []);

  const fetchVendorProfile = async () => {
    try {
      setProfileLoading(true);
      const response = await storeApi.getProfile();
      if (response.data.success) {
        const profile = response.data.data;
        setVendorProfile(profile);

        // Set business form values
        businessForm.setFieldsValue({
          businessName: profile.businessName || '',
          businessDescription: profile.businessDescription || '',
          businessType: profile.businessType || 'individual',
          contactPerson: profile.user?.firstName + ' ' + profile.user?.lastName || '',
          businessEmail: profile.contactInfo?.businessEmail || profile.user?.email || '',
          businessPhone: profile.contactInfo?.businessPhone || profile.user?.phone || '',
          website: profile.contactInfo?.website || '',
          street: profile.businessAddress?.street || '',
          city: profile.businessAddress?.city || '',
          state: profile.businessAddress?.state || '',
          zipCode: profile.businessAddress?.zipCode || '',
          country: profile.businessAddress?.country || '',
          returnPolicy: profile.settings?.returnPolicy || '',
          shippingPolicy: profile.settings?.shippingPolicy || '',
          minimumOrderAmount: profile.settings?.minimumOrderAmount || 0,
          processingTime: profile.settings?.processingTime || 2,
          taxId: profile.taxId || '',
          businessLicense: profile.businessRegistrationNumber || ''
        });

        // Set profile form values with enhanced address fields
        profileForm.setFieldsValue({
          firstName: profile.user?.firstName || '',
          lastName: profile.user?.lastName || '',
          email: profile.user?.email || '',
          phone: profile.user?.phone || '',
          countryCode: profile.user?.countryCode || '',
          address: profile.user?.address || '',
          city: profile.user?.city || '',
          state: profile.user?.state || '',
          zipCode: profile.user?.zipCode || '',
          country: profile.user?.country || '',
          language: profile.user?.preferences?.language || 'en',
          currency: profile.user?.preferences?.currency || 'INR'
        });
      }
    } catch (error) {
      console.error('Error fetching vendor profile:', error);
      message.error('Failed to load profile data');
    } finally {
      setProfileLoading(false);
    }
  };

  const handleSaveSettings = async (formType, values) => {
    setLoading(true);
    try {
      if (formType === 'Business') {
        // Format business data for backend
        const businessData = {
          businessName: values.businessName,
          businessDescription: values.businessDescription,
          businessType: values.businessType,
          contactInfo: {
            businessEmail: values.businessEmail,
            businessPhone: values.businessPhone,
            website: values.website
          },
          businessAddress: {
            street: values.street,
            city: values.city,
            state: values.state,
            zipCode: values.zipCode,
            country: values.country
          },
          settings: {
            returnPolicy: values.returnPolicy,
            shippingPolicy: values.shippingPolicy,
            minimumOrderAmount: values.minimumOrderAmount,
            processingTime: values.processingTime
          },
          taxId: values.taxId,
          businessRegistrationNumber: values.businessLicense
        };
        await storeApi.updateProfile(businessData);
        
      } else if (formType === 'Profile') {
        // Format user profile data for backend  
        const profileData = {
          firstName: values.firstName,
          lastName: values.lastName,
          email: values.email,
          phone: values.phone,
          countryCode: values.countryCode,
          address: values.address,
          city: values.city,
          state: values.state,
          zipCode: values.zipCode,
          country: values.country,
          preferences: {
            language: values.language,
            currency: values.currency
          }
        };
        await storeApi.updateProfile(profileData);
        
      } else if (formType === 'Security') {
        // Handle password reset
        if (values.newPassword && values.currentPassword) {
          if (values.newPassword !== values.confirmPassword) {
            message.error('New passwords do not match');
            return;
          }
          
          const securityData = {
            security: {
              currentPassword: values.currentPassword,
              newPassword: values.newPassword
            }
          };
          await storeApi.updateSettings(securityData);
          
          // Clear form after successful password update
          securityForm.resetFields();
        }
      }

      message.success(`${formType} settings saved successfully`);

      // Refresh profile data after business or profile updates
      if (formType === 'Business' || formType === 'Profile') {
        await fetchVendorProfile();
      }
      
    } catch (error) {
      console.error('Error saving settings:', error);
      const errorMessage = error.response?.data?.message || 'Failed to save settings';
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleLogoUpload = async (file) => {
    const formData = new FormData();
    formData.append('logo', file);
    
    try {
      setLoading(true);
      const response = await storeApi.uploadLogo(formData);
      if (response.data.success) {
        message.success('Logo uploaded successfully');
        await fetchVendorProfile();
      }
    } catch (error) {
      console.error('Logo upload error:', error);
      message.error('Failed to upload logo');
    } finally {
      setLoading(false);
    }
    return false; // Prevent default upload behavior
  };

  const handleAvatarUpload = async (file) => {
    const formData = new FormData();
    formData.append('avatar', file);
    
    try {
      setLoading(true);
      const response = await storeApi.uploadAvatar(formData);
      if (response.data.success) {
        message.success('Avatar uploaded successfully');
        await fetchVendorProfile();
      }
    } catch (error) {
      console.error('Avatar upload error:', error);
      message.error('Failed to upload avatar');
    } finally {
      setLoading(false);
    }
    return false; // Prevent default upload behavior
  };

  const businessSettings = (
    <Card>
      {profileLoading ? (
        <div className="text-center py-8">
          <Spin size="large" />
          <p className="mt-4 text-gray-500">Loading profile data...</p>
        </div>
      ) : (
        <Form
          form={businessForm}
          layout="vertical"
          onFinish={(values) => handleSaveSettings('Business', values)}
          initialValues={{
            providesWarranty: true
          }}
        >
        <Row gutter={16}>
          <Col span={24} style={{ textAlign: 'center', marginBottom: 24 }}>
            <Avatar
              size={100}
              src={vendorProfile?.logo}
              icon={<ShopOutlined />}
              style={{ backgroundColor: '#52c41a' }}
            />
            <div style={{ marginTop: 8 }}>
              <Upload
                showUploadList={false}
                beforeUpload={handleLogoUpload}
                accept="image/*"
              >
                <Button icon={<CameraOutlined />} type="link" loading={loading}>
                  Change Logo
                </Button>
              </Upload>
            </div>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="businessName"
              label="Business Name"
              rules={[{ required: true, message: 'Please enter business name' }]}
            >
              <Input placeholder="Enter business name" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="businessType"
              label="Business Type"
              rules={[{ required: true, message: 'Please select business type' }]}
            >
              <Select placeholder="Select business type">
                <Option value="individual">Individual</Option>
                <Option value="company">Company</Option>
                <Option value="partnership">Partnership</Option>
                <Option value="corporation">Corporation</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="businessDescription"
          label="Business Description"
          rules={[{ required: true, message: 'Please enter business description' }]}
        >
          <TextArea rows={3} placeholder="Describe your business" />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="contactPerson"
              label="Contact Person"
              rules={[{ required: true, message: 'Please enter contact person' }]}
            >
              <Input placeholder="Enter contact person name" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="businessEmail"
              label="Business Email"
              rules={[
                { required: true, message: 'Please enter business email' },
                { type: 'email', message: 'Please enter valid email' }
              ]}
            >
              <Input placeholder="Enter business email" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="businessPhone"
              label="Business Phone"
              rules={[{ required: true, message: 'Please enter business phone' }]}
            >
              <Input placeholder="Enter business phone" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="website"
              label="Website"
            >
              <Input placeholder="Enter website URL" />
            </Form.Item>
          </Col>
        </Row>

        <Divider>Business Address</Divider>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="street"
              label="Street Address"
              rules={[{ required: true, message: 'Please enter street address' }]}
            >
              <Input placeholder="Enter street address" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="city"
              label="City"
              rules={[{ required: true, message: 'Please enter city' }]}
            >
              <Input placeholder="Enter city" />
            </Form.Item>
          </Col>
        </Row>
        
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="state"
              label="State/Province"
              rules={[{ required: true, message: 'Please enter state/province' }]}
            >
              <Input placeholder="Enter state/province" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="zipCode"
              label="Zip/Postal Code"
              rules={[{ required: true, message: 'Please enter zip/postal code' }]}
            >
              <Input placeholder="Enter zip/postal code" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="country"
              label="Country"
              rules={[{ required: true, message: 'Please enter country' }]}
            >
              <Input placeholder="Enter country" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="taxId"
              label="Tax ID"
            >
              <Input placeholder="Enter tax identification number" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="businessLicense"
              label="Business License"
            >
              <Input placeholder="Enter business license number" />
            </Form.Item>
          </Col>
        </Row>

        <Divider>Business Policies</Divider>

        <Form.Item
          name="returnPolicy"
          label="Return Policy"
        >
          <TextArea rows={2} placeholder="Describe your return policy" />
        </Form.Item>

        <Form.Item
          name="shippingPolicy"
          label="Shipping Policy"
        >
          <TextArea rows={2} placeholder="Describe your shipping policy" />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="minimumOrderAmount"
              label="Minimum Order Amount ($)"
            >
              <InputNumber
                min={0}
                style={{ width: '100%' }}
                placeholder="Enter minimum order amount"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="processingTime"
              label="Processing Time (days)"
            >
              <InputNumber
                min={1}
                max={30}
                style={{ width: '100%' }}
                placeholder="Enter processing time"
              />
            </Form.Item>
          </Col>
        </Row>


          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
              Save Business Settings
            </Button>
          </Form.Item>
        </Form>
      )}
    </Card>
  );

  const profileSettings = (
    <Card>
      {profileLoading ? (
        <div className="text-center py-8">
          <Spin size="large" />
          <p className="mt-4 text-gray-500">Loading profile data...</p>
        </div>
      ) : (
        <Form
          form={profileForm}
          layout="vertical"
          onFinish={(values) => handleSaveSettings('Profile', values)}
        >
        <Row gutter={16}>
          <Col span={24} style={{ textAlign: 'center', marginBottom: 24 }}>
            <Avatar
              size={100}
              src={vendorProfile?.user?.avatar}
              icon={<UserOutlined />}
              style={{ backgroundColor: '#1890ff' }}
            />
            <div style={{ marginTop: 8 }}>
              <Upload
                showUploadList={false}
                beforeUpload={handleAvatarUpload}
                accept="image/*"
              >
                <Button icon={<CameraOutlined />} type="link" loading={loading}>
                  Change Photo
                </Button>
              </Upload>
            </div>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="firstName"
              label="First Name"
              rules={[{ required: true, message: 'Please enter first name' }]}
            >
              <Input placeholder="Enter first name" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="lastName"
              label="Last Name"
              rules={[{ required: true, message: 'Please enter last name' }]}
            >
              <Input placeholder="Enter last name" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="email"
              label="Email"
              rules={[
                { required: true, message: 'Please enter email' },
                { type: 'email', message: 'Please enter valid email' }
              ]}
            >
              <Input placeholder="Enter email address" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="phone"
              label="Phone"
              rules={[{ required: true, message: 'Please enter phone number' }]}
            >
              <Input placeholder="Enter phone number" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="address"
          label="Street Address"
        >
          <TextArea rows={2} placeholder="Enter your street address" />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="city"
              label="City"
            >
              <Input placeholder="Enter city" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="state"
              label="State/Province"
            >
              <Input placeholder="Enter state/province" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="zipCode"
              label="Zip/Postal Code"
            >
              <Input placeholder="Enter zip code" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="country"
              label="Country"
            >
              <Input placeholder="Enter country" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="countryCode"
              label="Country Code"
            >
              <Input placeholder="e.g. +1, +91" maxLength={5} />
            </Form.Item>
          </Col>
        </Row>

        <Divider>Preferences</Divider>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="language"
              label="Language"
            >
              <Select placeholder="Select language">
                <Option value="en">English</Option>
                <Option value="es">Spanish</Option>
                <Option value="fr">French</Option>
                <Option value="de">German</Option>
                <Option value="hi">Hindi</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="currency"
              label="Preferred Currency"
            >
              <Select placeholder="Select currency">
                <Option value="INR">INR - Indian Rupee</Option>
                <Option value="USD">USD - US Dollar</Option>
                <Option value="EUR">EUR - Euro</Option>
                <Option value="GBP">GBP - British Pound</Option>
                <Option value="CAD">CAD - Canadian Dollar</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
              Save Profile Settings
            </Button>
          </Form.Item>
        </Form>
      )}
    </Card>
  );


  const securitySettings = (
    <Card>
      <Form
        form={securityForm}
        layout="vertical"
        onFinish={(values) => handleSaveSettings('Security', values)}
        initialValues={{
          twoFactorAuth: false,
          loginAlerts: true,
          sessionTimeout: 30
        }}
      >
        <Divider>Password Security</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="currentPassword"
              label="Current Password"
            >
              <Input.Password placeholder="Enter current password" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="newPassword"
              label="New Password"
            >
              <Input.Password placeholder="Enter new password" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="confirmPassword"
              label="Confirm New Password"
            >
              <Input.Password placeholder="Confirm new password" />
            </Form.Item>
          </Col>
        </Row>


        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
            Save Security Settings
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );

  const tabItems = [
    {
      key: 'business',
      label: (
        <span>
          <ShopOutlined />
          Business Info
        </span>
      ),
      children: businessSettings,
    },
    {
      key: 'profile',
      label: (
        <span>
          <UserOutlined />
          Profile
        </span>
      ),
      children: profileSettings,
    },
    {
      key: 'security',
      label: (
        <span>
          <SecurityScanOutlined />
          Security
        </span>
      ),
      children: securitySettings,
    },
  ];

  return (
    <div>
      <Title level={2}>
        <SettingOutlined /> Vendor Settings
      </Title>
      <Tabs defaultActiveKey="business" items={tabItems} />
    </div>
  );
};

export default Settings;