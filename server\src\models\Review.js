const mongoose = require('mongoose');

const reviewSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: [true, 'Product is required'],
    index: true
  },
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Customer is required'],
    index: true
  },
  vendor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Vendor is required'],
    index: true
  },
  rating: {
    type: Number,
    required: [true, 'Rating is required'],
    min: [1, 'Rating must be at least 1'],
    max: [5, 'Rating cannot exceed 5']
  },
  comment: {
    type: String,
    required: [true, 'Review comment is required'],
    trim: true,
    maxlength: [300, 'Review comment cannot exceed 300 characters'],
    minlength: [10, 'Review comment must be at least 10 characters']
  },
  status: {
    type: String,
    enum: ['active', 'hidden', 'flagged'],
    default: 'active'
  },
  isVerifiedPurchase: {
    type: Boolean,
    default: false
  },
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    default: null
  },
  helpfulVotes: {
    type: Number,
    default: 0
  },
  replies: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ReviewReply'
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
reviewSchema.index({ product: 1, customer: 1 }, { unique: true }); // One review per customer per product
reviewSchema.index({ product: 1, rating: -1 });
reviewSchema.index({ vendor: 1, createdAt: -1 });
reviewSchema.index({ customer: 1, createdAt: -1 });
reviewSchema.index({ status: 1 });
reviewSchema.index({ isVerifiedPurchase: 1 });

// Virtual for formatted date
reviewSchema.virtual('formattedDate').get(function() {
  return this.createdAt.toDateString();
});

// Static method to get average rating for a product
reviewSchema.statics.getProductRating = async function(productId) {
  const result = await this.aggregate([
    { $match: { product: new mongoose.Types.ObjectId(productId), status: 'active' } },
    {
      $group: {
        _id: null,
        averageRating: { $avg: '$rating' },
        totalReviews: { $sum: 1 },
        ratingDistribution: {
          $push: '$rating'
        }
      }
    }
  ]);

  if (result.length === 0) {
    return {
      averageRating: 0,
      totalReviews: 0,
      ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
    };
  }

  const data = result[0];
  const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
  
  data.ratingDistribution.forEach(rating => {
    distribution[rating] = (distribution[rating] || 0) + 1;
  });

  return {
    averageRating: Math.round(data.averageRating * 10) / 10,
    totalReviews: data.totalReviews,
    ratingDistribution: distribution
  };
};

// Static method to get vendor reviews
reviewSchema.statics.getVendorReviews = function(vendorId, options = {}) {
  const { page = 1, limit = 10, status = 'active' } = options;
  const skip = (page - 1) * limit;

  return this.find({ vendor: vendorId, status })
    .populate('customer', 'firstName lastName avatar')
    .populate('product', 'name images')
    .populate('replies')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

// Static method to check if customer can review product
reviewSchema.statics.canCustomerReview = async function(customerId, productId) {
  try {
    // Validate input parameters
    if (!customerId || !productId) {
      return false;
    }
    
    // Check for existing review (including all statuses)
    const existingReview = await this.findOne({
      customer: customerId,
      product: productId
    });
    
    // Return true if no existing review found, false otherwise
    return !existingReview;
  } catch (error) {
    console.error('Error checking if customer can review:', error);
    return false;
  }
};

module.exports = mongoose.model('Review', reviewSchema);
