import React from 'react';
import { Modal, Form, Input, Button, Space, Avatar, Rate, Typography } from 'antd';
import { UserOutlined, SendOutlined, EditOutlined } from '@ant-design/icons';

const { TextArea } = Input;
const { Text } = Typography;

const ReviewModals = ({
  // Reply Modal Props
  replyModalVisible,
  selectedReview,
  replyForm,
  onReplySubmit,
  onReplyCancel,
  
  // Edit Reply Modal Props
  editReplyModalVisible,
  selectedReply,
  editReplyForm,
  onReplyUpdate,
  onEditReplyCancel,
  
  // Common Props
  submitting = false
}) => {
  // Format date helper
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <>
      {/* Reply Modal */}
      <Modal
        title="Reply to Review"
        open={replyModalVisible}
        onCancel={onReplyCancel}
        footer={null}
        width={600}
        className="reply-modal"
      >
        {selectedReview && (
          <div className="bg-gray-50 rounded-lg p-4 mb-6 border border-gray-200">
            <div className="flex items-center gap-3 mb-3">
              <Avatar
                src={selectedReview.customer?.avatar}
                icon={<UserOutlined />}
              />
              <div className="flex flex-col gap-1">
                <Text strong className="text-gray-900">
                  {selectedReview.customer?.firstName} {selectedReview.customer?.lastName}
                </Text>
                <div className="flex items-center gap-2">
                  <Rate disabled value={selectedReview.rating} size="small" />
                  <Text type="secondary" className="text-xs ml-2">
                    {formatDate(selectedReview.createdAt)}
                  </Text>
                </div>
              </div>
            </div>
            <div className="bg-white p-3 rounded-md border-l-4 border-blue-500">
              <Text className="text-gray-700">"{selectedReview.comment}"</Text>
            </div>
          </div>
        )}
        
        <Form
          form={replyForm}
          onFinish={onReplySubmit}
          layout="vertical"
          className="reply-form"
        >
          <Form.Item
            name="message"
            label="Your Reply"
            rules={[
              { required: true, message: 'Please enter your reply' },
              { min: 10, message: 'Reply must be at least 10 characters' },
              { max: 500, message: 'Reply cannot exceed 500 characters' }
            ]}
          >
            <TextArea
              rows={4}
              placeholder="Write a professional response to this review..."
              showCount
              maxLength={500}
            />
          </Form.Item>
          <Form.Item className="form-actions">
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={submitting}
                icon={<SendOutlined />}
              >
                Post Reply
              </Button>
              <Button onClick={onReplyCancel}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Edit Reply Modal */}
      <Modal
        title="Edit Reply"
        open={editReplyModalVisible}
        onCancel={onEditReplyCancel}
        footer={null}
        width={600}
        className="edit-reply-modal"
      >
        <Form
          form={editReplyForm}
          onFinish={onReplyUpdate}
          layout="vertical"
          className="edit-reply-form"
        >
          <Form.Item
            name="message"
            label="Edit Reply"
            rules={[
              { required: true, message: 'Please enter your reply' },
              { min: 10, message: 'Reply must be at least 10 characters' },
              { max: 500, message: 'Reply cannot exceed 500 characters' }
            ]}
          >
            <TextArea
              rows={4}
              showCount
              maxLength={500}
            />
          </Form.Item>
          <Form.Item className="form-actions">
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={submitting}
                icon={<EditOutlined />}
              >
                Update Reply
              </Button>
              <Button onClick={onEditReplyCancel}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default ReviewModals;
