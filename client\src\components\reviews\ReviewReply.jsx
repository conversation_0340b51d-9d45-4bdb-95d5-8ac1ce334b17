import React from 'react';
import { Avatar, Tag } from 'antd';
import { ShopOutlined, CalendarOutlined } from '@ant-design/icons';
import './ReviewReply.css';

const ReviewReply = ({ reply }) => {
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="review-reply">
      <div className="reply-header">
        <div className="vendor-info">
          <Avatar 
            src={reply.vendor?.avatar} 
            icon={<ShopOutlined />}
            size="small"
            className="vendor-avatar"
          />
          <div className="vendor-details">
            <span className="vendor-name">
              {reply.vendor?.businessName || 'Vendor'}
            </span>
            <Tag color="blue" className="vendor-tag">
              Seller
            </Tag>
          </div>
        </div>
        <div className="reply-meta">
          <span className="reply-date">
            <CalendarOutlined /> {formatDate(reply.createdAt)}
          </span>
          {reply.isEdited && (
            <Tag color="orange" size="small">
              Edited
            </Tag>
          )}
        </div>
      </div>
      
      <div className="reply-content">
        <p>{reply.message}</p>
      </div>
    </div>
  );
};

export default ReviewReply;
