const mongoose = require('mongoose');

const shippingSchema = new mongoose.Schema({
  weight: {
    type: Number,
    min: 0
  },
  dimensions: {
    length: Number,
    width: Number,
    height: Number
  },
  shippingClass: {
    type: String,
    enum: ['standard', 'heavy', 'fragile', 'digital'],
    default: 'standard'
  },
  freeShipping: {
    type: Boolean,
    default: false
  },
  shippingCost: {
    type: Number,
    min: 0,
    default: 0
  }
}, { _id: false });

module.exports = { shippingSchema };
