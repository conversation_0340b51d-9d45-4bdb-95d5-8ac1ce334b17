const mongoose = require('mongoose');

const returnPolicySchema = new mongoose.Schema({
  returnsAccepted: {
    type: Boolean,
    default: true,
    required: true
  },
  returnWindow: {
    type: Number,
    min: 0,
    max: 365,
    default: 30,
    required: function() { return this.returnsAccepted; }
  },
  returnConditions: [{
    type: String,
    enum: ['unused', 'original_packaging', 'with_tags', 'no_damage', 'with_receipt'],
    default: ['unused', 'original_packaging']
  }],
  restockingFee: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  returnShippingCost: {
    type: String,
    enum: ['buyer_pays', 'seller_pays', 'shared'],
    default: 'buyer_pays'
  },
  customReturnPolicy: {
    type: String,
    trim: true,
    maxlength: [1000, 'Custom return policy cannot exceed 1000 characters']
  }
}, { _id: false });

module.exports = { returnPolicySchema };
