const { body } = require('express-validator');

const orderTrackingValidator = [
  body('orderId')
    .notEmpty()
    .withMessage('Order ID is required')
    .isMongoId()
    .withMessage('Invalid order ID'),
  
  body('carrier.name')
    .optional()
    .isString()
    .withMessage('Carrier name must be a string')
    .isLength({ min: 1, max: 100 })
    .withMessage('Carrier name must be between 1 and 100 characters'),
  
  body('carrier.code')
    .optional()
    .isString()
    .withMessage('Carrier code must be a string')
    .isLength({ min: 1, max: 20 })
    .withMessage('Carrier code must be between 1 and 20 characters'),
  
  body('carrier.contactInfo.phone')
    .optional()
    .isMobilePhone()
    .withMessage('Invalid phone number'),
  
  body('carrier.contactInfo.website')
    .optional()
    .isURL()
    .withMessage('Invalid website URL'),
  
  body('carrier.contactInfo.email')
    .optional()
    .isEmail()
    .withMessage('Invalid email address'),
  
  body('estimatedDelivery')
    .optional()
    .isISO8601()
    .withMessage('Invalid estimated delivery date'),
  
  body('deliveryAddress.street')
    .optional()
    .isString()
    .withMessage('Street must be a string')
    .isLength({ min: 1, max: 200 })
    .withMessage('Street must be between 1 and 200 characters'),
  
  body('deliveryAddress.city')
    .optional()
    .isString()
    .withMessage('City must be a string')
    .isLength({ min: 1, max: 100 })
    .withMessage('City must be between 1 and 100 characters'),
  
  body('deliveryAddress.state')
    .optional()
    .isString()
    .withMessage('State must be a string')
    .isLength({ min: 1, max: 100 })
    .withMessage('State must be between 1 and 100 characters'),
  
  body('deliveryAddress.zipCode')
    .optional()
    .isString()
    .withMessage('Zip code must be a string')
    .isLength({ min: 1, max: 20 })
    .withMessage('Zip code must be between 1 and 20 characters'),
  
  body('deliveryAddress.country')
    .optional()
    .isString()
    .withMessage('Country must be a string')
    .isLength({ min: 1, max: 100 })
    .withMessage('Country must be between 1 and 100 characters'),
  
  body('recipient.name')
    .optional()
    .isString()
    .withMessage('Recipient name must be a string')
    .isLength({ min: 1, max: 100 })
    .withMessage('Recipient name must be between 1 and 100 characters'),
  
  body('recipient.phone')
    .optional()
    .isMobilePhone()
    .withMessage('Invalid recipient phone number'),
  
  body('recipient.email')
    .optional()
    .isEmail()
    .withMessage('Invalid recipient email address'),
  
  body('vendorId')
    .optional()
    .isMongoId()
    .withMessage('Invalid vendor ID')
];

const statusUpdateValidator = [
  body('status')
    .notEmpty()
    .withMessage('Status is required')
    .isIn(['order_confirmed', 'processing', 'shipped', 'out_for_delivery', 'delivered', 'cancelled', 'returned'])
    .withMessage('Invalid status value'),
  
  body('title')
    .optional()
    .isString()
    .withMessage('Title must be a string')
    .isLength({ min: 1, max: 200 })
    .withMessage('Title must be between 1 and 200 characters'),
  
  body('description')
    .optional()
    .isString()
    .withMessage('Description must be a string')
    .isLength({ min: 1, max: 500 })
    .withMessage('Description must be between 1 and 500 characters'),
  
  body('location.city')
    .optional()
    .isString()
    .withMessage('Location city must be a string')
    .isLength({ min: 1, max: 100 })
    .withMessage('Location city must be between 1 and 100 characters'),
  
  body('location.state')
    .optional()
    .isString()
    .withMessage('Location state must be a string')
    .isLength({ min: 1, max: 100 })
    .withMessage('Location state must be between 1 and 100 characters'),
  
  body('location.country')
    .optional()
    .isString()
    .withMessage('Location country must be a string')
    .isLength({ min: 1, max: 100 })
    .withMessage('Location country must be between 1 and 100 characters'),
  
  body('location.coordinates.latitude')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Invalid latitude'),
  
  body('location.coordinates.longitude')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Invalid longitude'),
  
  body('timestamp')
    .optional()
    .isISO8601()
    .withMessage('Invalid timestamp'),
  
  body('metadata.carrierStatusCode')
    .optional()
    .isString()
    .withMessage('Carrier status code must be a string'),
  
  body('metadata.carrierMessage')
    .optional()
    .isString()
    .withMessage('Carrier message must be a string')
    .isLength({ max: 500 })
    .withMessage('Carrier message must not exceed 500 characters'),
  
  body('metadata.estimatedDelivery')
    .optional()
    .isISO8601()
    .withMessage('Invalid estimated delivery date'),
  
  body('metadata.actualDelivery')
    .optional()
    .isISO8601()
    .withMessage('Invalid actual delivery date')
];

const noteValidator = [
  body('message')
    .notEmpty()
    .withMessage('Message is required')
    .isString()
    .withMessage('Message must be a string')
    .isLength({ min: 1, max: 1000 })
    .withMessage('Message must be between 1 and 1000 characters'),
  
  body('isPublic')
    .optional()
    .isBoolean()
    .withMessage('isPublic must be a boolean')
];

module.exports = {
  orderTrackingValidator,
  statusUpdateValidator,
  noteValidator
};
