import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Modal, Spin, Alert } from 'antd';
import { StarOutlined, GiftOutlined, AudioOutlined, CrownOutlined, ContactsOutlined, HomeOutlined, CloseOutlined, AppstoreOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { ProductInfo } from '../../utils/Api';
import { categoriesApi } from '../../services/publicApi';
import { getHomepageData } from '../../services/homepageApi';

const CatergoriesMenu = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState([]);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [categoriesError, setCategoriesError] = useState(null);
  const navigate = useNavigate();
  const isMountedRef = useRef(true);
  const isLoadingRef = useRef(false);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Fetch categories on component mount with safeguards
  const fetchCategories = useCallback(async () => {
    // Prevent multiple simultaneous API calls
    if (isLoadingRef.current || !isMountedRef.current) {
      return;
    }
    
    isLoadingRef.current = true;
    
    try {
      console.log('Fetching categories from API...');
      
      // Set default categories first to avoid loading state
      if (isMountedRef.current) {
        setCategories(getDefaultCategories());
        setCategoriesLoading(false);
      }
      
      // Try to get categories from API in background
      try {
        const response = await categoriesApi.getCategories();
        
        console.log('Categories API response:', response);
        
        if (!isMountedRef.current) return;
        
        if (response?.data?.success && response.data.data?.categories?.length > 0) {
          console.log('Categories found:', response.data.data.categories.length);
          // Map categories to include icons and limit to top-level categories
          const mappedCategories = response.data.data.categories
            .filter(cat => cat && !cat.parent) // Only top-level categories
            .slice(0, 7) // Limit to 7 categories
            .map(cat => ({
              _id: cat._id || `cat-${Date.now()}-${Math.random()}`,
              name: cat.name || 'Unknown Category',
              slug: cat.slug || '',
              icon: getCategoryIcon(cat.name || '')
            }));
          
          console.log('Mapped categories:', mappedCategories);
          
          // Update with real categories from API
          if (isMountedRef.current && mappedCategories.length > 0) {
            setCategories(mappedCategories);
            setCategoriesError(null);
          }
        }
      } catch (apiError) {
        console.error('Error fetching categories:', apiError);
        
        // Keep default categories on API error
        if (isMountedRef.current) {
          setCategoriesError('Using default categories');
        }
      }
    } catch (error) {
      console.error('Unexpected error in fetchCategories:', error);
      // Fallback to defaults on any error
      if (isMountedRef.current) {
        setCategories(getDefaultCategories());
        setCategoriesLoading(false);
        setCategoriesError('Using default categories');
      }
    } finally {
      isLoadingRef.current = false;
    }
  }, []);

  // Get default categories as fallback
  const getDefaultCategories = () => [
    { _id: 'default-1', name: 'Electronics', slug: 'electronics', icon: getCategoryIcon('electronics') },
    { _id: 'default-2', name: 'Fashion', slug: 'fashion', icon: getCategoryIcon('fashion') },
    { _id: 'default-3', name: 'Home & Garden', slug: 'home-garden', icon: getCategoryIcon('home') },
    { _id: 'default-4', name: 'Sports', slug: 'sports', icon: getCategoryIcon('sports') },
    { _id: 'default-5', name: 'Automotive', slug: 'automotive', icon: getCategoryIcon('automotive') }
  ];

  // Fetch categories on component mount
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  // Fetch products when modal opens
  useEffect(() => {
    if (isModalOpen) {
      setLoading(true);
      ProductInfo()
        .then(data => {
          if (isMountedRef.current) {
            setProducts(data.slice(0, 8)); // Limit to 8 products for display
            setLoading(false);
          }
        })
        .catch(error => {
          console.error('Error fetching products:', error);
          if (isMountedRef.current) {
            setLoading(false);
          }
        });
    }
  }, [isModalOpen]);

  const getCategoryIcon = (categoryName) => {
    const name = categoryName.toLowerCase();
    if (name.includes('electronic') || name.includes('tech')) return <AudioOutlined />;
    if (name.includes('home') || name.includes('garden')) return <HomeOutlined />;
    if (name.includes('jewelry') || name.includes('watch') || name.includes('fashion')) return <CrownOutlined />;
    if (name.includes('apparel') || name.includes('clothing') || name.includes('accessories')) return <ContactsOutlined />;
    if (name.includes('craft') || name.includes('gift') || name.includes('decor')) return <GiftOutlined />;
    return <AppstoreOutlined />; // Default icon
  };

  const handleCategoryClick = (category) => {
    setSelectedCategory(category);
    setIsModalOpen(true);
  };

  const handleProductClick = (productId) => {
    navigate(`/product/${productId}`);
    setIsModalOpen(false);
  };

  if (categoriesLoading) {
    return (
      <div className="w-full max-w-full h-full">
        <Card className="w-full sm:w-64 md:w-56 lg:w-72 xl:w-80 max-w-full p-3 border border-gray-200 shadow-sm bg-white h-full rounded-lg">
          <div className="w-full h-full flex items-center justify-center">
            <Spin size="large" />
          </div>
        </Card>
      </div>
    );
  }

  if (categoriesError || categories.length === 0) {
    return (
      <div className="w-full max-w-full h-full">
        <Card className="w-full sm:w-64 md:w-56 lg:w-72 xl:w-80 max-w-full p-3 border border-gray-200 shadow-sm bg-white h-full rounded-lg">
          <div className="w-full h-full flex items-center justify-center">
            <Alert
              message="Categories unavailable"
              description="Unable to load categories at this time."
              type="info"
              showIcon
              size="small"
              className="w-full"
            />
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full max-w-full h-full">
      <Card className="w-full sm:w-64 md:w-56 lg:w-72 xl:w-80 max-w-full p-2 sm:p-3 border border-gray-200 shadow-sm bg-white h-full rounded-lg">
        <div className="h-full flex flex-col">
          <h3 className="text-base sm:text-lg font-semibold text-gray-800 mb-2 sm:mb-3 border-b border-gray-100 pb-1 sm:pb-2">
            Categories
          </h3>
          <div className="flex-1 overflow-y-auto">
            <ul className="space-y-0.5 sm:space-y-1">
              {categories.map((category, index) => (
                <li
                  key={category._id || index}
                  className="flex items-center justify-between cursor-pointer hover:bg-gray-50 rounded-md px-2 sm:px-3 py-1.5 sm:py-2 transition-colors duration-200"
                  onClick={() => handleCategoryClick(category)}
                >
                  <span className="flex items-center gap-1.5 sm:gap-2 text-gray-700 text-xs sm:text-sm font-medium min-w-0 flex-1">
                    <span className="text-sm sm:text-base flex-shrink-0">{category.icon}</span>
                    <span className="truncate">{category.name}</span>
                  </span>
                  <span className="text-gray-400 text-xs ml-1 flex-shrink-0">&gt;</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </Card>

      <Modal
        title={selectedCategory?.name || "Category Products"}
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        footer={null}
        width={800}
        closeIcon={<CloseOutlined />}
      >
        <div className="flex flex-col md:flex-row">
          {/* Left side - Categories */}
          <div className="w-full md:w-1/3 border-r border-gray-200 pr-4">
            <ul className="space-y-2">
              {categories.map((category, index) => (
                <li
                  key={category._id || index}
                  className={`flex items-center cursor-pointer hover:bg-gray-100 rounded px-2 py-2 ${selectedCategory?._id === category._id ? 'bg-blue-50 text-blue-600' : ''}`}
                  onClick={() => setSelectedCategory(category)}
                >
                  <span className="flex items-center gap-2">{category.icon} {category.name}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Right side - Products */}
          <div className="w-full md:w-2/3 pl-4 mt-4 md:mt-0">
            {loading ? (
              <div className="grid grid-cols-2 gap-4">
                {[1, 2, 3, 4].map((_, index) => (
                  <div key={index} className="animate-pulse bg-gray-200 h-40 rounded-lg"></div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-4">
                {products.map(product => (
                  <div
                    key={product.id}
                    className="cursor-pointer hover:shadow-md transition-all duration-200 border border-gray-100 rounded-lg overflow-hidden"
                    onClick={() => handleProductClick(product.id)}
                  >
                    <div className="h-32 overflow-hidden">
                      <img
                        src={product.images?.[0] || 'https://via.placeholder.com/200x200?text=No+Image'}
                        alt={product.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.src = 'https://via.placeholder.com/200x200?text=No+Image';
                        }}
                      />
                    </div>
                    <div className="p-2">
                      <p className="text-sm font-medium text-gray-800 truncate">{product.title}</p>
                      <p className="text-sm font-bold text-red-600">${product.price}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CatergoriesMenu;