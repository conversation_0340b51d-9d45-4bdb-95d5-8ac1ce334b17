import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance for public API calls
const publicApi = axios.create({
  baseURL: API_BASE_URL.endsWith('/api') ? `${API_BASE_URL}/public` : `${API_BASE_URL}/public`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Handle response errors
publicApi.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

/**
 * Fetch products from the real database
 */
async function ProductInfo(params = {}) {
    try {
        const queryParams = new URLSearchParams({
            page: params.page || 1,
            limit: params.limit || 20,
            status: 'active',
            visibility: 'public',
            ...params
        });

        const response = await publicApi.get(`/products?${queryParams}`);

        if (response.data.success) {
            // Transform the data to match the expected format
            const products = response.data.data.products.map(product => ({
                id: product._id,
                title: product.name,
                price: product.pricing?.salePrice || product.pricing?.basePrice || 0,
                images: product.images || [],
                category: product.category,
                vendor: product.vendor,
                rating: product.reviews?.averageRating || 0,
                reviewCount: product.reviews?.totalReviews || 0,
                inStock: product.inventory?.quantity > 0,
                stockCount: product.inventory?.quantity || 0
            }));

            return products;
        } else {
            throw new Error('Failed to fetch products from database');
        }
    } catch (error) {
        console.error('Fetch products error:', error);

        // Fallback to empty array instead of external API
        console.warn('Using empty product list as fallback');
        return [];
    }
}

/**
 * Fetch featured products
 */
async function getFeaturedProducts(limit = 8) {
    try {
        const response = await publicApi.get(`/products/featured?limit=${limit}`);

        if (response.data.success) {
            return response.data.data.products.map(product => ({
                id: product._id,
                title: product.name,
                price: product.pricing?.salePrice || product.pricing?.basePrice || 0,
                images: product.images || [],
                category: product.category,
                vendor: product.vendor,
                rating: product.reviews?.averageRating || 0,
                reviewCount: product.reviews?.totalReviews || 0,
                inStock: product.inventory?.quantity > 0,
                stockCount: product.inventory?.quantity || 0
            }));
        }

        return [];
    } catch (error) {
        console.error('Fetch featured products error:', error);
        return [];
    }
}

/**
 * Fetch new arrivals
 */
async function getNewArrivals(limit = 8) {
    try {
        const response = await publicApi.get(`/products/new-arrivals?limit=${limit}`);

        if (response.data.success) {
            return response.data.data.products.map(product => ({
                id: product._id,
                title: product.name,
                price: product.pricing?.salePrice || product.pricing?.basePrice || 0,
                images: product.images || [],
                category: product.category,
                vendor: product.vendor,
                rating: product.reviews?.averageRating || 0,
                reviewCount: product.reviews?.totalReviews || 0,
                inStock: product.inventory?.quantity > 0,
                stockCount: product.inventory?.quantity || 0
            }));
        }

        return [];
    } catch (error) {
        console.error('Fetch new arrivals error:', error);
        return [];
    }
}

/**
 * Fetch single product by ID
 */
async function getProductById(id) {
    try {
        const response = await publicApi.get(`/products/${id}`);

        if (response.data.success) {
            const product = response.data.data;
            return {
                id: product._id,
                title: product.name,
                price: product.pricing?.salePrice || product.pricing?.basePrice || 0,
                originalPrice: product.pricing?.basePrice || 0,
                images: product.images || [],
                description: product.description,
                category: product.category,
                vendor: product.vendor,
                rating: product.reviews?.averageRating || 0,
                reviewCount: product.reviews?.totalReviews || 0,
                inStock: product.inventory?.quantity > 0,
                stockCount: product.inventory?.quantity || 0,
                specifications: product.specifications || {},
                variants: product.variants || []
            };
        }

        throw new Error('Product not found');
    } catch (error) {
        console.error('Fetch product by ID error:', error);
        throw error;
    }
}

export { ProductInfo, getFeaturedProducts, getNewArrivals, getProductById };