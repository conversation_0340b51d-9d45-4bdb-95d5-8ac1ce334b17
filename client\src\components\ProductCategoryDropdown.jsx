import React, { useState } from "react";
import { DownOutlined } from "@ant-design/icons";

const ProductCategoryDropdown = () => {
    const [isProductDropdownOpen, setIsProductDropdownOpen] = useState(false);

    const productCategories = [
        "Electronics",
        "Fashion",
        "Home & Garden",
        "Sports",
        "Books",
        "Toys",
        "Beauty",
        "Automotive"
    ];

    return (
        <div className="relative">
            <button 
                className="flex items-center px-2 xl:px-3 border border-r-0 border-gray-600 rounded-l-md bg-gray-800 text-white h-10 hover:bg-gray-700 text-xs xl:text-sm"
            >
                <span 
                    className="hidden xl:inline cursor-pointer"
                    onClick={() => setIsProductDropdownOpen(!isProductDropdownOpen)}
                >Products</span>
                <span 
                    className="xl:hidden cursor-pointer"
                    onClick={() => setIsProductDropdownOpen(!isProductDropdownOpen)}
                >All</span>
                <DownOutlined 
                    className="ml-1 text-xs cursor-pointer" 
                    onClick={(e) => {
                        e.stopPropagation();
                        const navBar = document.querySelector('.container');
                        if (navBar) navBar.scrollLeft += 100;
                    }}
                />
            </button>
            
            {/* Products Dropdown */}
            {isProductDropdownOpen && (
                <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                    {productCategories.map((category, index) => (
                        <a
                            key={index}
                            href="#"
                            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                            onClick={() => setIsProductDropdownOpen(false)}
                        >
                            {category}
                        </a>
                    ))}
                </div>
            )}
        </div>
    );
};

export default ProductCategoryDropdown;
