const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Database URIs
const LOCAL_DB_URI = 'mongodb://localhost:27017/multi-vendor-ecommerce';
const PRODUCTION_DB_URI = 'mongodb+srv://itsprogresspulse:<EMAIL>/multi-vendor-ecommerce?retryWrites=true&w=majority&appName=Cluster0';

console.log('🔧 Database Migration Setup:');
console.log('📍 Local:', LOCAL_DB_URI);
console.log('📍 Production:', PRODUCTION_DB_URI.replace(/\/\/.*@/, '//***:***@'));

class DatabaseMigrator {
    constructor() {
        this.localConnection = null;
        this.productionConnection = null;
        this.backupDir = path.join(__dirname, '../backups');
    }

    async createBackupDirectory() {
        if (!fs.existsSync(this.backupDir)) {
            fs.mkdirSync(this.backupDir, { recursive: true });
            console.log(`✅ Created backup directory: ${this.backupDir}`);
        }
    }

    async connectToLocalDB() {
        try {
            this.localConnection = await mongoose.createConnection(LOCAL_DB_URI, {
                serverSelectionTimeoutMS: 10000,
                socketTimeoutMS: 45000,
            });
            
            await new Promise((resolve, reject) => {
                this.localConnection.once('open', resolve);
                this.localConnection.once('error', reject);
                setTimeout(() => reject(new Error('Connection timeout')), 15000);
            });
            
            console.log('✅ Connected to local MongoDB');
            return this.localConnection;
        } catch (error) {
            console.error('❌ Failed to connect to local MongoDB:', error.message);
            throw error;
        }
    }

    async connectToProductionDB() {
        try {
            console.log('🔗 Connecting to production Atlas...');
            
            // Updated connection options for better compatibility
            const connectionOptions = [
                {
                    name: 'Modern Connection',
                    options: {
                        serverSelectionTimeoutMS: 60000,
                        socketTimeoutMS: 60000,
                        connectTimeoutMS: 60000,
                        maxPoolSize: 10,
                        retryWrites: true,
                        w: 'majority',
                        authSource: 'admin',
                        useNewUrlParser: true,
                        useUnifiedTopology: true
                    }
                },
                {
                    name: 'Basic Connection',
                    options: {
                        serverSelectionTimeoutMS: 60000,
                        socketTimeoutMS: 60000,
                        connectTimeoutMS: 60000,
                        retryWrites: true,
                        w: 'majority'
                    }
                },
                {
                    name: 'Minimal Connection',
                    options: {
                        serverSelectionTimeoutMS: 30000,
                        retryWrites: true
                    }
                }
            ];

            for (const { name, options } of connectionOptions) {
                try {
                    console.log(`🔄 Trying ${name}...`);
                    
                    this.productionConnection = await mongoose.createConnection(PRODUCTION_DB_URI, options);
                    
                    await new Promise((resolve, reject) => {
                        this.productionConnection.once('open', resolve);
                        this.productionConnection.once('error', reject);
                        setTimeout(() => reject(new Error('Connection timeout')), 60000);
                    });
                    
                    console.log(`✅ Connected to production MongoDB Atlas using ${name}`);
                    return this.productionConnection;
                } catch (error) {
                    console.log(`❌ ${name} failed: ${error.message}`);
                    if (this.productionConnection) {
                        try {
                            await this.productionConnection.close();
                        } catch (closeError) {
                            // Ignore close errors
                        }
                        this.productionConnection = null;
                    }
                }
            }
            
            throw new Error('All connection methods failed');
            
        } catch (error) {
            console.error('❌ Failed to connect to production MongoDB:', error.message);
            console.error('💡 Troubleshooting tips:');
            console.error('   - Check if your IP address is whitelisted in MongoDB Atlas (0.0.0.0/0 for all IPs)');
            console.error('   - Verify username and password are correct');
            console.error('   - Ensure network connectivity to Atlas');
            console.error('   - Check if the cluster is running and accessible');
            console.error('   - Try connecting from MongoDB Compass first to verify credentials');
            throw error;
        }
    }

    async getCollectionNames() {
        try {
            if (!this.localConnection || this.localConnection.readyState !== 1) {
                throw new Error('Local connection not ready');
            }
            
            const collections = await this.localConnection.db.listCollections().toArray();
            const actualCollections = collections.map(col => col.name);
            console.log('📋 Collections found in local database:', actualCollections.join(', '));
            return actualCollections;
        } catch (error) {
            console.error('❌ Failed to get collection names:', error.message);
            throw error;
        }
    }

    async exportCollection(collectionName) {
        try {
            const collection = this.localConnection.db.collection(collectionName);
            const documents = await collection.find({}).toArray();
            
            if (documents.length === 0) {
                console.log(`⚠️  Collection '${collectionName}' is empty, skipping...`);
                return null;
            }

            const backupFile = path.join(this.backupDir, `${collectionName}.json`);
            fs.writeFileSync(backupFile, JSON.stringify(documents, null, 2));
            
            console.log(`✅ Exported ${documents.length} documents from '${collectionName}'`);
            return documents;
        } catch (error) {
            console.error(`❌ Failed to export collection '${collectionName}':`, error.message);
            return null;
        }
    }

    async importCollection(collectionName, documents) {
        try {
            if (!documents || documents.length === 0) {
                console.log(`⚠️  No documents to import for collection '${collectionName}'`);
                return;
            }

            const collection = this.productionConnection.db.collection(collectionName);
            
            // Check if collection exists and has data
            const existingCount = await collection.countDocuments();
            if (existingCount > 0) {
                console.log(`⚠️  Collection '${collectionName}' already has ${existingCount} documents in production`);
                console.log(`🗑️  Clearing existing documents from '${collectionName}'...`);
                await collection.deleteMany({});
            }

            // Insert documents in batches to avoid memory issues
            const batchSize = 100; // Reduced batch size for better reliability
            let imported = 0;

            for (let i = 0; i < documents.length; i += batchSize) {
                const batch = documents.slice(i, i + batchSize);
                try {
                    await collection.insertMany(batch, { ordered: false });
                    imported += batch.length;
                    console.log(`📥 Imported ${imported}/${documents.length} documents to '${collectionName}'`);
                } catch (batchError) {
                    console.error(`⚠️  Batch import error for ${collectionName}:`, batchError.message);
                    // Try inserting documents one by one for this batch
                    for (const doc of batch) {
                        try {
                            await collection.insertOne(doc);
                            imported++;
                        } catch (docError) {
                            console.error(`❌ Failed to insert document in ${collectionName}:`, docError.message);
                        }
                    }
                }
            }

            console.log(`✅ Successfully imported ${imported} documents to '${collectionName}'`);
        } catch (error) {
            console.error(`❌ Failed to import collection '${collectionName}':`, error.message);
            throw error;
        }
    }

    async promptUser(question) {
        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        return new Promise((resolve) => {
            rl.question(question, (answer) => {
                rl.close();
                resolve(answer);
            });
        });
    }

    async migrateData() {
        try {
            console.log('🚀 Starting database migration...\n');

            // Create backup directory
            await this.createBackupDirectory();

            // Connect to databases
            await this.connectToLocalDB();
            await this.connectToProductionDB();

            // Get all collections from local database
            const collections = await this.getCollectionNames();
            console.log(`📋 Found ${collections.length} collections to migrate`);

            // Count total documents
            let totalDocs = 0;
            for (const collectionName of collections) {
                const count = await this.localConnection.db.collection(collectionName).countDocuments();
                totalDocs += count;
                console.log(`   - ${collectionName}: ${count} documents`);
            }

            // Ask for confirmation
            console.log(`\n⚠️  This will migrate ${totalDocs} documents from local MongoDB to production Atlas database.`);
            console.log(`📍 Local: ${LOCAL_DB_URI}`);
            console.log(`📍 Production: ${PRODUCTION_DB_URI.replace(/\/\/.*@/, '//***:***@')}`);
            console.log(`\n📊 Collections to migrate: ${collections.join(', ')}`);
            
            const confirm = await this.promptUser('\nDo you want to continue? (y/n): ');
            if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
                console.log('❌ Migration cancelled by user');
                return;
            }

            // Export and import each collection
            let successCount = 0;
            for (const collectionName of collections) {
                console.log(`\n📦 Processing collection: ${collectionName}`);
                
                try {
                    // Export from local
                    const documents = await this.exportCollection(collectionName);
                    
                    // Import to production
                    if (documents) {
                        await this.importCollection(collectionName, documents);
                        successCount++;
                    }
                } catch (error) {
                    console.error(`❌ Failed to migrate collection ${collectionName}:`, error.message);
                }
            }

            console.log(`\n🎉 Migration completed! Successfully migrated ${successCount}/${collections.length} collections`);
            console.log(`📁 Backup files saved in: ${this.backupDir}`);

        } catch (error) {
            console.error('\n❌ Migration failed:', error.message);
            throw error;
        } finally {
            // Close connections
            if (this.localConnection) {
                await this.localConnection.close();
                console.log('🔌 Closed local database connection');
            }
            if (this.productionConnection) {
                await this.productionConnection.close();
                console.log('🔌 Closed production database connection');
            }
        }
    }

    async verifyMigration() {
        try {
            console.log('\n🔍 Verifying migration...');
            
            await this.connectToLocalDB();
            await this.connectToProductionDB();

            const collections = await this.getCollectionNames();
            
            console.log('\n📊 Migration Verification Results:');
            let totalMatched = 0;
            let totalMismatched = 0;
            
            for (const collectionName of collections) {
                const localCount = await this.localConnection.db.collection(collectionName).countDocuments();
                const prodCount = await this.productionConnection.db.collection(collectionName).countDocuments();
                
                if (localCount === prodCount) {
                    console.log(`✅ ${collectionName}: ${localCount} documents (matched)`);
                    totalMatched++;
                } else {
                    console.log(`⚠️  ${collectionName}: Local=${localCount}, Production=${prodCount} (mismatch)`);
                    totalMismatched++;
                }
            }
            
            console.log(`\n📈 Summary: ${totalMatched} collections matched, ${totalMismatched} mismatched`);

        } catch (error) {
            console.error('❌ Verification failed:', error.message);
        } finally {
            if (this.localConnection) await this.localConnection.close();
            if (this.productionConnection) await this.productionConnection.close();
        }
    }

    async testConnections() {
        try {
            console.log('🔍 Testing database connections...\n');
            
            // Test local connection
            console.log('Testing local MongoDB...');
            await this.connectToLocalDB();
            const localCollections = await this.getCollectionNames();
            console.log(`✅ Local: ${localCollections.length} collections found`);
            
            // Count total documents in local
            let totalLocalDocs = 0;
            for (const collection of localCollections) {
                const count = await this.localConnection.db.collection(collection).countDocuments();
                totalLocalDocs += count;
                console.log(`   - ${collection}: ${count} documents`);
            }
            console.log(`📊 Total local documents: ${totalLocalDocs}`);
            
            await this.localConnection.close();
            
            // Test production connection
            console.log('\nTesting production Atlas...');
            await this.connectToProductionDB();
            const prodCollections = await this.productionConnection.db.listCollections().toArray();
            console.log(`✅ Production: ${prodCollections.length} collections found`);
            
            if (prodCollections.length > 0) {
                let totalProdDocs = 0;
                for (const collection of prodCollections) {
                    const count = await this.productionConnection.db.collection(collection.name).countDocuments();
                    totalProdDocs += count;
                    console.log(`   - ${collection.name}: ${count} documents`);
                }
                console.log(`📊 Total production documents: ${totalProdDocs}`);
            } else {
                console.log('📊 Production database is empty');
            }
            
            await this.productionConnection.close();
            
            console.log('\n🎉 Both connections successful!');
            console.log('✅ Ready for migration!');
            
        } catch (error) {
            console.error('❌ Connection test failed:', error.message);
            console.error('\n💡 If production connection fails, try:');
            console.error('   1. Whitelist your IP in MongoDB Atlas (Network Access)');
            console.error('   2. Test connection with MongoDB Compass first');
            console.error('   3. Check if cluster is paused/sleeping');
        }
    }
}

// CLI interface
async function main() {
    const migrator = new DatabaseMigrator();
    
    const args = process.argv.slice(2);
    const command = args[0];

    try {
        switch (command) {
            case 'migrate':
                await migrator.migrateData();
                break;
            case 'verify':
                await migrator.verifyMigration();
                break;
            case 'test':
                await migrator.testConnections();
                break;
            default:
                console.log(`
🚀 Database Migration Tool for Live Database (Fixed Version)

Usage:
  node migrate-to-live-fixed.js migrate  - Migrate all data from local to production
  node migrate-to-live-fixed.js verify   - Verify migration by comparing document counts
  node migrate-to-live-fixed.js test     - Test both database connections

Examples:
  node scripts/migrate-to-live-fixed.js test
  node scripts/migrate-to-live-fixed.js migrate
  node scripts/migrate-to-live-fixed.js verify

Note: Make sure your IP is whitelisted in MongoDB Atlas Network Access settings.
                `);
        }
    } catch (error) {
        console.error('❌ Operation failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = DatabaseMigrator;