/**
 * 404 Not Found middleware
 * This middleware is called when no route matches the request
 */
const notFound = (req, res, next) => {
  const error = new Error(`Route ${req.originalUrl} not found`);
  error.status = 404;

  res.status(404).json({
    success: false,
    message: `Route ${req.originalUrl} not found`,
    error: {
      status: 404,
      path: req.originalUrl,
      method: req.method,
      timestamp: new Date().toISOString()
    }
  });
};

module.exports = notFound;