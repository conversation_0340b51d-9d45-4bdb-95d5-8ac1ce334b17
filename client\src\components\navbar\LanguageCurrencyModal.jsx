import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import currencies from 'currency-codes'
import isoLanguages from 'iso-639-1'
import countries from 'world-countries'

const LanguageCurrencyModal = ({ 
    showModal, 
    setShowModal, 
    language,
    setLanguage,
    currency,
    setCurrency,
    selectedCountry,
    setSelectedCountry,
    onMouseEnter,
    onMouseLeave 
}) => {
    const [languages, setLanguages] = useState([]);
    const [currencyOptions, setCurrencyOptions] = useState([]);

    useEffect(() => {
        // Get all language names
        const langs = isoLanguages.getAllNames();
        setLanguages(langs);

        // Get all currency names
        const currs = currencies.data.map(curr => ({
            code: curr.code,
            name: curr.currency
        }));
        setCurrencyOptions(currs);

    }, []);
    
    const handleSave = () => {
        localStorage.setItem('selectedLanguage', language)
        localStorage.setItem('selectedCurrency', currency)
        setShowModal(false)
    }

    const handleCurrencyChange = (value) => {
        const selectedCurrencyCode = value.split(' ')[0]
        const selectedCurrency = currencyOptions.find(curr => curr.code === selectedCurrencyCode)
        
        if (selectedCurrency) {
            // Find country that uses this currency
            const countryMatch = countries.find(country => {
                return country.currencies && Object.keys(country.currencies).includes(selectedCurrencyCode)
            })
            
            if (countryMatch && setSelectedCountry) {
                const countryData = { code: countryMatch.cca2, name: countryMatch.name.common }
                setSelectedCountry(countryData)
                localStorage.setItem('selectedCountry', JSON.stringify(countryData))
            }
        }
        setCurrency(value)
    }

    if (!showModal) return null;

    return (
        <div
            className={`navbar-hover-modal absolute top-full right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-4 transform transition-all duration-300 ease-in-out ${
                showModal
                    ? 'opacity-100 translate-y-0 scale-100'
                    : 'opacity-0 translate-y-2 scale-95 pointer-events-none'
            }`}
            onMouseEnter={onMouseEnter}
            onMouseLeave={onMouseLeave}
        >
            <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Set language and currency</h3>
                <button 
                    onClick={() => setShowModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                >
                    <X className="w-5 h-5" />
                </button>
            </div>
            
            <p className="text-sm text-gray-600 mb-4">
                Select your preferred language and currency. You can update the settings at any time.
            </p>
            
            {/* Language Label */}
            <div className="mb-2">
                <label className="block text-sm font-medium text-gray-700">Language</label>
            </div>
            
            {/* Language Dropdown */}
            <div className="mb-4">
                <select 
                    value={language}
                    onChange={(e) => setLanguage(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 bg-white"
                >
                    {languages.map((lang) => (
                        <option key={lang} value={lang}>
                            {lang}
                        </option>
                    ))}
                </select>
            </div>
            
            {/* Currency Label */}
            <div className="mb-2">
                <label className="block text-sm font-medium text-gray-700">Currency</label>
            </div>
            
            {/* Currency Dropdown */}
            <div className="mb-4">
                <select 
                    value={currency}
                    onChange={(e) => handleCurrencyChange(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 bg-white"
                >
                    {currencyOptions.map((curr) => (
                        <option key={curr.code} value={`${curr.code} - ${curr.name}`}>
                            {curr.code} - {curr.name}
                        </option>
                    ))}
                </select>
            </div>
            
            {/* Save Button */}
            <button 
                onClick={handleSave}
                className="w-full bg-orange-500 text-white py-2 px-4 rounded-md hover:bg-orange-600 transition-colors"
            >
                Save
            </button>
        </div>
    );
}

export default LanguageCurrencyModal;
