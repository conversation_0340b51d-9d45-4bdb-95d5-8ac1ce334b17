const mongoose = require('mongoose');

const orderTrackingSchema = new mongoose.Schema({
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    required: true
  },
  trackingNumber: {
    type: String,
    required: true,
    unique: true
  },
  carrier: {
    name: {
      type: String,
      required: true
    },
    code: String,
    contactInfo: {
      phone: String,
      website: String,
      email: String
    }
  },
  currentStatus: {
    type: String,
    enum: ['order_confirmed', 'processing', 'shipped', 'out_for_delivery', 'delivered', 'cancelled', 'returned'],
    default: 'order_confirmed'
  },
  trackingSteps: [{
    status: {
      type: String,
      enum: ['order_confirmed', 'processing', 'shipped', 'out_for_delivery', 'delivered', 'cancelled', 'returned'],
      required: true
    },
    title: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: true
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    location: {
      city: String,
      state: String,
      country: String,
      coordinates: {
        latitude: Number,
        longitude: Number
      }
    },
    updatedBy: {
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      vendor: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Vendor'
      },
      type: {
        type: String,
        enum: ['system', 'vendor', 'admin', 'carrier'],
        default: 'system'
      }
    },
    isActive: {
      type: Boolean,
      default: true
    },
    metadata: {
      carrierStatusCode: String,
      carrierMessage: String,
      estimatedDelivery: Date,
      actualDelivery: Date
    }
  }],
  estimatedDelivery: {
    type: Date
  },
  actualDelivery: {
    type: Date
  },
  deliveryAddress: {
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: String,
    instructions: String
  },
  recipient: {
    name: String,
    phone: String,
    email: String
  },
  notes: [{
    message: {
      type: String,
      required: true
    },
    addedBy: {
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      vendor: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Vendor'
      },
      type: {
        type: String,
        enum: ['customer', 'vendor', 'admin', 'system'],
        default: 'system'
      }
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    isPublic: {
      type: Boolean,
      default: true
    }
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  vendor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vendor',
    required: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes (trackingNumber index is automatically created by unique: true)
orderTrackingSchema.index({ order: 1 });
orderTrackingSchema.index({ vendor: 1 });
orderTrackingSchema.index({ currentStatus: 1 });
orderTrackingSchema.index({ 'trackingSteps.timestamp': -1 });
orderTrackingSchema.index({ order: 1, vendor: 1 }); // Compound index for order-vendor queries

// Virtual for progress percentage
orderTrackingSchema.virtual('progressPercentage').get(function() {
  const statusOrder = ['order_confirmed', 'processing', 'shipped', 'out_for_delivery', 'delivered'];
  const currentIndex = statusOrder.indexOf(this.currentStatus);
  
  if (currentIndex === -1) return 0;
  if (this.currentStatus === 'delivered') return 100;
  
  return Math.round((currentIndex / (statusOrder.length - 1)) * 100);
});

// Virtual for current step index
orderTrackingSchema.virtual('currentStepIndex').get(function() {
  const statusOrder = ['order_confirmed', 'processing', 'shipped', 'out_for_delivery', 'delivered'];
  return statusOrder.indexOf(this.currentStatus);
});

// Virtual for latest update
orderTrackingSchema.virtual('latestUpdate').get(function() {
  if (this.trackingSteps.length === 0) return null;
  return this.trackingSteps.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))[0];
});

// Static method to create tracking with initial status
orderTrackingSchema.statics.createTracking = async function(orderData) {
  const trackingNumber = `TRK${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`;
  
  const tracking = new this({
    order: orderData.orderId,
    trackingNumber,
    carrier: orderData.carrier || {
      name: 'Standard Delivery',
      code: 'STD'
    },
    currentStatus: 'order_confirmed',
    trackingSteps: [{
      status: 'order_confirmed',
      title: 'Order Confirmed',
      description: 'Your order has been confirmed and is being processed',
      timestamp: new Date(),
      updatedBy: {
        type: 'system'
      }
    }],
    deliveryAddress: orderData.deliveryAddress,
    recipient: orderData.recipient,
    vendor: orderData.vendorId,
    estimatedDelivery: orderData.estimatedDelivery
  });
  
  return tracking.save();
};

// Instance method to update status
orderTrackingSchema.methods.updateStatus = function(newStatus, updateData = {}) {
  const statusTitles = {
    'order_confirmed': 'Order Confirmed',
    'processing': 'Processing',
    'shipped': 'Shipped',
    'out_for_delivery': 'Out for Delivery',
    'delivered': 'Delivered',
    'cancelled': 'Cancelled',
    'returned': 'Returned'
  };
  
  const statusDescriptions = {
    'order_confirmed': 'Your order has been confirmed and is being processed',
    'processing': 'Your order is being prepared for shipment',
    'shipped': 'Your order has been shipped and is on the way',
    'out_for_delivery': 'Your order is out for delivery and will arrive soon',
    'delivered': 'Your order has been successfully delivered',
    'cancelled': 'Your order has been cancelled',
    'returned': 'Your order has been returned'
  };
  
  this.currentStatus = newStatus;
  
  const newStep = {
    status: newStatus,
    title: updateData.title || statusTitles[newStatus],
    description: updateData.description || statusDescriptions[newStatus],
    timestamp: updateData.timestamp || new Date(),
    location: updateData.location,
    updatedBy: updateData.updatedBy || { type: 'system' },
    metadata: updateData.metadata
  };
  
  // If status is delivered, set actual delivery date
  if (newStatus === 'delivered') {
    this.actualDelivery = newStep.timestamp;
  }
  
  this.trackingSteps.push(newStep);
  
  return this.save();
};

// Instance method to add note
orderTrackingSchema.methods.addNote = function(message, addedBy, isPublic = true) {
  this.notes.push({
    message,
    addedBy,
    timestamp: new Date(),
    isPublic
  });
  
  return this.save();
};

// Static method to get tracking by order ID (returns first tracking record for backward compatibility)
orderTrackingSchema.statics.getByOrderId = function(orderId) {
  return this.findOne({ order: orderId })
    .populate('order', 'orderNumber customer items pricing')
    .populate('vendor', 'businessName contactInfo')
    .populate('trackingSteps.updatedBy.user', 'firstName lastName')
    .populate('trackingSteps.updatedBy.vendor', 'businessName')
    .populate('notes.addedBy.user', 'firstName lastName')
    .populate('notes.addedBy.vendor', 'businessName');
};

// Static method to get all tracking records by order ID (for multi-vendor orders)
orderTrackingSchema.statics.getAllByOrderId = function(orderId) {
  return this.find({ order: orderId })
    .populate('order', 'orderNumber customer items pricing')
    .populate('vendor', 'businessName contactInfo')
    .populate('trackingSteps.updatedBy.user', 'firstName lastName')
    .populate('trackingSteps.updatedBy.vendor', 'businessName')
    .populate('notes.addedBy.user', 'firstName lastName')
    .populate('notes.addedBy.vendor', 'businessName');
};

// Static method to get tracking by tracking number
orderTrackingSchema.statics.getByTrackingNumber = function(trackingNumber) {
  return this.findOne({ trackingNumber })
    .populate('order', 'orderNumber customer items pricing')
    .populate('vendor', 'businessName contactInfo')
    .populate('trackingSteps.updatedBy.user', 'firstName lastName')
    .populate('trackingSteps.updatedBy.vendor', 'businessName')
    .populate('notes.addedBy.user', 'firstName lastName')
    .populate('notes.addedBy.vendor', 'businessName');
};

module.exports = mongoose.model('OrderTracking', orderTrackingSchema);
