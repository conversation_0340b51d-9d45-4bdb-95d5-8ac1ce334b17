import React from 'react';
import {
  Table,
  Space,
  Tag,
  Button,
  Avatar,
  Rate,
  Popconfirm
} from 'antd';
import {
  ShopOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';

const VendorTable = ({
  vendors,
  loading,
  onEdit,
  onDelete,
  onApprove,
  onSuspend,
  pagination,
  onTableChange
}) => {
  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'pending_approval':
        return 'orange';
      case 'suspended':
        return 'red';
      case 'inactive':
        return 'default';
      default:
        return 'default';
    }
  };

  const getVerificationStatusColor = (status) => {
    switch (status) {
      case 'verified':
        return 'green';
      case 'pending':
        return 'orange';
      case 'rejected':
        return 'red';
      case 'suspended':
        return 'red';
      default:
        return 'default';
    }
  };

  const columns = [
    {
      title: 'Vendor',
      key: 'vendor',
      render: (record) => (
        <Space>
          <Avatar icon={<ShopOutlined />} style={{ backgroundColor: '#52c41a' }} />
          <div>
            <div style={{ fontWeight: 500 }}>{record.businessName}</div>
            <div style={{ color: '#666', fontSize: '12px' }}>
              {record.user?.firstName} {record.user?.lastName}
            </div>
            <div style={{ color: '#666', fontSize: '12px' }}>
              {record.user?.email || record.contactInfo?.email}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: 'Status',
      key: 'status',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Tag color={getStatusColor(record.status)}>
            {record.status?.replace('_', ' ').toUpperCase()}
          </Tag>
          <Tag color={getVerificationStatusColor(record.verification?.status)} size="small">
            {record.verification?.status?.toUpperCase()}
          </Tag>
        </Space>
      ),
    },
    {
      title: 'Rating',
      key: 'rating',
      render: (_, record) => (
        <Space>
          <Rate disabled defaultValue={record.performance?.rating || 0} style={{ fontSize: '14px' }} />
          <span>({record.performance?.rating || 0})</span>
        </Space>
      ),
    },
    {
      title: 'Products',
      key: 'products',
      align: 'center',
      render: (_, record) => record.performance?.totalProducts || 0,
    },
    {
      title: 'Commission',
      key: 'commission',
      render: (_, record) => `${record.commission?.rate || 0}%`,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Space wrap>
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => onEdit(record)}
            >
              Edit
            </Button>
            {record.status === 'pending_approval' && (
              <Button
                type="primary"
                size="small"
                icon={<CheckCircleOutlined />}
                onClick={() => onApprove(record._id || record.id)}
                style={{ backgroundColor: '#52c41a' }}
              >
                Approve
              </Button>
            )}
            {record.status === 'active' && (
              <Button
                size="small"
                icon={<CloseCircleOutlined />}
                onClick={() => onSuspend(record._id || record.id)}
                danger
              >
                Suspend
              </Button>
            )}
          </Space>
          <Popconfirm
            title="Are you sure you want to delete this vendor?"
            onConfirm={() => onDelete(record._id || record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              type="primary"
              danger
              size="small"
              icon={<DeleteOutlined />}
              block
            >
              Delete
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={vendors}
      rowKey={(record) => record._id || record.id}
      loading={loading}
      scroll={{ x: 800 }}
      pagination={{
        ...pagination,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) =>
          `${range[0]}-${range[1]} of ${total} vendors`,
        responsive: true,
      }}
      onChange={onTableChange}
    />
  );
};

export default VendorTable;
