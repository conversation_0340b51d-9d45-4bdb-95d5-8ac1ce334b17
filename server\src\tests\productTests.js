const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../app');
const Product = require('../models/Product');
const Category = require('../models/Category');
const Vendor = require('../models/Vendor');
const User = require('../models/User');

describe('Product Management Tests', () => {
  let vendorToken;
  let adminToken;
  let vendorId;
  let categoryId;
  let productId;

  beforeAll(async () => {
    // Connect to test database
    await mongoose.connect(process.env.TEST_DATABASE_URL || 'mongodb://localhost:27017/ecommerce_test');
    
    // Clean up test data
    await Promise.all([
      Product.deleteMany({}),
      Category.deleteMany({}),
      Vendor.deleteMany({}),
      User.deleteMany({})
    ]);

    // Create test category
    const category = new Category({
      name: 'Test Category',
      slug: 'test-category',
      status: 'active'
    });
    await category.save();
    categoryId = category._id;

    // Create test vendor user
    const vendorUser = new User({
      firstName: 'Test',
      lastName: 'Vendor',
      email: '<EMAIL>',
      password: 'password123',
      userType: 'vendor',
      emailVerification: { isVerified: true }
    });
    await vendorUser.save();

    // Create test vendor
    const vendor = new Vendor({
      user: vendorUser._id,
      businessName: 'Test Business',
      businessType: 'company',
      businessAddress: {
        street: '123 Test St',
        city: 'Test City',
        state: 'Test State',
        zipCode: '12345',
        country: 'Test Country'
      },
      contactInfo: {
        businessPhone: '+*********0',
        businessEmail: '<EMAIL>'
      },
      bankDetails: {
        accountHolderName: 'Test Vendor',
        bankName: 'Test Bank',
        accountNumber: '*********0',
        routingNumber: '*********',
        accountType: 'checking'
      },
      verification: {
        status: 'verified'
      },
      status: 'active'
    });
    await vendor.save();
    vendorId = vendor._id;

    // Create test admin user
    const adminUser = new User({
      firstName: 'Test',
      lastName: 'Admin',
      email: '<EMAIL>',
      password: 'password123',
      userType: 'admin',
      emailVerification: { isVerified: true }
    });
    await adminUser.save();

    // Generate tokens
    vendorToken = vendorUser.generateAuthToken();
    adminToken = adminUser.generateAuthToken();
  });

  afterAll(async () => {
    // Clean up test data
    await Promise.all([
      Product.deleteMany({}),
      Category.deleteMany({}),
      Vendor.deleteMany({}),
      User.deleteMany({})
    ]);
    
    await mongoose.connection.close();
  });

  describe('Vendor Product Management', () => {
    test('Should create a new product', async () => {
      const productData = {
        name: 'Test Product',
        description: 'This is a test product description',
        category: categoryId,
        sku: 'TEST-001',
        pricing: {
          basePrice: 99.99,
          currency: 'USD'
        },
        inventory: {
          quantity: 100,
          trackQuantity: true
        },
        status: 'active'
      };

      const response = await request(app)
        .post('/api/vendor/products')
        .set('Authorization', `Bearer ${vendorToken}`)
        .send(productData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.product.name).toBe(productData.name);
      expect(response.body.data.product.sku).toBe(productData.sku);
      
      productId = response.body.data.product._id;
    });

    test('Should get vendor products', async () => {
      const response = await request(app)
        .get('/api/vendor/products')
        .set('Authorization', `Bearer ${vendorToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.products).toHaveLength(1);
      expect(response.body.data.products[0].name).toBe('Test Product');
    });

    test('Should get single product', async () => {
      const response = await request(app)
        .get(`/api/vendor/products/${productId}`)
        .set('Authorization', `Bearer ${vendorToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.product._id).toBe(productId);
    });

    test('Should update product', async () => {
      const updateData = {
        name: 'Updated Test Product',
        pricing: {
          basePrice: 149.99
        }
      };

      const response = await request(app)
        .put(`/api/vendor/products/${productId}`)
        .set('Authorization', `Bearer ${vendorToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.product.name).toBe(updateData.name);
    });

    test('Should update product stock', async () => {
      const stockData = {
        quantity: 50,
        operation: 'set'
      };

      const response = await request(app)
        .patch(`/api/vendor/products/${productId}/stock`)
        .set('Authorization', `Bearer ${vendorToken}`)
        .send(stockData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.product.inventory.quantity).toBe(stockData.quantity);
    });

    test('Should get product statistics', async () => {
      const response = await request(app)
        .get('/api/vendor/products/stats')
        .set('Authorization', `Bearer ${vendorToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.stats.totalProducts).toBe(1);
    });

    test('Should fail to create product with duplicate SKU', async () => {
      const productData = {
        name: 'Another Test Product',
        description: 'This is another test product',
        category: categoryId,
        sku: 'TEST-001', // Same SKU as first product
        pricing: {
          basePrice: 79.99
        }
      };

      const response = await request(app)
        .post('/api/vendor/products')
        .set('Authorization', `Bearer ${vendorToken}`)
        .send(productData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('SKU already exists');
    });
  });

  describe('Admin Product Management', () => {
    test('Should get all products as admin', async () => {
      const response = await request(app)
        .get('/api/admin/products')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.products).toHaveLength(1);
    });

    test('Should get product statistics as admin', async () => {
      const response = await request(app)
        .get('/api/admin/products/stats')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.stats.totalProducts).toBe(1);
    });

    test('Should update product status as admin', async () => {
      const statusData = {
        status: 'inactive',
        reason: 'Admin review required'
      };

      const response = await request(app)
        .patch(`/api/admin/products/${productId}/status`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(statusData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.product.status).toBe(statusData.status);
    });

    test('Should toggle featured status as admin', async () => {
      const response = await request(app)
        .patch(`/api/admin/products/${productId}/featured`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.product.featured).toBe(true);
    });
  });

  describe('Public Product Access', () => {
    beforeAll(async () => {
      // Make product active and public for testing
      await Product.findByIdAndUpdate(productId, {
        status: 'active',
        visibility: 'public'
      });
    });

    test('Should get public products', async () => {
      const response = await request(app)
        .get('/api/public/products')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.products).toHaveLength(1);
    });

    test('Should get single public product', async () => {
      const response = await request(app)
        .get(`/api/public/products/${productId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.product._id).toBe(productId);
    });

    test('Should get featured products', async () => {
      const response = await request(app)
        .get('/api/public/products/featured')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.products).toHaveLength(1);
    });

    test('Should search products', async () => {
      const response = await request(app)
        .get('/api/public/products/search?q=test')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.products).toHaveLength(1);
    });

    test('Should get products by category', async () => {
      const response = await request(app)
        .get(`/api/public/products/category/${categoryId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.products).toHaveLength(1);
    });

    test('Should get products by vendor', async () => {
      const response = await request(app)
        .get(`/api/public/products/vendor/${vendorId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.products).toHaveLength(1);
    });
  });

  describe('Product Validation', () => {
    test('Should fail to create product without required fields', async () => {
      const invalidProductData = {
        name: 'Test Product'
        // Missing required fields
      };

      const response = await request(app)
        .post('/api/vendor/products')
        .set('Authorization', `Bearer ${vendorToken}`)
        .send(invalidProductData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Validation failed');
    });

    test('Should fail to create product with invalid price', async () => {
      const invalidProductData = {
        name: 'Test Product',
        description: 'Test description',
        category: categoryId,
        sku: 'TEST-002',
        pricing: {
          basePrice: -10 // Invalid negative price
        }
      };

      const response = await request(app)
        .post('/api/vendor/products')
        .set('Authorization', `Bearer ${vendorToken}`)
        .send(invalidProductData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    test('Should fail to access vendor routes without authentication', async () => {
      const response = await request(app)
        .get('/api/vendor/products')
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    test('Should fail to access admin routes without admin privileges', async () => {
      const response = await request(app)
        .get('/api/admin/products')
        .set('Authorization', `Bearer ${vendorToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
    });
  });
});

module.exports = {
  runProductTests: () => {
    console.log('Product management tests completed successfully!');
  }
};