const express = require('express');
const router = express.Router();
const {
  getOrders,
  getOrder,
  trackOrder,
  cancelOrder,
  getOrderStats
} = require('../../controllers/customer/orderController');
const {
  createOrder
} = require('../../controllers/customerOrderController');
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');
const { orderValidator, cancelOrderValidator, returnOrderValidator } = require('../../validators/orderValidators');

// All routes require customer authentication
router.use(verifyToken);
router.use(requireUserType('customer'));

// Order CRUD operations
router.post('/', createOrder); // Add the missing POST route for creating orders
router.get('/', getOrders);
router.get('/stats', getOrderStats);
router.get('/:id', getOrder);

// Order tracking
router.get('/track/:identifier', trackOrder);

// Order actions
router.put('/:id/cancel', cancelOrder);

module.exports = router;