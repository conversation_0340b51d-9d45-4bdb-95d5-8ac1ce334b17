import React from 'react';
import {
  Table,
  Space,
  Tag,
  Button,
  Select,
  Tooltip,
  Image,
  Typography,
  Rate
} from 'antd';
import {
  EyeOutlined,
  StarOutlined
} from '@ant-design/icons';

const { Text } = Typography;
const { Option } = Select;

const ProductTable = ({
  products,
  loading,
  pagination,
  onPaginationChange,
  onStatusChange,
  onToggleFeatured,
  onViewDetails
}) => {
  const getStatusColor = (status) => {
    const colors = {
      draft: 'default',
      pending_approval: 'processing',
      active: 'success',
      inactive: 'warning',
      rejected: 'error',
      archived: 'default'
    };
    return colors[status] || 'default';
  };

  const getApprovalStatusColor = (status) => {
    const colors = {
      pending: 'processing',
      approved: 'success',
      rejected: 'error',
      requires_changes: 'warning'
    };
    return colors[status] || 'default';
  };

  const columns = [
    {
      title: 'Product',
      key: 'product',
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <Image
            width={50}
            height={50}
            src={record.images?.[0]?.url || '/placeholder-product.png'}
            alt={record.name}
            style={{ borderRadius: '4px', objectFit: 'cover' }}
            fallback="/placeholder-product.png"
          />
          <div>
            <div style={{ fontWeight: 500, marginBottom: '4px' }}>
              {record.name}
            </div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              SKU: {record.sku}
            </Text>
          </div>
        </div>
      ),
      width: 250
    },
    {
      title: 'Vendor',
      dataIndex: ['vendor', 'businessName'],
      key: 'vendor',
      width: 150
    },
    {
      title: 'Category',
      dataIndex: ['category', 'name'],
      key: 'category',
      width: 120
    },
    {
      title: 'Price',
      key: 'price',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>
            ${record.pricing?.basePrice?.toFixed(2)}
          </div>
          {record.pricing?.salePrice && (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              Sale: ${record.pricing.salePrice.toFixed(2)}
            </Text>
          )}
        </div>
      ),
      width: 100
    },
    {
      title: 'Stock',
      key: 'stock',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>
            {record.inventory?.trackQuantity ? record.inventory.quantity : '∞'}
          </div>
          <Tag color={record.inventory?.stockStatus === 'in_stock' ? 'success' : 'error'} size="small">
            {record.inventory?.stockStatus?.replace('_', ' ')}
          </Tag>
        </div>
      ),
      width: 100
    },
    {
      title: 'Status',
      key: 'status',
      render: (_, record) => (
        <div>
          <Tag color={getStatusColor(record.status)}>
            {record.status?.replace('_', ' ')}
          </Tag>
          {record.approval?.status && (
            <Tag color={getApprovalStatusColor(record.approval.status)} size="small">
              {record.approval.status?.replace('_', ' ')}
            </Tag>
          )}
        </div>
      ),
      width: 120
    },
    {
      title: 'Rating',
      key: 'rating',
      render: (_, record) => (
        <div style={{ textAlign: 'center' }}>
          <Rate disabled value={record.reviews?.averageRating || 0} style={{ fontSize: '12px' }} />
          <div style={{ fontSize: '11px', color: '#666' }}>
            ({record.reviews?.totalReviews || 0})
          </div>
        </div>
      ),
      width: 100
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Space>
            <Tooltip title="View Details">
              <Button
                type="primary"
                size="small"
                icon={<EyeOutlined />}
                onClick={() => onViewDetails?.(record)}
              />
            </Tooltip>
            
            <Tooltip title={record.featured ? "Remove from Featured" : "Add to Featured"}>
              <Button
                size="small"
                icon={<StarOutlined />}
                style={{ 
                  color: record.featured ? '#faad14' : '#d9d9d9',
                  borderColor: record.featured ? '#faad14' : '#d9d9d9'
                }}
                onClick={() => onToggleFeatured(record._id, record.featured)}
              />
            </Tooltip>
          </Space>
          
          <Space>
            <Select
              size="small"
              value={record.status}
              style={{ width: 120 }}
              onChange={(value) => onStatusChange(record._id, value)}
            >
              <Option value="draft">Draft</Option>
              <Option value="active">Active</Option>
              <Option value="inactive">Inactive</Option>
              <Option value="archived">Archived</Option>
            </Select>
          </Space>
        </Space>
      ),
      width: 150,
      fixed: 'right'
    }
  ];

  return (
    <Table
      columns={columns}
      dataSource={products}
      rowKey="_id"
      loading={loading}
      pagination={{
        ...pagination,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => 
          `${range[0]}-${range[1]} of ${total} products`,
        onChange: onPaginationChange
      }}
      scroll={{ x: 800 }}
    />
  );
};

export default ProductTable;
