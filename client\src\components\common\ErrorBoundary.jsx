import React from 'react';
import { Result, Button } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to console for debugging
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  handleReset = () => {
    this.setState({ hasError: false, error: null, errorInfo: null });
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      return (
        <div style={{ padding: '20px', minHeight: '400px' }}>
          <Result
            status="error"
            title="Something went wrong"
            subTitle="An error occurred while rendering this component. Please try refreshing the page or contact support if the problem persists."
            extra={[
              <Button 
                type="primary" 
                key="retry"
                icon={<ReloadOutlined />}
                onClick={this.handleReset}
              >
                Try Again
              </Button>,
              <Button 
                key="refresh"
                onClick={() => window.location.reload()}
              >
                Refresh Page
              </Button>
            ]}
          />
          
          {/* Development error details - only show in development */}
          {process.env.NODE_ENV === 'development' && (
            <details style={{ 
              marginTop: 20, 
              padding: 10, 
              backgroundColor: '#f5f5f5', 
              borderRadius: 4,
              whiteSpace: 'pre-wrap'
            }}>
              <summary style={{ cursor: 'pointer', fontWeight: 'bold', marginBottom: 10 }}>
                Error Details (Development Only)
              </summary>
              <div style={{ fontSize: '12px', fontFamily: 'monospace' }}>
                <strong>Error:</strong> {this.state.error && this.state.error.toString()}
                <br />
                <strong>Stack Trace:</strong>
                <br />
                {this.state.errorInfo && this.state.errorInfo.componentStack}
              </div>
            </details>
          )}
        </div>
      );
    }

    // No error, render children normally
    return this.props.children;
  }
}

export default ErrorBoundary;
