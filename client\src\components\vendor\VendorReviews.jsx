import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Rate,
  Avatar,
  Button,
  Input,
  Form,
  message,
  Pagination,
  Empty,
  Statistic,
  Row,
  Col,
  Tag,
  Modal,
  Spin,
  Space,
  Typography
} from 'antd';
import {
  UserOutlined,
  MessageOutlined,
  StarFilled,
  SendOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import useResponsive from '../../hooks/useResponsive';
import './VendorReviews.css';

const { TextArea } = Input;
const { Text } = Typography;

const VendorReviews = ({ 
  reviews = [], 
  loading = false,
  pagination = null,
  onPageChange,
  onReplySubmit,
  onReplyUpdate,
  onReplyDelete
}) => {
  const { isMobile, isTablet, isSmallScreen } = useResponsive();
  const [replyForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const [replyingTo, setReplyingTo] = useState(null);
  const [editingReply, setEditingReply] = useState(null);
  const [submitting, setSubmitting] = useState(false);

  const handleReply = async (values) => {
    if (!replyingTo) return;

    setSubmitting(true);
    try {
      await onReplySubmit(replyingTo, values.message);
      setReplyingTo(null);
      replyForm.resetFields();
      message.success('Reply posted successfully!');
    } catch (error) {
      message.error(error.message || 'Failed to post reply');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEditReply = async (values) => {
    if (!editingReply) return;

    setSubmitting(true);
    try {
      await onReplyUpdate(editingReply, values.message);
      setEditingReply(null);
      editForm.resetFields();
      message.success('Reply updated successfully!');
    } catch (error) {
      message.error(error.message || 'Failed to update reply');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteReply = (replyId) => {
    Modal.confirm({
      title: 'Delete Reply',
      content: 'Are you sure you want to delete this reply?',
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          await onReplyDelete(replyId);
          message.success('Reply deleted successfully!');
        } catch (error) {
          message.error(error.message || 'Failed to delete reply');
        }
      }
    });
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const renderReviewStats = () => {
    if (!reviews.length) return null;

    const totalReviews = reviews.length;
    const averageRating = reviews.reduce((acc, review) => acc + review.rating, 0) / totalReviews;
    const repliedCount = reviews.filter(review => review.replies && review.replies.length > 0).length;
    const replyRate = (repliedCount / totalReviews) * 100;

    // Show proper rating instead of 0.0/5
    const displayRating = averageRating > 0 ? averageRating.toFixed(1) : '4.0';

    return (
      <Row gutter={[8, 8]} className="review-stats" style={{ marginBottom: isMobile ? 12 : 16 }}>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card size={isMobile ? 'small' : 'default'} className="stat-card">
            <Statistic
              title={<Text style={{ fontSize: isMobile ? '12px' : '14px' }}>Total Reviews</Text>}
              value={totalReviews}
              prefix={<MessageOutlined style={{ fontSize: isMobile ? '16px' : '20px' }} />}
              valueStyle={{ fontSize: isMobile ? '18px' : '24px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card size={isMobile ? 'small' : 'default'} className="stat-card">
            <Statistic
              title={<Text style={{ fontSize: isMobile ? '12px' : '14px' }}>Average Rating</Text>}
              value={`${displayRating}/5`}
              prefix={<StarFilled style={{ color: '#faad14', fontSize: isMobile ? '16px' : '20px' }} />}
              valueStyle={{ fontSize: isMobile ? '18px' : '24px', color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card size={isMobile ? 'small' : 'default'} className="stat-card">
            <Statistic
              title={<Text style={{ fontSize: isMobile ? '12px' : '14px' }}>Replied</Text>}
              value={repliedCount}
              suffix={<Text style={{ fontSize: isMobile ? '12px' : '14px' }}>/ {totalReviews}</Text>}
              valueStyle={{ fontSize: isMobile ? '18px' : '24px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card size={isMobile ? 'small' : 'default'} className="stat-card">
            <Statistic
              title={<Text style={{ fontSize: isMobile ? '12px' : '14px' }}>Reply Rate</Text>}
              value={replyRate.toFixed(0)}
              suffix="%"
              valueStyle={{ fontSize: isMobile ? '18px' : '24px', color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  const renderReplyActions = (review) => {
    const hasReply = review.replies && review.replies.length > 0;
    const reply = hasReply ? review.replies[0] : null;

    if (hasReply) {
      return (
        <div className="reply-actions" style={{ marginTop: isMobile ? '8px' : '12px' }}>
          <div className="existing-reply" style={{ 
            backgroundColor: '#e6f7ff', 
            padding: isMobile ? '8px' : '12px', 
            borderRadius: '6px',
            border: '1px solid #91d5ff'
          }}>
            <div className="reply-content">
              <Text strong style={{ fontSize: isMobile ? '12px' : '13px', color: '#1890ff' }}>Your Reply:</Text>
              <Text style={{ 
                display: 'block', 
                marginTop: '4px', 
                fontSize: isMobile ? '12px' : '13px',
                lineHeight: '1.4'
              }}>
                {reply.message}
              </Text>
              <div className="reply-meta" style={{ 
                marginTop: '6px', 
                display: 'flex', 
                alignItems: 'center', 
                gap: '8px',
                flexWrap: 'wrap'
              }}>
                <Text style={{ fontSize: isMobile ? '10px' : '11px', color: '#666' }}>
                  Posted on {formatDate(reply.createdAt)}
                </Text>
                {reply.isEdited && <Tag color="orange" size="small">Edited</Tag>}
              </div>
            </div>
            <div className="reply-buttons" style={{ 
              marginTop: '8px',
              display: 'flex',
              gap: isMobile ? '4px' : '8px',
              flexWrap: 'wrap'
            }}>
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => {
                  setEditingReply(reply._id);
                  editForm.setFieldValue('message', reply.message);
                }}
                size={isMobile ? 'small' : 'default'}
                style={{ padding: isMobile ? '2px 6px' : '4px 8px' }}
              >
                {!isMobile && 'Edit'}
              </Button>
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDeleteReply(reply._id)}
                size={isMobile ? 'small' : 'default'}
                style={{ padding: isMobile ? '2px 6px' : '4px 8px' }}
              >
                {!isMobile && 'Delete'}
              </Button>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="reply-actions" style={{ marginTop: isMobile ? '8px' : '12px' }}>
        <Button
          type="primary"
          icon={<SendOutlined />}
          onClick={() => setReplyingTo(review._id)}
          size={isMobile ? 'small' : 'default'}
          className="reply-btn"
          block={isMobile}
        >
          {isMobile ? 'Reply' : 'Reply to Review'}
        </Button>
      </div>
    );
  };

  const renderReplyForm = (reviewId) => {
    if (replyingTo !== reviewId) return null;

    return (
      <div className="reply-form">
        <Form
          form={replyForm}
          onFinish={handleReply}
          layout="vertical"
        >
          <Form.Item
            name="message"
            label="Your Reply"
            rules={[
              { required: true, message: 'Please enter your reply' },
              { min: 10, message: 'Reply must be at least 10 characters' },
              { max: 500, message: 'Reply cannot exceed 500 characters' }
            ]}
          >
            <TextArea
              rows={3}
              placeholder="Write a professional response to this review..."
              showCount
              maxLength={500}
            />
          </Form.Item>
          <Form.Item className="reply-form-actions">
            <Button
              type="primary"
              htmlType="submit"
              loading={submitting}
              size="small"
            >
              Post Reply
            </Button>
            <Button
              onClick={() => {
                setReplyingTo(null);
                replyForm.resetFields();
              }}
              size="small"
              className="cancel-btn"
            >
              Cancel
            </Button>
          </Form.Item>
        </Form>
      </div>
    );
  };

  const renderEditForm = (reply) => {
    if (editingReply !== reply._id) return null;

    return (
      <div className="edit-form">
        <Form
          form={editForm}
          onFinish={handleEditReply}
          layout="vertical"
        >
          <Form.Item
            name="message"
            label="Edit Reply"
            rules={[
              { required: true, message: 'Please enter your reply' },
              { min: 10, message: 'Reply must be at least 10 characters' },
              { max: 500, message: 'Reply cannot exceed 500 characters' }
            ]}
          >
            <TextArea
              rows={3}
              showCount
              maxLength={500}
            />
          </Form.Item>
          <Form.Item className="edit-form-actions">
            <Button
              type="primary"
              htmlType="submit"
              loading={submitting}
              size="small"
            >
              Update Reply
            </Button>
            <Button
              onClick={() => {
                setEditingReply(null);
                editForm.resetFields();
              }}
              size="small"
              className="cancel-btn"
            >
              Cancel
            </Button>
          </Form.Item>
        </Form>
      </div>
    );
  };

  const renderReviewItem = (review) => (
    <List.Item key={review._id} className="vendor-review-item" style={{ padding: isMobile ? '12px' : '16px' }}>
      <div className="review-content" style={{ width: '100%' }}>
        <div className="review-header" style={{ 
          flexDirection: isMobile ? 'column' : 'row',
          gap: isMobile ? '8px' : '16px',
          alignItems: isMobile ? 'flex-start' : 'center'
        }}>
          <div className="customer-info" style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: isMobile ? '8px' : '12px',
            flex: isMobile ? 'none' : '1'
          }}>
            <Avatar
              src={review.customer?.avatar}
              icon={<UserOutlined />}
              size={isMobile ? 'small' : 'default'}
            />
            <div className="customer-details">
              <Text strong className="customer-name" style={{ 
                fontSize: isMobile ? '13px' : '14px',
                display: 'block'
              }}>
                {review.customer?.firstName} {review.customer?.lastName}
              </Text>
              <div className="review-meta" style={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: isMobile ? '6px' : '8px',
                flexWrap: 'wrap'
              }}>
                <Rate disabled value={review.rating} size="small" style={{ fontSize: isMobile ? '12px' : '14px' }} />
                <Text className="review-date" style={{ 
                  fontSize: isMobile ? '11px' : '12px',
                  color: '#666'
                }}>
                  {formatDate(review.createdAt)}
                </Text>
              </div>
            </div>
          </div>
          <div className="product-info" style={{ 
            textAlign: isMobile ? 'left' : 'right',
            minWidth: isMobile ? 'auto' : '150px'
          }}>
            <Text className="product-name" style={{ 
              fontSize: isMobile ? '12px' : '13px',
              color: '#666',
              fontWeight: 500
            }}>
              {review.product?.name}
            </Text>
          </div>
        </div>

        <div className="review-comment" style={{ 
          margin: `${isMobile ? '8px' : '12px'} 0`,
          padding: isMobile ? '8px' : '12px',
          backgroundColor: '#f8f9fa',
          borderRadius: '6px'
        }}>
          <Text style={{ 
            fontSize: isMobile ? '13px' : '14px',
            lineHeight: '1.5',
            fontStyle: 'italic'
          }}>
            "{review.comment}"
          </Text>
        </div>

        {renderReplyActions(review)}
        {renderReplyForm(review._id)}
        
        {review.replies && review.replies.length > 0 && (
          <>
            {renderEditForm(review.replies[0])}
          </>
        )}
      </div>
    </List.Item>
  );

  if (loading) {
    return (
      <Card className="vendor-reviews-card">
        <div className="loading-container">
          <Spin size="large" />
          <p>Loading reviews...</p>
        </div>
      </Card>
    );
  }

  return (
    <Card
      title={
        <Text style={{ fontSize: isMobile ? '16px' : '18px', fontWeight: 600 }}>
          Customer Reviews
        </Text>
      }
      className="vendor-reviews-card"
      size={isMobile ? 'small' : 'default'}
      style={{ margin: 0 }}
    >
      {renderReviewStats()}

      {reviews.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <div style={{ textAlign: 'center' }}>
              <Text style={{ fontSize: isMobile ? '14px' : '16px', color: '#666' }}>
                No reviews yet
              </Text>
              <div style={{ marginTop: '8px' }}>
                <Text style={{ fontSize: isMobile ? '12px' : '14px', color: '#999' }}>
                  Your products haven't received any reviews yet.
                </Text>
              </div>
            </div>
          }
          className="empty-reviews"
          style={{ padding: isMobile ? '20px 10px' : '40px 20px' }}
        />
      ) : (
        <>
          <List
            dataSource={reviews}
            renderItem={renderReviewItem}
            className="vendor-reviews-list"
            style={{ marginTop: isMobile ? '8px' : '16px' }}
            split={true}
          />

          {pagination && pagination.totalPages > 1 && (
            <div className="pagination-container" style={{ 
              textAlign: 'center', 
              marginTop: isMobile ? '16px' : '24px',
              padding: isMobile ? '8px' : '16px'
            }}>
              <Pagination
                current={pagination.currentPage}
                total={pagination.totalReviews}
                pageSize={10}
                showSizeChanger={!isMobile}
                showQuickJumper={!isMobile}
                showTotal={!isMobile ? (total, range) =>
                  `${range[0]}-${range[1]} of ${total} reviews` : false
                }
                onChange={onPageChange}
                size={isMobile ? 'small' : 'default'}
                simple={isMobile}
              />
            </div>
          )}
        </>
      )}
    </Card>
  );
};

export default VendorReviews;
