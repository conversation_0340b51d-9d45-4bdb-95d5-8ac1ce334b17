const Cart = require('../models/Cart');
const Product = require('../models/Product');
const User = require('../models/User');
const asyncHandler = require('express-async-handler');

// @desc    Get customer's cart
// @route   GET /api/customer/cart
// @access  Private (Customer)
const getCart = asyncHandler(async (req, res) => {
  try {
    const userCurrency = req.userCurrency || 'INR';
    let cart = await Cart.findByCustomerWithCurrency(req.user._id, userCurrency);
    
    if (!cart) {
      cart = await Cart.getOrCreateForCustomer(req.user._id);
      
      // Get the newly created cart with currency transformation
      cart = await Cart.findByCustomerWithCurrency(req.user._id, userCurrency);
    }
    
    res.json({
      success: true,
      data: cart
    });
  } catch (error) {
    console.error('Error fetching cart for user:', req.user._id, error);
    res.status(500).json({
      success: false,
      message: 'Error fetching cart',
      error: error.message
    });
  }
});

// @desc    Add item to cart
// @route   POST /api/cart/add
// @access  Private (Customer)
const addToCart = asyncHandler(async (req, res) => {
  try {
    const { productId, quantity = 1, variantSku } = req.body;
    
    if (!productId) {
      return res.status(400).json({
        success: false,
        message: 'Product ID is required'
      });
    }
    
    // Check if product exists and is active
    const product = await Product.findById(productId).populate('vendor', 'businessName');
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }
    
    if (product.status !== 'active') {
      return res.status(400).json({
        success: false,
        message: 'Product is not available for purchase'
      });
    }
    
    // Check stock availability
    const availableStock = product.inventory?.quantity || 0;
    if (availableStock < quantity) {
      return res.status(400).json({
        success: false,
        message: `Only ${availableStock} items available in stock`
      });
    }
    
    // Handle variant selection
    let selectedVariant = null;
    let price = product.pricing?.salePrice || product.pricing?.basePrice || 0;
    
    if (variantSku && product.variants && product.variants.length > 0) {
      selectedVariant = product.variants.find(v => v.sku === variantSku);
      if (!selectedVariant) {
        return res.status(400).json({
          success: false,
          message: 'Selected variant not found'
        });
      }
      price = selectedVariant.price || price;
    }
    
    // Find or create cart
    let cart = await Cart.findOne({ customer: req.user._id });
    if (!cart) {
      cart = new Cart({ customer: req.user._id });
    }
    
    // Add item to cart
    await cart.addItem(productId, product.vendor._id, quantity, price, selectedVariant);
    
    // Return updated cart with currency transformation
    const userCurrency = req.userCurrency || 'INR';
    const updatedCart = await Cart.findByCustomerWithCurrency(req.user._id, userCurrency);
    
    res.json({
      success: true,
      message: 'Item added to cart successfully',
      data: updatedCart
    });
  } catch (error) {
    console.error('Add to cart error:', error);
    res.status(500).json({
      success: false,
      message: 'Error adding item to cart',
      error: error.message
    });
  }
});

// @desc    Update cart item quantity
// @route   PUT /api/cart/update
// @access  Private (Customer)
const updateCartItem = asyncHandler(async (req, res) => {
  try {
    const { productId, quantity, variantSku } = req.body;
    
    // Enhanced logging for debugging
    console.log('Cart update request:', {
      userId: req.user._id,
      productId,
      quantity,
      variantSku: variantSku || 'null'
    });
    
    if (!productId || quantity === undefined) {
      return res.status(400).json({
        success: false,
        message: 'Product ID and quantity are required'
      });
    }
    
    const cart = await Cart.findOne({ customer: req.user._id });
    if (!cart) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }
    
    // Log current cart items for debugging
    console.log('Current cart items:', cart.items.map(item => ({
      productId: item.product.toString(),
      quantity: item.quantity,
      variantSku: item.selectedVariant?.sku || 'null'
    })));
    
    // Find the item to update (for logging)
    const itemToUpdate = cart.items.find(item => {
      const productMatch = item.product.toString() === productId.toString();
      const variantMatch = (!variantSku && !item.selectedVariant?.sku) ||
                          (variantSku && item.selectedVariant?.sku === variantSku);
      return productMatch && variantMatch;
    });
    
    if (!itemToUpdate) {
      console.log('Item not found for update. Looking for:', {
        productId: productId.toString(),
        variantSku: variantSku || 'null'
      });
    } else {
      console.log('Found item to update:', {
        productId: itemToUpdate.product.toString(),
        currentQuantity: itemToUpdate.quantity,
        variantSku: itemToUpdate.selectedVariant?.sku || 'null'
      });
    }
    
    // If quantity > 0, check stock availability
    if (quantity > 0) {
      const product = await Product.findById(productId);
      if (!product) {
        return res.status(404).json({
          success: false,
          message: 'Product not found'
        });
      }
      
      const availableStock = product.inventory?.quantity || 0;
      if (availableStock < quantity) {
        return res.status(400).json({
          success: false,
          message: `Only ${availableStock} items available in stock`
        });
      }
    }
    
    await cart.updateItemQuantity(productId, quantity, variantSku);
    
    // Return updated cart with currency transformation
    const userCurrency = req.userCurrency || 'INR';
    const updatedCart = await Cart.findByCustomerWithCurrency(req.user._id, userCurrency);
    
    res.json({
      success: true,
      message: quantity > 0 ? 'Cart updated successfully' : 'Item removed from cart',
      data: updatedCart
    });
  } catch (error) {
    if (error.message === 'Item not found in cart') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Error updating cart item',
      error: error.message
    });
  }
});

// @desc    Remove item from cart
// @route   DELETE /api/cart/remove/:productId
// @access  Private (Customer)
const removeFromCart = asyncHandler(async (req, res) => {
  try {
    const { productId } = req.params;
    const { variantSku } = req.query;
    
    const cart = await Cart.findOne({ customer: req.user._id });
    if (!cart) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }
    
    await cart.removeItem(productId, variantSku);
    
    // Return updated cart with currency transformation
    const userCurrency = req.userCurrency || 'INR';
    const updatedCart = await Cart.findByCustomerWithCurrency(req.user._id, userCurrency);
    
    res.json({
      success: true,
      message: 'Item removed from cart successfully',
      data: updatedCart
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error removing item from cart',
      error: error.message
    });
  }
});

// @desc    Clear entire cart
// @route   DELETE /api/cart/clear
// @access  Private (Customer)
const clearCart = asyncHandler(async (req, res) => {
  try {
    const cart = await Cart.findOne({ customer: req.user._id });
    if (!cart) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }
    
    await cart.clearCart();
    
    res.json({
      success: true,
      message: 'Cart cleared successfully',
      data: cart
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error clearing cart',
      error: error.message
    });
  }
});

// @desc    Get cart summary/count
// @route   GET /api/cart/summary
// @access  Private (Customer)
const getCartSummary = asyncHandler(async (req, res) => {
  try {
    const cart = await Cart.findOne({ customer: req.user._id });
    
    const summary = {
      totalItems: cart ? cart.totalItems : 0,
      totalAmount: cart ? cart.totalAmount : 0,
      vendorCount: cart ? cart.vendorCount : 0,
      itemCount: cart ? cart.items.length : 0
    };
    
    res.json({
      success: true,
      data: summary
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching cart summary',
      error: error.message
    });
  }
});

// @desc    Bulk add items to cart
// @route   POST /api/cart/bulk-add
// @access  Private (Customer)
const bulkAddToCart = asyncHandler(async (req, res) => {
  try {
    const { items } = req.body;
    
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Items array is required'
      });
    }
    
    // Validate each item
    const validatedItems = [];
    for (const item of items) {
      const { productId, quantity = 1, variantSku } = item;
      
      if (!productId) {
        return res.status(400).json({
          success: false,
          message: 'Each item must have a productId'
        });
      }
      
      // Check if product exists and is active
      const product = await Product.findById(productId).populate('vendor', 'businessName');
      if (!product || product.status !== 'active') {
        return res.status(400).json({
          success: false,
          message: `Product ${productId} is not available`
        });
      }
      
      // Check stock availability
      const availableStock = product.inventory?.quantity || 0;
      if (availableStock < quantity) {
        return res.status(400).json({
          success: false,
          message: `Only ${availableStock} items available for product ${product.name}`
        });
      }
      
      // Handle variant selection
      let selectedVariant = null;
      let price = product.pricing?.salePrice || product.pricing?.basePrice || 0;
      
      if (variantSku && product.variants && product.variants.length > 0) {
        selectedVariant = product.variants.find(v => v.sku === variantSku);
        if (!selectedVariant) {
          return res.status(400).json({
            success: false,
            message: `Selected variant ${variantSku} not found for product ${product.name}`
          });
        }
        price = selectedVariant.price || price;
      }
      
      validatedItems.push({
        productId,
        vendorId: product.vendor._id,
        quantity,
        price,
        variant: selectedVariant
      });
    }
    
    // Find or create cart
    let cart = await Cart.findOne({ customer: req.user._id });
    if (!cart) {
      cart = new Cart({ customer: req.user._id });
    }
    
    // Bulk add items
    await cart.bulkAddItems(validatedItems);
    
    // Return updated cart with currency transformation
    const userCurrency = req.userCurrency || 'INR';
    const updatedCart = await Cart.findByCustomerWithCurrency(req.user._id, userCurrency);
    
    res.json({
      success: true,
      message: `${validatedItems.length} items added to cart successfully`,
      data: updatedCart
    });
  } catch (error) {
    console.error('Bulk add to cart error:', error);
    res.status(500).json({
      success: false,
      message: 'Error adding items to cart',
      error: error.message
    });
  }
});

// @desc    Bulk update cart items
// @route   PUT /api/cart/bulk-update
// @access  Private (Customer)
const bulkUpdateCart = asyncHandler(async (req, res) => {
  try {
    const { items } = req.body;
    
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Items array is required'
      });
    }
    
    const cart = await Cart.findOne({ customer: req.user._id });
    if (!cart) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }
    
    // Validate each update
    for (const item of items) {
      const { productId, quantity, variantSku } = item;
      
      if (!productId || quantity === undefined) {
        return res.status(400).json({
          success: false,
          message: 'Each item must have productId and quantity'
        });
      }
      
      // If quantity > 0, check stock availability
      if (quantity > 0) {
        const product = await Product.findById(productId);
        if (!product) {
          return res.status(404).json({
            success: false,
            message: `Product ${productId} not found`
          });
        }
        
        const availableStock = product.inventory?.quantity || 0;
        if (availableStock < quantity) {
          return res.status(400).json({
            success: false,
            message: `Only ${availableStock} items available for product ${product.name}`
          });
        }
      }
    }
    
    // Bulk update items
    await cart.bulkUpdateItems(items);
    
    // Return updated cart with currency transformation
    const userCurrency = req.userCurrency || 'INR';
    const updatedCart = await Cart.findByCustomerWithCurrency(req.user._id, userCurrency);
    
    res.json({
      success: true,
      message: 'Cart updated successfully',
      data: updatedCart
    });
  } catch (error) {
    console.error('Bulk update cart error:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating cart items',
      error: error.message
    });
  }
});

module.exports = {
  getCart,
  addToCart,
  updateCartItem,
  removeFromCart,
  clearCart,
  getCartSummary,
  bulkAddToCart,
  bulkUpdateCart
};
