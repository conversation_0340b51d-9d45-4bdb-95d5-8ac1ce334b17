import React from 'react';
import { Card, Space, Progress } from 'antd';
import useResponsive from '../../../hooks/useResponsive';

const PerformanceMetrics = ({ dashboardData, loading }) => {
  const { isMobile } = useResponsive();
  
  const metricsData = [
    {
      label: "Average Customer Rating",
      value: `${dashboardData?.products?.averageRating?.toFixed(1) || 'N/A'}/5.0`,
      percentage: (dashboardData?.products?.averageRating || 0) * 20,
      color: '#faad14'
    },
    {
      label: "Order Fulfillment Rate",
      value: (() => {
        const totalOrders = dashboardData?.orders?.totalOrders || 0;
        const deliveredOrders = dashboardData?.orders?.deliveredOrders || 0;
        const rate = totalOrders > 0 ? ((deliveredOrders / totalOrders) * 100).toFixed(1) : '0.0';
        return `${rate}%`;
      })(),
      percentage: (() => {
        const totalOrders = dashboardData?.orders?.totalOrders || 0;
        const deliveredOrders = dashboardData?.orders?.deliveredOrders || 0;
        return totalOrders > 0 ? ((deliveredOrders / totalOrders) * 100) : 0;
      })(),
      color: '#52c41a'
    },
    {
      label: "Active Products",
      value: `${dashboardData?.products?.activeProducts || 0} / ${dashboardData?.products?.totalProducts || 0}`,
      percentage: (() => {
        const total = dashboardData?.products?.totalProducts || 0;
        const active = dashboardData?.products?.activeProducts || 0;
        return total > 0 ? ((active / total) * 100) : 0;
      })(),
      color: '#1890ff'
    },
    {
      label: "Today's Performance",
      value: `${dashboardData?.today?.todayOrders || 0} orders today`,
      percentage: Math.min(((dashboardData?.today?.todayOrders || 0) / Math.max(dashboardData?.orders?.totalOrders || 1, 10)) * 100, 100),
      color: '#722ed1'
    }
  ];
  
  return (
    <Card 
      title="Performance Metrics" 
      size={isMobile ? 'small' : 'default'}
      loading={loading}
    >
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        {metricsData.map((metric, index) => (
          <div key={index}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              marginBottom: '8px',
              fontSize: isMobile ? '12px' : '14px'
            }}>
              <span>{metric.label}</span>
              <span style={{ 
                fontWeight: 500,
                color: metric.color
              }}>
                {metric.value}
              </span>
            </div>
            <Progress 
              percent={metric.percentage} 
              strokeColor={metric.color} 
              size="small"
              showInfo={false}
            />
          </div>
        ))}
      </Space>
    </Card>
  );
};

export default PerformanceMetrics;
