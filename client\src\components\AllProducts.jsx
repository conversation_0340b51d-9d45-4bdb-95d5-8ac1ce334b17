import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { message, Spin } from 'antd';
import { ShoppingCartOutlined, StarFilled, EnvironmentOutlined } from '@ant-design/icons';
import { productsApi } from '../services/publicApi';
import ProductImage from './ui/ProductImage';
import { getPrimaryProductImage, getAbsoluteImageUrl } from '../utils/imageUtils';
import { useCurrency } from '../contexts/CurrencyContext';

const AllProducts = ({ limit }) => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const { currentCurrency, filterProductsByCurrency, getProductPriceInCurrency, getCurrencySymbol } = useCurrency();

  // Simple function to get image URL from product
  const getProductImageUrl = (product) => {
    console.log('Getting image for product:', product.name, 'Images:', product.images);
    
    if (!product || !product.images || !Array.isArray(product.images) || product.images.length === 0) {
      console.log('No images found for product:', product?.name);
      return null;
    }

    // Try to find primary image first
    const primaryImage = product.images.find(img => img && img.isPrimary);
    if (primaryImage && primaryImage.url) {
      const url = getAbsoluteImageUrl(primaryImage.url);
      console.log('Using primary image:', url);
      return url;
    }

    // Fallback to first image
    const firstImage = product.images[0];
    if (firstImage) {
      const url = typeof firstImage === 'string' ? firstImage : firstImage.url;
      const absoluteUrl = getAbsoluteImageUrl(url);
      console.log('Using first image:', absoluteUrl);
      return absoluteUrl;
    }

    console.log('No usable image found for product:', product.name);
    return null;
  };

  const fetchAllProducts = useCallback(async () => {
    try {
      setLoading(true);
      const params = limit ? { limit } : {};
      const response = await productsApi.getProducts(params);

      if (response?.success && response?.data?.products) {
        const products = response.data.products;

        // Apply currency filtering
        const filteredProducts = filterProductsByCurrency(products);
        setProducts(filteredProducts);
      } else {
        console.warn('No products found');
        setProducts([]);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      message.error('Failed to load products');
      setProducts([]);
    } finally {
      setLoading(false);
    }
  }, [limit, filterProductsByCurrency]);

  useEffect(() => {
    fetchAllProducts();
  }, [fetchAllProducts]);

  const handleProductClick = (productId) => {
    navigate(`/product/${productId}`);
  };

  const handleCartClick = (e, productId) => {
    e.stopPropagation();
    console.log('Added to cart:', productId);
    message.success('Added to cart');
  };

  if (loading) {
    return (
      <div className="text-center py-12">
        <Spin size="large" />
        <p className="mt-4 text-gray-500">Loading products...</p>
      </div>
    );
  }

  if (!products.length) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 mb-4">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Products Available</h3>
        <p className="text-gray-500">
          No products are currently available. Vendors can add products from their dashboard.
        </p>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-7xl">
      <div className="px-4 py-2">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">All Products</h2>
      </div>
      <div className="grid gap-3 p-4 grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-4 xl:grid-cols-4">
        {products.map(product => (
          <div
            key={product._id}
            onClick={() => handleProductClick(product._id)}
            className="bg-white rounded-lg overflow-hidden shadow-sm border border-gray-100 hover:shadow-md hover:border-gray-200 transition-all duration-300 transform hover:-translate-y-1 cursor-pointer"
          >
            {/* Image container: square */}
            <div className="relative w-full pb-[100%] bg-gray-100">
              <img
                src={getProductImageUrl(product) || 'https://via.placeholder.com/400x400?text=No+Image'}
                alt={product.name}
                className="absolute inset-0 w-full h-full object-cover"
                onError={(e) => {
                  console.error('Direct img failed to load:', e.target.src);
                  e.target.src = 'https://via.placeholder.com/400x400?text=Image+Error';
                }}
                onLoad={(e) => {
                  console.log('Direct img loaded successfully:', e.target.src);
                }}
              />
            </div>

            {/* Content */}
            <div className="p-2 sm:p-3 flex flex-col justify-between h-36 sm:h-44">
              <div>
                {/* Title: clamp to 2 lines */}
                <h3 className="text-xs sm:text-sm font-medium text-gray-800 leading-tight mb-1"
                    style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      minHeight: '2.5em'
                    }}>
                  {product.name}
                </h3>
                
                {/* Dynamic Price */}
                <div className="mb-1">
                  {(() => {
                    const priceData = getProductPriceInCurrency(product);
                    if (priceData) {
                      const { salePrice, basePrice, currency } = priceData;
                      return (
                        basePrice && salePrice && salePrice < basePrice ? (
                          <div className="flex items-center space-x-2">
                            <span className="text-sm sm:text-base font-bold text-[#ed2b2a]">
                              {getCurrencySymbol(currency)} {salePrice}
                            </span>
                            <span className="text-xs sm:text-sm text-gray-500 line-through">
                              {getCurrencySymbol(currency)} {basePrice}
                            </span>
                          </div>
                        ) : (
                          <span className="text-sm sm:text-base font-bold text-[#ed2b2a]">
                            {getCurrencySymbol(currency)} {basePrice || salePrice || 0}
                          </span>
                        )
                      );
                    }
                    return null;
                  })()}
                </div>
                
                {/* Rating and Stock */}
                <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                  <div className="flex items-center">
                    <StarFilled className="text-yellow-400 text-xs" />
                    <span className="ml-1">{product.reviews?.averageRating?.toFixed(1) || '0.0'}</span>
                    <span className="ml-1">({product.reviews?.totalReviews || 0})</span>
                  </div>
                  <span className={`${product.inventory?.quantity > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {product.inventory?.quantity > 0 ? 'In Stock' : 'Out of Stock'}
                  </span>
                </div>
              </div>

              {/* Footer row: vendor + actions */}
              <div className="flex items-center justify-between mt-auto pt-2 border-t border-gray-50">
                <span className="text-xs text-gray-500 flex items-center">
                  <EnvironmentOutlined className="mr-1 text-xs" />
                  {product.vendor?.businessName || 'Store'}
                </span>
                <div className="flex space-x-2">
                  <button 
                    onClick={(e) => handleCartClick(e, product._id)}
                    className="text-gray-400 hover:text-blue-600 cursor-pointer transition-colors duration-200 p-1 rounded hover:bg-gray-50"
                    title="Add to Cart"
                    disabled={product.inventory?.quantity <= 0}
                  >
                    <ShoppingCartOutlined className="text-sm" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AllProducts;