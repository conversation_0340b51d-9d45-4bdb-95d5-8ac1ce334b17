import React, { useEffect, useState } from 'react';
import { productsApi } from '../services/publicApi';
import { getPrimaryProductImage, getAbsoluteImageUrl } from '../utils/imageUtils';
import ProductImage from './ui/ProductImage';

const ImageDebugger = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await productsApi.getProducts({ limit: 3 });
        if (response.data.success) {
          const products = response.data.data.products || response.data.data;
          setProducts(products);
          
          // Debug each product
          products.forEach((product, index) => {
            console.log(`\n=== Product ${index + 1}: ${product.name} ===`);
            console.log('Raw images:', product.images);
            console.log('Primary image result:', getPrimaryProductImage(product));
            
            if (product.images && product.images.length > 0) {
              product.images.forEach((img, imgIndex) => {
                const url = typeof img === 'string' ? img : img.url;
                console.log(`Image ${imgIndex + 1}:`, url, '-> Absolute:', getAbsoluteImageUrl(url));
              });
            }
          });
        }
      } catch (error) {
        console.error('Error fetching products for debug:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  if (loading) {
    return <div className="p-4">Loading products for debugging...</div>;
  }

  return (
    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
      <h3 className="text-lg font-bold mb-4">Image Debug Information</h3>
      
      {products.map((product, index) => (
        <div key={product._id} className="mb-6 p-4 bg-white rounded border">
          <h4 className="font-semibold mb-2">Product {index + 1}: {product.name}</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h5 className="font-medium mb-2">Raw Data:</h5>
              <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
                {JSON.stringify(product.images, null, 2)}
              </pre>
              
              <h5 className="font-medium mb-2 mt-4">Processed URLs:</h5>
              <ul className="text-sm">
                <li><strong>Primary Image:</strong> {getPrimaryProductImage(product) || 'None'}</li>
                {product.images && product.images.map((img, imgIndex) => {
                  const url = typeof img === 'string' ? img : img.url;
                  return (
                    <li key={imgIndex}>
                      <strong>Image {imgIndex + 1}:</strong> {getAbsoluteImageUrl(url) || 'Invalid'}
                    </li>
                  );
                })}
              </ul>
            </div>
            
            <div>
              <h5 className="font-medium mb-2">Rendered Image:</h5>
              <div className="w-32 h-32 border border-gray-300 rounded">
                <ProductImage
                  src={getPrimaryProductImage(product)}
                  alt={product.name}
                  className="w-full h-full object-cover rounded"
                  fallbackSrc="https://via.placeholder.com/128x128?text=Debug+Fallback"
                />
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ImageDebugger;