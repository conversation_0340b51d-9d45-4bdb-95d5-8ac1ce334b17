// Auth error handler utility
export const handleAuthError = (error) => {
  // Check if it's a token validation error
  if (error.message?.includes('Token is not valid') || 
      error.response?.status === 401 || 
      error.response?.status === 403) {
    
    console.log('🔒 Authentication error detected, clearing auth data');
    
    // Clear all auth-related localStorage items
    const authKeys = ['authToken', 'authUser', 'authUserType', 'refreshToken'];
    authKeys.forEach(key => {
      localStorage.removeItem(key);
    });
    
    // Reload the page to reset the app state
    window.location.reload();
    
    return true; // Indicates auth error was handled
  }
  
  return false; // Not an auth error
};

// Check if token is expired
export const isTokenExpired = (token) => {
  if (!token) return true;
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true;
  }
};

// Get valid token from localStorage
export const getValidToken = () => {
  const token = localStorage.getItem('authToken');
  
  if (!token || isTokenExpired(token)) {
    // Clear expired token
    localStorage.removeItem('authToken');
    localStorage.removeItem('authUser');
    localStorage.removeItem('authUserType');
    return null;
  }
  
  return token;
};