import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { productsApi } from '../services/publicApi';

const ProductDebugPage = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const response = await productsApi.getProducts({ limit: 20 });
        
        if (response?.data?.success) {
          setProducts(response.data.data.products || []);
        } else {
          throw new Error('Failed to fetch products');
        }
      } catch (err) {
        console.error('Error fetching products:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-4">Loading Products...</h1>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-4 text-red-600">Error Loading Products</h1>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Product Debug Page</h1>
          <p className="text-gray-600">
            This page shows all available products with their valid URLs for testing.
          </p>
          <button
            onClick={() => navigate('/')}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
          >
            Back to Home
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold mb-4">Available Products ({products.length})</h2>
          
          {products.length === 0 ? (
            <p className="text-gray-500">No products found. Please add some products from the vendor dashboard.</p>
          ) : (
            <div className="space-y-4">
              {products.map((product, index) => (
                <div key={product._id} className="border-b pb-4 last:border-b-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-800">{product.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">
                        ID: <code className="bg-gray-100 px-2 py-1 rounded text-xs">{product._id}</code>
                      </p>
                      <p className="text-sm text-gray-600">
                        Price: ${product.pricing?.salePrice || product.pricing?.basePrice || 0}
                      </p>
                      <p className="text-sm text-gray-600">
                        Status: <span className={`px-2 py-1 rounded text-xs ${
                          product.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {product.status}
                        </span>
                      </p>
                      <div className="mt-2">
                        <p className="text-sm text-gray-600">Valid URL:</p>
                        <code className="text-xs bg-blue-50 text-blue-800 px-2 py-1 rounded block mt-1">
                          http://localhost:5173/product/{product._id}
                        </code>
                      </div>
                    </div>
                    <div className="ml-4 space-y-2">
                      <button
                        onClick={() => navigate(`/product/${product._id}`)}
                        className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors block w-full"
                      >
                        View Product
                      </button>
                      <button
                        onClick={() => {
                          navigator.clipboard.writeText(`http://localhost:5173/product/${product._id}`);
                          alert('URL copied to clipboard!');
                        }}
                        className="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700 transition-colors block w-full"
                      >
                        Copy URL
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">Testing Invalid URLs</h3>
          <p className="text-yellow-700 mb-4">
            Try these invalid URLs to test the error handling:
          </p>
          <div className="space-y-2">
            <div>
              <code className="text-sm bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                http://localhost:5173/product/6881d8b66525760610a0e95
              </code>
              <button
                onClick={() => navigate('/product/6881d8b66525760610a0e95')}
                className="ml-2 bg-yellow-600 text-white px-2 py-1 rounded text-sm hover:bg-yellow-700 transition-colors"
              >
                Test
              </button>
            </div>
            <div>
              <code className="text-sm bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                http://localhost:5173/product/invalid-id
              </code>
              <button
                onClick={() => navigate('/product/invalid-id')}
                className="ml-2 bg-yellow-600 text-white px-2 py-1 rounded text-sm hover:bg-yellow-700 transition-colors"
              >
                Test
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDebugPage;
