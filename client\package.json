{"name": "ecommerce", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-slot": "^1.2.3", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.11", "antd": "^5.26.4", "axios": "^1.10.0", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "currency-codes": "^2.2.0", "dayjs": "^1.11.13", "i18next": "^25.3.2", "iso-639-1": "^3.1.5", "jwt-decode": "^4.0.0", "libphonenumber-js": "^1.12.10", "lucide-react": "^0.532.0", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-i18next": "^15.6.1", "react-phone-number-input": "^3.4.12", "react-redux": "^9.2.0", "react-router-dom": "^7.6.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "world-countries": "^5.1.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "terser": "^5.43.1", "tw-animate-css": "^1.3.6", "vite": "^7.0.4"}}