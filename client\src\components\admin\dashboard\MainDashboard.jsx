import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Button } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';

// Import custom hook and components
import useDashboardData from './useDashboardData';
import DashboardHeader from './DashboardHeader';
import StatisticsCards from './StatisticsCards';
import ChartsSection from './ChartsSection';
import RecentActivityTables from './RecentActivityTables';
import AlertsSection from './AlertsSection';

const MainDashboard = () => {
  // Use custom hook for data management
  const {
    loading,
    refreshing,
    dashboardData,
    realTimeData,
    recentOrders,
    recentUsers,
    pendingApprovals,
    vendorStats,
    productStats,
    alerts,
    analyticsData,
    selectedPeriod,
    error,
    handleRefresh,
    handlePeriodChange
  } = useDashboardData();

  // Loading state
  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        background: '#f0f2f5'
      }}>
        <Spin size="large" tip="Loading dashboard..." />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
        <Alert
          message="Dashboard Error"
          description={error}
          type="error"
          action={
            <Button 
              onClick={handleRefresh}
              icon={<ReloadOutlined />}
              type="primary"
            >
              Retry
            </Button>
          }
          style={{
            borderRadius: '8px',
            border: 'none',
            boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
          }}
        />
      </div>
    );
  }

  return (
    <div>
      {/* Header Section */}
      <DashboardHeader
        onRefresh={handleRefresh}
        loading={refreshing}
        lastUpdated={realTimeData?.timestamp}
        userName="Rahul Raaj"
      />



      {/* Alerts Section */}
      <AlertsSection alerts={alerts} />

      {/* Statistics Cards Section */}
      <StatisticsCards 
        dashboardData={dashboardData}
        realTimeData={realTimeData}
        vendorStats={vendorStats}
        productStats={productStats}
        pendingApprovals={pendingApprovals}
      />

      {/* Charts Section */}
      <ChartsSection 
        analyticsData={analyticsData}
        dashboardData={dashboardData}
        selectedPeriod={selectedPeriod}
        onPeriodChange={handlePeriodChange}
        loading={refreshing}
      />

      {/* Recent Activity Tables Section */}
      <RecentActivityTables 
        recentOrders={recentOrders}
        recentUsers={recentUsers}
      />
    </div>
  );
};

export default MainDashboard;
