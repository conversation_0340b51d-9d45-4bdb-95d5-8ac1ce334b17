const mongoose = require('mongoose');

const settingSchema = new mongoose.Schema({
  key: {
    type: String,
    required: [true, 'Setting key is required'],
    unique: true,
    trim: true,
    maxlength: [100, 'Setting key cannot exceed 100 characters']
  },
  value: {
    type: mongoose.Schema.Types.Mixed,
    required: [true, 'Setting value is required']
  },
  type: {
    type: String,
    enum: ['string', 'number', 'boolean', 'object', 'array'],
    default: 'string'
  },
  category: {
    type: String,
    enum: ['general', 'email', 'payment', 'security', 'vendor', 'shipping', 'tax', 'appearance', 'seo'],
    default: 'general'
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  isPublic: {
    type: Boolean,
    default: false
  },
  isEditable: {
    type: Boolean,
    default: true
  },
  validation: {
    required: {
      type: Boolean,
      default: false
    },
    min: Number,
    max: Number,
    pattern: String,
    options: [String]
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes (key index is automatically created by unique: true)
settingSchema.index({ category: 1 });
settingSchema.index({ isPublic: 1 });
settingSchema.index({ updatedAt: -1 });

// Static method to get settings by category
settingSchema.statics.getByCategory = function(category) {
  return this.find({ category }).lean();
};

// Static method to get public settings
settingSchema.statics.getPublicSettings = function() {
  return this.find({ isPublic: true }).lean();
};

// Static method to get setting value by key
settingSchema.statics.getValue = async function(key, defaultValue = null) {
  const setting = await this.findOne({ key }).lean();
  return setting ? setting.value : defaultValue;
};

// Static method to set setting value
settingSchema.statics.setValue = async function(key, value, updatedBy = null) {
  return this.findOneAndUpdate(
    { key },
    {
      key,
      value,
      updatedBy,
      updatedAt: new Date()
    },
    {
      upsert: true,
      new: true,
      setDefaultsOnInsert: true
    }
  );
};

// Static method to get all settings as key-value pairs
settingSchema.statics.getAllAsObject = async function() {
  const settings = await this.find({}).lean();
  const settingsObject = {};

  settings.forEach(setting => {
    settingsObject[setting.key] = setting.value;
  });

  return settingsObject;
};

module.exports = mongoose.models.Setting || mongoose.model('Setting', settingSchema);