const HomepageSettings = require('../../models/HomepageSettings');
const Category = require('../../models/Category');
const { deleteImage, extractPublicId } = require('../../config/cloudinary');

// Get homepage settings
const getHomepageSettings = async (req, res) => {
  try {
    // Get settings with population
    let settings = await HomepageSettings.findOne()
      .populate('featuredCategories.category', 'name slug');

    // If no settings exist, create default settings
    if (!settings) {
      settings = await HomepageSettings.create({
        heroSection: {
          title: 'Welcome to Our Store',
          subtitle: 'Discover amazing products from trusted vendors',
          backgroundImage: '',
          ctaText: 'Shop Now',
          ctaLink: '/products'
        },
        featuredCategories: [],
        promotionalBanners: [],
        testimonials: []
      });
    }

    res.status(200).json({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('Error fetching homepage settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch homepage settings',
      error: error.message
    });
  }
};

// Update general settings
const updateGeneralSettings = async (req, res) => {
  try {
    const { autoPlayCarousel, carouselSpeed, showPromotions, maxCarouselImages, maxPromotionImages, featuredCategoryIds } = req.body;

    const settings = await HomepageSettings.getSettings();

    if (autoPlayCarousel !== undefined) settings.settings.autoPlayCarousel = autoPlayCarousel;
    if (carouselSpeed !== undefined) settings.settings.carouselSpeed = carouselSpeed;
    if (showPromotions !== undefined) settings.settings.showPromotions = showPromotions;
    if (maxCarouselImages !== undefined) settings.settings.maxCarouselImages = maxCarouselImages;
    if (maxPromotionImages !== undefined) settings.settings.maxPromotionImages = maxPromotionImages;
    if (featuredCategoryIds !== undefined) {
      // Limit to 5 categories
      settings.featuredCategoryIds = featuredCategoryIds.slice(0, 5);
    }

    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(200).json({
      success: true,
      message: 'Settings updated successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error updating settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update settings',
      error: error.message
    });
  }
};

// Add carousel image
const addCarouselImage = async (req, res) => {
  try {
    console.log('🎠 Adding carousel image...');
    console.log('📝 Request body:', req.body);

    const { title, description, linkUrl, imageUrl } = req.body;

    if (!imageUrl) {
      return res.status(400).json({
        success: false,
        message: 'Image URL is required'
      });
    }

    // Validate URL format
    if (!/^https?:\/\/.+/.test(imageUrl)) {
      return res.status(400).json({
        success: false,
        message: 'Please provide a valid HTTP/HTTPS image URL'
      });
    }

    const settings = await HomepageSettings.getSettings();

    // Check if we've reached the maximum number of carousel images
    if (settings.carouselImages.length >= (settings.settings?.maxCarouselImages || 10)) {
      return res.status(400).json({
        success: false,
        message: `Maximum ${settings.settings?.maxCarouselImages || 10} carousel images allowed`
      });
    }

    const imageData = {
      title: title || '',
      description: description || '',
      imageUrl,
      linkUrl: linkUrl || '',
      isActive: true,
      sortOrder: settings.carouselImages.length
    };

    console.log('💾 Saving image data:', imageData);
    await settings.addCarouselImage(imageData);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    console.log('🎉 Carousel image saved successfully');
    res.status(201).json({
      success: true,
      message: 'Carousel image added successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error adding carousel image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add carousel image',
      error: error.message
    });
  }
};

// Update carousel image
const updateCarouselImage = async (req, res) => {
  try {
    const { imageId } = req.params;
    const { title, description, linkUrl, imageUrl, isActive, sortOrder } = req.body;

    const settings = await HomepageSettings.getSettings();
    const imageIndex = settings.carouselImages.findIndex(img => img._id.toString() === imageId);

    if (imageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Carousel image not found'
      });
    }

    const image = settings.carouselImages[imageIndex];

    // Update image URL if provided
    if (imageUrl !== undefined) {
      // Validate URL format
      if (imageUrl && !/^https?:\/\/.+/.test(imageUrl)) {
        return res.status(400).json({
          success: false,
          message: 'Please provide a valid HTTP/HTTPS image URL'
        });
      }
      image.imageUrl = imageUrl;
    }

    // Update other fields
    if (title !== undefined) image.title = title;
    if (description !== undefined) image.description = description;
    if (linkUrl !== undefined) image.linkUrl = linkUrl;
    if (isActive !== undefined) image.isActive = isActive;
    if (sortOrder !== undefined) image.sortOrder = sortOrder;

    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(200).json({
      success: true,
      message: 'Carousel image updated successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error updating carousel image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update carousel image',
      error: error.message
    });
  }
};

// Delete carousel image
const deleteCarouselImage = async (req, res) => {
  try {
    const { imageId } = req.params;

    const settings = await HomepageSettings.getSettings();
    const imageIndex = settings.carouselImages.findIndex(img => img._id.toString() === imageId);

    if (imageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Carousel image not found'
      });
    }

    // Remove image from array
    settings.carouselImages.splice(imageIndex, 1);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(200).json({
      success: true,
      message: 'Carousel image deleted successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error deleting carousel image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete carousel image',
      error: error.message
    });
  }
};

// Add promotion image
const addPromotionImage = async (req, res) => {
  try {
    const { title, imageUrl } = req.body;

    if (!imageUrl) {
      return res.status(400).json({
        success: false,
        message: 'Image URL is required'
      });
    }

    // Validate URL format
    if (!/^https?:\/\/.+/.test(imageUrl)) {
      return res.status(400).json({
        success: false,
        message: 'Please provide a valid HTTP/HTTPS image URL'
      });
    }

    const settings = await HomepageSettings.getSettings();

    // Check if we've reached the maximum number of promotion images
    if (settings.promotionImages.length >= (settings.settings?.maxPromotionImages || 5)) {
      return res.status(400).json({
        success: false,
        message: `Maximum ${settings.settings?.maxPromotionImages || 5} promotion images allowed`
      });
    }

    const imageData = {
      title: title || '',
      imageUrl,
      position: 'sidebar', // Default position
      isActive: true,
      sortOrder: settings.promotionImages.length
    };

    await settings.addPromotionImage(imageData);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(201).json({
      success: true,
      message: 'Promotion image added successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error adding promotion image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add promotion image',
      error: error.message
    });
  }
};

// Update promotion image
const updatePromotionImage = async (req, res) => {
  try {
    const { imageId } = req.params;
    const { title, imageUrl, isActive, sortOrder } = req.body;

    const settings = await HomepageSettings.getSettings();
    const imageIndex = settings.promotionImages.findIndex(img => img._id.toString() === imageId);

    if (imageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Promotion image not found'
      });
    }

    const image = settings.promotionImages[imageIndex];

    // Update image URL if provided
    if (imageUrl !== undefined) {
      // Validate URL format
      if (imageUrl && !/^https?:\/\/.+/.test(imageUrl)) {
        return res.status(400).json({
          success: false,
          message: 'Please provide a valid HTTP/HTTPS image URL'
        });
      }
      image.imageUrl = imageUrl;
    }

    // Update other fields
    if (title !== undefined) image.title = title;
    if (isActive !== undefined) image.isActive = isActive;
    if (sortOrder !== undefined) image.sortOrder = sortOrder;

    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(200).json({
      success: true,
      message: 'Promotion image updated successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error updating promotion image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update promotion image',
      error: error.message
    });
  }
};

// Delete promotion image
const deletePromotionImage = async (req, res) => {
  try {
    const { imageId } = req.params;

    const settings = await HomepageSettings.getSettings();
    const imageIndex = settings.promotionImages.findIndex(img => img._id.toString() === imageId);

    if (imageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Promotion image not found'
      });
    }

    // Remove image from array
    settings.promotionImages.splice(imageIndex, 1);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(200).json({
      success: true,
      message: 'Promotion image deleted successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error deleting promotion image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete promotion image',
      error: error.message
    });
  }
};

// Add featured category
const addFeaturedCategory = async (req, res) => {
  try {
    const { categoryId, displayName } = req.body;

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Image file is required'
      });
    }

    if (!categoryId) {
      return res.status(400).json({
        success: false,
        message: 'Category ID is required'
      });
    }

    const settings = await HomepageSettings.getSettings();

    // Check if we've reached the maximum number of featured categories (5)
    if (settings.featuredCategories.length >= 5) {
      return res.status(400).json({
        success: false,
        message: 'Maximum 5 featured categories allowed'
      });
    }

    // Check if category is already featured
    const existingCategory = settings.featuredCategories.find(
      cat => cat.category.toString() === categoryId
    );

    if (existingCategory) {
      return res.status(400).json({
        success: false,
        message: 'Category is already featured'
      });
    }

    const categoryData = {
      category: categoryId,
      displayName,
      imageUrl: req.file.path, // Cloudinary URL
      cloudinaryPublicId: req.file.filename, // Cloudinary public ID
      sortOrder: settings.featuredCategories.length
    };

    await settings.addFeaturedCategory(categoryData);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    // Populate the category data for response
    await settings.populate('featuredCategories.category', 'name slug');

    res.status(201).json({
      success: true,
      message: 'Featured category added successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error adding featured category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add featured category',
      error: error.message
    });
  }
};

// Update featured category
const updateFeaturedCategory = async (req, res) => {
  try {
    const { categoryId } = req.params;
    const { displayName, isActive, sortOrder } = req.body;

    const settings = await HomepageSettings.getSettings();
    const categoryIndex = settings.featuredCategories.findIndex(
      cat => cat._id.toString() === categoryId
    );

    if (categoryIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Featured category not found'
      });
    }

    const category = settings.featuredCategories[categoryIndex];

    // Update image file if provided
    if (req.file) {
      // Delete old image from Cloudinary
      if (category.cloudinaryPublicId) {
        await deleteImage(category.cloudinaryPublicId);
      }

      category.imageUrl = req.file.path; // Cloudinary URL
      category.cloudinaryPublicId = req.file.filename; // Cloudinary public ID
    }

    // Update other fields
    if (displayName !== undefined) category.displayName = displayName;
    if (isActive !== undefined) category.isActive = isActive;
    if (sortOrder !== undefined) category.sortOrder = sortOrder;

    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    // Populate the category data for response
    await settings.populate('featuredCategories.category', 'name slug');

    res.status(200).json({
      success: true,
      message: 'Featured category updated successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error updating featured category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update featured category',
      error: error.message
    });
  }
};

// Delete featured category
const deleteFeaturedCategory = async (req, res) => {
  try {
    const { categoryId } = req.params;

    const settings = await HomepageSettings.getSettings();
    const categoryIndex = settings.featuredCategories.findIndex(
      cat => cat._id.toString() === categoryId
    );

    if (categoryIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Featured category not found'
      });
    }

    const category = settings.featuredCategories[categoryIndex];

    // Delete image from Cloudinary
    if (category.cloudinaryPublicId) {
      await deleteImage(category.cloudinaryPublicId);
    }

    // Remove category from array
    settings.featuredCategories.splice(categoryIndex, 1);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(200).json({
      success: true,
      message: 'Featured category deleted successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error deleting featured category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete featured category',
      error: error.message
    });
  }
};

// All Categories Modal Management

// Get all categories modal data
const getAllCategoriesModal = async (req, res) => {
  try {
    const settings = await HomepageSettings.getSettings();
    
    // Initialize default categories if not exists
    if (!settings.allCategoriesModal || 
        !settings.allCategoriesModal.mainCategories || 
        settings.allCategoriesModal.mainCategories.length === 0) {
      await settings.initializeDefaultCategories();
      // Refresh settings after initialization
      await settings.save();
    }

    res.status(200).json({
      success: true,
      data: {
        mainCategories: settings.activeMainCategories,
        popularCategories: settings.activePopularCategories
      }
    });
  } catch (error) {
    console.error('Error fetching all categories modal:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch all categories modal data',
      error: error.message
    });
  }
};

// Update all categories modal
const updateAllCategoriesModal = async (req, res) => {
  try {
    const { mainCategories, popularCategories } = req.body;

    const settings = await HomepageSettings.getSettings();

    if (mainCategories) {
      if (!settings.allCategoriesModal) {
        settings.allCategoriesModal = { mainCategories: [], popularCategories: [] };
      }
      settings.allCategoriesModal.mainCategories = mainCategories.map((cat, index) => ({
        ...cat,
        sortOrder: index
      }));
    }

    if (popularCategories) {
      if (!settings.allCategoriesModal) {
        settings.allCategoriesModal = { mainCategories: [], popularCategories: [] };
      }
      settings.allCategoriesModal.popularCategories = popularCategories.map((cat, index) => ({
        ...cat,
        sortOrder: index
      }));
    }

    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(200).json({
      success: true,
      message: 'All categories modal updated successfully',
      data: {
        mainCategories: settings.activeMainCategories,
        popularCategories: settings.activePopularCategories
      }
    });
  } catch (error) {
    console.error('Error updating all categories modal:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update all categories modal',
      error: error.message
    });
  }
};

// Add main category
const addMainCategory = async (req, res) => {
  try {
    const { name, icon, linkUrl } = req.body;

    if (!name || !icon) {
      return res.status(400).json({
        success: false,
        message: 'Name and icon are required'
      });
    }

    const settings = await HomepageSettings.getSettings();

    // Check if we've reached the maximum number of main categories (18)
    if (settings.allCategoriesModal?.mainCategories?.length >= 18) {
      return res.status(400).json({
        success: false,
        message: 'Maximum 18 main categories allowed'
      });
    }

    const categoryData = {
      name: name.trim(),
      icon: icon.trim(),
      linkUrl: linkUrl?.trim() || '',
      isActive: true
    };

    await settings.addMainCategory(categoryData);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(201).json({
      success: true,
      message: 'Main category added successfully',
      data: {
        mainCategories: settings.activeMainCategories,
        popularCategories: settings.activePopularCategories
      }
    });
  } catch (error) {
    console.error('Error adding main category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add main category',
      error: error.message
    });
  }
};

// Add popular category
const addPopularCategory = async (req, res) => {
  try {
    const { name, linkUrl } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Name is required'
      });
    }

    const settings = await HomepageSettings.getSettings();

    // Check if we've reached the maximum number of popular categories (10)
    if (settings.allCategoriesModal?.popularCategories?.length >= 10) {
      return res.status(400).json({
        success: false,
        message: 'Maximum 10 popular categories allowed'
      });
    }

    const categoryData = {
      name: name.trim(),
      linkUrl: linkUrl?.trim() || '',
      isActive: true
    };

    await settings.addPopularCategory(categoryData);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(201).json({
      success: true,
      message: 'Popular category added successfully',
      data: {
        mainCategories: settings.activeMainCategories,
        popularCategories: settings.activePopularCategories
      }
    });
  } catch (error) {
    console.error('Error adding popular category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add popular category',
      error: error.message
    });
  }
};

// Update main category
const updateMainCategory = async (req, res) => {
  try {
    const { categoryId } = req.params;
    const { name, icon, linkUrl, isActive, sortOrder } = req.body;

    const settings = await HomepageSettings.getSettings();
    
    if (!settings.allCategoriesModal || !settings.allCategoriesModal.mainCategories) {
      return res.status(404).json({
        success: false,
        message: 'Main categories not found'
      });
    }

    const categoryIndex = settings.allCategoriesModal.mainCategories.findIndex(
      cat => cat._id.toString() === categoryId
    );

    if (categoryIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Main category not found'
      });
    }

    const category = settings.allCategoriesModal.mainCategories[categoryIndex];

    // Update fields
    if (name !== undefined) category.name = name.trim();
    if (icon !== undefined) category.icon = icon.trim();
    if (linkUrl !== undefined) category.linkUrl = linkUrl.trim();
    if (isActive !== undefined) category.isActive = isActive;
    if (sortOrder !== undefined) category.sortOrder = sortOrder;

    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(200).json({
      success: true,
      message: 'Main category updated successfully',
      data: {
        mainCategories: settings.activeMainCategories,
        popularCategories: settings.activePopularCategories
      }
    });
  } catch (error) {
    console.error('Error updating main category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update main category',
      error: error.message
    });
  }
};

// Update popular category
const updatePopularCategory = async (req, res) => {
  try {
    const { categoryId } = req.params;
    const { name, linkUrl, isActive, sortOrder } = req.body;

    const settings = await HomepageSettings.getSettings();
    
    if (!settings.allCategoriesModal || !settings.allCategoriesModal.popularCategories) {
      return res.status(404).json({
        success: false,
        message: 'Popular categories not found'
      });
    }

    const categoryIndex = settings.allCategoriesModal.popularCategories.findIndex(
      cat => cat._id.toString() === categoryId
    );

    if (categoryIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Popular category not found'
      });
    }

    const category = settings.allCategoriesModal.popularCategories[categoryIndex];

    // Update fields
    if (name !== undefined) category.name = name.trim();
    if (linkUrl !== undefined) category.linkUrl = linkUrl.trim();
    if (isActive !== undefined) category.isActive = isActive;
    if (sortOrder !== undefined) category.sortOrder = sortOrder;

    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(200).json({
      success: true,
      message: 'Popular category updated successfully',
      data: {
        mainCategories: settings.activeMainCategories,
        popularCategories: settings.activePopularCategories
      }
    });
  } catch (error) {
    console.error('Error updating popular category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update popular category',
      error: error.message
    });
  }
};

// Delete main category
const deleteMainCategory = async (req, res) => {
  try {
    const { categoryId } = req.params;

    const settings = await HomepageSettings.getSettings();
    
    if (!settings.allCategoriesModal || !settings.allCategoriesModal.mainCategories) {
      return res.status(404).json({
        success: false,
        message: 'Main categories not found'
      });
    }

    const categoryIndex = settings.allCategoriesModal.mainCategories.findIndex(
      cat => cat._id.toString() === categoryId
    );

    if (categoryIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Main category not found'
      });
    }

    // Remove category from array
    settings.allCategoriesModal.mainCategories.splice(categoryIndex, 1);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(200).json({
      success: true,
      message: 'Main category deleted successfully',
      data: {
        mainCategories: settings.activeMainCategories,
        popularCategories: settings.activePopularCategories
      }
    });
  } catch (error) {
    console.error('Error deleting main category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete main category',
      error: error.message
    });
  }
};

// Delete popular category
const deletePopularCategory = async (req, res) => {
  try {
    const { categoryId } = req.params;

    const settings = await HomepageSettings.getSettings();
    
    if (!settings.allCategoriesModal || !settings.allCategoriesModal.popularCategories) {
      return res.status(404).json({
        success: false,
        message: 'Popular categories not found'
      });
    }

    const categoryIndex = settings.allCategoriesModal.popularCategories.findIndex(
      cat => cat._id.toString() === categoryId
    );

    if (categoryIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Popular category not found'
      });
    }

    // Remove category from array
    settings.allCategoriesModal.popularCategories.splice(categoryIndex, 1);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(200).json({
      success: true,
      message: 'Popular category deleted successfully',
      data: {
        mainCategories: settings.activeMainCategories,
        popularCategories: settings.activePopularCategories
      }
    });
  } catch (error) {
    console.error('Error deleting popular category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete popular category',
      error: error.message
    });
  }
};

// Initialize default categories
const initializeDefaultCategories = async (req, res) => {
  try {
    const settings = await HomepageSettings.getSettings();
    await settings.initializeDefaultCategories();

    res.status(200).json({
      success: true,
      message: 'Default categories initialized successfully',
      data: {
        mainCategories: settings.activeMainCategories,
        popularCategories: settings.activePopularCategories
      }
    });
  } catch (error) {
    console.error('Error initializing default categories:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initialize default categories',
      error: error.message
    });
  }
};

module.exports = {
  getHomepageSettings,
  updateGeneralSettings,
  addCarouselImage,
  updateCarouselImage,
  deleteCarouselImage,
  addPromotionImage,
  updatePromotionImage,
  deletePromotionImage,
  addFeaturedCategory,
  updateFeaturedCategory,
  deleteFeaturedCategory,
  // All Categories Modal
  getAllCategoriesModal,
  updateAllCategoriesModal,
  addMainCategory,
  addPopularCategory,
  updateMainCategory,
  updatePopularCategory,
  deleteMainCategory,
  deletePopularCategory,
  initializeDefaultCategories
};
