const { formatCurrency } = require('../currency');

/**
 * Transform product pricing to show price in user's preferred currency
 * @param {Object} product - Product document
 * @param {string} userCurrency - User's preferred currency (default: INR)
 * @returns {Object} - Product with transformed pricing
 */
function transformProductPricing(product, userCurrency = 'INR') {
  if (!product) return product;
  
  // Convert to plain object if it's a mongoose document
  const productObj = product.toObject ? product.toObject() : product;
  
  // Get price in user's currency
  const basePrice = getProductPriceInCurrency(productObj, userCurrency, 'base');
  const salePrice = getProductPriceInCurrency(productObj, userCurrency, 'sale');
  const costPrice = getProductPriceInCurrency(productObj, userCurrency, 'cost');
  
  // Create display pricing
  productObj.displayPricing = {
    basePrice,
    salePrice,
    costPrice,
    currency: userCurrency,
    currentPrice: salePrice || basePrice,
    formattedBasePrice: formatCurrency(basePrice, userCurrency),
    formattedSalePrice: salePrice ? formatCurrency(salePrice, userCurrency) : null,
    formattedCurrentPrice: formatCurrency(salePrice || basePrice, userCurrency),
    hasDiscount: !!(salePrice && salePrice < basePrice),
    discountPercentage: (salePrice && salePrice < basePrice) 
      ? Math.round(((basePrice - salePrice) / basePrice) * 100) 
      : 0
  };
  
  // Keep original pricing for reference
  productObj.originalPricing = productObj.pricing;
  
  return productObj;
}

/**
 * Get product price in specific currency
 * @param {Object} product - Product object
 * @param {string} currency - Target currency
 * @param {string} priceType - 'base', 'sale', or 'cost'
 * @returns {number|null} - Price in target currency
 */
function getProductPriceInCurrency(product, currency = 'INR', priceType = 'base') {
  if (!product || !product.pricing) return null;
  
  const pricing = product.pricing;
  
  // Check if multi-currency pricing exists for this currency
  if (pricing.multiCurrency && pricing.multiCurrency[currency]) {
    const currencyPricing = pricing.multiCurrency[currency];
    switch (priceType) {
      case 'sale':
        return currencyPricing.salePrice || null;
      case 'cost':
        return currencyPricing.costPrice || null;
      case 'base':
      default:
        return currencyPricing.basePrice || null;
    }
  }
  
  // Fallback to default pricing if currency matches or no multi-currency data
  if (pricing.currency === currency) {
    switch (priceType) {
      case 'sale':
        return pricing.salePrice || null;
      case 'cost':
        return pricing.costPrice || null;
      case 'base':
      default:
        return pricing.basePrice || null;
    }
  }
  
  // If currency not available, try to get INR price as fallback
  if (currency !== 'INR') {
    return getProductPriceInCurrency(product, 'INR', priceType);
  }
  
  return null;
}

/**
 * Transform multiple products pricing
 * @param {Array} products - Array of products
 * @param {string} userCurrency - User's preferred currency
 * @returns {Array} - Products with transformed pricing
 */
function transformProductsPricing(products, userCurrency = 'INR') {
  if (!Array.isArray(products)) return products;
  
  return products.map(product => transformProductPricing(product, userCurrency));
}

/**
 * Get available currencies for a product
 * @param {Object} product - Product object
 * @returns {Array} - Array of available currency codes
 */
function getProductAvailableCurrencies(product) {
  if (!product || !product.pricing) return ['INR'];
  
  const currencies = [product.pricing.currency || 'INR'];
  
  if (product.pricing.multiCurrency) {
    Object.keys(product.pricing.multiCurrency).forEach(currency => {
      if (product.pricing.multiCurrency[currency].basePrice && !currencies.includes(currency)) {
        currencies.push(currency);
      }
    });
  }
  
  return currencies;
}

/**
 * Transform cart item pricing
 * @param {Object} cartItem - Cart item object
 * @param {string} userCurrency - User's preferred currency
 * @returns {Object} - Cart item with transformed pricing
 */
function transformCartItemPricing(cartItem, userCurrency = 'INR') {
  if (!cartItem || !cartItem.product) return cartItem;
  
  const itemObj = cartItem.toObject ? cartItem.toObject() : cartItem;
  
  // Transform the product pricing
  if (itemObj.product.pricing) {
    itemObj.product = transformProductPricing(itemObj.product, userCurrency);
  }
  
  // Update cart item price display
  const currentPrice = itemObj.product.displayPricing 
    ? itemObj.product.displayPricing.currentPrice 
    : itemObj.priceAtAdd;
    
  itemObj.displayPrice = {
    unitPrice: currentPrice,
    totalPrice: currentPrice * itemObj.quantity,
    formattedUnitPrice: formatCurrency(currentPrice, userCurrency),
    formattedTotalPrice: formatCurrency(currentPrice * itemObj.quantity, userCurrency),
    currency: userCurrency
  };
  
  return itemObj;
}

module.exports = {
  transformProductPricing,
  transformProductsPricing,
  getProductPriceInCurrency,
  getProductAvailableCurrencies,
  transformCartItemPricing
};
