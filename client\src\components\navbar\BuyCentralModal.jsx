import React from 'react';
import { cn } from '@/lib/utils';

const ListItem = React.forwardRef(({ className, title, ...props }, ref) => {
  return (
    <li className={cn('mb-2 last:mb-0', className)}>
      <a
        ref={ref}
        className="block select-none p-0 leading-none no-underline outline-none transition-colors text-sm font-normal text-gray-600 hover:text-black"
        {...props}
      >
        {title}
      </a>
    </li>
  );
});
ListItem.displayName = 'ListItem';

const BuyCentralModal = ({ showModal, onMouseEnter, onMouseLeave }) => {
  if (!showModal) return null;

  return (
    <>
      <style>
        {`
          .buy-central-modal-lists ul {
            list-style: none !important;
            margin-left: 0 !important;
            padding-left: 0 !important;
          }
        `}
      </style>
      <div
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        className="absolute top-full left-0 w-full bg-white shadow-lg"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-5 gap-x-8 py-4 buy-central-modal-lists">
            <div>
              <h3 className="font-semibold mb-2 text-sm">Get started</h3>
              <ul>
                <ListItem title="What is Alibaba.com" href="#" />
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-2 text-sm">Why Alibaba.com</h3>
              <ul>
                <ListItem title="How sourcing works" href="#" />
                <ListItem title="Membership program" href="#" />
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-2 text-sm">Trade services</h3>
              <ul>
                <ListItem title="Order protections" href="#" />
                <ListItem title="Logistics Services" href="#" />
                <ListItem title="Letter of Credit" href="#" />
                <ListItem title="Production monitoring & inspection services" href="#" />
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-2 text-sm">Resources</h3>
              <ul>
                <ListItem title="Success stories" href="#" />
                <ListItem title="Blogs" href="#" />
                <ListItem title="Industry reports" href="#" />
                <ListItem title="Help Center" href="#" />
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-2 text-sm">Webinars</h3>
              <ul>
                <ListItem title="Overview" href="#" />
                <ListItem title="Meet the peers" href="#" />
                <ListItem title="Ecommerce Academy" href="#" />
                <ListItem title="How to source on Alibaba.com" href="#" />
              </ul>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default BuyCentralModal;
