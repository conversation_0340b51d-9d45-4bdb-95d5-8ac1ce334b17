const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('authToken');
  return {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` })
  };
};

// Helper function to handle API responses
const handleResponse = async (response) => {
  const data = await response.json();
  
  if (!response.ok) {
    let errorMessage = data.message || `HTTP error! status: ${response.status}`;
    
    if (data.details) {
      errorMessage += `\nDetails: ${data.details}`;
    }
    
    if (data.errors && Array.isArray(data.errors)) {
      const errorsList = data.errors.map(err => `${err.path || err.param || 'Field'}: ${err.msg}`).join(', ');
      errorMessage += `\nValidation errors: ${errorsList}`;
    }
    
    console.error('Vendor Order API Error:', data);
    throw new Error(errorMessage);
  }
  
  return data;
};

// Vendor Order API functions
export const vendorOrderApi = {
  // Get vendor's orders
  getOrders: async (params = {}) => {
    try {
      const queryParams = new URLSearchParams();
      
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value);
        }
      });

      const url = `${API_BASE_URL}/vendor/orders${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching vendor orders:', error);
      throw error;
    }
  },

  // Get order by ID
  getOrderById: async (orderId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/vendor/orders/${orderId}`, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching vendor order by ID:', error);
      throw error;
    }
  },

  // Update order status
  updateOrderStatus: async (orderId, statusData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/vendor/orders/${orderId}/status`, {
        method: 'PATCH',
        headers: getAuthHeaders(),
        body: JSON.stringify(statusData)
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  },

  // Update shipping information
  updateShipping: async (orderId, shippingData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/vendor/orders/${orderId}/shipping`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(shippingData)
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error updating shipping info:', error);
      throw error;
    }
  },

  // Create shipping tracking
  createShippingTracking: async (orderId, trackingData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/vendor/orders/${orderId}/tracking`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(trackingData)
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error creating shipping tracking:', error);
      throw error;
    }
  },

  // Update tracking status
  updateTrackingStatus: async (trackingId, statusData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/vendor/orders/tracking/${trackingId}/status`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(statusData)
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error updating tracking status:', error);
      throw error;
    }
  },

  // Process order fulfillment
  processOrderFulfillment: async (orderId, fulfillmentData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/vendor/orders/${orderId}/fulfillment`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(fulfillmentData)
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error processing order fulfillment:', error);
      throw error;
    }
  },

  // Get order analytics
  getOrderAnalytics: async (period = '30d') => {
    try {
      const response = await fetch(`${API_BASE_URL}/vendor/orders/analytics?period=${period}`, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching order analytics:', error);
      throw error;
    }
  }
};

export default vendorOrderApi;
