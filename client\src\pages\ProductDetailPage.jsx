import React, { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { message, notification } from 'antd';
import ProductInfoCard from '../components/ProductInfoCard';
import SuggestedProducts from '../components/SuggestedProducts';
import ReviewForm from '../components/reviews/ReviewForm';
import ReviewList from '../components/reviews/ReviewList';
import Footer from '../components/Footer';
import { productsApi } from '../services/publicApi';
import { reviewService } from '../services/reviewService';
import AuthContext from '../contexts/AuthContext';

const ProductDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useContext(AuthContext);
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [suggestedProducts, setSuggestedProducts] = useState([]);
  const [redirectCountdown, setRedirectCountdown] = useState(null);
  
  // Review states
  const [reviews, setReviews] = useState([]);
  const [reviewsLoading, setReviewsLoading] = useState(false);
  const [canReview, setCanReview] = useState(false);
  const [reviewPagination, setReviewPagination] = useState(null);
  const [reviewRating, setReviewRating] = useState(null);

  // Load reviews
  const loadReviews = async (page = 1, sortBy = 'newest') => {
    if (!id) return;
    
    setReviewsLoading(true);
    try {
      const data = await reviewService.getProductReviews(id, { page, sortBy });
      console.log('Reviews API Response:', data); // Debug log
      
      setReviews(data.reviews || []);
      setReviewPagination(data.pagination || null);
      
      // Extract rating statistics from response
      if (data.rating || data.statistics) {
        setReviewRating(data.rating || data.statistics);
      } else {
        // Create rating statistics from available data
        // Use pagination.totalReviews for accurate count, but current page reviews for calculations
        const totalReviews = data.pagination?.totalReviews || data.reviews?.length || 0;
        const currentPageReviews = data.reviews || [];
        
        if (totalReviews > 0 && currentPageReviews.length > 0) {
          // For current page calculations
          const totalRating = currentPageReviews.reduce((sum, review) => sum + review.rating, 0);
          const averageRating = totalRating / currentPageReviews.length;
          
          // Calculate rating distribution for current page only
          // Note: This is limited data - ideally API should provide full distribution
          const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
          currentPageReviews.forEach(review => {
            ratingDistribution[review.rating]++;
          });
          
          setReviewRating({
            averageRating: parseFloat(averageRating.toFixed(1)),
            totalReviews,
            ratingDistribution
          });
        } else {
          setReviewRating({
            averageRating: 0,
            totalReviews: 0,
            ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
          });
        }
      }
    } catch (error) {
      console.error('Error loading reviews:', error);
      message.error('Failed to load reviews');
    } finally {
      setReviewsLoading(false);
    }
  };

  // Check if user can review
  const checkCanReview = async () => {
    // Only check if user is authenticated and is a customer
    if (!isAuthenticated || !user || user.userType !== 'customer' || !id) {
      setCanReview(false);
      return;
    }

    // Additional check to ensure token exists
    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    if (!token) {
      console.log('No auth token found for review check');
      setCanReview(false);
      return;
    }

    try {
      console.log('Checking if user can review product:', id);
      console.log('Current user:', { id: user._id, email: user.email, name: `${user.firstName} ${user.lastName}` });
      
      // First check locally if user has already reviewed by looking at existing reviews
      const hasAlreadyReviewedLocally = reviews.some(review => 
        review.customer && review.customer._id === user._id
      );
      
      if (hasAlreadyReviewedLocally) {
        console.log('✅ User has already reviewed this product (found in local reviews)');
        setCanReview(false);
        return;
      }
      
      // If not found locally, check with server
      const response = await reviewService.canReviewProduct(id);
      console.log('Can review API response:', response);
      
      setCanReview(response.canReview === true);
      
    } catch (error) {
      console.error('Error checking review eligibility:', error);
      
      // Fallback: check if user has already reviewed from existing reviews
      const hasAlreadyReviewedLocally = reviews.some(review => 
        review.customer && review.customer._id === user._id
      );
      
      if (hasAlreadyReviewedLocally) {
        console.log('✅ User has already reviewed (fallback local check)');
        setCanReview(false);
      } else {
        // If API fails and no existing review found locally, be conservative and don't show form
        console.log('⚠️ API failed and no local review found, hiding review form to be safe');
        setCanReview(false);
      }
    }
  };

  // Navigate to a random product
  const handleRandomProduct = async () => {
    try {
      const response = await productsApi.getProducts({ limit: 1, page: Math.floor(Math.random() * 10) + 1 });
      if (response?.data?.success && response.data.data.products.length > 0) {
        const randomProduct = response.data.data.products[0];
        navigate(`/product/${randomProduct._id}`);
      } else {
        navigate('/');
      }
    } catch (error) {
      console.error('Error fetching random product:', error);
      navigate('/');
    }
  };

  // Handle review submission
  const handleReviewSubmit = async (reviewData) => {
    try {
      console.log('Submitting review data:', reviewData);
      const response = await reviewService.createReview(reviewData);
      console.log('Review submission response:', response);
      
      // Immediately hide the review form
      setCanReview(false);
      
      // Show success notification
      notification.success({
        message: 'Review Submitted Successfully!',
        description: 'Thank you for your feedback. Your review will help other customers make informed decisions.',
        placement: 'topRight',
        duration: 4,
      });
      
      // Refresh reviews after a short delay to ensure the new review is available
      setTimeout(() => {
        loadReviews();
      }, 1000);
      
      return { success: true };
    } catch (error) {
      console.error('Error submitting review:', error);
      
      // Show error notification
      notification.error({
        message: 'Review Submission Failed',
        description: error.message || 'Failed to submit review. Please try again.',
        placement: 'topRight',
        duration: 5,
      });
      
      throw error; // Let the form handle the error too
    }
  };

  // Handle review pagination
  const handleReviewPageChange = (page) => {
    loadReviews(page);
  };

  // Handle review sorting
  const handleReviewSortChange = (sortBy) => {
    loadReviews(1, sortBy);
  };

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        setError(null); // Clear any previous errors

        // Validate product ID format
        if (!id || !id.match(/^[0-9a-fA-F]{24}$/)) {
          throw new Error('Invalid product ID format');
        }

        console.log('Fetching product with ID:', id);

        // Fetch product from the real database
        const response = await productsApi.getProduct(id);

        if (!response || !response.success) {
          throw new Error(response?.message || 'Product not found');
        }

        // Handle both single product and nested product response
        const productData = response.data.product || response.data;

        // Transform API data to match our ProductInfoCard component structure
        const transformedProduct = {
          id: productData._id,
          name: productData.name,
          brand: productData.vendor?.businessName || productData.category?.name || "Unknown Brand",
          sku: `SKU-${productData._id}`,
          price: productData.pricing?.salePrice || productData.pricing?.basePrice || 0,
          originalPrice: productData.pricing?.basePrice || productData.pricing?.salePrice || 0,
          discount: productData.pricing?.basePrice && productData.pricing?.salePrice ?
            Math.round(((productData.pricing.basePrice - productData.pricing.salePrice) / productData.pricing.basePrice) * 100) : 0,
          rating: productData.reviews?.averageRating || 0,
          reviewCount: productData.reviews?.totalReviews || 0,
          inStock: productData.inventory?.quantity > 0,
          stockCount: productData.inventory?.quantity || 0,
          images: productData.images || [],
          variants: productData.variants || [
            { id: 1, name: "Default", color: "#000000", available: true }
          ],
          badges: ["Free Shipping"],
          description: productData.description || "No description available for this product.",
          specifications: {
            "General": {
              "Category": productData.category?.name || "N/A",
              "Product ID": productData._id || "N/A",
              "Availability": productData.inventory?.quantity > 0 ? "In Stock" : "Out of Stock"
            },
            "Details": {
              "Brand": productData.vendor?.businessName || "Unknown",
              "Model": `Model-${productData._id}`,
              "Warranty": "1 Year"
            }
          },
          features: [
            "High Quality Materials",
            "Durable Construction",
            "Modern Design",
            "Easy to Use"
          ],
          shipping: {
            freeShipping: true,
            estimatedDays: "2-3",
            returnPolicy: "30-day return"
          }
        };

        setProduct(transformedProduct);
      } catch (err) {
        console.error('Error fetching product:', err);
        console.error('Product ID:', id);
        console.error('Error details:', {
          message: err.message,
          response: err.response?.data,
          status: err.response?.status
        });

        let errorMessage = 'Product not found';
        if (err.response?.status === 404) {
          errorMessage = 'This product does not exist or has been removed';
        } else if (err.message === 'Invalid product ID format') {
          errorMessage = 'Invalid product link. Please check the URL and try again.';
        } else if (err.response?.status >= 500) {
          errorMessage = 'Server error. Please try again later.';
        } else if (err.message.includes('Network Error') || err.code === 'NETWORK_ERROR') {
          errorMessage = 'Unable to connect to server. Please check your internet connection.';
        }

        setError(errorMessage);
        message.error(errorMessage);

        // Fetch some suggested products when there's an error
        try {
          const suggestedResponse = await productsApi.getProducts({ limit: 4 });
          if (suggestedResponse?.data?.success) {
            setSuggestedProducts(suggestedResponse.data.data.products || []);
          }
        } catch (suggestedErr) {
          console.error('Error fetching suggested products:', suggestedErr);
        }
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchProduct();
    }
  }, [id]);

  // Load reviews when product is loaded
  useEffect(() => {
    if (product) {
      loadReviews();
    }
  }, [product]);

  // Check review eligibility after reviews are loaded and user state changes
  useEffect(() => {
    if (product && reviews.length >= 0) { // Check even if reviews is empty array
      checkCanReview();
    }
  }, [product, reviews, isAuthenticated, user]);

  // Auto-redirect countdown when there's an error
  useEffect(() => {
    if (error && !product) {
      setRedirectCountdown(10);
      const interval = setInterval(() => {
        setRedirectCountdown(prev => {
          if (prev <= 1) {
            clearInterval(interval);
            navigate('/');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [error, product, navigate]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading product details...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center max-w-md mx-auto px-4">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">Product Not Found</h2>
            <p className="text-gray-600 mb-6">{error}</p>

            <div className="space-y-3">
              <button
                onClick={() => navigate('/')}
                className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Back to Home
              </button>

              <button
                onClick={() => navigate('/page')}
                className="w-full bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors"
              >
                Browse All Products
              </button>

              <button
                onClick={handleRandomProduct}
                className="w-full bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
              >
                Try a Random Product
              </button>
            </div>

            <div className="mt-6 text-sm text-gray-500">
              <p>Product ID: {id}</p>
              <p>If you believe this is an error, please contact support.</p>
              {redirectCountdown && (
                <p className="mt-2 text-blue-600">
                  Redirecting to home page in {redirectCountdown} seconds...
                </p>
              )}
            </div>

            {/* Show suggested products if available */}
            {suggestedProducts.length > 0 && (
              <div className="mt-8">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">You might like these products:</h3>
                <div className="grid grid-cols-2 gap-4">
                  {suggestedProducts.slice(0, 4).map(product => (
                    <div
                      key={product._id}
                      onClick={() => navigate(`/product/${product._id}`)}
                      className="bg-white p-3 rounded-lg shadow-sm border hover:shadow-md transition-shadow cursor-pointer"
                    >
                      <div className="aspect-square bg-gray-100 rounded mb-2">
                        <img
                          src={product.images?.[0]?.url || 'https://via.placeholder.com/150'}
                          alt={product.name}
                          className="w-full h-full object-cover rounded"
                          onError={(e) => {
                            e.target.src = 'https://via.placeholder.com/150?text=No+Image';
                          }}
                        />
                      </div>
                      <h4 className="text-sm font-medium text-gray-800 truncate">{product.name}</h4>
                      <p className="text-sm text-blue-600 font-semibold">
                        ${product.pricing?.salePrice || product.pricing?.basePrice || 0}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      
      {/* Breadcrumb */}
      <div className="max-w-7xl mx-auto px-4 py-2">
        <nav className="text-sm text-gray-500">
          <button 
            onClick={() => navigate('/')}
            className="hover:text-blue-600 transition-colors"
          >
            Home
          </button>
          <span className="mx-2">/</span>
          <button 
            onClick={() => navigate('/')}
            className="hover:text-blue-600 transition-colors"
          >
            Products
          </button>
          <span className="mx-2">/</span>
          <span className="text-gray-800">{product?.name}</span>
        </nav>
      </div>

      <ProductInfoCard product={product} />
      
      {/* Reviews Section */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Review Form - Only show for eligible customers */}
        {canReview && isAuthenticated && user?.userType === 'customer' && (
          <ReviewForm
            productId={id}
            productName={product?.name}
            onReviewSubmit={handleReviewSubmit}
          />
        )}
        
        {/* Reviews List */}
        <ReviewList
          productId={id}
          reviews={reviews}
          rating={reviewRating}
          loading={reviewsLoading}
          pagination={reviewPagination}
          onPageChange={handleReviewPageChange}
          onSortChange={handleReviewSortChange}
        />
      </div>
      
      {/* Suggested Products Section */}
      <div className="bg-white">
        <SuggestedProducts 
          currentProductId={id}
          categoryName={product?.brand}
          maxProducts={8}
        />
      </div>
      
      <Footer />
    </div>
  );
};

export default ProductDetailPage;