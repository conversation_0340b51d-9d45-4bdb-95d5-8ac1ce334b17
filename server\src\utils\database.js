const mongoose = require('mongoose');
const dotenv = require("dotenv");

dotenv.config();

// Use 127.0.0.1 instead of localhost for better Node.js 18+ compatibility
const DB_URL = process.env.MONGODB_URI || process.env.DB_url || 'mongodb://127.0.0.1:27017/multi-vendor-ecommerce';

const DbConnect = async () => {
    try {
        console.log('🔄 Attempting to connect to MongoDB...');
        console.log('📍 Database URL:', DB_URL.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@')); // Hide credentials in logs
        
        // Set global mongoose options before connecting
        mongoose.set('bufferCommands', true); // Enable buffering for better reliability
        mongoose.set('strictQuery', false); // Prepare for Mongoose 7
        
        // Different connection options for production vs development
        const connectionOptions = {
            serverSelectionTimeoutMS: process.env.NODE_ENV === 'production' ? 30000 : 10000,
            socketTimeoutMS: 45000,
            maxPoolSize: process.env.NODE_ENV === 'production' ? 20 : 10,
            minPoolSize: process.env.NODE_ENV === 'production' ? 10 : 5,
            maxIdleTimeMS: 30000,
            retryWrites: true,
            w: 'majority'
        };
        
        // Add family option only for local connections
        if (!DB_URL.includes('mongodb+srv')) {
            connectionOptions.family = 4;
        }
        
        await mongoose.connect(DB_URL, connectionOptions);
        
        console.log("✅ Database connected successfully");
        
        // Handle connection events
        mongoose.connection.on('error', (error) => {
            console.error('❌ MongoDB connection error:', error);
        });
        
        mongoose.connection.on('disconnected', () => {
            console.log('⚠️ MongoDB disconnected');
        });
        
        mongoose.connection.on('reconnected', () => {
            console.log('🔄 MongoDB reconnected');
        });
        
        mongoose.connection.on('connected', () => {
            console.log('🔗 MongoDB connected');
        });
        
    } catch (error) {
        console.error("❌ Database connection failed:", error.message);
        
        // If Atlas connection fails, try local MongoDB
        if (DB_URL.includes('mongodb+srv') || DB_URL.includes('mongodb.net')) {
            console.log('🔄 Atlas connection failed, trying local MongoDB...');
            try {
                await mongoose.connect('mongodb://127.0.0.1:27017/multi-vendor-ecommerce', {
                    serverSelectionTimeoutMS: 5000,
                    socketTimeoutMS: 45000,
                    family: 4,
                });
                console.log("✅ Connected to local MongoDB successfully");
            } catch (localError) {
                console.error("❌ Local MongoDB connection also failed:", localError.message);
                console.log("💡 Please ensure MongoDB is running locally or check your Atlas connection");
            }
        }
    }
};

// Graceful shutdown
process.on('SIGINT', async () => {
    try {
        await mongoose.connection.close();
        console.log('📴 MongoDB connection closed through app termination');
        process.exit(0);
    } catch (error) {
        console.error('Error closing MongoDB connection:', error);
        process.exit(1);
    }
});

module.exports = DbConnect;