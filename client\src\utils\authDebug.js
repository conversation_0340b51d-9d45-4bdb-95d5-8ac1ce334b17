// Authentication debugging utility
export const debugAuthState = () => {
  const token = localStorage.getItem('authToken');
  const userString = localStorage.getItem('authUser');
  const userType = localStorage.getItem('authUserType');
  
  let user = null;
  try {
    user = userString ? JSON.parse(userString) : null;
  } catch (error) {
    console.error('Error parsing user data:', error);
  }
  
  const authState = {
    hasToken: !!token,
    tokenLength: token ? token.length : 0,
    hasUser: !!user,
    userType: user?.userType || userType,
    userId: user?._id || user?.id,
    userEmail: user?.email,
    isTokenExpired: token ? isTokenExpired(token) : true
  };
  
  console.log('🔍 Auth Debug State:', authState);
  
  // Check for common issues
  const issues = [];
  
  if (!token) {
    issues.push('No authentication token found');
  } else if (isTokenExpired(token)) {
    issues.push('Authentication token is expired');
  }
  
  if (!user) {
    issues.push('No user data found');
  } else if (!user.userType) {
    issues.push('User type is missing');
  } else if (user.userType !== 'customer') {
    issues.push(`User type is '${user.userType}', but cart requires 'customer'`);
  }
  
  if (issues.length > 0) {
    console.warn('🚨 Auth Issues Found:', issues);
  } else {
    console.log('✅ Auth state looks good');
  }
  
  return { authState, issues };
};

// Helper function to check if token is expired
const isTokenExpired = (token) => {
  if (!token) return true;
  
  try {
    // Simple JWT decode without verification (just for expiry check)
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true;
  }
};

// Function to fix common auth issues
export const fixAuthIssues = () => {
  const { authState, issues } = debugAuthState();
  
  if (issues.length === 0) {
    console.log('✅ No auth issues to fix');
    return { success: true, message: 'Authentication state is valid' };
  }
  
  console.log('🔧 Attempting to fix auth issues...');
  
  // If token is expired or missing, clear all auth data
  if (issues.some(issue => issue.includes('token'))) {
    console.log('🧹 Clearing expired/invalid auth data');
    localStorage.removeItem('authToken');
    localStorage.removeItem('authUser');
    localStorage.removeItem('authUserType');
    localStorage.removeItem('refreshToken');
    return { 
      success: false, 
      message: 'Authentication expired. Please log in again.',
      action: 'redirect_to_login'
    };
  }
  
  // If user type is wrong, this is a more serious issue
  if (issues.some(issue => issue.includes('User type'))) {
    console.log('⚠️ User type mismatch detected');
    return {
      success: false,
      message: 'Account type mismatch. Cart functionality is only available for customer accounts.',
      action: 'show_user_type_error'
    };
  }
  
  return {
    success: false,
    message: 'Unable to automatically fix authentication issues. Please log in again.',
    action: 'redirect_to_login'
  };
};

// Function to validate cart access
export const validateCartAccess = () => {
  const { authState, issues } = debugAuthState();
  
  if (issues.length > 0) {
    return {
      canAccess: false,
      reason: issues[0],
      authState
    };
  }
  
  return {
    canAccess: true,
    reason: 'Authentication valid',
    authState
  };
};