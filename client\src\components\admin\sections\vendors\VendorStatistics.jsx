import React from 'react';
import { Row, Col, Card, Statistic } from 'antd';
import {
  ShopOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';

const VendorStatistics = ({ vendors, stats, loading }) => {
  // Use stats from API if available, fallback to calculated values
  const totalVendors = stats.totalVendors || vendors.length;
  const activeVendors = stats.activeVendors || vendors.filter(vendor => vendor.status === 'active').length;
  const pendingVendorsCount = stats.pendingVendors || vendors.filter(vendor => vendor.status === 'pending_approval').length;

  return (
    <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
      <Col xs={24} sm={12} lg={8}>
        <Card>
          <Statistic
            title="Total Vendors"
            value={totalVendors}
            prefix={<ShopOutlined />}
            valueStyle={{ color: '#1890ff' }}
            loading={loading}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} lg={8}>
        <Card>
          <Statistic
            title="Active"
            value={activeVendors}
            prefix={<CheckCircleOutlined />}
            valueStyle={{ color: '#52c41a' }}
            loading={loading}
          />
        </Card>
      </Col>
      <Col xs={24} sm={24} lg={8}>
        <Card>
          <Statistic
            title="Pending Approval"
            value={pendingVendorsCount}
            prefix={<CloseCircleOutlined />}
            valueStyle={{ color: '#faad14' }}
            loading={loading}
          />
        </Card>
      </Col>
    </Row>
  );
};

export default VendorStatistics;
