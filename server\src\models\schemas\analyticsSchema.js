const mongoose = require('mongoose');

const analyticsSchema = new mongoose.Schema({
  views: {
    total: {
      type: Number,
      default: 0
    },
    unique: {
      type: Number,
      default: 0
    },
    daily: [{
      date: {
        type: Date,
        required: true
      },
      views: {
        type: Number,
        default: 0
      },
      uniqueViews: {
        type: Number,
        default: 0
      }
    }]
  },
  cartAdditions: {
    type: Number,
    default: 0
  },
  conversionRate: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  searchKeywords: [{
    keyword: String,
    count: Number
  }]
}, { _id: false });

module.exports = { analyticsSchema };
