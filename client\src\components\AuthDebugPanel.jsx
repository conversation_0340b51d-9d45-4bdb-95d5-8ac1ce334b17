import React, { useState } from 'react';
import { Card, Button, Typography, Space, Alert, Collapse, Tag } from 'antd';
import { BugOutlined, ReloadOutlined, UserOutlined, KeyOutlined } from '@ant-design/icons';
import { debugAuthState, validateCartAccess, fixAuthIssues } from '../utils/authDebug';

const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

const AuthDebugPanel = () => {
  const [debugInfo, setDebugInfo] = useState(null);
  const [cartValidation, setCartValidation] = useState(null);
  const [fixResult, setFixResult] = useState(null);

  const runDebug = () => {
    const { authState, issues } = debugAuthState();
    const validation = validateCartAccess();
    
    setDebugInfo({ authState, issues });
    setCartValidation(validation);
    setFixResult(null);
  };

  const attemptFix = () => {
    const result = fixAuthIssues();
    setFixResult(result);
    
    // Re-run debug after fix attempt
    setTimeout(() => {
      runDebug();
    }, 1000);
  };

  const clearAuthData = () => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('authUser');
    localStorage.removeItem('authUserType');
    localStorage.removeItem('refreshToken');
    
    setDebugInfo(null);
    setCartValidation(null);
    setFixResult({ success: true, message: 'All authentication data cleared' });
  };

  return (
    <Card 
      title={
        <Space>
          <BugOutlined />
          <Title level={4} style={{ margin: 0 }}>Authentication Debug Panel</Title>
        </Space>
      }
      style={{ margin: '20px', maxWidth: '800px' }}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <Paragraph>
          This panel helps diagnose authentication issues that may be causing cart errors.
        </Paragraph>
        
        <Space>
          <Button 
            type="primary" 
            icon={<BugOutlined />} 
            onClick={runDebug}
          >
            Run Debug Check
          </Button>
          
          {debugInfo && debugInfo.issues.length > 0 && (
            <Button 
              type="default" 
              icon={<ReloadOutlined />} 
              onClick={attemptFix}
            >
              Attempt Auto-Fix
            </Button>
          )}
          
          <Button 
            danger 
            onClick={clearAuthData}
          >
            Clear All Auth Data
          </Button>
        </Space>

        {fixResult && (
          <Alert
            message={fixResult.success ? 'Fix Applied' : 'Fix Failed'}
            description={fixResult.message}
            type={fixResult.success ? 'success' : 'error'}
            showIcon
          />
        )}

        {debugInfo && (
          <Collapse>
            <Panel header="Authentication State" key="auth">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text strong>Token Status: </Text>
                  <Tag color={debugInfo.authState.hasToken ? 'green' : 'red'}>
                    {debugInfo.authState.hasToken ? 'Present' : 'Missing'}
                  </Tag>
                  {debugInfo.authState.hasToken && (
                    <Tag color={debugInfo.authState.isTokenExpired ? 'red' : 'green'}>
                      {debugInfo.authState.isTokenExpired ? 'Expired' : 'Valid'}
                    </Tag>
                  )}
                </div>
                
                <div>
                  <Text strong>User Data: </Text>
                  <Tag color={debugInfo.authState.hasUser ? 'green' : 'red'}>
                    {debugInfo.authState.hasUser ? 'Present' : 'Missing'}
                  </Tag>
                </div>
                
                <div>
                  <Text strong>User Type: </Text>
                  <Tag color={debugInfo.authState.userType === 'customer' ? 'green' : 'orange'}>
                    {debugInfo.authState.userType || 'Unknown'}
                  </Tag>
                </div>
                
                <div>
                  <Text strong>User ID: </Text>
                  <Text code>{debugInfo.authState.userId || 'Not found'}</Text>
                </div>
                
                <div>
                  <Text strong>Email: </Text>
                  <Text code>{debugInfo.authState.userEmail || 'Not found'}</Text>
                </div>
              </Space>
            </Panel>
            
            {debugInfo.issues.length > 0 && (
              <Panel header={`Issues Found (${debugInfo.issues.length})`} key="issues">
                <Space direction="vertical">
                  {debugInfo.issues.map((issue, index) => (
                    <Alert
                      key={index}
                      message={issue}
                      type="warning"
                      showIcon
                      size="small"
                    />
                  ))}
                </Space>
              </Panel>
            )}
          </Collapse>
        )}

        {cartValidation && (
          <Alert
            message={`Cart Access: ${cartValidation.canAccess ? 'Allowed' : 'Denied'}`}
            description={cartValidation.reason}
            type={cartValidation.canAccess ? 'success' : 'error'}
            showIcon
          />
        )}

        <Alert
          message="Common Solutions"
          description={
            <ul>
              <li>If you see "User type mismatch", you may be logged in as a vendor or admin. Log out and log in with a customer account.</li>
              <li>If the token is expired, log out and log in again.</li>
              <li>If you see "Missing" data, try clearing all auth data and logging in fresh.</li>
              <li>Make sure you're accessing the cart from a customer account, not vendor or admin.</li>
            </ul>
          }
          type="info"
          showIcon
        />
      </Space>
    </Card>
  );
};

export default AuthDebugPanel;