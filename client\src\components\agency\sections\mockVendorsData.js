// Mock vendors data for testing agency functionality
export const mockVendors = [
  {
    _id: '1',
    businessName: 'Tech Solutions Inc',
    contactPerson: '<PERSON>',
    firstName: '<PERSON>',
    lastName: '<PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+**********',
    businessCategory: 'Electronics & Technology',
    bankAccount: '**********',
    gstin: 'GST123456789',
    description: 'Leading technology solutions provider',
    isVendorApproved: true,
    status: 'active',
    verification: { status: 'verified' },
    performance: { rating: 4.5, totalProducts: 25 },
    commission: { rate: 15 },
    user: {
      firstName: '<PERSON>',
      lastName: 'Doe',
      email: '<EMAIL>'
    }
  },
  {
    _id: '2',
    businessName: 'Fashion Forward',
    contactPerson: '<PERSON>',
    firstName: 'Jane',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+**********',
    businessCategory: 'Fashion & Apparel',
    bankAccount: '**********',
    gstin: 'GST234567890',
    description: 'Trendy fashion and apparel store',
    isVendorApproved: true,
    status: 'active',
    verification: { status: 'verified' },
    performance: { rating: 4.2, totalProducts: 40 },
    commission: { rate: 12 },
    user: {
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>'
    }
  },
  {
    _id: '3',
    businessName: 'Home & Garden Paradise',
    contactPerson: '<PERSON> Johnson',
    firstName: 'Mike',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phone: '+**********',
    businessCategory: 'Home & Garden',
    bankAccount: '**********',
    gstin: '',
    description: 'Everything for your home and garden needs',
    isVendorApproved: true,
    status: 'pending_approval',
    verification: { status: 'pending' },
    performance: { rating: 0, totalProducts: 0 },
    commission: { rate: 18 },
    user: {
      firstName: 'Mike',
      lastName: 'Johnson',
      email: '<EMAIL>'
    }
  }
];

// Mock service functions
export const mockVendorService = {
  getVendors: () => {
    return Promise.resolve({
      success: true,
      vendors: mockVendors,
      pagination: {
        current: 1,
        pageSize: 10,
        total: mockVendors.length
      }
    });
  },

  createVendor: (vendorData) => {
    const newVendor = {
      _id: Date.now().toString(),
      ...vendorData,
      status: 'active',
      verification: { status: 'verified' },
      performance: { rating: 0, totalProducts: 0 },
      commission: { rate: 15 },
      user: {
        firstName: vendorData.firstName,
        lastName: vendorData.lastName,
        email: vendorData.email
      }
    };
    mockVendors.push(newVendor);
    return Promise.resolve({ success: true, vendor: newVendor });
  },

  updateVendor: (vendorId, vendorData) => {
    const index = mockVendors.findIndex(v => v._id === vendorId);
    if (index !== -1) {
      mockVendors[index] = { ...mockVendors[index], ...vendorData };
      return Promise.resolve({ success: true, vendor: mockVendors[index] });
    }
    return Promise.resolve({ success: false });
  },

  approveVendor: (vendorId) => {
    const index = mockVendors.findIndex(v => v._id === vendorId);
    if (index !== -1) {
      mockVendors[index].status = 'active';
      mockVendors[index].verification.status = 'verified';
      return Promise.resolve({ success: true });
    }
    return Promise.resolve({ success: false });
  },

  suspendVendor: (vendorId) => {
    const index = mockVendors.findIndex(v => v._id === vendorId);
    if (index !== -1) {
      mockVendors[index].status = 'suspended';
      mockVendors[index].verification.status = 'suspended';
      return Promise.resolve({ success: true });
    }
    return Promise.resolve({ success: false });
  },

  deleteVendor: (vendorId) => {
    const index = mockVendors.findIndex(v => v._id === vendorId);
    if (index !== -1) {
      mockVendors.splice(index, 1);
      return Promise.resolve({ success: true });
    }
    return Promise.resolve({ success: false });
  }
};
