import React from 'react';
import { useSearchParams } from 'react-router-dom';
import ProductCard from '../components/ui/ProductCard';

const ProductsPage = () => {
    const [searchParams] = useSearchParams();
    const currentPage = parseInt(searchParams.get('q')) || 1;

    return (
        <div>
            <div className='mx-auto max-w-7xl'>
                <div className="px-4 py-2">
                    <h2 className="text-xl font-semibold text-gray-800 mb-4">
                        All Products - Page {currentPage}
                    </h2>
                </div>
                <ProductCard initialPage={currentPage} />
            </div>
        </div>
    );
};

export default ProductsPage;