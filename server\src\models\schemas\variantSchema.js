const mongoose = require('mongoose');

const variantOptionSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  value: {
    type: String,
    required: true,
    trim: true
  },
  priceModifier: {
    type: Number,
    default: 0
  },
  sku: {
    type: String,
    trim: true
  },
  quantity: {
    type: Number,
    min: 0,
    default: 0
  },
  image: String
});

const variantSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  options: [variantOptionSchema]
});

module.exports = { variantSchema };
