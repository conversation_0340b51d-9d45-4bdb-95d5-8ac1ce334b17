const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('authToken');
  return {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` })
  };
};

// Helper function to handle API responses
const handleResponse = async (response) => {
  const data = await response.json();
  
  if (!response.ok) {
    throw new Error(data.message || `HTTP error! status: ${response.status}`);
  }
  
  return data;
};

// Order Tracking API functions
export const orderTrackingApi = {
  // Create order tracking (Vendor/Admin)
  createTracking: async (trackingData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/order-tracking`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(trackingData)
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error creating tracking:', error);
      throw error;
    }
  },

  // Get tracking by tracking number (Public)
  getTrackingByNumber: async (trackingNumber) => {
    try {
      const response = await fetch(`${API_BASE_URL}/order-tracking/${trackingNumber}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching tracking by number:', error);
      throw error;
    }
  },

  // Get tracking by order ID (Private)
  getTrackingByOrderId: async (orderId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/order-tracking/order/${orderId}`, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching tracking by order ID:', error);
      throw error;
    }
  },

  // Update tracking status (Vendor/Admin) - Enhanced for multi-vendor
  updateTrackingStatus: async (trackingId, statusData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/order-tracking/${trackingId}/status`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(statusData)
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error updating tracking status:', error);
      throw error;
    }
  },

  // Create tracking for multi-vendor order
  createMultiVendorTracking: async (orderId, vendorId, trackingData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/order-tracking`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({
          orderId,
          vendorId,
          ...trackingData
        })
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error creating multi-vendor tracking:', error);
      throw error;
    }
  },

  // Get tracking with vendor-specific details
  getTrackingWithVendorDetails: async (trackingNumber) => {
    try {
      const response = await fetch(`${API_BASE_URL}/order-tracking/${trackingNumber}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching tracking with vendor details:', error);
      throw error;
    }
  },

  // Add tracking note (Vendor/Admin/Customer)
  addTrackingNote: async (trackingId, noteData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/order-tracking/${trackingId}/notes`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(noteData)
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error adding tracking note:', error);
      throw error;
    }
  },

  // Get vendor's order trackings
  getVendorTrackings: async (params = {}) => {
    try {
      const queryParams = new URLSearchParams();
      
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value);
        }
      });

      const url = `${API_BASE_URL}/order-tracking/vendor/my-trackings${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching vendor trackings:', error);
      throw error;
    }
  },

  // Get customer's order trackings
  getCustomerTrackings: async (params = {}) => {
    try {
      const queryParams = new URLSearchParams();
      
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value);
        }
      });

      const url = `${API_BASE_URL}/order-tracking/customer/my-trackings${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching customer trackings:', error);
      throw error;
    }
  },

  // Get tracking statistics (Admin)
  getTrackingStats: async (params = {}) => {
    try {
      const queryParams = new URLSearchParams();
      
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value);
        }
      });

      const url = `${API_BASE_URL}/order-tracking/stats${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching tracking stats:', error);
      throw error;
    }
  }
};

export default orderTrackingApi;
