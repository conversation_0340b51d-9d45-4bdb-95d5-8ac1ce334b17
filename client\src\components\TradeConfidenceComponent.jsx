import React from 'react';

const TradeConfidenceComponent = () => {
  return (
    <div className="relative w-full min-h-screen overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <img
          src="https://i.ibb.co/XrR4cL4H/dl-beatsnoop-com-3000-NYe-A8n7g-Gr.jpg"
          alt="Trade confidence background"
          className="object-cover w-full h-full"
        />
        {/* Dark overlay for better text readability */}
        <div className="absolute inset-0 bg-black/40"></div>
      </div>

      {/* Content Container */}
      <div className="relative z-10 flex flex-col justify-center min-h-screen px-6 md:px-12 lg:px-16 xl:px-20">
        {/* Header Text */}
        <div className="mb-12 md:mb-16 lg:mb-20">
          <h1 className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-white leading-tight max-w-4xl">
            Trade with confidence from production quality to purchase protection
          </h1>
        </div>

        {/* Cards Container */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8 max-w-6xl">
          
          {/* Verified Supplier Card */}
          <div className="bg-black/30 backdrop-blur-sm rounded-xl md:rounded-2xl p-6 md:p-8 border border-white/10">
            <div className="mb-4">
              <p className="text-white/80 text-sm md:text-base mb-4">
                Ensure production quality with
              </p>
              <div className="flex items-center gap-2 mb-6">
                {/* Verified checkmark icon */}
                <div className="flex items-center justify-center w-8 h-8 bg-blue-500 rounded-full">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-white">
                  Verified Supplier
                </h2>
              </div>
            </div>
            
            <p className="text-white/90 text-sm md:text-base leading-relaxed">
              Connect with a variety of suppliers with third-party-verified credentials and capabilities. Look for the "Verified" logo to begin sourcing with experienced suppliers your business could rely on.
            </p>
          </div>

          {/* Trade Assurance Card */}
          <div className="bg-black/30 backdrop-blur-sm rounded-xl md:rounded-2xl p-6 md:p-8 border border-white/10">
            <div className="mb-4">
              <p className="text-white/80 text-sm md:text-base mb-4">
                Protect your purchase with
              </p>
              <div className="flex items-center gap-2 mb-6">
                {/* Shield icon */}
                <div className="flex items-center justify-center w-8 h-8 bg-yellow-500 rounded-full">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-white">
                  Trade Assurance
                </h2>
              </div>
            </div>
            
            <p className="text-white/90 text-sm md:text-base leading-relaxed">
              Source confidently with access to secure payment options, protection against product or shipping issues, and mediation support for any purchase-related concerns when you order and pay on Alibaba.com.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TradeConfidenceComponent;
