const mongoose = require('mongoose');
const User = require('../src/models/User');

async function fixUserRoles() {
  try {
    console.log('🔍 Starting user role migration...');
    
    // Connect to database if not already connected
    if (mongoose.connection.readyState !== 1) {
      const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce';
      await mongoose.connect(MONGODB_URI);
      console.log('📦 Connected to MongoDB');
    }

    // Find users with missing or invalid userType
    const usersToFix = await User.find({
      $or: [
        { userType: { $exists: false } },
        { userType: null },
        { userType: '' },
        { userType: { $nin: ['customer', 'vendor', 'admin'] } }
      ]
    });

    console.log(`📊 Found ${usersToFix.length} users with missing or invalid userType`);

    if (usersToFix.length === 0) {
      console.log('✅ All users have valid userType values');
      return;
    }

    // Fix each user
    let fixedCount = 0;
    for (const user of usersToFix) {
      try {
        // Default to 'customer' for users without a userType
        let newUserType = 'customer';
        
        // Check if user has vendor-specific fields to determine if they should be a vendor
        if (user.businessName || user.businessType || user.isVendorApproved) {
          newUserType = 'vendor';
        }
        
        // Check if user email suggests admin role
        if (user.email && (user.email.includes('admin') || user.email.includes('super'))) {
          newUserType = 'admin';
        }

        await User.findByIdAndUpdate(user._id, { userType: newUserType });
        console.log(`✅ Fixed user ${user.email}: set userType to '${newUserType}'`);
        fixedCount++;
        
      } catch (error) {
        console.error(`❌ Error fixing user ${user.email}:`, error.message);
      }
    }

    console.log(`🎉 Migration completed! Fixed ${fixedCount}/${usersToFix.length} users`);

    // Verify the fix
    const remainingIssues = await User.find({
      $or: [
        { userType: { $exists: false } },
        { userType: null },
        { userType: '' },
        { userType: { $nin: ['customer', 'vendor', 'admin'] } }
      ]
    });

    if (remainingIssues.length === 0) {
      console.log('✅ All users now have valid userType values');
    } else {
      console.log(`⚠️  Still ${remainingIssues.length} users with issues`);
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
}

// Run migration if called directly
if (require.main === module) {
  fixUserRoles()
    .then(() => {
      console.log('🏁 Migration finished');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration error:', error);
      process.exit(1);
    });
}

module.exports = fixUserRoles;
