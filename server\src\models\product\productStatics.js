const addProductStatics = (productSchema) => {
  productSchema.statics.getStats = async function () {
    try {
      const totalProducts = await this.countDocuments();
      const activeProducts = await this.countDocuments({ status: 'active' });
      const pendingProducts = await this.countDocuments({ status: 'pending_approval' });
      const lowStockProducts = await this.countDocuments({
        'inventory.quantity': { $lt: 10, $gt: 0 },
        'inventory.trackQuantity': true
      });
      const outOfStockProducts = await this.countDocuments({
        'inventory.quantity': 0,
        'inventory.trackQuantity': true
      });

      return {
        totalProducts,
        activeProducts,
        pendingProducts,
        lowStockProducts,
        outOfStockProducts,
      };
    } catch (error) {
      console.error('Error fetching product stats:', error);
      throw new Error('Error fetching product statistics');
    }
  };
};

module.exports = { addProductStatics };
            $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
          },
          draftProducts: {
            $sum: { $cond: [{ $eq: ['$status', 'draft'] }, 1, 0] }
          },
          inactiveProducts: {
            $sum: { $cond: [{ $eq: ['$status', 'inactive'] }, 1, 0] }
          },
          featuredProducts: {
            $sum: { $cond: ['$featured', 1, 0] }
          },
          outOfStockProducts: {
            $sum: { $cond: [{ $eq: ['$inventory.stockStatus', 'out_of_stock'] }, 1, 0] }
          },
          lowStockProducts: {
            $sum: { $cond: [{ $lte: ['$inventory.quantity', '$inventory.lowStockThreshold'] }, 1, 0] }
          },
          totalRevenue: { $sum: '$sales.totalRevenue' },
          totalSold: { $sum: '$sales.totalSold' },
          averagePrice: { $avg: '$pricing.basePrice' },
          averageRating: { $avg: '$reviews.averageRating' }
        }
      }
    ]);
    
    return stats[0] || {
      totalProducts: 0,
      activeProducts: 0,
      draftProducts: 0,
      inactiveProducts: 0,
      featuredProducts: 0,
      outOfStockProducts: 0,
      lowStockProducts: 0,
      totalRevenue: 0,
      totalSold: 0,
      averagePrice: 0,
      averageRating: 0
    };
  };

  // Static method to get top selling products
  productSchema.statics.getTopSelling = function(limit = 10) {
    return this.find({ status: 'active' })
      .populate({ path: 'vendor', select: 'businessName' })
      .populate('category', 'name')
      .sort({ 'sales.totalSold': -1 })
      .limit(limit)
      .select('name pricing.basePrice sales vendor category images');
  };

  // Static method to get recent products
  productSchema.statics.getRecent = function(limit = 10) {
    return this.find({ status: 'active' })
      .populate({ path: 'vendor', select: 'businessName' })
      .populate('category', 'name')
      .sort({ createdAt: -1 })
      .limit(limit)
      .select('name pricing.basePrice vendor category images createdAt');
  };

  // Static method to get low stock products
  productSchema.statics.getLowStock = function() {
    return this.find({
      'inventory.trackQuantity': true,
      $expr: { $lte: ['$inventory.quantity', '$inventory.lowStockThreshold'] }
    })
      .populate('vendor', 'businessName')
      .select('name sku inventory vendor')
      .sort({ 'inventory.quantity': 1 });
  };

  // Static method to get products pending approval
  productSchema.statics.getPendingApproval = function(limit = 20) {
    return this.find({ 'approval.status': 'pending' })
      .populate('vendor', 'businessName')
      .populate('category', 'name')
      .sort({ 'approval.submittedAt': 1 })
      .limit(limit);
  };
}

module.exports = { addProductStatics };
