import React, { useState, useEffect } from 'react';
import { 
  List, 
  Rate, 
  Avatar, 
  Button, 
  Empty, 
  Pagination, 
  Select, 
  Progress,
  Divider,
  Spin
} from 'antd';
import { 
  UserOutlined, 
  CalendarOutlined,
  MessageOutlined,
  StarFilled 
} from '@ant-design/icons';
import ReviewReply from './ReviewReply';

const { Option } = Select;

const ReviewList = ({ 
  productId, 
  reviews = [], 
  rating = null,
  loading = false,
  pagination = null,
  onPageChange,
  onSortChange 
}) => {
  const [sortBy, setSortBy] = useState('newest');
  const [showReplies, setShowReplies] = useState({});

  const handleSortChange = (value) => {
    setSortBy(value);
    onSortChange && onSortChange(value);
  };

  const toggleReply = (reviewId) => {
    setShowReplies(prev => ({
      ...prev,
      [reviewId]: !prev[reviewId]
    }));
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const renderRatingDistribution = () => {
    if (!rating || !rating.ratingDistribution) return null;

    const total = rating.totalReviews;
    if (total === 0) return null;

    return (
      <div className="rating-distribution">
        <h3>Rating Breakdown</h3>
        {[5, 4, 3, 2, 1].map(star => {
          const count = rating.ratingDistribution[star] || 0;
          const percentage = total > 0 ? (count / total) * 100 : 0;
          
          return (
            <div key={star} className="rating-row">
              <div className="rating-label">
                <span className="star-count">{star}</span>
                <StarFilled className="star-icon" />
              </div>
              <Progress 
                percent={percentage} 
                showInfo={false}
                className="rating-progress"
              />
              <span className="rating-count">({count})</span>
            </div>
          );
        })}
      </div>
    );
  };

  const renderOverallRating = () => {
    // If no rating data provided, try to calculate from reviews
    let ratingData = rating;
    if (!ratingData && reviews && reviews.length > 0) {
      const totalReviews = reviews.length;
      const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
      const averageRating = totalRating / totalReviews;
      
      const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
      reviews.forEach(review => {
        ratingDistribution[review.rating]++;
      });
      
      ratingData = {
        averageRating: parseFloat(averageRating.toFixed(1)),
        totalReviews,
        ratingDistribution
      };
    }
    
    if (!ratingData || ratingData.totalReviews === 0) {
      return (
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-400">0</div>
            <Rate disabled value={0} className="text-sm" />
            <div className="text-xs text-gray-500 mt-1">No reviews yet</div>
          </div>
          <div className="flex-1 ml-6">
            <div className="text-sm text-gray-500 mb-2">Be the first to review!</div>
            {[5, 4, 3, 2, 1].map(star => (
              <div key={star} className="flex items-center gap-2 mb-1">
                <span className="text-xs text-gray-400 w-3">{star}</span>
                <Progress percent={0} showInfo={false} strokeColor="#e5e7eb" className="flex-1" strokeWidth={4} />
                <span className="text-xs text-gray-400 w-6">(0)</span>
              </div>
            ))}
          </div>
        </div>
      );
    }

    return (
      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 mb-4">
        <div className="text-center">
          <div className="text-3xl font-bold text-blue-600">{ratingData.averageRating.toFixed(1)}</div>
          <Rate disabled allowHalf value={ratingData.averageRating} className="text-base" />
          <div className="text-xs text-gray-600 mt-1">
            {ratingData.totalReviews} review{ratingData.totalReviews !== 1 ? 's' : ''}
          </div>
        </div>
        <div className="flex-1 ml-6">
          {[5, 4, 3, 2, 1].map(star => {
            const count = ratingData.ratingDistribution?.[star] || 0;
            const percentage = ratingData.totalReviews > 0 ? (count / ratingData.totalReviews) * 100 : 0;
            return (
              <div key={star} className="flex items-center gap-2 mb-1">
                <span className="text-xs text-gray-600 w-3">{star}</span>
                <Progress 
                  percent={percentage} 
                  showInfo={false} 
                  strokeColor="#3b82f6"
                  trailColor="#e5e7eb"
                  className="flex-1"
                  strokeWidth={4}
                />
                <span className="text-xs text-gray-600 w-8">({count})</span>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderReviewItem = (review) => (
    <div key={review._id} className="py-4 border-b border-gray-100 last:border-b-0">
      <div className="flex gap-3">
        <Avatar 
          src={review.customer?.avatar} 
          icon={<UserOutlined />}
          size={40}
          className="flex-shrink-0"
        />
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium text-gray-900 text-sm">
              {review.customer?.firstName} {review.customer?.lastName}
            </span>
            {review.isVerifiedPurchase && (
              <span className="px-2 py-0.5 bg-green-100 text-green-700 text-xs rounded-full">
                ✓ Verified
              </span>
            )}
          </div>
          <div className="flex items-center gap-2 mb-2">
            <Rate disabled value={review.rating} className="text-sm" />
            <span className="text-xs text-gray-500">{formatDate(review.createdAt)}</span>
          </div>
          <p className="text-gray-700 text-sm leading-relaxed mb-2">{review.comment}</p>
          
          {/* Vendor Reply */}
          {review.replies && review.replies.length > 0 && (
            <div className="mt-3 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-200">
              <div className="flex items-center gap-2 mb-1">
                <MessageOutlined className="text-blue-600 text-xs" />
                <span className="text-xs font-medium text-blue-800">Vendor Reply</span>
              </div>
              {review.replies.map(reply => (
                <p key={reply._id} className="text-sm text-blue-700">
                  {reply.message}
                </p>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg border border-gray-200">
        <div className="flex flex-col items-center justify-center py-12">
          <Spin size="large" />
          <p className="text-gray-600 mt-4">Loading reviews...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <h2 className="text-xl font-bold text-gray-900">Customer Reviews</h2>
        {reviews.length > 0 && (
          <Select
            value={sortBy}
            onChange={handleSortChange}
            className="w-40"
            placeholder="Sort by"
          >
            <Option value="newest">Newest First</Option>
            <Option value="oldest">Oldest First</Option>
            <Option value="highest-rating">Highest Rating</Option>
            <Option value="lowest-rating">Lowest Rating</Option>
          </Select>
        )}
      </div>

      {/* Content */}
      <div className="p-6">
        {renderOverallRating()}
        
        {reviews.length === 0 ? (
          <div className="text-center py-12">
            <Empty 
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <div>
                  <p className="text-gray-500 text-lg mb-2">No reviews yet</p>
                  <p className="text-gray-400">Be the first to review this product!</p>
                </div>
              }
            />
          </div>
        ) : (
          <>
            <Divider className="my-6" />
            <div className="space-y-6">
              {reviews.map(renderReviewItem)}
            </div>
            
            {pagination && pagination.totalPages > 1 && (
              <div className="flex justify-center mt-8 pt-6 border-t border-gray-200">
                <Pagination
                  current={pagination.currentPage}
                  total={pagination.totalReviews}
                  pageSize={10}
                  showSizeChanger={false}
                  showQuickJumper
                  showTotal={(total, range) => 
                    `${range[0]}-${range[1]} of ${total} reviews`
                  }
                  onChange={onPageChange}
                />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ReviewList;
