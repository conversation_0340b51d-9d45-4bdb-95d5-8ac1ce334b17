const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const userSchema = new mongoose.Schema({}, { strict: false });
const User = mongoose.model('User', userSchema);

async function migrateUserPreferences() {
  try {
    console.log('Starting user preferences migration...');
    
    // Find users without preferences field
    const usersWithoutPreferences = await User.find({
      $or: [
        { preferences: { $exists: false } },
        { preferences: null },
        { 'preferences.language': { $exists: false } },
        { 'preferences.currency': { $exists: false } },
        { 'preferences.timezone': { $exists: false } }
      ]
    });
    
    console.log(`Found ${usersWithoutPreferences.length} users to update`);
    
    if (usersWithoutPreferences.length > 0) {
      // Update users with default preferences
      const result = await User.updateMany(
        {
          $or: [
            { preferences: { $exists: false } },
            { preferences: null },
            { 'preferences.language': { $exists: false } },
            { 'preferences.currency': { $exists: false } },
            { 'preferences.timezone': { $exists: false } }
          ]
        },
        {
          $set: {
            preferences: {
              language: 'en',
              timezone: 'UTC',
              currency: 'USD'
            }
          }
        }
      );
      
      console.log(`Successfully updated ${result.modifiedCount} users with default preferences`);
    }
    
    // Also ensure all users have default country if missing
    const usersWithoutCountry = await User.find({
      $or: [
        { country: { $exists: false } },
        { country: null },
        { country: '' }
      ]
    });
    
    if (usersWithoutCountry.length > 0) {
      const countryResult = await User.updateMany(
        {
          $or: [
            { country: { $exists: false } },
            { country: null },
            { country: '' }
          ]
        },
        {
          $set: {
            country: 'United States'
          }
        }
      );
      
      console.log(`Successfully updated ${countryResult.modifiedCount} users with default country`);
    }
    
    console.log('Migration completed successfully!');
    
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run migration
migrateUserPreferences();
