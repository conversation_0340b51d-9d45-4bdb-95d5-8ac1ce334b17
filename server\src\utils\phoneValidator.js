const { parsePhoneNumberFromString } = require('libphonenumber-js');

/**
 * Validates a phone number against a country code using libphonenumber-js.
 * It robustly handles E.164 format (e.g., +18185088040) or national format numbers.
 *
 * @param {string} phone - The phone number to validate.
 * @param {string} countryCode - The two-letter country code (e.g., 'US').
 * @returns {boolean} - True if the phone number is valid for the country.
 */
function isValidPhoneForCountry(phone, countryCode) {
  if (!phone || !countryCode) {
    return false;
  }

  try {
    // The library can parse a full E.164 number and will ignore the
    // passed countryCode if the number is absolute (starts with +).
    // Or, it will use the countryCode if the number is in national format.
    const phoneNumber = parsePhoneNumberFromString(phone, countryCode);

    // Check if the number is valid AND if its country matches the provided countryCode.
    return phoneNumber && phoneNumber.isValid() && phoneNumber.country === countryCode;
  } catch (error) {
    console.error(`Phone validation error for phone: ${phone}, country: ${countryCode}`, error);
    return false;
  }
}

module.exports = { isValidPhoneForCountry };