const jwt = require('jsonwebtoken');
const User = require('../../models/User');

// Verify JWT token
const verifyToken = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                success: false,
                message: 'Access token is required'
            });
        }

        const token = authHeader.substring(7); // Remove 'Bearer ' prefix

        // Verify token
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
        
        // Find user
        const user = await User.findById(decoded.userId);
        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'User not found'
            });
        }

        // Check if user status is active (using the existing status field)
        if (user.status !== 'active') {
            return res.status(401).json({
                success: false,
                message: 'Account is not active'
            });
        }

        // Update last login time
        user.lastLogin = new Date();
        await user.save();

        // Attach user info to request with consistent structure
        req.user = {
            _id: user._id, // Add _id for cart controller compatibility
            userId: user._id,
            userType: user.userType,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            status: user.status,
            isEmailVerified: user.isEmailVerified,
            // Keep the full user object available as well
            _fullUser: user
        };

        next();
    } catch (error) {
        console.error('Token verification error:', error);
        
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({
                success: false,
                message: 'Invalid token'
            });
        }
        
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                success: false,
                message: 'Token expired'
            });
        }

        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

// Check if user type matches required type
const requireUserType = (allowedTypes) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
        }

        const userTypes = Array.isArray(allowedTypes) ? allowedTypes : [allowedTypes];

        if (!userTypes.includes(req.user.userType)) {
            console.log(`Access denied: User type '${req.user.userType}' not in allowed types [${userTypes.join(', ')}]`);
            return res.status(403).json({
                success: false,
                message: 'Access denied. Insufficient permissions.',
                debug: {
                    userType: req.user.userType,
                    allowedTypes: userTypes
                }
            });
        }

        next();
    };
};

// Check if user role matches required role
const requireRole = (allowedRoles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
        }

        const roles = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];
        
        if (!roles.includes(req.user.role)) {
            return res.status(403).json({
                success: false,
                message: 'Access denied. Insufficient permissions.'
            });
        }

        next();
    };
};

// Require email verification
const requireEmailVerification = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({
            success: false,
            message: 'Authentication required'
        });
    }

    if (!req.user.isEmailVerified) {
        return res.status(403).json({
            success: false,
            message: 'Email verification required',
            code: 'EMAIL_NOT_VERIFIED'
        });
    }

    next();
};


// Check if vendor is approved
const requireVendorApproval = async (req, res, next) => {
    try {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
        }

        if (req.user.userType !== 'vendor') {
            return res.status(403).json({
                success: false,
                message: 'Vendor access required'
            });
        }

        // Check vendor approval directly from req.user since it's now the full user object
        if (!req.user.isVendorApproved) {
            return res.status(403).json({
                success: false,
                message: 'Vendor approval required',
                code: 'VENDOR_NOT_APPROVED'
            });
        }

        next();
    } catch (error) {
        console.error('Vendor approval check error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

// Optional authentication (doesn't fail if no token)
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return next(); // Continue without authentication
        }

        const token = authHeader.substring(7);

        try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
            const user = await User.findById(decoded.userId);
            
            if (user && user.status === 'active') {
                req.user = {
                    userId: user._id,
                    userType: user.userType,
                    email: user.email,
                    isEmailVerified: user.isEmailVerified
                };
            }
        } catch (tokenError) {
            // Invalid token, but continue without authentication
            console.log('Optional auth token error:', tokenError.message);
        }

        next();
    } catch (error) {
        console.error('Optional auth error:', error);
        next(); // Continue even if there's an error
    }
};

// Rate limiting middleware for authentication endpoints
const authRateLimit = (maxAttempts = 5, windowMs = 15 * 60 * 1000) => {
    const attempts = new Map();

    return (req, res, next) => {
        const key = req.ip + ':' + (req.body.email || req.body.phone || 'unknown');
        const now = Date.now();
        
        // Clean old entries
        for (const [k, v] of attempts.entries()) {
            if (now - v.firstAttempt > windowMs) {
                attempts.delete(k);
            }
        }

        const userAttempts = attempts.get(key);
        
        if (!userAttempts) {
            attempts.set(key, { count: 1, firstAttempt: now });
            return next();
        }

        if (userAttempts.count >= maxAttempts) {
            const timeLeft = Math.ceil((userAttempts.firstAttempt + windowMs - now) / 1000 / 60);
            return res.status(429).json({
                success: false,
                message: `Too many attempts. Please try again in ${timeLeft} minutes.`
            });
        }

        userAttempts.count++;
        next();
    };
};

module.exports = {
    verifyToken,
    requireUserType,
    requireRole,
    requireEmailVerification,
    requireVendorApproval,
    optionalAuth,
    authRateLimit
};