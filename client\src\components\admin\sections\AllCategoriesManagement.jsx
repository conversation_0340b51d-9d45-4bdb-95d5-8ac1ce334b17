import React, { useState, useEffect } from 'react';
import {
  Card, Button, Form, Input, message, Table, Switch, 
  Space, Popconfirm, Tag, Tooltip, Modal, Select, Divider
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, 
  AppstoreOutlined, ReloadOutlined, SettingOutlined,
  ShopOutlined, MobileOutlined, CrownOutlined, HeartOutlined,
  CarOutlined, HomeOutlined, SkinOutlined, ShoppingOutlined,
  PrinterOutlined, BugOutlined, SmileOutlined, MedicineBoxOutlined,
  GiftOutlined, TableOutlined, BulbOutlined, SecurityScanOutlined,
  BookOutlined, CameraOutlined, ClockCircleOutlined, CoffeeOutlined,
  CompassOutlined, DatabaseOutlined, DesktopOutlined, DollarOutlined,
  EnvironmentOutlined, ExperimentOutlined, EyeOutlined, FileOutlined,
  FireOutlined, FlagOutlined, FolderOutlined, FundOutlined,
  GlobalOutlined, GoldOutlined, GroupOutlined, HddOutlined,
  <PERSON>Outlined, IdcardOutlined, InfoCircleOutlined, <PERSON>Outlined,
  LaptopOutlined, LayoutOutlined, LikeOutlined, <PERSON>Outlined,
  <PERSON>Outlined, MoneyCollectOutlined, NotificationOutlined, Paper<PERSON>lipOutlined,
  PhoneOutlined, PictureOutlined, PlayCircleOutlined, PlusCircleOutlined,
  ProfileOutlined, ProjectOutlined, PushpinOutlined, QuestionCircleOutlined,
  ReadOutlined, RocketOutlined, SafetyOutlined, SaveOutlined,
  ScanOutlined, ScheduleOutlined, SearchOutlined, ShakeOutlined,
  ShareAltOutlined, SoundOutlined, StarOutlined, SyncOutlined,
  TagOutlined, TeamOutlined, ThunderboltOutlined, ToolOutlined,
  TrophyOutlined, UsbOutlined, UserOutlined, VideoCameraOutlined,
  WalletOutlined, WifiOutlined, ZoomInOutlined
} from '@ant-design/icons';
import { homepageSettingsApi } from '../../../services/adminApi';

const { Option } = Select;

// Icon mapping for rendering actual icons
const iconMap = {
  ShopOutlined, MobileOutlined, CrownOutlined, HeartOutlined,
  CarOutlined, HomeOutlined, SkinOutlined, ShoppingOutlined,
  PrinterOutlined, BugOutlined, SmileOutlined, MedicineBoxOutlined,
  GiftOutlined, TableOutlined, BulbOutlined, SettingOutlined,
  SecurityScanOutlined, AppstoreOutlined, BookOutlined, CameraOutlined,
  ClockCircleOutlined, CoffeeOutlined, CompassOutlined, DatabaseOutlined,
  DesktopOutlined, DollarOutlined, EnvironmentOutlined, ExperimentOutlined,
  EyeOutlined, FileOutlined, FireOutlined, FlagOutlined,
  FolderOutlined, FundOutlined, GlobalOutlined, GoldOutlined,
  GroupOutlined, HddOutlined, HistoryOutlined, IdcardOutlined,
  InfoCircleOutlined, KeyOutlined, LaptopOutlined, LayoutOutlined,
  LikeOutlined, LockOutlined, MailOutlined, MoneyCollectOutlined,
  NotificationOutlined, PaperClipOutlined, PhoneOutlined, PictureOutlined,
  PlayCircleOutlined, PlusCircleOutlined, ProfileOutlined, ProjectOutlined,
  PushpinOutlined, QuestionCircleOutlined, ReadOutlined, RocketOutlined,
  SafetyOutlined, SaveOutlined, ScanOutlined, ScheduleOutlined,
  SearchOutlined, ShakeOutlined, ShareAltOutlined, SoundOutlined,
  StarOutlined, SyncOutlined, TagOutlined, TeamOutlined,
  ThunderboltOutlined, ToolOutlined, TrophyOutlined, UsbOutlined,
  UserOutlined, VideoCameraOutlined, WalletOutlined, WifiOutlined,
  ZoomInOutlined
};

// Available icons for main categories
const availableIcons = Object.keys(iconMap);

const AllCategoriesManagement = () => {
  const [loading, setLoading] = useState(false);
  const [categoriesData, setCategoriesData] = useState({
    mainCategories: [],
    popularCategories: []
  });

  // Modal states
  const [mainCategoryModalVisible, setMainCategoryModalVisible] = useState(false);
  const [popularCategoryModalVisible, setPopularCategoryModalVisible] = useState(false);
  const [editingMainCategory, setEditingMainCategory] = useState(null);
  const [editingPopularCategory, setEditingPopularCategory] = useState(null);

  // Form instances
  const [mainCategoryForm] = Form.useForm();
  const [popularCategoryForm] = Form.useForm();

  useEffect(() => {
    fetchCategoriesData();
  }, []);

  const fetchCategoriesData = async () => {
    setLoading(true);
    try {
      const response = await homepageSettingsApi.getAllCategoriesModal();
      if (response.data.success) {
        setCategoriesData(response.data.data);
      }
    } catch (error) {
      console.error('Failed to fetch categories data:', error);
      message.error('Failed to fetch categories data');
    } finally {
      setLoading(false);
    }
  };

  const handleInitializeDefaults = async () => {
    setLoading(true);
    try {
      const response = await homepageSettingsApi.initializeDefaultCategories();
      if (response.data.success) {
        setCategoriesData(response.data.data);
        message.success('Default categories initialized successfully');
      }
    } catch (error) {
      console.error('Failed to initialize default categories:', error);
      message.error('Failed to initialize default categories');
    } finally {
      setLoading(false);
    }
  };

  // Main Category handlers
  const handleAddMainCategory = () => {
    setEditingMainCategory(null);
    mainCategoryForm.resetFields();
    setMainCategoryModalVisible(true);
  };

  const handleEditMainCategory = (category) => {
    setEditingMainCategory(category);
    mainCategoryForm.setFieldsValue({
      name: category.name,
      icon: category.icon,
      linkUrl: category.linkUrl,
      isActive: category.isActive
    });
    setMainCategoryModalVisible(true);
  };

  const handleMainCategorySubmit = async () => {
    try {
      const values = await mainCategoryForm.validateFields();
      setLoading(true);

      if (editingMainCategory) {
        // Update existing category
        await homepageSettingsApi.updateMainCategory(editingMainCategory._id, values);
        message.success('Main category updated successfully');
      } else {
        // Add new category
        await homepageSettingsApi.addMainCategory(values);
        message.success('Main category added successfully');
      }

      setMainCategoryModalVisible(false);
      fetchCategoriesData();
    } catch (error) {
      console.error('Error saving main category:', error);
      message.error('Failed to save main category');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteMainCategory = async (categoryId) => {
    setLoading(true);
    try {
      await homepageSettingsApi.deleteMainCategory(categoryId);
      message.success('Main category deleted successfully');
      fetchCategoriesData();
    } catch (error) {
      console.error('Error deleting main category:', error);
      message.error('Failed to delete main category');
    } finally {
      setLoading(false);
    }
  };

  // Popular Category handlers
  const handleAddPopularCategory = () => {
    setEditingPopularCategory(null);
    popularCategoryForm.resetFields();
    setPopularCategoryModalVisible(true);
  };

  const handleEditPopularCategory = (category) => {
    setEditingPopularCategory(category);
    popularCategoryForm.setFieldsValue({
      name: category.name,
      linkUrl: category.linkUrl,
      isActive: category.isActive
    });
    setPopularCategoryModalVisible(true);
  };

  const handlePopularCategorySubmit = async () => {
    try {
      const values = await popularCategoryForm.validateFields();
      setLoading(true);

      if (editingPopularCategory) {
        // Update existing category
        await homepageSettingsApi.updatePopularCategory(editingPopularCategory._id, values);
        message.success('Popular category updated successfully');
      } else {
        // Add new category
        await homepageSettingsApi.addPopularCategory(values);
        message.success('Popular category added successfully');
      }

      setPopularCategoryModalVisible(false);
      fetchCategoriesData();
    } catch (error) {
      console.error('Error saving popular category:', error);
      message.error('Failed to save popular category');
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePopularCategory = async (categoryId) => {
    setLoading(true);
    try {
      await homepageSettingsApi.deletePopularCategory(categoryId);
      message.success('Popular category deleted successfully');
      fetchCategoriesData();
    } catch (error) {
      console.error('Error deleting popular category:', error);
      message.error('Failed to delete popular category');
    } finally {
      setLoading(false);
    }
  };

  // Table columns for main categories
  const mainCategoriesColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: 'Icon',
      dataIndex: 'icon',
      key: 'icon',
      width: 120,
      render: (icon) => (
        <Tag color="blue">{icon}</Tag>
      ),
    },
    {
      title: 'Link URL',
      dataIndex: 'linkUrl',
      key: 'linkUrl',
      render: (linkUrl) => linkUrl || '-',
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 100,
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Sort Order',
      dataIndex: 'sortOrder',
      key: 'sortOrder',
      width: 100,
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditMainCategory(record)}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this main category?"
            onConfirm={() => handleDeleteMainCategory(record._id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // Table columns for popular categories
  const popularCategoriesColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: 250,
    },
    {
      title: 'Link URL',
      dataIndex: 'linkUrl',
      key: 'linkUrl',
      render: (linkUrl) => linkUrl || '-',
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 100,
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Sort Order',
      dataIndex: 'sortOrder',
      key: 'sortOrder',
      width: 100,
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditPopularCategory(record)}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this popular category?"
            onConfirm={() => handleDeletePopularCategory(record._id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <h3>All Categories Modal Management</h3>
        <p style={{ color: '#666', marginBottom: 16 }}>
          Manage the categories that appear in the "All Categories" modal on the main navigation menu.
          This affects only the MainNavigationMenu's "All Categories" dropdown.
        </p>
        <Space>
          <Button
            type="default"
            icon={<ReloadOutlined />}
            onClick={fetchCategoriesData}
            loading={loading}
          >
            Refresh
          </Button>
          <Button
            type="default"
            icon={<SettingOutlined />}
            onClick={handleInitializeDefaults}
            loading={loading}
          >
            Initialize Defaults
          </Button>
        </Space>
      </div>

      {/* Main Categories Section */}
      <Card 
        title="Main Categories (6 columns grid)" 
        style={{ marginBottom: 24 }}
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddMainCategory}
            disabled={categoriesData.mainCategories.length >= 18}
          >
            Add Main Category
          </Button>
        }
      >
        <div style={{ marginBottom: 16 }}>
          <p style={{ color: '#666' }}>
            Maximum 18 main categories allowed. These appear in a 6-column grid layout.
          </p>
        </div>
        <Table
          columns={mainCategoriesColumns}
          dataSource={categoriesData.mainCategories}
          rowKey="_id"
          loading={loading}
          pagination={{ pageSize: 10 }}
          scroll={{ x: 800 }}
        />
      </Card>

      {/* Popular Categories Section */}
      <Card 
        title="Popular Categories (5 per row)" 
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddPopularCategory}
            disabled={categoriesData.popularCategories.length >= 10}
          >
            Add Popular Category
          </Button>
        }
      >
        <div style={{ marginBottom: 16 }}>
          <p style={{ color: '#666' }}>
            Maximum 10 popular categories allowed. These appear as orange buttons in a 5-column layout.
          </p>
        </div>
        <Table
          columns={popularCategoriesColumns}
          dataSource={categoriesData.popularCategories}
          rowKey="_id"
          loading={loading}
          pagination={{ pageSize: 10 }}
          scroll={{ x: 600 }}
        />
      </Card>

      {/* Main Category Modal */}
      <Modal
        title={editingMainCategory ? 'Edit Main Category' : 'Add Main Category'}
        open={mainCategoryModalVisible}
        onOk={handleMainCategorySubmit}
        onCancel={() => setMainCategoryModalVisible(false)}
        confirmLoading={loading}
        width={600}
      >
        <Form form={mainCategoryForm} layout="vertical">
          <Form.Item
            name="name"
            label="Category Name"
            rules={[
              { required: true, message: 'Please enter category name' },
              { max: 50, message: 'Category name cannot exceed 50 characters' }
            ]}
          >
            <Input placeholder="Enter category name" />
          </Form.Item>

          <Form.Item
            name="icon"
            label="Icon"
            rules={[{ required: true, message: 'Please select an icon' }]}
          >
            <Select
              placeholder="Select an icon"
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              optionRender={(option) => {
                const IconComponent = iconMap[option.value];
                return (
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    {IconComponent && <IconComponent style={{ fontSize: 16 }} />}
                    <span>{option.value}</span>
                  </div>
                );
              }}
            >
              {availableIcons.map(icon => {
                const IconComponent = iconMap[icon];
                return (
                  <Option key={icon} value={icon}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      {IconComponent && <IconComponent style={{ fontSize: 16 }} />}
                      <span>{icon}</span>
                    </div>
                  </Option>
                );
              })}
            </Select>
          </Form.Item>

          <Form.Item
            name="linkUrl"
            label="Link URL (optional)"
          >
            <Input placeholder="Enter link URL (e.g., /category/electronics)" />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="Status"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="Active" unCheckedChildren="Inactive" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Popular Category Modal */}
      <Modal
        title={editingPopularCategory ? 'Edit Popular Category' : 'Add Popular Category'}
        open={popularCategoryModalVisible}
        onOk={handlePopularCategorySubmit}
        onCancel={() => setPopularCategoryModalVisible(false)}
        confirmLoading={loading}
        width={500}
      >
        <Form form={popularCategoryForm} layout="vertical">
          <Form.Item
            name="name"
            label="Category Name"
            rules={[
              { required: true, message: 'Please enter category name' },
              { max: 50, message: 'Category name cannot exceed 50 characters' }
            ]}
          >
            <Input placeholder="Enter category name" />
          </Form.Item>

          <Form.Item
            name="linkUrl"
            label="Link URL (optional)"
          >
            <Input placeholder="Enter link URL (e.g., /category/electronics)" />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="Status"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="Active" unCheckedChildren="Inactive" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AllCategoriesManagement;