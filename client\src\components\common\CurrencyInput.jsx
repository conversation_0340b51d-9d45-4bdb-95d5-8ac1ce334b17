import React, { useCallback } from 'react';
import { Card, InputNumber, Row, Col, Typography, Space, Tag, Button, Tooltip } from 'antd';
import { DeleteOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { validatePrice } from '../../utils/priceValidation';

const { Text } = Typography;

const CurrencyInput = React.memo(({
  currency,
  currencyInfo,
  prices = {},
  onPriceChange,
  onRemove,
  disabled = false,
  required = false,
  isDefault = false
}) => {
  const errors = validatePrice(currency, prices.basePrice, prices.salePrice, required);

  const handleBasePriceChange = useCallback((value) => {
    onPriceChange(currency, 'basePrice', value);
  }, [currency, onPriceChange]);

  const handleSalePriceChange = useCallback((value) => {
    onPriceChange(currency, 'salePrice', value);
  }, [currency, onPriceChange]);

  const handleRemoveClick = useCallback(() => {
    onRemove(currency);
  }, [currency, onRemove]);

  return (
    <Card
      size="small"
      title={
        <Space>
          <Tag color={isDefault ? 'green' : 'blue'}>
            {currencyInfo.symbol} {currency}
          </Tag>
          <Text type="secondary">{currencyInfo.name}</Text>
          {isDefault && <Tag color="gold">Default</Tag>}
        </Space>
      }
      extra={
        !isDefault && (
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={handleRemoveClick}
            disabled={disabled}
          />
        )
      }
      style={{ marginBottom: 16 }}
    >
      <Row gutter={16}>
        <Col span={12}>
          <div style={{ marginBottom: 8 }}>
            <Text strong>Base Price *</Text>
          </div>
          <InputNumber
            placeholder="Enter base price"
            value={prices.basePrice}
            onChange={handleBasePriceChange}
            min={0}
            step={0.01}
            style={{ width: '100%' }}
            disabled={disabled}
            status={errors.some(e => e.includes('base price')) ? 'error' : ''}
            addonBefore={currencyInfo.symbol}
          />
        </Col>
        <Col span={12}>
          <div style={{ marginBottom: 8 }}>
            <Space>
              <Text strong>Sale Price</Text>
              <Tooltip title="Optional. Must be less than base price">
                <InfoCircleOutlined style={{ color: '#999' }} />
              </Tooltip>
            </Space>
          </div>
          <InputNumber
            placeholder="Enter sale price"
            value={prices.salePrice}
            onChange={handleSalePriceChange}
            min={0}
            step={0.01}
            style={{ width: '100%' }}
            disabled={disabled}
            status={errors.some(e => e.includes('sale price')) ? 'error' : ''}
            addonBefore={currencyInfo.symbol}
          />
        </Col>
      </Row>
      {errors.length > 0 && (
        <div style={{ marginTop: 8 }}>
          {errors.map((error, index) => (
            <Text key={index} type="danger" style={{ fontSize: '12px', display: 'block' }}>
              {error}
            </Text>
          ))}
        </div>
      )}
    </Card>
  );
});

CurrencyInput.displayName = 'CurrencyInput';

export default CurrencyInput;
