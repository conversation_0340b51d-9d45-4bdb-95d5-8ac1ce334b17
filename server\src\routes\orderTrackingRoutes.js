const express = require('express');
const router = express.Router();
const {
  createOrderTracking,
  getTrackingByNumber,
  getTrackingByOrderId,
  updateTrackingStatus,
  addTrackingNote,
  getVendorTrackings,
  getCustomerTrackings,
  getTrackingStats
} = require('../controllers/orderTrackingController');
const { verifyToken, requireUserType } = require('../middleware/auth/authMiddleware');
const { orderTrackingValidator, statusUpdateValidator, noteValidator } = require('../validators/orderTrackingValidators');

// Vendor/Admin create tracking
router.post('/', verifyToken, requireUserType(['vendor', 'admin']), orderTrackingValidator, createOrderTracking);

// Public get tracking by tracking number
router.get('/:trackingNumber', getTrackingByNumber);

// Private get tracking by order ID
router.get('/order/:orderId', verifyToken, getTrackingByOrderId);

// Vendor/Admin update tracking status
router.put('/:trackingId/status', verifyToken, requireUserType(['vendor', 'admin']), statusUpdateValidator, updateTrackingStatus);

// Vendor/Admin/Customer add tracking note
router.post('/:trackingId/notes', verifyToken, noteValidator, addTrackingNote);

// Vendor get their order trackings
router.get('/vendor/my-trackings', verifyToken, requireUserType(['vendor']), getVendorTrackings);

// Customer get their order trackings
router.get('/customer/my-trackings', verifyToken, requireUserType(['customer']), getCustomerTrackings);

// Admin get tracking statistics
router.get('/stats', verifyToken, requireUserType(['admin']), getTrackingStats);

module.exports = router;
