const { uploaders, deleteImage, extractPublicId, validateCloudinaryConfig } = require('../../config/cloudinary');

/**
 * Middleware for handling image uploads with Cloudinary
 * Supports single image, multiple images, and specific field uploads
 */
const imageUpload = {
  /**
   * Upload a single image to Cloudinary
   * @param {string} fieldName - Form field name for the image
   * @param {string} uploaderType - Type of uploader to use (default: 'userAvatar')
   * @returns {Function} Express middleware
   */
  single: (fieldName = 'image', uploaderType = 'userAvatar') => {
    // Check if Cloudinary is configured
    if (!validateCloudinaryConfig()) {
      // Fallback to local storage if Cloudinary is not configured
      const { createUploader, FILE_SIZE_LIMITS } = require('../../config/upload');
      const uploader = createUploader('image').single(fieldName);
      
      return (req, res, next) => {
        uploader(req, res, (err) => {
          if (err) {
            return res.status(400).json({
              success: false,
              error: err.message || 'Error uploading image'
            });
          }
          
          if (req.file) {
            // Determine the correct path based on the upload type
            let subfolder = 'users';
            if (req.path.includes('logo') || req.path.includes('banner')) {
              subfolder = 'vendors';
            } else if (req.path.includes('product')) {
              subfolder = 'products';
            }
            req.fileUrl = `/uploads/images/${subfolder}/${req.file.filename}`;
          }
          next();
        });
      };
    }

    const uploader = uploaders[uploaderType] || uploaders.userAvatar;
    console.log(`📤 Setting up ${uploaderType} uploader for field: ${fieldName}`);
    
    return (req, res, next) => {
      console.log(`🚀 Starting ${uploaderType} upload process...`);
      
      uploader.single(fieldName)(req, res, (err) => {
        if (err) {
          console.error('❌ Cloudinary upload error:', err);
          
          if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
              success: false,
              error: 'Image size should not exceed the limit'
            });
          }
          
          // This might be our timeout error
          if (err.message && err.message.includes('timeout')) {
            console.error('⏰ Upload timeout detected:', err.message);
            return res.status(400).json({
              success: false,
              error: 'Request Timeout'
            });
          }
          
          return res.status(400).json({
            success: false,
            error: err.message || 'Error uploading image'
          });
        }
        
        // If no file was uploaded, just continue
        if (!req.file) {
          console.log('ℹ️ No file detected, continuing...');
          return next();
        }
        
        console.log('✅ Upload successful! File info:', {
          path: req.file.path,
          filename: req.file.filename,
          size: req.file.size
        });
        
        // Add Cloudinary URL to request
        req.fileUrl = req.file.path; // Cloudinary URL
        req.cloudinaryPublicId = req.file.filename; // Cloudinary public ID
        
        console.log('📋 Request properties set:', {
          fileUrl: req.fileUrl,
          cloudinaryPublicId: req.cloudinaryPublicId
        });
        
        next();
      });
    };
  },
  
  /**
   * Upload multiple images to Cloudinary
   * @param {string} fieldName - Form field name for the images
   * @param {number} maxCount - Maximum number of images to upload
   * @param {string} uploaderType - Type of uploader to use (default: 'productImages')
   * @returns {Function} Express middleware
   */
  multiple: (fieldName = 'images', maxCount = 5, uploaderType = 'productImages') => {
    // Check if Cloudinary is configured
    if (!validateCloudinaryConfig()) {
      // Fallback to local storage if Cloudinary is not configured
      const { createUploader, FILE_SIZE_LIMITS } = require('../../config/upload');
      const uploader = createUploader('image').array(fieldName, maxCount);
      
      return (req, res, next) => {
        uploader(req, res, (err) => {
          if (err) {
            return res.status(400).json({
              success: false,
              error: err.message || 'Error uploading images'
            });
          }
          
          if (req.files && req.files.length > 0) {
            req.fileUrls = req.files.map(file => `/uploads/images/${file.filename}`);
          }
          next();
        });
      };
    }

    const uploader = uploaders[uploaderType] || uploaders.productImages;
    
    return (req, res, next) => {
      uploader.array(fieldName, maxCount)(req, res, (err) => {
        if (err) {
          console.error('Cloudinary upload error:', err);
          
          if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
              success: false,
              error: 'Each image size should not exceed the limit'
            });
          }
          
          if (err.code === 'LIMIT_UNEXPECTED_FILE') {
            return res.status(400).json({
              success: false,
              error: `Maximum ${maxCount} images allowed`
            });
          }
          
          return res.status(400).json({
            success: false,
            error: err.message || 'Error uploading images'
          });
        }
        
        // If no files were uploaded, just continue
        if (!req.files || req.files.length === 0) {
          return next();
        }
        
        // Add Cloudinary URLs to request
        req.fileUrls = req.files.map(file => file.path); // Cloudinary URLs
        req.cloudinaryPublicIds = req.files.map(file => file.filename); // Cloudinary public IDs
        next();
      });
    };
  },
  
  /**
   * Upload profile avatar to Cloudinary
   * @returns {Function} Express middleware
   */
  avatar: () => {
    return imageUpload.single('avatar', 'userAvatar');
  },
  
  /**
   * Upload product images to Cloudinary
   * @param {number} maxCount - Maximum number of product images
   * @returns {Function} Express middleware
   */
  product: (maxCount = 8) => {
    return imageUpload.multiple('productImages', maxCount, 'productImages');
  },
  
  /**
   * Upload vendor logo to Cloudinary
   * @returns {Function} Express middleware
   */
  vendorLogo: () => {
    return imageUpload.single('logo', 'vendorLogo');
  },
  
  /**
   * Upload category image to Cloudinary
   * @returns {Function} Express middleware
   */
  categoryImage: () => {
    return imageUpload.single('image', 'categoryImage');
  },
  
  /**
   * Upload carousel image to Cloudinary
   * @returns {Function} Express middleware
   */
  carouselImage: () => {
    console.log('🔧 Creating carousel image uploader...');
    return imageUpload.single('image', 'carouselImage');
  },
  
  /**
   * Upload promotion image to Cloudinary
   * @returns {Function} Express middleware
   */
  promotionImage: () => {
    return imageUpload.single('image', 'promotionImage');
  },
  
  /**
   * Delete an uploaded image from Cloudinary
   * @param {string} imageUrl - Cloudinary URL or public ID
   * @returns {Promise<boolean>} Success status
   */
  deleteImage: async (imageUrl) => {
    try {
      // Check if Cloudinary is configured
      if (!validateCloudinaryConfig()) {
        console.warn('Cloudinary not configured, cannot delete image');
        return false;
      }
      
      // Extract public ID from URL if needed
      const publicId = extractPublicId(imageUrl) || imageUrl;
      
      if (!publicId) {
        console.error('Invalid image URL or public ID:', imageUrl);
        return false;
      }
      
      // Delete from Cloudinary
      const result = await deleteImage(publicId);
      return result;
    } catch (error) {
      console.error('Error deleting image:', error);
      return false;
    }
  },
  
  /**
   * Helper to extract public ID from Cloudinary URL
   */
  extractPublicId,
  
  /**
   * Helper to check if Cloudinary is configured
   */
  isCloudinaryConfigured: validateCloudinaryConfig
};

module.exports = imageUpload;