const { Vendor, Order, OrderTracking } = require('../../models');

/**
 * Get vendor's orders with pagination and filters - IMPROVED MULTI-VENDOR SUPPORT
 */
const getOrders = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const {
      page = 1,
      limit = 10,
      status,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    console.log('Getting orders for vendor user ID:', vendorId);

    // Get vendor details with proper validation
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found',
        code: 'VENDOR_NOT_FOUND'
      });
    }

    console.log('Found vendor:', {
      id: vendor._id,
      businessName: vendor.businessName,
      status: vendor.status
    });

    // Build filter query for orders containing this vendor's items
    const matchStage = {
      'items.vendor': vendor._id
    };

    if (status) {
      // Filter by individual item status for this vendor
      matchStage['items.status'] = status;
    }

    if (search) {
      matchStage.$or = [
        { orderNumber: { $regex: search, $options: 'i' } },
        { 'billing.firstName': { $regex: search, $options: 'i' } },
        { 'billing.lastName': { $regex: search, $options: 'i' } },
        { 'billing.email': { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Use aggregation pipeline to get orders containing this vendor's items
    const pipeline = [
      { $match: matchStage },
      {
        $addFields: {
          vendorItems: {
            $filter: {
              input: '$items',
              cond: { $eq: ['$$this.vendor', vendor._id] }
            }
          }
        }
      },
      {
        $lookup: {
          from: 'vendors',
          localField: 'items.vendor',
          foreignField: '_id',
          as: 'vendorInfo'
        }
      },
      {
        $addFields: {
          vendorItemsCount: { $size: '$vendorItems' },
          vendorTotal: {
            $sum: '$vendorItems.totalPrice'
          },
          vendorEarnings: {
            $sum: {
              $map: {
                input: '$vendorItems',
                in: {
                  $let: {
                    vars: {
                      vendorData: {
                        $arrayElemAt: [
                          {
                            $filter: {
                              input: '$vendorInfo',
                              cond: { $eq: ['$$this._id', vendor._id] }
                            }
                          },
                          0
                        ]
                      }
                    },
                    in: {
                      $cond: [
                        { $in: ['$$this.status', ['delivered']] },
                        {
                          $subtract: [
                            '$$this.totalPrice',
                            {
                              $multiply: [
                                '$$this.totalPrice',
                                { $divide: [{ $ifNull: ['$$vendorData.commission.rate', 15] }, 100] }
                              ]
                            }
                          ]
                        },
                        0
                      ]
                    }
                  }
                }
              }
            }
          }
        }
      },
      { $sort: sortOptions },
      { $skip: skip },
      { $limit: parseInt(limit) },
      {
        $lookup: {
          from: 'users',
          localField: 'customer',
          foreignField: '_id',
          as: 'customerInfo'
        }
      },
      {
        $unwind: '$customerInfo'
      },
      {
        $project: {
          orderNumber: 1,
          customer: {
            _id: '$customerInfo._id',
            firstName: '$customerInfo.firstName',
            lastName: '$customerInfo.lastName',
            email: '$customerInfo.email',
            phone: '$customerInfo.phone'
          },
          vendorItems: 1,
          vendorItemsCount: 1,
          vendorTotal: 1,
          status: 1,
          payment: 1,
          shipping: 1,
          createdAt: 1,
          updatedAt: 1,
          timeline: 1
        }
      }
    ];

    const [orders, totalCountResult] = await Promise.all([
      Order.aggregate(pipeline),
      Order.aggregate([
        { $match: matchStage },
        { $count: 'total' }
      ])
    ]);

    const totalOrders = totalCountResult.length > 0 ? totalCountResult[0].total : 0;

    const totalPages = Math.ceil(totalOrders / parseInt(limit));

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalOrders,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    console.error('Get vendor orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch orders',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get single order by ID - IMPROVED FOR MULTI-VENDOR
 */
const getOrder = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const { id } = req.params;

    console.log('Getting order:', id, 'for vendor user:', vendorId);

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found',
        code: 'VENDOR_NOT_FOUND'
      });
    }

    // Get order that contains this vendor's items
    const order = await Order.findOne({ 
      _id: id, 
      'items.vendor': vendor._id 
    })
      .populate('customer', 'firstName lastName email phone')
      .populate('items.product', 'name images pricing sku')
      .populate('items.vendor', 'businessName')
      .lean();

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found or you do not have access to this order',
        code: 'ORDER_NOT_FOUND'
      });
    }

    // Filter items to show only this vendor's items
    const vendorItems = order.items.filter(item => 
      item.vendor._id.toString() === vendor._id.toString()
    );

    // Calculate vendor-specific totals
    const vendorTotal = vendorItems.reduce((sum, item) => sum + item.totalPrice, 0);
    const vendorItemsCount = vendorItems.reduce((sum, item) => sum + item.quantity, 0);

    // Prepare response with vendor-specific data
    const vendorOrder = {
      ...order,
      items: vendorItems,
      vendorTotal,
      vendorItemsCount,
      totalItems: order.items.length,
      isMultiVendor: order.items.some(item => 
        item.vendor._id.toString() !== vendor._id.toString()
      )
    };

    console.log('Order found:', {
      orderNumber: order.orderNumber,
      vendorItemsCount,
      vendorTotal,
      isMultiVendor: vendorOrder.isMultiVendor
    });

    res.json({
      success: true,
      data: vendorOrder
    });

  } catch (error) {
    console.error('Get order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update order item status for vendor - IMPROVED MULTI-VENDOR SUPPORT
 */
const updateOrderStatus = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const { id } = req.params;
    const { status, note, itemIds } = req.body;

    console.log('Updating order status:', { orderId: id, status, note, itemIds });

    // Validate status
    const validStatuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid order status',
        validStatuses
      });
    }

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found',
        code: 'VENDOR_NOT_FOUND'
      });
    }

    // Find order containing this vendor's items
    const order = await Order.findOne({ 
      _id: id, 
      'items.vendor': vendor._id 
    });
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found or you do not have access to this order',
        code: 'ORDER_NOT_FOUND'
      });
    }

    console.log('Found order:', order.orderNumber, 'with', order.items.length, 'items');

    // Update status for vendor's items only
    let updatedItemsCount = 0;
    order.items.forEach((item, index) => {
      if (item.vendor.toString() === vendor._id.toString()) {
        // If specific itemIds provided, only update those items
        if (!itemIds || itemIds.includes(item._id.toString())) {
          console.log('Updating item:', item._id, 'from', item.status, 'to', status);
          order.items[index].status = status;
          updatedItemsCount++;
        }
      }
    });

    if (updatedItemsCount === 0) {
      return res.status(400).json({
        success: false,
        message: 'No items found to update for this vendor',
        code: 'NO_ITEMS_TO_UPDATE'
      });
    }

    // Add to timeline with vendor-specific note
    order.timeline.push({
      status: status,
      timestamp: new Date(),
      note: note || `${vendor.businessName} updated ${updatedItemsCount} item(s) to ${status}`,
      updatedBy: vendorId
    });

    // Update overall order status based on all items
    const allItemStatuses = order.items.map(item => item.status);
    const uniqueStatuses = [...new Set(allItemStatuses)];
    
    // Logic to determine overall order status
    if (uniqueStatuses.length === 1) {
      // All items have the same status
      order.status = uniqueStatuses[0];
    } else if (allItemStatuses.every(s => ['delivered', 'cancelled', 'returned'].includes(s))) {
      // All items are in final states
      order.status = 'delivered'; // Or use a "completed" status
    } else if (allItemStatuses.some(s => s === 'shipped')) {
      // Some items are shipped
      order.status = 'shipped';
    } else if (allItemStatuses.some(s => s === 'processing')) {
      // Some items are processing
      order.status = 'processing';
    } else {
      // Default to confirmed if items are mixed
      order.status = 'confirmed';
    }

    await order.save();

    // Update order tracking if it exists
    try {
      const tracking = await OrderTracking.findOne({ order: id });
      if (tracking) {
        // Map order status to tracking status
        const statusMapping = {
          'pending': 'order_confirmed',
          'confirmed': 'order_confirmed',
          'processing': 'processing',
          'shipped': 'shipped',
          'delivered': 'delivered',
          'cancelled': 'cancelled',
          'returned': 'returned'
        };

        const trackingStatus = statusMapping[order.status] || order.status;
        await tracking.updateStatus(trackingStatus, {
          title: `Order ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}`,
          description: note || `${vendor.businessName} updated ${updatedItemsCount} item(s) to ${status}`,
          updatedBy: { type: 'vendor', id: vendorId, name: vendor.businessName }
        });
      }
    } catch (trackingError) {
      console.error('Error updating order tracking:', trackingError);
      // Don't fail the main operation if tracking update fails
    }

    console.log('Order status updated successfully:', {
      orderNumber: order.orderNumber,
      updatedItemsCount,
      newOverallStatus: order.status
    });

    // Return order with only vendor's items
    const vendorItems = order.items.filter(item => 
      item.vendor.toString() === vendor._id.toString()
    );

    const responseOrder = {
      ...order.toObject(),
      items: vendorItems,
      updatedItemsCount
    };

    res.json({
      success: true,
      data: responseOrder,
      message: `Successfully updated ${updatedItemsCount} item(s) to ${status}`
    });

  } catch (error) {
    console.error('Update order status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update order status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update shipping information - IMPROVED FOR MULTI-VENDOR
 */
const updateShipping = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const { id } = req.params;
    const { trackingNumber, carrier, shippingDate } = req.body;

    console.log('Updating shipping for order:', id, 'by vendor:', vendorId);

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found',
        code: 'VENDOR_NOT_FOUND'
      });
    }

    // Find order containing this vendor's items
    const order = await Order.findOne({ 
      _id: id, 
      'items.vendor': vendor._id 
    });
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found or you do not have access to this order',
        code: 'ORDER_NOT_FOUND'
      });
    }

    // Update shipping information
    const updateData = {};
    if (trackingNumber) updateData['shipping.trackingNumber'] = trackingNumber;
    if (carrier) updateData['shipping.carrier'] = carrier;
    if (shippingDate) updateData['shipping.shippedAt'] = new Date(shippingDate);

    // Add shipping update to timeline
    order.timeline.push({
      status: 'shipped',
      timestamp: new Date(),
      note: `${vendor.businessName} updated shipping info${trackingNumber ? ` - Tracking: ${trackingNumber}` : ''}`,
      updatedBy: vendorId
    });

    const updatedOrder = await Order.findByIdAndUpdate(
      order._id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).populate('customer', 'firstName lastName email phone')
     .populate('items.product', 'name images pricing')
     .populate('items.vendor', 'businessName');

    console.log('Shipping information updated successfully:', {
      orderNumber: updatedOrder.orderNumber,
      trackingNumber,
      carrier
    });

    res.json({
      success: true,
      data: updatedOrder,
      message: 'Shipping information updated successfully'
    });

  } catch (error) {
    console.error('Update shipping error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update shipping information',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Create shipping tracking for vendor's items
 */
const createShippingTracking = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const { id } = req.params;
    const { carrier, estimatedDelivery, deliveryAddress, recipient } = req.body;

    console.log('Creating shipping tracking for order:', id);

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found',
        code: 'VENDOR_NOT_FOUND'
      });
    }

    // Find order containing this vendor's items
    const order = await Order.findOne({ 
      _id: id, 
      'items.vendor': vendor._id 
    });
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found or you do not have access to this order',
        code: 'ORDER_NOT_FOUND'
      });
    }

    // Check if tracking already exists for this vendor
    const existingTracking = await OrderTracking.findOne({ 
      order: order._id,
      vendor: vendor._id 
    });

    if (existingTracking) {
      return res.status(400).json({
        success: false,
        message: 'Tracking already exists for this order',
        code: 'TRACKING_EXISTS',
        data: { trackingNumber: existingTracking.trackingNumber }
      });
    }

    // Create tracking
    const trackingData = {
      orderId: order._id,
      vendorId: vendor._id,
      carrier: carrier || {
        name: 'Standard Delivery',
        code: 'STD'
      },
      estimatedDelivery: estimatedDelivery ? new Date(estimatedDelivery) : null,
      deliveryAddress: deliveryAddress || order.shipping.address,
      recipient: recipient || {
        name: `${order.billing.firstName} ${order.billing.lastName}`,
        phone: order.billing.phone,
        email: order.billing.email
      }
    };

    const tracking = await OrderTracking.createTracking(trackingData);

    // Update order status to shipped for vendor's items
    let updatedItemsCount = 0;
    order.items.forEach((item, index) => {
      if (item.vendor.toString() === vendor._id.toString()) {
        order.items[index].status = 'shipped';
        updatedItemsCount++;
      }
    });

    // Add to timeline
    order.timeline.push({
      status: 'shipped',
      timestamp: new Date(),
      note: `${vendor.businessName} created shipping tracking - ${tracking.trackingNumber}`,
      updatedBy: vendorId
    });

    // Update overall order status
    const allItemStatuses = order.items.map(item => item.status);
    if (allItemStatuses.some(s => s === 'shipped')) {
      order.status = 'shipped';
    }

    await order.save();

    console.log('Shipping tracking created successfully:', {
      orderNumber: order.orderNumber,
      trackingNumber: tracking.trackingNumber,
      updatedItemsCount
    });

    res.json({
      success: true,
      data: {
        tracking: {
          _id: tracking._id,
          trackingNumber: tracking.trackingNumber,
          carrier: tracking.carrier,
          currentStatus: tracking.currentStatus,
          estimatedDelivery: tracking.estimatedDelivery
        },
        updatedItemsCount,
        message: `Shipping tracking created for ${updatedItemsCount} item(s)`
      }
    });

  } catch (error) {
    console.error('Create shipping tracking error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create shipping tracking',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update order tracking status
 */
const updateTrackingStatus = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const { trackingId } = req.params;
    const { status, location, description, estimatedDelivery } = req.body;

    console.log('Updating tracking status:', { trackingId, status });

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found',
        code: 'VENDOR_NOT_FOUND'
      });
    }

    // Find tracking record
    const tracking = await OrderTracking.findOne({ 
      _id: trackingId,
      vendor: vendor._id 
    });

    if (!tracking) {
      return res.status(404).json({
        success: false,
        message: 'Tracking record not found or access denied',
        code: 'TRACKING_NOT_FOUND'
      });
    }

    // Validate status
    const validStatuses = ['order_confirmed', 'processing', 'shipped', 'out_for_delivery', 'delivered', 'cancelled', 'returned'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid tracking status',
        validStatuses
      });
    }

    // Update tracking status
    const updateData = {
      description,
      location,
      timestamp: new Date(),
      updatedBy: {
        vendor: vendor._id,
        user: vendorId,
        type: 'vendor'
      },
      metadata: {
        estimatedDelivery: estimatedDelivery ? new Date(estimatedDelivery) : undefined
      }
    };

    await tracking.updateStatus(status, updateData);

    // Update corresponding order items
    const order = await Order.findById(tracking.order);
    if (order) {
      const statusMapping = {
        'order_confirmed': 'confirmed',
        'processing': 'processing',
        'shipped': 'shipped',
        'out_for_delivery': 'shipped',
        'delivered': 'delivered',
        'cancelled': 'cancelled',
        'returned': 'returned'
      };

      const orderStatus = statusMapping[status];
      let updatedItemsCount = 0;

      order.items.forEach((item, index) => {
        if (item.vendor.toString() === vendor._id.toString()) {
          order.items[index].status = orderStatus;
          updatedItemsCount++;
        }
      });

      // Add to order timeline
      order.timeline.push({
        status: orderStatus,
        timestamp: new Date(),
        note: `${vendor.businessName} updated tracking: ${status}`,
        updatedBy: vendorId
      });

      // Update overall order status
      const allItemStatuses = order.items.map(item => item.status);
      const uniqueStatuses = [...new Set(allItemStatuses)];
      
      if (uniqueStatuses.length === 1) {
        order.status = uniqueStatuses[0];
      } else if (allItemStatuses.every(s => ['delivered', 'cancelled', 'returned'].includes(s))) {
        order.status = 'delivered';
      } else if (allItemStatuses.some(s => s === 'shipped')) {
        order.status = 'shipped';
      } else if (allItemStatuses.some(s => s === 'processing')) {
        order.status = 'processing';
      }

      await order.save();

      console.log('Tracking and order updated:', {
        trackingNumber: tracking.trackingNumber,
        newStatus: status,
        updatedItemsCount
      });
    }

    res.json({
      success: true,
      data: {
        tracking: {
          _id: tracking._id,
          trackingNumber: tracking.trackingNumber,
          currentStatus: tracking.currentStatus,
          progressPercentage: tracking.progressPercentage,
          latestUpdate: tracking.latestUpdate
        }
      },
      message: 'Tracking status updated successfully'
    });

  } catch (error) {
    console.error('Update tracking status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update tracking status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Process order fulfillment - Complete workflow
 */
const processOrderFulfillment = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const { id } = req.params;
    const { 
      action, // 'confirm', 'process', 'ship', 'deliver'
      items, // specific item IDs to process
      shippingInfo, // for ship action
      note 
    } = req.body;

    console.log('Processing order fulfillment:', { orderId: id, action, items });

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found',
        code: 'VENDOR_NOT_FOUND'
      });
    }

    // Find order
    const order = await Order.findOne({ 
      _id: id, 
      'items.vendor': vendor._id 
    });
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found or you do not have access to this order',
        code: 'ORDER_NOT_FOUND'
      });
    }

    const validActions = ['confirm', 'process', 'ship', 'deliver'];
    if (!validActions.includes(action)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid fulfillment action',
        validActions
      });
    }

    const statusMapping = {
      'confirm': 'confirmed',
      'process': 'processing',
      'ship': 'shipped',
      'deliver': 'delivered'
    };

    const newStatus = statusMapping[action];
    let updatedItemsCount = 0;
    let processedItems = [];

    // Update vendor's items
    order.items.forEach((item, index) => {
      if (item.vendor.toString() === vendor._id.toString()) {
        // If specific items provided, only update those
        if (!items || items.includes(item._id.toString())) {
          // Validate status transition
          const currentStatus = item.status;
          const validTransitions = {
            'pending': ['confirmed', 'cancelled'],
            'confirmed': ['processing', 'cancelled'],
            'processing': ['shipped', 'cancelled'],
            'shipped': ['delivered', 'returned'],
            'delivered': ['returned'],
            'cancelled': [],
            'returned': []
          };

          if (validTransitions[currentStatus].includes(newStatus)) {
            order.items[index].status = newStatus;
            updatedItemsCount++;
            processedItems.push({
              _id: item._id,
              name: item.name,
              oldStatus: currentStatus,
              newStatus: newStatus
            });
          }
        }
      }
    });

    if (updatedItemsCount === 0) {
      return res.status(400).json({
        success: false,
        message: 'No items could be processed or invalid status transition',
        code: 'NO_ITEMS_PROCESSED'
      });
    }

    // Handle shipping action
    if (action === 'ship' && shippingInfo) {
      // Update shipping information
      if (shippingInfo.trackingNumber) {
        order.shipping.trackingNumber = shippingInfo.trackingNumber;
      }
      if (shippingInfo.carrier) {
        order.shipping.carrier = shippingInfo.carrier;
      }
      if (shippingInfo.estimatedDelivery) {
        order.shipping.estimatedDelivery = new Date(shippingInfo.estimatedDelivery);
      }

      // Create tracking if not exists
      const existingTracking = await OrderTracking.findOne({ 
        order: order._id,
        vendor: vendor._id 
      });

      if (!existingTracking) {
        await OrderTracking.createTracking({
          orderId: order._id,
          vendorId: vendor._id,
          carrier: shippingInfo.carrier,
          estimatedDelivery: shippingInfo.estimatedDelivery,
          deliveryAddress: order.shipping.address,
          recipient: {
            name: `${order.billing.firstName} ${order.billing.lastName}`,
            phone: order.billing.phone,
            email: order.billing.email
          }
        });
      }
    }

    // Add to timeline
    order.timeline.push({
      status: newStatus,
      timestamp: new Date(),
      note: note || `${vendor.businessName} ${action}ed ${updatedItemsCount} item(s)`,
      updatedBy: vendorId
    });

    // Update overall order status
    const allItemStatuses = order.items.map(item => item.status);
    const uniqueStatuses = [...new Set(allItemStatuses)];
    
    if (uniqueStatuses.length === 1) {
      order.status = uniqueStatuses[0];
    } else if (allItemStatuses.every(s => ['delivered', 'cancelled', 'returned'].includes(s))) {
      order.status = 'delivered';
    } else if (allItemStatuses.some(s => s === 'shipped')) {
      order.status = 'shipped';
    } else if (allItemStatuses.some(s => s === 'processing')) {
      order.status = 'processing';
    } else {
      order.status = 'confirmed';
    }

    await order.save();

    console.log('Order fulfillment processed:', {
      orderNumber: order.orderNumber,
      action,
      updatedItemsCount,
      newOverallStatus: order.status
    });

    res.json({
      success: true,
      data: {
        order: {
          _id: order._id,
          orderNumber: order.orderNumber,
          status: order.status,
          updatedAt: order.updatedAt
        },
        processedItems,
        updatedItemsCount
      },
      message: `Successfully ${action}ed ${updatedItemsCount} item(s)`
    });

  } catch (error) {
    console.error('Process order fulfillment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process order fulfillment',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get vendor's order analytics and fulfillment stats
 */
const getOrderAnalytics = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const { period = '30d' } = req.query; // 7d, 30d, 90d, 1y

    console.log('Getting order analytics for vendor:', vendorId, 'period:', period);

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found',
        code: 'VENDOR_NOT_FOUND'
      });
    }

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    
    switch (period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    // Get orders containing vendor's items
    const analyticsData = await Order.aggregate([
      {
        $match: {
          'items.vendor': vendor._id,
          createdAt: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $addFields: {
          vendorItems: {
            $filter: {
              input: '$items',
              cond: { $eq: ['$$this.vendor', vendor._id] }
            }
          }
        }
      },
      {
        $addFields: {
          vendorTotal: { $sum: '$vendorItems.totalPrice' },
          vendorItemsCount: {
            $reduce: {
              input: '$vendorItems',
              initialValue: 0,
              in: { $add: ['$$value', '$$this.quantity'] }
            }
          }
        }
      },
      {
        $group: {
          _id: null,
          totalOrders: { $sum: 1 },
          totalRevenue: { $sum: '$vendorTotal' },
          totalItems: { $sum: '$vendorItemsCount' },
          averageOrderValue: { $avg: '$vendorTotal' },
          pendingOrders: {
            $sum: {
              $cond: [
                { $anyElementTrue: {
                  $map: {
                    input: '$vendorItems',
                    in: { $eq: ['$$this.status', 'pending'] }
                  }
                }}, 1, 0
              ]
            }
          },
          processingOrders: {
            $sum: {
              $cond: [
                { $anyElementTrue: {
                  $map: {
                    input: '$vendorItems',
                    in: { $eq: ['$$this.status', 'processing'] }
                  }
                }}, 1, 0
              ]
            }
          },
          shippedOrders: {
            $sum: {
              $cond: [
                { $anyElementTrue: {
                  $map: {
                    input: '$vendorItems',
                    in: { $eq: ['$$this.status', 'shipped'] }
                  }
                }}, 1, 0
              ]
            }
          },
          deliveredOrders: {
            $sum: {
              $cond: [
                { $anyElementTrue: {
                  $map: {
                    input: '$vendorItems',
                    in: { $eq: ['$$this.status', 'delivered'] }
                  }
                }}, 1, 0
              ]
            }
          }
        }
      }
    ]);

    const stats = analyticsData[0] || {
      totalOrders: 0,
      totalRevenue: 0,
      totalItems: 0,
      averageOrderValue: 0,
      pendingOrders: 0,
      processingOrders: 0,
      shippedOrders: 0,
      deliveredOrders: 0
    };

    // Get daily revenue trend
    const revenueTrend = await Order.aggregate([
      {
        $match: {
          'items.vendor': vendor._id,
          createdAt: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $addFields: {
          vendorTotal: {
            $sum: {
              $map: {
                input: {
                  $filter: {
                    input: '$items',
                    cond: { $eq: ['$$this.vendor', vendor._id] }
                  }
                },
                in: '$$this.totalPrice'
              }
            }
          }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$createdAt'
            }
          },
          revenue: { $sum: '$vendorTotal' },
          orders: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    res.json({
      success: true,
      data: {
        period,
        dateRange: { startDate, endDate },
        stats,
        revenueTrend
      }
    });

  } catch (error) {
    console.error('Get order analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch order analytics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getOrders,
  getOrder,
  updateOrderStatus,
  updateShipping,
  createShippingTracking,
  updateTrackingStatus,
  processOrderFulfillment,
  getOrderAnalytics
};
