import React, { createContext, useContext, useReducer, useEffect, useRef, useCallback } from 'react';
import { cartApi } from '../services/cartApi';
import { notification } from 'antd';
import { debugAuthState, validateCartAccess, fixAuthIssues } from '../utils/authDebug';
import {
  ShoppingCartOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  DeleteOutlined,
  WarningOutlined,
  SyncOutlined
} from '@ant-design/icons';

// Cart context
const CartContext = createContext();

// Cart action types
const CART_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_CART: 'SET_CART',
  SET_SUMMARY: 'SET_SUMMARY',
  ADD_ITEM: 'ADD_ITEM',
  UPDATE_ITEM: 'UPDATE_ITEM',
  REMOVE_ITEM: 'REMOVE_ITEM',
  CLEAR_CART: 'CLEAR_CART',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR'
};

// Initial state
const initialState = {
  cart: null,
  summary: {
    totalItems: 0,
    totalAmount: 0,
    vendorCount: 0,
    itemCount: 0
  },
  loading: false,
  error: null
};

// Cart reducer
const cartReducer = (state, action) => {
  switch (action.type) {
    case CART_ACTIONS.SET_LOADING:
      return {
        ...state,
        loading: action.payload
      };

    case CART_ACTIONS.SET_CART:
      return {
        ...state,
        cart: action.payload,
        loading: false,
        error: null
      };

    case CART_ACTIONS.SET_SUMMARY:
      return {
        ...state,
        summary: action.payload,
        loading: false,
        error: null
      };

    case CART_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false
      };

    case CART_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null
      };

    case CART_ACTIONS.CLEAR_CART:
      return {
        ...state,
        cart: { ...state.cart, items: [] },
        summary: {
          totalItems: 0,
          totalAmount: 0,
          vendorCount: 0,
          itemCount: 0
        },
        loading: false,
        error: null
      };

    default:
      return state;
  }
};

// Cart provider component
export const CartProvider = ({ children }) => {
  const [state, dispatch] = useReducer(cartReducer, initialState);

  // Action creators
  const setLoading = (loading) => {
    dispatch({ type: CART_ACTIONS.SET_LOADING, payload: loading });
  };

  const setError = (error) => {
    dispatch({ type: CART_ACTIONS.SET_ERROR, payload: error });
  };

  const clearError = () => {
    dispatch({ type: CART_ACTIONS.CLEAR_ERROR });
  };

  // Refs for optimization features
  const debounceTimers = useRef({});
  const retryAttempts = useRef({});
  
  // Retry mechanism with exponential backoff
  const retryOperation = async (operation, maxRetries = 3, baseDelay = 1000) => {
    const operationKey = operation.name + JSON.stringify(operation.args || []);
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = await operation();
        // Clear retry count on success
        delete retryAttempts.current[operationKey];
        return result;
      } catch (error) {
        console.error(`Attempt ${attempt} failed:`, error.message);
        
        if (attempt === maxRetries) {
          retryAttempts.current[operationKey] = attempt;
          throw error;
        }
        
        // Exponential backoff: wait longer with each retry
        const delay = baseDelay * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // Show retry notification
        if (attempt === 1) {
          notification.info({
            message: 'Connection Issue',
            description: 'Retrying your request...',
            placement: 'topRight',
            duration: 2,
          });
        }
      }
    }
  };

  // Optimistic update helper
  

  // Fetch cart data
  const fetchCart = useCallback(async () => {
    try {
      setLoading(true);
      const response = await cartApi.getCart();
      if (response.success) {
        dispatch({ type: CART_ACTIONS.SET_CART, payload: response.data });
      }
    } catch (error) {
      setError(error.message);
    }
  }, []);

  // Fetch cart summary
  const fetchCartSummary = useCallback(async () => {
    try {
      // Validate cart access before making API call
      const validation = validateCartAccess();
      
      if (!validation.canAccess) {
        console.log('Cart access validation failed:', validation.reason);
        
        // Try to fix auth issues
        const fixResult = fixAuthIssues();
        if (!fixResult.success) {
          console.log('Auth fix result:', fixResult);
          
          // Show user-friendly notification for auth issues
          if (fixResult.action === 'show_user_type_error') {
            notification.warning({
              message: 'Account Type Mismatch',
              description: 'Cart functionality is only available for customer accounts. Please log in with a customer account.',
              placement: 'topRight',
              duration: 6,
            });
          }
        }
        return;
      }
      
      const response = await cartApi.getCartSummary();
      if (response.success) {
        dispatch({ type: CART_ACTIONS.SET_SUMMARY, payload: response.data });
      }
    } catch (error) {
      console.error('Error fetching cart summary:', error);
      
      // Handle specific authentication errors
      if (error.message.includes('Access denied') || error.message.includes('permissions')) {
        console.error('Authentication/permission error in cart summary.');
        
        // Debug the auth state
        debugAuthState();
        
        // Try to fix the issue
        const fixResult = fixAuthIssues();
        if (fixResult.action === 'show_user_type_error') {
          notification.error({
            message: 'Access Denied',
            description: 'Cart functionality is only available for customer accounts. Please log in with a customer account.',
            placement: 'topRight',
            duration: 6,
          });
        }
      }
      
      // Don't set error for summary as it's not critical
    }
  }, []);

  // Notification helpers
  const showSuccessNotification = (message, description = '') => {
    notification.success({
      message,
      description,
      placement: 'topRight',
      duration: 3,
    });
  };

  const showErrorNotification = (message, description = '') => {
    notification.error({
      message,
      description,
      placement: 'topRight',
      duration: 4,
    });
  };

  const showWarningNotification = (message, description = '') => {
    notification.warning({
      message,
      description,
      placement: 'topRight',
      duration: 4,
    });
  };

  // Check stock levels and show warnings
  const checkStockLevels = (cartData) => {
    if (!cartData || !cartData.items) return;
    
    cartData.items.forEach(item => {
      const product = item.product;
      const stock = product?.inventory?.quantity || 0;
      const lowStockThreshold = product?.inventory?.lowStockThreshold || 5;
      
      if (stock <= lowStockThreshold && stock > 0) {
        showWarningNotification(
          'Low Stock Warning',
          `Only ${stock} units left for ${product?.name || 'this item'}`
        );
      } else if (stock === 0) {
        showErrorNotification(
          'Out of Stock',
          `${product?.name || 'This item'} is now out of stock and will be removed from your cart`
        );
      }
    });
  };

  // Check if user is vendor or admin
  const isVendor = () => {
    const user = JSON.parse(localStorage.getItem('authUser') || '{}');
    return user.userType === 'vendor';
  };

  const isAdmin = () => {
    const user = JSON.parse(localStorage.getItem('authUser') || '{}');
    return user.userType === 'admin';
  };

  const isNonCustomer = () => {
    const user = JSON.parse(localStorage.getItem('authUser') || '{}');
    return user.userType !== 'customer';
  };

  // Add item to cart
  const addToCart = async (productId, quantity = 1, variantSku = null) => {
    try {
      setLoading(true);
      clearError();
      
      // Check if user is non-customer (vendor or admin) before making API call
      if (isNonCustomer()) {
        const user = JSON.parse(localStorage.getItem('authUser') || '{}');
        const userType = user.userType || 'unknown';
        const userTypeCapitalized = userType.charAt(0).toUpperCase() + userType.slice(1);
        
        notification.warning({
          message: 'Feature Not Available',
          description: `Cart functionality is only available for customers. ${userTypeCapitalized}s cannot add items to cart.`,
          icon: <WarningOutlined style={{ color: '#faad14' }} />,
          placement: 'topRight',
          duration: 5,
          style: {
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          },
        });
        setLoading(false);
        return Promise.reject(new Error(`Cart functionality not available for ${userType}s`));
      }
      
      // Validate inputs
      if (!productId) {
        throw new Error('Product ID is required');
      }
      
      if (quantity <= 0) {
        throw new Error('Quantity must be greater than 0');
      }
      
      const response = await cartApi.addToCart(productId, quantity, variantSku);
      if (response.success) {
        dispatch({ type: CART_ACTIONS.SET_CART, payload: response.data });
        persistCartToStorage(response.data);
        
        // Update summary from cart data to avoid extra API call
        if (response.data) {
          dispatch({ 
            type: CART_ACTIONS.SET_SUMMARY, 
            payload: {
              totalItems: response.data.totalItems || 0,
              totalAmount: response.data.totalAmount || 0,
              vendorCount: response.data.vendorCount || 0,
              itemCount: response.data.items?.length || 0
            }
          });
        }
        
        // Show success notification with icon
        notification.success({
          message: 'Added to Cart',
          description: (
            <div>
              <p>{`${quantity} item${quantity > 1 ? 's' : ''} added to your cart`}</p>
              <p style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>View cart to proceed to checkout</p>
            </div>
          ),
          icon: <ShoppingCartOutlined style={{ color: '#52c41a' }} />,
          placement: 'topRight',
          duration: 4,
          style: {
            width: 320,
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          },
        });
        
        // Check stock levels
        checkStockLevels(response.data);
        
        return response;
      } else {
        throw new Error(response.message || 'Failed to add item to cart');
      }
    } catch (error) {
      setError(error.message);
      
      // Show contextual error notifications
      let errorTitle = 'Failed to Add Item';
      let errorDescription = error.message;
      
      if (error.message.includes('log in')) {
        errorTitle = 'Login Required';
        errorDescription = 'Please log in to add items to your cart';
      } else if (error.message.includes('stock')) {
        errorTitle = 'Stock Unavailable';
        errorDescription = error.message;
      } else if (error.message.includes('not found')) {
        errorTitle = 'Product Unavailable';
        errorDescription = 'This product is no longer available';
      }
      
      notification.error({
        message: errorTitle,
        description: errorDescription,
        icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
        placement: 'topRight',
        duration: 5,
        style: {
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        },
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Update cart item quantity
  const updateCartItem = async (productId, quantity, variantSku = null) => {
    try {
      setLoading(true);
      clearError();
      
      const response = await cartApi.updateCartItem(productId, quantity, variantSku);
      if (response.success) {
        dispatch({ type: CART_ACTIONS.SET_CART, payload: response.data });
        persistCartToStorage(response.data);
        
        // Update summary from cart data to avoid extra API call
        if (response.data) {
          dispatch({ 
            type: CART_ACTIONS.SET_SUMMARY, 
            payload: {
              totalItems: response.data.totalItems || 0,
              totalAmount: response.data.totalAmount || 0,
              vendorCount: response.data.vendorCount || 0,
              itemCount: response.data.items?.length || 0
            }
          });
        }
        
        // Show appropriate notification with icons
        if (quantity > 0) {
          notification.success({
            message: 'Cart Updated',
            description: 'Item quantity updated successfully',
            icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
            placement: 'topRight',
            duration: 3,
            style: {
              borderRadius: '8px',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            },
          });
        } else {
          notification.success({
            message: 'Item Removed',
            description: 'Item removed from your cart',
            icon: <DeleteOutlined style={{ color: '#52c41a' }} />,
            placement: 'topRight',
            duration: 3,
            style: {
              borderRadius: '8px',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            },
          });
        }
        
        // Check stock levels
        checkStockLevels(response.data);
        
        return response;
      }
    } catch (error) {
      setError(error.message);
      showErrorNotification('Failed to Update Cart', error.message);
      throw error;
    }
  };

  // Remove item from cart
  const removeFromCart = async (productId, variantSku = null) => {
    try {
      setLoading(true);
      clearError();
      
      const response = await cartApi.removeFromCart(productId, variantSku);
      if (response.success) {
        dispatch({ type: CART_ACTIONS.SET_CART, payload: response.data });
        persistCartToStorage(response.data);
        
        // Update summary from cart data to avoid extra API call
        if (response.data) {
          dispatch({ 
            type: CART_ACTIONS.SET_SUMMARY, 
            payload: {
              totalItems: response.data.totalItems || 0,
              totalAmount: response.data.totalAmount || 0,
              vendorCount: response.data.vendorCount || 0,
              itemCount: response.data.items?.length || 0
            }
          });
        }
        
        // Show success notification
        showSuccessNotification('Item Removed', 'Item removed from your cart successfully');
        
        return response;
      }
    } catch (error) {
      setError(error.message);
      showErrorNotification('Failed to Remove Item', error.message);
      throw error;
    }
  };

  // Clear entire cart
  const clearCart = async () => {
    try {
      setLoading(true);
      clearError();
      
      const response = await cartApi.clearCart();
      if (response.success) {
        dispatch({ type: CART_ACTIONS.CLEAR_CART });
        clearPersistedCart();
        
        // Show success notification
        showSuccessNotification('Cart Cleared', 'All items removed from your cart');
        
        return response;
      }
    } catch (error) {
      setError(error.message);
      showErrorNotification('Failed to Clear Cart', error.message);
      throw error;
    }
  };

  // Persist cart state to localStorage
  const persistCartToStorage = (cartData) => {
    try {
      if (cartData && cartData.items) {
        const cartToStore = {
          items: cartData.items,
          totalItems: cartData.totalItems,
          totalAmount: cartData.totalAmount,
          lastUpdated: cartData.lastUpdated || new Date().toISOString()
        };
        localStorage.setItem('cartBackup', JSON.stringify(cartToStore));
      }
    } catch (error) {
      console.error('Error persisting cart to storage:', error);
    }
  };

  // Load persisted cart from localStorage
  const loadPersistedCart = () => {
    try {
      const storedCart = localStorage.getItem('cartBackup');
      if (storedCart) {
        const parsedCart = JSON.parse(storedCart);
        // Only use persisted cart if it's recent (within 7 days)
        const lastUpdated = new Date(parsedCart.lastUpdated);
        const daysDiff = (new Date() - lastUpdated) / (1000 * 60 * 60 * 24);
        
        if (daysDiff <= 7) {
          return parsedCart;
        } else {
          // Remove old cart data
          localStorage.removeItem('cartBackup');
        }
      }
    } catch (error) {
      console.error('Error loading persisted cart:', error);
      localStorage.removeItem('cartBackup');
    }
    return null;
  };

  // Clear persisted cart
  const clearPersistedCart = () => {
    try {
      localStorage.removeItem('cartBackup');
    } catch (error) {
      console.error('Error clearing persisted cart:', error);
    }
  };

  // Load cart summary on mount (for header cart count)
  useEffect(() => {
    const token = localStorage.getItem('authToken');
    if (token) {
      fetchCartSummary();
      
      // Try to restore from persisted data if summary shows empty cart
      const persistedCart = loadPersistedCart();
      if (persistedCart && persistedCart.totalItems > 0) {
        // Set persisted data temporarily while fetching real data
        dispatch({ 
          type: CART_ACTIONS.SET_SUMMARY, 
          payload: {
            totalItems: persistedCart.totalItems,
            totalAmount: persistedCart.totalAmount,
            vendorCount: 0,
            itemCount: persistedCart.items?.length || 0
          }
        });
      }
    }
  }, []); // Keep empty dependency array since we only want this to run once on mount

  const value = {
    // State
    cart: state.cart,
    summary: state.summary,
    loading: state.loading,
    error: state.error,
    
    // Actions
    fetchCart,
    fetchCartSummary,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    clearError
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};

// Custom hook to use cart context
export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

export default CartContext;
