const express = require('express');
const router = express.Router();
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');
const {
  getDashboardStats,
  getRealTimeMetrics,
  getAnalytics,
  getSystemHealth
} = require('../../controllers/admin/dashboardController');

// Apply authentication middleware to all routes
router.use(verifyToken);
router.use(requireUserType(['admin']));

// Dashboard routes
router.get('/stats', getDashboardStats);
router.get('/realtime', getRealTimeMetrics);
router.get('/analytics', getAnalytics);
router.get('/health', getSystemHealth);

module.exports = router;