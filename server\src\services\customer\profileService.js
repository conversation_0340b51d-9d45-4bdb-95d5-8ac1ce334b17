const User = require('../../models/User');
const imageUpload = require('../../middleware/upload/imageUpload');

/**
 * Profile service for handling user profile operations
 */
class ProfileService {
  /**
   * Get user profile by ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} User profile data
   */
  async getUserProfile(userId) {
    try {
      const user = await User.findById(userId).select('-password');
      
      if (!user) {
        throw new Error('User not found');
      }
      
      return user;
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Update user profile
   * @param {string} userId - User ID
   * @param {Object} updateData - Data to update
   * @param {File} avatarFile - Avatar file (optional)
   * @returns {Promise<Object>} Updated user profile
   */
  async updateProfile(userId, updateData, avatarFile = null) {
    try {
      // Get current user
      const user = await User.findById(userId);
      
      if (!user) {
        throw new Error('User not found');
      }
      
      // Handle avatar upload if provided
      if (avatarFile) {
        // If user already has an avatar, delete the old one
        if (user.avatar) {
          await imageUpload.deleteImage(user.avatar);
        }
        
        // Process and save the new avatar
        // Note: This assumes avatarFile has already been processed by multer
        // and has a path property
        updateData.avatar = avatarFile.path.replace(/\\/g, '/').split('uploads')[1];
        if (!updateData.avatar.startsWith('/')) {
          updateData.avatar = '/uploads' + updateData.avatar;
        }
      }
      
      // Update user in database
      const updatedUser = await User.findByIdAndUpdate(
        userId,
        { $set: updateData },
        { new: true, runValidators: true }
      ).select('-password');
      
      return updatedUser;
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Update user profile picture
   * @param {string} userId - User ID
   * @param {string} avatarUrl - URL of the uploaded avatar
   * @returns {Promise<Object>} Updated user profile
   */
  async updateProfilePicture(userId, avatarUrl) {
    try {
      // Get current user
      const user = await User.findById(userId);
      
      if (!user) {
        throw new Error('User not found');
      }
      
      // If user already has an avatar, delete the old one
      if (user.avatar) {
        await imageUpload.deleteImage(user.avatar);
      }
      
      // Update user with new avatar URL
      const updatedUser = await User.findByIdAndUpdate(
        userId,
        { $set: { avatar: avatarUrl } },
        { new: true }
      ).select('-password');
      
      return updatedUser;
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Delete user profile picture
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteProfilePicture(userId) {
    try {
      // Get current user
      const user = await User.findById(userId);
      
      if (!user) {
        throw new Error('User not found');
      }
      
      // Check if user has an avatar
      if (!user.avatar) {
        return false;
      }
      
      // Delete the avatar file
      const deleteResult = await imageUpload.deleteImage(user.avatar);
      
      if (!deleteResult) {
        return false;
      }
      
      // Update user to remove avatar URL
      await User.findByIdAndUpdate(
        userId,
        { $set: { avatar: null } }
      );
      
      return true;
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Add a new address to user profile
   * @param {string} userId - User ID
   * @param {Object} addressData - Address data
   * @returns {Promise<Array>} Updated addresses array
   */
  async addAddress(userId, addressData) {
    try {
      // Get current user
      const user = await User.findById(userId);
      
      if (!user) {
        throw new Error('User not found');
      }
      
      // Create new address object
      const newAddress = {
        type: addressData.type || 'home',
        street: addressData.street,
        city: addressData.city,
        state: addressData.state,
        zipCode: addressData.zipCode,
        country: addressData.country,
        isDefault: addressData.isDefault || false
      };
      
      // If new address is default, update existing addresses
      if (newAddress.isDefault) {
        await User.updateOne(
          { _id: userId, 'addresses.isDefault': true },
          { $set: { 'addresses.$.isDefault': false } }
        );
      }
      
      // Add new address to user
      const updatedUser = await User.findByIdAndUpdate(
        userId,
        { $push: { addresses: newAddress } },
        { new: true }
      ).select('-password');
      
      return updatedUser.addresses;
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Update an existing address
   * @param {string} userId - User ID
   * @param {string} addressId - Address ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Array>} Updated addresses array
   */
  async updateAddress(userId, addressId, updateData) {
    try {
      // Get current user
      const user = await User.findById(userId);
      
      if (!user) {
        throw new Error('User not found');
      }
      
      // Check if address exists
      const addressExists = user.addresses.some(addr => addr._id.toString() === addressId);
      if (!addressExists) {
        throw new Error('Address not found');
      }
      
      // Create update object
      const updateFields = {};
      ['type', 'street', 'city', 'state', 'zipCode', 'country'].forEach(field => {
        if (updateData[field] !== undefined) {
          updateFields[`addresses.$.${field}`] = updateData[field];
        }
      });
      
      // Handle isDefault separately
      if (updateData.isDefault === true) {
        // First, set all addresses to non-default
        await User.updateOne(
          { _id: userId, 'addresses.isDefault': true },
          { $set: { 'addresses.$.isDefault': false } }
        );
        
        // Then set this address as default
        updateFields['addresses.$.isDefault'] = true;
      }
      
      // Update the address
      await User.updateOne(
        { _id: userId, 'addresses._id': addressId },
        { $set: updateFields }
      );
      
      // Get updated user
      const updatedUser = await User.findById(userId).select('-password');
      
      return updatedUser.addresses;
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Delete an address
   * @param {string} userId - User ID
   * @param {string} addressId - Address ID
   * @returns {Promise<Array>} Updated addresses array
   */
  async deleteAddress(userId, addressId) {
    try {
      // Get current user
      const user = await User.findById(userId);
      
      if (!user) {
        throw new Error('User not found');
      }
      
      // Check if address exists
      const address = user.addresses.find(addr => addr._id.toString() === addressId);
      if (!address) {
        throw new Error('Address not found');
      }
      
      // Remove address from user
      await User.updateOne(
        { _id: userId },
        { $pull: { addresses: { _id: addressId } } }
      );
      
      // If deleted address was default and there are other addresses, set a new default
      if (address.isDefault && user.addresses.length > 1) {
        const remainingAddresses = user.addresses.filter(addr => addr._id.toString() !== addressId);
        await User.updateOne(
          { _id: userId, 'addresses._id': remainingAddresses[0]._id },
          { $set: { 'addresses.$.isDefault': true } }
        );
      }
      
      // Get updated user
      const updatedUser = await User.findById(userId).select('-password');
      
      return updatedUser.addresses;
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Update user preferences
   * @param {string} userId - User ID
   * @param {Object} preferences - User preferences
   * @returns {Promise<Object>} Updated preferences
   */
  async updatePreferences(userId, preferences) {
    try {
      // Get current user
      const user = await User.findById(userId);
      
      if (!user) {
        throw new Error('User not found');
      }
      
      // Update user preferences
      const updatedUser = await User.findByIdAndUpdate(
        userId,
        { $set: { preferences } },
        { new: true }
      ).select('-password');
      
      return updatedUser.preferences;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new ProfileService();