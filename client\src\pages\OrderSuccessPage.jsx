import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { CheckCircleOutlined, ShoppingOutlined, EyeOutlined } from '@ant-design/icons';
import { But<PERSON>, Card, Result, Spin } from 'antd';
import { getApiUrl, getAuthHeaders } from '../config/api';

const OrderSuccessPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [orderDetails, setOrderDetails] = useState(null);
  const [loading, setLoading] = useState(true);
  const orderId = searchParams.get('orderId');

  useEffect(() => {
    if (orderId) {
      fetchOrderDetails();
    } else {
      setLoading(false);
    }
  }, [orderId]);

  const fetchOrderDetails = async () => {
    try {
      const response = await fetch(getApiUrl(`payments/status/${orderId}`), {
        headers: getAuthHeaders()
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setOrderDetails(result.data);
        }
      }
    } catch (error) {
      console.error('Error fetching order details:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  if (!orderId) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Result
          status="warning"
          title="Invalid Order"
          subTitle="No order ID provided. Please check your payment confirmation."
          extra={[
            <Button type="primary" key="home" onClick={() => navigate('/')}>
              Go to Home
            </Button>
          ]}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto">
          <Result
            icon={<CheckCircleOutlined style={{ color: '#52c41a', fontSize: '72px' }} />}
            title="Payment Successful!"
            subTitle="Your order has been placed successfully and is being processed."
            extra={[
              <div key="order-info" className="mb-6">
                {orderDetails && (
                  <Card className="text-left">
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="font-medium">Order Number:</span>
                        <span className="text-blue-600 font-mono">{orderDetails.orderNumber}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">Payment Status:</span>
                        <span className="text-green-600 capitalize">{orderDetails.paymentStatus}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">Order Status:</span>
                        <span className="text-blue-600 capitalize">{orderDetails.orderStatus}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">Total Amount:</span>
                        <span className="text-lg font-semibold">₹{orderDetails.total}</span>
                      </div>
                    </div>
                  </Card>
                )}
              </div>,
              <div key="actions" className="space-x-4">
                <Button 
                  type="primary" 
                  icon={<EyeOutlined />}
                  onClick={() => navigate('/orders')}
                  size="large"
                >
                  View Orders
                </Button>
                <Button 
                  icon={<ShoppingOutlined />}
                  onClick={() => navigate('/')}
                  size="large"
                >
                  Continue Shopping
                </Button>
              </div>
            ]}
          />
          
          <div className="mt-8 bg-white rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold mb-4">What happens next?</h3>
            <div className="space-y-3 text-gray-600">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 text-sm font-semibold">1</span>
                </div>
                <div>
                  <p className="font-medium text-gray-900">Order Confirmation</p>
                  <p className="text-sm">You'll receive an email confirmation shortly with your order details.</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 text-sm font-semibold">2</span>
                </div>
                <div>
                  <p className="font-medium text-gray-900">Processing</p>
                  <p className="text-sm">Your order is being prepared by our vendors.</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 text-sm font-semibold">3</span>
                </div>
                <div>
                  <p className="font-medium text-gray-900">Shipping</p>
                  <p className="text-sm">Once shipped, you'll receive tracking information via email and SMS.</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 text-sm font-semibold">4</span>
                </div>
                <div>
                  <p className="font-medium text-gray-900">Delivery</p>
                  <p className="text-sm">Your order will be delivered to your specified address.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderSuccessPage;
