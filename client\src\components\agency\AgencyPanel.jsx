import React, { useState } from 'react';
import AgencyLayout from './AgencyLayout';
import VendorsSection from './sections/VendorsSection';
import OrdersSection from './sections/OrdersSection';
import ProductsSection from './sections/ProductsSection';

const AgencyPanel = () => {
  const [activeSection, setActiveSection] = useState('vendors');

  const renderContent = () => {
    switch (activeSection) {
      case 'vendors':
        return <VendorsSection />;
      case 'orders':
        return <OrdersSection />;
      case 'products':
        return <ProductsSection />;
      default:
        return <VendorsSection />;
    }
  };

  return (
    <AgencyLayout 
      activeKey={activeSection} 
      onMenuSelect={setActiveSection}
    >
      {renderContent()}
    </AgencyLayout>
  );
};

export default AgencyPanel;
