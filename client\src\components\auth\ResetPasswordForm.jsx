import React, { useState, useEffect } from 'react';
import { Form, message, Result } from 'antd';
import { LockOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { useSearchParams, useNavigate } from 'react-router-dom';
import AuthInput from '../ui/AuthInput';
import AuthButton from '../ui/AuthButton';
import { authAPI } from '../../utils/authApi';

const ResetPasswordForm = () => {
    const [form] = Form.useForm();
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const [loading, setLoading] = useState(false);
    const [success, setSuccess] = useState(false);
    const [error, setError] = useState(null);

    const token = searchParams.get('token');
    const email = searchParams.get('email');

    useEffect(() => {
        if (!token || !email) {
            setError('Invalid reset link. Please request a new password reset.');
        }
    }, [token, email]);

    const handleSubmit = async (values) => {
        if (!token || !email) {
            message.error('Invalid reset link');
            return;
        }

        setLoading(true);
        try {
            const response = await authAPI.resetPassword({
                token,
                email,
                newPassword: values.password
            });

            if (response.success) {
                setSuccess(true);
                message.success('Password reset successfully!');
            } else {
                message.error(response.message || 'Password reset failed');
            }
        } catch (error) {
            console.error('Password reset error:', error);
            const errorMessage = error.response?.data?.message || 'Password reset failed';
            message.error(errorMessage);
            
            if (errorMessage.includes('expired') || errorMessage.includes('invalid')) {
                setError('Reset link has expired or is invalid. Please request a new password reset.');
            }
        } finally {
            setLoading(false);
        }
    };

    const validatePassword = (_, value) => {
        if (!value) {
            return Promise.reject(new Error('Please enter your password'));
        }
        
        if (value.length < 8) {
            return Promise.reject(new Error('Password must be at least 8 characters long'));
        }
        
        if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
            return Promise.reject(new Error('Password must contain uppercase, lowercase and number'));
        }
        
        return Promise.resolve();
    };

    const validateConfirmPassword = (_, value) => {
        if (!value) {
            return Promise.reject(new Error('Please confirm your password'));
        }
        
        if (value !== form.getFieldValue('password')) {
            return Promise.reject(new Error('Passwords do not match'));
        }
        
        return Promise.resolve();
    };

    if (success) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="max-w-md w-full mx-auto p-8 bg-white rounded-lg shadow-lg">
                    <Result
                        icon={<CheckCircleOutlined className="text-green-500" />}
                        title="Password Reset Successfully!"
                        subTitle="Your password has been reset. You can now login with your new password."
                        extra={[
                            <AuthButton
                                key="login"
                                variant="primary"
                                onClick={() => navigate('/auth')}
                                block
                            >
                                Go to Login
                            </AuthButton>
                        ]}
                    />
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="max-w-md w-full mx-auto p-8 bg-white rounded-lg shadow-lg">
                    <Result
                        status="error"
                        title="Invalid Reset Link"
                        subTitle={error}
                        extra={[
                            <AuthButton
                                key="forgot"
                                variant="primary"
                                onClick={() => navigate('/forgot-password')}
                                block
                            >
                                Request New Reset Link
                            </AuthButton>,
                            <AuthButton
                                key="login"
                                variant="ghost"
                                onClick={() => navigate('/auth')}
                                block
                                className="mt-2"
                            >
                                Back to Login
                            </AuthButton>
                        ]}
                    />
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="max-w-md w-full mx-auto p-8 bg-white rounded-lg shadow-lg">
                <div className="text-center mb-8">
                    <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <LockOutlined className="text-red-500 text-2xl" />
                    </div>
                    <h2 className="text-3xl font-bold text-gray-800 mb-2">
                        Reset Your Password
                    </h2>
                    <p className="text-gray-600">
                        Enter your new password below
                    </p>
                    {email && (
                        <p className="text-sm text-gray-500 mt-2">
                            Resetting password for: <strong>{email}</strong>
                        </p>
                    )}
                </div>

                <Form
                    form={form}
                    onFinish={handleSubmit}
                    layout="vertical"
                    requiredMark={false}
                    className="space-y-4"
                >
                    <Form.Item
                        name="password"
                        rules={[{ validator: validatePassword }]}
                    >
                        <AuthInput
                            type="password"
                            placeholder="Enter new password"
                            icon={<LockOutlined className="text-gray-400" />}
                        />
                    </Form.Item>

                    <Form.Item
                        name="confirmPassword"
                        dependencies={['password']}
                        rules={[{ validator: validateConfirmPassword }]}
                    >
                        <AuthInput
                            type="password"
                            placeholder="Confirm new password"
                            icon={<LockOutlined className="text-gray-400" />}
                        />
                    </Form.Item>

                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <h4 className="text-sm font-medium text-blue-800 mb-2">Password Requirements:</h4>
                        <ul className="text-xs text-blue-700 space-y-1">
                            <li>• At least 8 characters long</li>
                            <li>• Contains uppercase and lowercase letters</li>
                            <li>• Contains at least one number</li>
                            <li>• Avoid common passwords</li>
                        </ul>
                    </div>

                    <Form.Item>
                        <AuthButton
                            type="primary"
                            htmlType="submit"
                            loading={loading}
                            block
                            variant="primary"
                        >
                            Reset Password
                        </AuthButton>
                    </Form.Item>
                </Form>

                <div className="text-center">
                    <AuthButton
                        variant="ghost"
                        onClick={() => navigate('/auth')}
                        block
                    >
                        Back to Login
                    </AuthButton>
                </div>
            </div>
        </div>
    );
};

export default ResetPasswordForm;