const mongoose = require('mongoose');
const { mockUser, mockVendor, mockProduct, generateToken, authenticatedRequest, cleanupDatabase } = require('../helpers/testSetup');
const Cart = require('../../../models/Cart');
const Product = require('../../../models/Product');
const User = require('../../../models/User');

// Token for authentication in tests
let userToken;

// Setup and teardown
beforeAll(async () => {
  // Mock MongoDB Connection
  const mongoURI = 'mongodb://127.0.0.1/alicartify-test';
  await mongoose.connect(mongoURI, { useNewUrlParser: true, useUnifiedTopology: true });

  // Ensure clean state
  await cleanupDatabase();

  // Mock user, vendor, and product in the database
  await new User(mockUser).save();
  await new User(mockVendor).save();
  await new Product(mockProduct).save();

  // Generate auth token
  userToken = generateToken(mockUser);
});

afterAll(async () => {
  await cleanupDatabase();
  await mongoose.connection.close();
});

describe('Cart API Tests', () => {
  it('should add item to cart', async () => {
    const response = await authenticatedRequest(userToken)
      .post('/api/customer/cart/add')
      .send({
        productId: mockProduct._id,
        quantity: 1
      });

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.data.items.length).toBe(1);
    expect(response.body.data.items[0].quantity).toBe(1);
  });

  it('should update item quantity in cart', async () => {
    const response = await authenticatedRequest(userToken)
      .put('/api/customer/cart/update')
      .send({
        productId: mockProduct._id,
        quantity: 3
      });

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    const updatedCart = await Cart.findByCustomer(mockUser._id);
    expect(updatedCart.items[0].quantity).toBe(3);
  });

  it('should remove item from cart', async () => {
    const response = await authenticatedRequest(userToken)
      .delete(`/api/customer/cart/remove/${mockProduct._id}`);

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    const updatedCart = await Cart.findByCustomer(mockUser._id);
    expect(updatedCart.items.length).toBe(0);
  });

  it('should get cart summary', async () => {
    // Add items to cart again
    await authenticatedRequest(userToken)
      .post('/api/customer/cart/add')
      .send({
        productId: mockProduct._id,
        quantity: 2
      });

    const response = await authenticatedRequest(userToken)
      .get('/api/customer/cart/summary');

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.data.totalItems).toBe(2);
  });

  it('should clear cart', async () => {
    const response = await authenticatedRequest(userToken)
      .delete('/api/customer/cart/clear');

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    const clearedCart = await Cart.findByCustomer(mockUser._id);
    expect(clearedCart.items.length).toBe(0);
  });

  it('should bulk add items to cart', async () => {
    const response = await authenticatedRequest(userToken)
      .post('/api/customer/cart/bulk-add')
      .send({
        items: [
          { productId: mockProduct._id, quantity: 2 }
        ]
      });

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.data.items.length).toBe(1);
  });

  it('should bulk update items in cart', async () => {
    const response = await authenticatedRequest(userToken)
      .put('/api/customer/cart/bulk-update')
      .send({
        items: [
          { productId: mockProduct._id, quantity: 5 }
        ]
      });

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    const updatedCart = await Cart.findByCustomer(mockUser._id);
    expect(updatedCart.items[0].quantity).toBe(5);
  });
});
