const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('authToken');
  return {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` })
  };
};

// Helper function to handle API responses
const handleResponse = async (response) => {
  const data = await response.json();
  
  if (!response.ok) {
    // Provide more detailed error information
    let errorMessage = data.message || `HTTP error! status: ${response.status}`;
    
    // If there are validation details, include them
    if (data.details) {
      errorMessage += `\nDetails: ${data.details}`;
    }
    
    // If there are validation errors array, format them
    if (data.errors && Array.isArray(data.errors)) {
      const errorsList = data.errors.map(err => `${err.path || err.param || 'Field'}: ${err.msg}`).join(', ');
      errorMessage += `\nValidation errors: ${errorsList}`;
    }
    
    console.error('API Error:', data);
    throw new Error(errorMessage);
  }
  
  return data;
};

// Order API functions
export const orderApi = {
  // Create a new order
  createOrder: async (orderData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/customer/orders`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(orderData)
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  },

  // Get customer's orders
  getCustomerOrders: async (params = {}) => {
    try {
      const queryParams = new URLSearchParams();
      
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value);
        }
      });

      const url = `${API_BASE_URL}/customer/orders${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching customer orders:', error);
      throw error;
    }
  },

  // Get order by ID
  getOrderById: async (orderId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/customer/orders/${orderId}`, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching order by ID:', error);
      throw error;
    }
  },

  // Cancel order
  cancelOrder: async (orderId, reason) => {
    try {
      const response = await fetch(`${API_BASE_URL}/customer/orders/${orderId}/cancel`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify({ reason })
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error cancelling order:', error);
      throw error;
    }
  },

  // Return order
  returnOrder: async (orderId, returnData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/customer/orders/${orderId}/return`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(returnData)
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error returning order:', error);
      throw error;
    }
  },

  // Track order by ID or tracking number
  trackOrder: async (identifier) => {
    try {
      const response = await fetch(`${API_BASE_URL}/customer/orders/track/${identifier}`, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error tracking order:', error);
      throw error;
    }
  },

  // Update order item status (for vendors)
  updateOrderItemStatus: async (orderId, status, itemIds, note) => {
    try {
      const response = await fetch(`${API_BASE_URL}/vendor/orders/${orderId}/status`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify({ status, itemIds, note })
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  },

  // Get order with vendor details
  getOrderWithVendorDetails: async (orderId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/customer/orders/${orderId}`, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching order with vendor details:', error);
      throw error;
    }
  },

  // Get order summary statistics
  getOrderStats: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/customer/orders/stats`, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching order stats:', error);
      throw error;
    }
  }
};

export default orderApi;
