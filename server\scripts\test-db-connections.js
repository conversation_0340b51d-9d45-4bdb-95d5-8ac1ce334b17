const mongoose = require('mongoose');
require('dotenv').config();

// Database URIs
const LOCAL_DB_URI = 'mongodb://localhost:27017/multi-vendor-ecommerce';
const PRODUCTION_DB_URI = 'mongodb+srv://cojeh66312:<EMAIL>/multi-vendor-ecommerce?retryWrites=true&w=majority&appName=Cluster0Test';

console.log('Environment check:');
console.log('MONGODB_URI from env:', process.env.MONGODB_URI);
console.log('Using PRODUCTION_DB_URI:', PRODUCTION_DB_URI);

async function testConnection(uri, name) {
    let connection = null;
    try {
        console.log(`\n🔍 Testing ${name} connection...`);
        console.log(`📍 URI: ${uri.replace(/\/\/.*@/, '//***:***@')}`);
        
        // Create connection with timeout
        connection = await mongoose.createConnection(uri, {
            serverSelectionTimeoutMS: 10000, // 10 second timeout
            socketTimeoutMS: 45000,
        });
        
        // Wait for connection to be ready
        await new Promise((resolve, reject) => {
            connection.once('open', resolve);
            connection.once('error', reject);
            setTimeout(() => reject(new Error('Connection timeout')), 15000);
        });
        
        console.log(`✅ ${name} connection established!`);
        
        // Test database access
        if (!connection.db) {
            throw new Error('Database object not available');
        }
        
        // Test basic operations
        const collections = await connection.db.listCollections().toArray();
        const dbStats = await connection.db.stats();
        
        console.log(`📊 Database: ${connection.db.databaseName}`);
        console.log(`📁 Collections: ${collections.length}`);
        console.log(`💾 Data size: ${(dbStats.dataSize / 1024 / 1024).toFixed(2)} MB`);
        
        if (collections.length > 0) {
            console.log(`📋 Collections found: ${collections.map(c => c.name).join(', ')}`);
            
            // Count documents in each collection
            for (const col of collections) {
                try {
                    const count = await connection.db.collection(col.name).countDocuments();
                    console.log(`   - ${col.name}: ${count} documents`);
                } catch (countError) {
                    console.log(`   - ${col.name}: Error counting documents`);
                }
            }
        } else {
            console.log(`📋 No collections found (empty database)`);
        }
        
        return true;
    } catch (error) {
        console.error(`❌ ${name} connection failed:`, error.message);
        if (error.code) {
            console.error(`   Error code: ${error.code}`);
        }
        return false;
    } finally {
        if (connection) {
            try {
                await connection.close();
                console.log(`🔌 Closed ${name} connection`);
            } catch (closeError) {
                console.error(`Warning: Error closing ${name} connection:`, closeError.message);
            }
        }
    }
}

async function main() {
    console.log('🚀 Testing database connections...\n');
    
    const localSuccess = await testConnection(LOCAL_DB_URI, 'Local MongoDB');
    const prodSuccess = await testConnection(PRODUCTION_DB_URI, 'Production MongoDB Atlas');
    
    console.log('\n📊 Connection Summary:');
    console.log(`Local MongoDB: ${localSuccess ? '✅ Connected' : '❌ Failed'}`);
    console.log(`Production Atlas: ${prodSuccess ? '✅ Connected' : '❌ Failed'}`);
    
    if (localSuccess && prodSuccess) {
        console.log('\n🎉 Both connections successful! You can proceed with migration.');
        console.log('\nTo migrate your data, run:');
        console.log('  npm run migrate:prod');
    } else {
        console.log('\n⚠️  Please fix connection issues before proceeding with migration.');
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { testConnection };