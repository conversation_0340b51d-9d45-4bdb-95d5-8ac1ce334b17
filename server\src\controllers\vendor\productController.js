const Product = require('../../models/Product');
const Category = require('../../models/Category');
const Vendor = require('../../models/Vendor');
const imageUpload = require('../../middleware/upload/imageUpload');
const NotificationService = require('../../services/notificationService');
const { transformProductPricing } = require('../../utils/helpers/pricingHelper');
const {
  parseColorsData,
  parseSizesData,
  parseHighlightsData,
  parseDeliveryCharges,
  parseShippingOptions,
  parseReturnPolicy,
  parseWarranty,
  parseSpecifications,
  parsePricing,
  parseInventory,
  parseArray
} = require('../../utils/formDataParser');
const path = require('path');
const fs = require('fs').promises;

/**
 * Create a new product - IMPROVED VERSION WITH PROPER VENDOR ID ATTACHMENT
 */
const createProduct = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    console.log('Creating product for vendor user ID:', vendorId);

    // Get vendor details with proper validation
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      console.log('Vendor not found for user:', vendorId);
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found. Please complete vendor registration first.',
        code: 'VENDOR_NOT_FOUND'
      });
    }

    console.log('Found vendor:', {
      id: vendor._id,
      businessName: vendor.businessName,
      status: vendor.status,
      verificationStatus: vendor.verification.status
    });

    // Enhanced validation - check vendor status and verification
    if (vendor.status === 'suspended') {
      return res.status(403).json({
        success: false,
        message: 'Account suspended. Contact support.',
        code: 'VENDOR_SUSPENDED'
      });
    }

    if (vendor.status !== 'active') {
      return res.status(403).json({
        success: false,
        message: 'Vendor account is not active. Please complete your profile.',
        code: 'VENDOR_INACTIVE'
      });
    }

    // Generate simple SKU if not provided
    let sku = req.body.sku;
    if (!sku) {
      sku = `PROD-${Date.now()}-${Math.random().toString(36).substr(2, 5).toUpperCase()}`;
    }
    sku = sku.toUpperCase();

    // Check SKU uniqueness - simplified
    const existingSku = await Product.findOne({ sku: sku });
    if (existingSku) {
      sku = `${sku}-${Math.random().toString(36).substr(2, 3).toUpperCase()}`;
    }

    // Process uploaded images (Cloudinary URLs)
    let productImages = [];
    if (req.fileUrls && req.fileUrls.length > 0) {
      productImages = req.fileUrls.map((url, index) => ({
        url, // This is now the Cloudinary URL
        publicId: req.cloudinaryPublicIds ? req.cloudinaryPublicIds[index] : null, // Store Cloudinary public ID for deletion
        alt: req.body.name || 'Product Image',
        isPrimary: index === 0,
        order: index
      }));
    }

    // Process pricing data with multi-currency support using utility function
    const pricing = parsePricing(req.body);

    // Process inventory data using utility function
    const inventory = parseInventory(req.body);

    // Validate required fields
    if (!req.body.name || req.body.name.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'Product name is required'
      });
    }

    // Simplified product data - improved validation
    const productData = {
      vendor: vendor._id,
      name: req.body.name.trim(),
      description: req.body.description || 'Product description',
      shortDescription: req.body.shortDescription || '',
      category: req.body.category || null,
      brand: req.body.brand || '',
      sku: sku,
      images: productImages,
      pricing: pricing,
      inventory: inventory,
      status: req.body.status || 'draft', // Default to draft for better workflow
      visibility: req.body.visibility || 'public',
      featured: req.body.featured === true,
      tags: Array.isArray(req.body.tags) ? req.body.tags : [],
      modifiedBy: vendorId
    };

    // Process enhanced fields using utility functions
    productData.colors = parseColorsData(req.body);
    productData.sizes = parseSizesData(req.body);
    productData.highlights = parseHighlightsData(req.body);
    productData.deliveryCharges = parseDeliveryCharges(req.body);
    productData.shippingOptions = parseShippingOptions(req.body);
    productData.returnPolicy = parseReturnPolicy(req.body);
    productData.warranty = parseWarranty(req.body);

    // Process specifications using utility function
    const specifications = parseSpecifications(req.body);
    if (specifications) {
      productData.specifications = specifications;
    }

    // Add optional fields if provided
    if (req.body.attributes) {
      productData.attributes = parseArray(req.body.attributes);
    }
    if (req.body.seo) {
      productData.seo = req.body.seo;
    }

    console.log('Product data prepared:', {
      name: productData.name,
      sku: productData.sku,
      pricing: productData.pricing,
      inventory: productData.inventory,
      vendor: vendor.businessName
    });
    console.log('Multi-currency pricing data:', JSON.stringify(productData.pricing.multiCurrency, null, 2));

    // Create product
    const product = new Product(productData);
    await product.save();

    console.log('Product created successfully:', product._id);

    // Update vendor product count - optional, don't fail if error
    try {
      await vendor.updatePerformance({ newProduct: true });
    } catch (perfError) {
      console.log('Performance update failed (non-critical):', perfError.message);
    }

    // Simple response without heavy population
    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      data: {
        product: {
          _id: product._id,
          name: product.name,
          sku: product.sku,
          price: product.pricing.basePrice,
          status: product.status,
          createdAt: product.createdAt
        }
      }
    });

  } catch (error) {
    console.error('Create product error:', error);

    // Clean up uploaded images on error - use Cloudinary deletion
    if (req.fileUrls && req.cloudinaryPublicIds) {
      for (let i = 0; i < req.cloudinaryPublicIds.length; i++) {
        try {
          await imageUpload.deleteImage(req.cloudinaryPublicIds[i]);
        } catch (deleteError) {
          console.error('Error deleting image from Cloudinary (non-critical):', deleteError.message);
        }
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create product',
      error: error.message,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

/**
 * Get vendor's products with pagination and filters
 */
const getProducts = async (req, res) => {
  try {
    const vendorId = req.user.userId;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Parse query parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const search = req.query.search;
    const status = req.query.status;
    const category = req.query.category;
    const sortBy = req.query.sortBy || 'createdAt';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Build filter
    const filter = { vendor: vendor._id };

    if (status) {
      filter.status = status;
    }

    if (category) {
      filter.category = category;
    }

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { sku: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // Build sort object
    const sort = {};
    if (sortBy === 'price') {
      sort['pricing.basePrice'] = sortOrder;
    } else if (sortBy === 'sales') {
      sort['sales.totalSold'] = sortOrder;
    } else {
      sort[sortBy] = sortOrder;
    }

    // Get products with pagination
    const [products, totalProducts] = await Promise.all([
      Product.find(filter)
        .populate('category', 'name slug')
        .populate('subcategory', 'name slug')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      Product.countDocuments(filter)
    ]);

    // Transform pricing for products in user's preferred currency
    const transformedProducts = products.map(product => {
      const transformedPricing = transformProductPricing(product, req.preferredCurrency);
      return {
        ...product,
        displayPricing: transformedPricing
      };
    });

    // Calculate pagination info
    const totalPages = Math.ceil(totalProducts / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    res.json({
      success: true,
      data: {
        products: transformedProducts,
        currency: req.preferredCurrency,
        pagination: {
          currentPage: page,
          totalPages,
          totalProducts,
          hasNextPage,
          hasPrevPage,
          limit
        }
      }
    });

  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch products',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get single product by ID
 */
const getProduct = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const productId = req.params.id;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Find product
    const product = await Product.findOne({ 
      _id: productId, 
      vendor: vendor._id 
    })
      .populate('vendor', 'businessName')
      .populate('category', 'name slug')
      .populate('subcategory', 'name slug');

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Transform pricing based on user's preferred currency
    const transformedPricing = transformProductPricing(product, req.preferredCurrency);

    res.json({
      success: true,
      data: {
        product: {
          ...product.toObject(),
          displayPricing: transformedPricing
        },
        currency: req.preferredCurrency
      }
    });

  } catch (error) {
    console.error('Get product error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch product',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update product
 */
const updateProduct = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const productId = req.params.id;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Find product
    const product = await Product.findOne({ 
      _id: productId, 
      vendor: vendor._id 
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if SKU is being changed and if it's unique
    if (req.body.sku && req.body.sku.toUpperCase() !== product.sku) {
      const existingSku = await Product.findOne({ 
        sku: req.body.sku.toUpperCase(),
        _id: { $ne: productId }
      });
      if (existingSku) {
        return res.status(400).json({
          success: false,
          message: 'SKU already exists'
        });
      }
    }

    // Validate category if being changed
    if (req.body.category && req.body.category !== (product.category ? product.category.toString() : null)) {
      const category = await Category.findById(req.body.category);
      if (!category || category.status !== 'active') {
        return res.status(400).json({
          success: false,
          message: 'Invalid or inactive category'
        });
      }
    }

    // Process new images if uploaded
    let newImages = [];
    if (req.fileUrls && req.fileUrls.length > 0) {
      newImages = req.fileUrls.map((url, index) => ({
        url,
        alt: req.body.name || product.name,
        isPrimary: index === 0 && product.images.length === 0,
        order: product.images.length + index
      }));
    }

    // Prepare update data - only update fields that are provided
    const updateData = {
      lastModified: new Date(),
      modifiedBy: vendorId
    };

    // Basic product fields
    if (req.body.name !== undefined) updateData.name = req.body.name.trim();
    if (req.body.description !== undefined) updateData.description = req.body.description;
    if (req.body.shortDescription !== undefined) updateData.shortDescription = req.body.shortDescription;
    if (req.body.category !== undefined) updateData.category = req.body.category;
    if (req.body.subcategory !== undefined) updateData.subcategory = req.body.subcategory;
    if (req.body.brand !== undefined) updateData.brand = req.body.brand;
    if (req.body.barcode !== undefined) updateData.barcode = req.body.barcode;
    if (req.body.status !== undefined) updateData.status = req.body.status;
    if (req.body.visibility !== undefined) updateData.visibility = req.body.visibility;
    if (req.body.featured !== undefined) updateData.featured = req.body.featured === true || req.body.featured === 'true';
    if (req.body.tags !== undefined) updateData.tags = Array.isArray(req.body.tags) ? req.body.tags : [];

    // Handle SKU update
    if (req.body.sku) {
      updateData.sku = req.body.sku.toUpperCase();
    }

    // Handle pricing updates - preserve existing pricing if not provided
    if (req.body.pricing || req.body.basePrice || req.body.salePrice || req.body.currency) {
      const existingPricing = product.pricing.toObject();
      
      // Handle direct price fields (for backward compatibility)
      if (req.body.basePrice !== undefined) {
        existingPricing.basePrice = parseFloat(req.body.basePrice) || existingPricing.basePrice;
      }
      if (req.body.salePrice !== undefined) {
        existingPricing.salePrice = req.body.salePrice ? parseFloat(req.body.salePrice) : null;
      }
      if (req.body.currency !== undefined) {
        existingPricing.currency = req.body.currency.toUpperCase();
      }
      
      // Handle structured pricing object
      if (req.body.pricing) {
        if (req.body.pricing.basePrice !== undefined) {
          existingPricing.basePrice = parseFloat(req.body.pricing.basePrice) || existingPricing.basePrice;
        }
        if (req.body.pricing.salePrice !== undefined) {
          existingPricing.salePrice = req.body.pricing.salePrice ? parseFloat(req.body.pricing.salePrice) : null;
        }
        if (req.body.pricing.costPrice !== undefined) {
          existingPricing.costPrice = req.body.pricing.costPrice ? parseFloat(req.body.pricing.costPrice) : null;
        }
        if (req.body.pricing.currency !== undefined) {
          existingPricing.currency = req.body.pricing.currency.toUpperCase();
        }
        if (req.body.pricing.taxClass !== undefined) {
          existingPricing.taxClass = req.body.pricing.taxClass;
        }
        if (req.body.pricing.taxRate !== undefined) {
          existingPricing.taxRate = parseFloat(req.body.pricing.taxRate) || 0;
        }
        
        // Handle multi-currency pricing
        if (req.body.pricing.multiCurrency) {
          if (!existingPricing.multiCurrency) {
            existingPricing.multiCurrency = {};
          }
          
          Object.keys(req.body.pricing.multiCurrency).forEach(currency => {
            const currencyData = req.body.pricing.multiCurrency[currency];
            if (currencyData && (currencyData.basePrice || currencyData.salePrice)) {
              const currencyKey = currency.toUpperCase();
              if (!existingPricing.multiCurrency[currencyKey]) {
                existingPricing.multiCurrency[currencyKey] = {};
              }
              
              if (currencyData.basePrice !== undefined) {
                existingPricing.multiCurrency[currencyKey].basePrice = parseFloat(currencyData.basePrice);
              }
              if (currencyData.salePrice !== undefined) {
                existingPricing.multiCurrency[currencyKey].salePrice = currencyData.salePrice ? parseFloat(currencyData.salePrice) : null;
              }
            }
          });
        }
      }
      
      updateData.pricing = existingPricing;
    }

    // Handle inventory updates - preserve existing inventory if not provided
    if (req.body.inventory || req.body.quantity !== undefined || req.body.trackQuantity !== undefined) {
      const existingInventory = product.inventory.toObject();
      
      // Handle direct inventory fields (for backward compatibility)
      if (req.body.quantity !== undefined) {
        existingInventory.quantity = parseInt(req.body.quantity) || 0;
      }
      if (req.body.trackQuantity !== undefined) {
        existingInventory.trackQuantity = req.body.trackQuantity === true || req.body.trackQuantity === 'true';
      }
      
      // Handle structured inventory object
      if (req.body.inventory) {
        if (req.body.inventory.quantity !== undefined) {
          existingInventory.quantity = parseInt(req.body.inventory.quantity) || 0;
        }
        if (req.body.inventory.trackQuantity !== undefined) {
          existingInventory.trackQuantity = req.body.inventory.trackQuantity === true || req.body.inventory.trackQuantity === 'true';
        }
        if (req.body.inventory.lowStockThreshold !== undefined) {
          existingInventory.lowStockThreshold = parseInt(req.body.inventory.lowStockThreshold) || 5;
        }
        if (req.body.inventory.allowBackorders !== undefined) {
          existingInventory.allowBackorders = req.body.inventory.allowBackorders === true || req.body.inventory.allowBackorders === 'true';
        }
      }
      
      updateData.inventory = existingInventory;
    }

    // Handle specifications updates
    if (req.body.specifications) {
      const existingSpecs = product.specifications ? product.specifications.toObject() : {};
      updateData.specifications = { ...existingSpecs, ...req.body.specifications };
    }

    // Handle shipping updates
    if (req.body.shipping) {
      const existingShipping = product.shipping ? product.shipping.toObject() : {};
      updateData.shipping = { ...existingShipping, ...req.body.shipping };
    }

    // Handle SEO updates
    if (req.body.seo) {
      const existingSEO = product.seo ? product.seo.toObject() : {};
      updateData.seo = { ...existingSEO, ...req.body.seo };
    }

    // Handle enhanced fields updates using utility functions
    if (req.body.colors !== undefined || Object.keys(req.body).some(key => key.startsWith('colors['))) {
      updateData.colors = parseColorsData(req.body);
    }

    if (req.body.shippingOptions || Object.keys(req.body).some(key => key.startsWith('shippingOptions['))) {
      updateData.shippingOptions = parseShippingOptions(req.body);
    }

    if (req.body.returnPolicy || Object.keys(req.body).some(key => key.startsWith('returnPolicy['))) {
      updateData.returnPolicy = parseReturnPolicy(req.body);
    }

    if (req.body.warranty || Object.keys(req.body).some(key => key.startsWith('warranty['))) {
      updateData.warranty = parseWarranty(req.body);
    }

    // Handle specifications updates using utility function
    if (req.body.specifications || Object.keys(req.body).some(key => key.startsWith('specifications['))) {
      const specifications = parseSpecifications(req.body);
      if (specifications) {
        updateData.specifications = specifications;
      }
    }

    // Handle variants and attributes
    if (req.body.variants !== undefined) {
      updateData.variants = Array.isArray(req.body.variants) ? req.body.variants : [];
    }
    if (req.body.attributes !== undefined) {
      updateData.attributes = Array.isArray(req.body.attributes) ? req.body.attributes : [];
    }

    // Handle images update
    if (newImages.length > 0) {
      updateData.$push = { images: { $each: newImages } };
    }

    console.log('Update data being applied:', JSON.stringify(updateData, null, 2));

    // Update product
    const updatedProduct = await Product.findByIdAndUpdate(
      productId,
      updateData,
      { new: true, runValidators: true }
    )
      .populate('vendor', 'businessName')
      .populate('category', 'name slug')
      .populate('subcategory', 'name slug');

    res.json({
      success: true,
      message: 'Product updated successfully',
      data: {
        product: updatedProduct
      }
    });

  } catch (error) {
    console.error('Update product error:', error);

    // Clean up uploaded images on error
    if (req.fileUrls) {
      for (const url of req.fileUrls) {
        try {
          await imageUpload.deleteImage(url);
        } catch (deleteError) {
          console.error('Error deleting image:', deleteError);
        }
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update product',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Delete product
 */
const deleteProduct = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const productId = req.params.id;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Find product
    const product = await Product.findOne({ 
      _id: productId, 
      vendor: vendor._id 
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if product has orders (prevent deletion if it has orders)
    const Order = require('../../models/Order');
    const hasOrders = await Order.findOne({ 
      'items.product': productId 
    });

    if (hasOrders) {
      // Archive instead of delete if product has orders
      product.status = 'archived';
      await product.save();

      return res.json({
        success: true,
        message: 'Product archived successfully (has existing orders)'
      });
    }

    // Delete product images
    for (const image of product.images) {
      try {
        await imageUpload.deleteImage(image.url);
      } catch (deleteError) {
        console.error('Error deleting image:', deleteError);
      }
    }

    // Delete product
    await Product.findByIdAndDelete(productId);

    res.json({
      success: true,
      message: 'Product deleted successfully'
    });

  } catch (error) {
    console.error('Delete product error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete product',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update product stock
 */
const updateStock = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const productId = req.params.id;
    const { quantity, operation = 'set' } = req.body;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Find product
    const product = await Product.findOne({ 
      _id: productId, 
      vendor: vendor._id 
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Update stock using the model method
    await product.updateStock(quantity, operation);

    res.json({
      success: true,
      message: 'Stock updated successfully',
      data: {
        product: {
          _id: product._id,
          name: product.name,
          sku: product.sku,
          inventory: product.inventory
        }
      }
    });

  } catch (error) {
    console.error('Update stock error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update stock',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Bulk operations on products
 */
const bulkOperation = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const { productIds, action } = req.body;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Verify all products belong to vendor
    const products = await Product.find({
      _id: { $in: productIds },
      vendor: vendor._id
    });

    if (products.length !== productIds.length) {
      return res.status(400).json({
        success: false,
        message: 'Some products not found or do not belong to vendor'
      });
    }

    let updateData = {};
    let message = '';

    switch (action) {
      case 'activate':
        updateData = { status: 'active' };
        message = 'Products activated successfully';
        break;
      case 'deactivate':
        updateData = { status: 'inactive' };
        message = 'Products deactivated successfully';
        break;
      case 'archive':
        updateData = { status: 'archived' };
        message = 'Products archived successfully';
        break;
      case 'delete':
        // Check if any product has orders
        const Order = require('../../models/Order');
        const hasOrders = await Order.findOne({
          'items.product': { $in: productIds }
        });

        if (hasOrders) {
          return res.status(400).json({
            success: false,
            message: 'Cannot delete products with existing orders. Use archive instead.'
          });
        }

        // Delete product images
        for (const product of products) {
          for (const image of product.images) {
            try {
              await imageUpload.deleteImage(image.url);
            } catch (deleteError) {
              console.error('Error deleting image:', deleteError);
            }
          }
        }

        await Product.deleteMany({ _id: { $in: productIds } });
        message = 'Products deleted successfully';
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid action'
        });
    }

    if (action !== 'delete') {
      await Product.updateMany(
        { _id: { $in: productIds } },
        { ...updateData, lastModified: new Date(), modifiedBy: vendorId }
      );
    }

    res.json({
      success: true,
      message,
      data: {
        affectedProducts: products.length
      }
    });

  } catch (error) {
    console.error('Bulk operation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to perform bulk operation',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Remove product image
 */
const removeImage = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const productId = req.params.id;
    const imageUrl = req.body.imageUrl;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Find product
    const product = await Product.findOne({ 
      _id: productId, 
      vendor: vendor._id 
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Find image in product
    const imageIndex = product.images.findIndex(img => img.url === imageUrl);
    if (imageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Image not found'
      });
    }

    // Check if it's the only image
    if (product.images.length === 1) {
      return res.status(400).json({
        success: false,
        message: 'Cannot remove the only product image'
      });
    }

    // Remove image from product
    const removedImage = product.images[imageIndex];
    product.images.splice(imageIndex, 1);

    // If removed image was primary, make first image primary
    if (removedImage.isPrimary && product.images.length > 0) {
      product.images[0].isPrimary = true;
    }

    // Update image orders
    product.images.forEach((img, index) => {
      img.order = index;
    });

    await product.save();

    // Delete image file
    try {
      await imageUpload.deleteImage(imageUrl);
    } catch (deleteError) {
      console.error('Error deleting image file:', deleteError);
    }

    res.json({
      success: true,
      message: 'Image removed successfully',
      data: {
        product: {
          _id: product._id,
          images: product.images
        }
      }
    });

  } catch (error) {
    console.error('Remove image error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove image',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get vendor product statistics
 */
const getProductStats = async (req, res) => {
  try {
    const vendorId = req.user.userId;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Get product statistics
    const stats = await Product.aggregate([
      { $match: { vendor: vendor._id } },
      {
        $group: {
          _id: null,
          totalProducts: { $sum: 1 },
          activeProducts: {
            $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
          },
          draftProducts: {
            $sum: { $cond: [{ $eq: ['$status', 'draft'] }, 1, 0] }
          },
          inactiveProducts: {
            $sum: { $cond: [{ $eq: ['$status', 'inactive'] }, 1, 0] }
          },
          outOfStockProducts: {
            $sum: { $cond: [{ $eq: ['$inventory.stockStatus', 'out_of_stock'] }, 1, 0] }
          },
          lowStockProducts: {
            $sum: { $cond: [{ $lte: ['$inventory.quantity', '$inventory.lowStockThreshold'] }, 1, 0] }
          },
          totalRevenue: { $sum: '$sales.totalRevenue' },
          totalSold: { $sum: '$sales.totalSold' },
          averagePrice: { $avg: '$pricing.basePrice' }
        }
      }
    ]);

    const productStats = stats[0] || {
      totalProducts: 0,
      activeProducts: 0,
      draftProducts: 0,
      inactiveProducts: 0,
      outOfStockProducts: 0,
      lowStockProducts: 0,
      totalRevenue: 0,
      totalSold: 0,
      averagePrice: 0
    };

    // Get top selling products
    const topSellingProducts = await Product.find({ vendor: vendor._id })
      .sort({ 'sales.totalSold': -1 })
      .limit(5)
      .select('name sales.totalSold sales.totalRevenue pricing.basePrice pricing.multiCurrency')
      .lean();

    // Transform pricing for top selling products
    const transformedTopSellingProducts = topSellingProducts.map(product => {
      const transformedPricing = transformProductPricing(product, req.preferredCurrency);
      return {
        ...product,
        displayPricing: transformedPricing
      };
    });

    res.json({
      success: true,
      data: {
        stats: productStats,
        topSellingProducts: transformedTopSellingProducts,
        currency: req.preferredCurrency,
        subscriptionLimits: {
          maxProducts: vendor.subscription.features.maxProducts,
          currentProducts: productStats.totalProducts,
          remainingProducts: vendor.subscription.features.maxProducts - productStats.totalProducts
        }
      }
    });

  } catch (error) {
    console.error('Get product stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch product statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Submit product for approval
 */
const submitForApproval = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const productId = req.params.id;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Check if vendor is verified
    if (vendor.verification.status !== 'verified') {
      return res.status(403).json({
        success: false,
        message: 'Vendor must be verified to submit products for approval'
      });
    }

    // Find product
    const product = await Product.findOne({
      _id: productId,
      vendor: vendor._id
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if product is in draft status
    if (product.status !== 'draft') {
      return res.status(400).json({
        success: false,
        message: 'Only draft products can be submitted for approval'
      });
    }

    // Validate product has required fields
    const requiredFields = ['name', 'description', 'pricing.basePrice', 'sku'];
    const missingFields = [];

    requiredFields.forEach(field => {
      const fieldParts = field.split('.');
      let value = product;

      for (const part of fieldParts) {
        value = value?.[part];
      }

      if (!value) {
        missingFields.push(field);
      }
    });

    if (product.images.length === 0) {
      missingFields.push('images');
    }

    if (missingFields.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Product is missing required fields',
        data: { missingFields }
      });
    }

    await product.submitForApproval(vendorId);

    // Send notification to admins
    try {
      await NotificationService.notifyProductApprovalRequest(product, vendor);
    } catch (notificationError) {
      console.error('Failed to send approval request notification:', notificationError);
      // Don't fail the submission if notification fails
    }

    res.json({
      success: true,
      message: 'Product submitted for approval successfully',
      data: { product }
    });

  } catch (error) {
    console.error('Submit for approval error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit product for approval',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get product approval history
 */
const getApprovalHistory = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const productId = req.params.id;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Find product
    const product = await Product.findOne({
      _id: productId,
      vendor: vendor._id
    })
      .populate('approval.history.performedBy', 'firstName lastName role')
      .populate('approval.reviewedBy', 'firstName lastName role')
      .select('approval');

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    res.json({
      success: true,
      data: { approval: product.approval }
    });

  } catch (error) {
    console.error('Get approval history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch approval history',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update multi-currency pricing for a product
 */
const updateMultiCurrencyPricing = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const productId = req.params.id;
    const { currency, basePrice, salePrice } = req.body;

    // Validate required fields
    if (!currency || !basePrice) {
      return res.status(400).json({
        success: false,
        message: 'Currency and base price are required'
      });
    }

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Find product
    const product = await Product.findOne({ 
      _id: productId, 
      vendor: vendor._id 
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Set price in currency using the model method
    product.setPriceInCurrency(currency, parseFloat(basePrice), salePrice ? parseFloat(salePrice) : null);
    await product.save();

    // Get all available currencies and their prices for response
    const availableCurrencies = product.getAvailableCurrencies();
    const multiCurrencyPrices = {};
    
    availableCurrencies.forEach(curr => {
      multiCurrencyPrices[curr] = {
        basePrice: product.getPriceInCurrency(curr, 'base'),
        salePrice: product.getPriceInCurrency(curr, 'sale'),
        currentPrice: product.getCurrentPriceInCurrency(curr)
      };
    });

    res.json({
      success: true,
      message: `Pricing updated successfully for ${currency}`,
      data: {
        product: {
          _id: product._id,
          name: product.name,
          sku: product.sku,
          availableCurrencies,
          multiCurrencyPrices
        }
      }
    });

  } catch (error) {
    console.error('Update multi-currency pricing error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update multi-currency pricing',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get product pricing in all available currencies
 */
const getMultiCurrencyPricing = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const productId = req.params.id;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Find product
    const product = await Product.findOne({ 
      _id: productId, 
      vendor: vendor._id 
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    const availableCurrencies = product.getAvailableCurrencies();
    const multiCurrencyPrices = {};
    
// Transform product pricing to the preferred currency
    const transformedPricing = transformProductPricing(product, req.preferredCurrency);

    availableCurrencies.forEach(currency => {
      multiCurrencyPrices[currency] = {
        basePrice: product.getPriceInCurrency(currency, 'base'),
        salePrice: product.getPriceInCurrency(currency, 'sale'),
        currentPrice: product.getCurrentPriceInCurrency(currency)
      };
    });
    
    // Add transformed pricing to the API response
    const { basePrice, salePrice, currentPrice } = transformedPricing;

res.json({
      success: true,
      data: {
        product: {
          _id: product._id,
          name: product.name,
          sku: product.sku,
          defaultCurrency: product.pricing.currency,
          availableCurrencies,
          multiCurrencyPrices,
          pricingInPreferredCurrency: {
            basePrice,
            salePrice,
            currentPrice
          }
        }
      }
    });

  } catch (error) {
    console.error('Get multi-currency pricing error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch multi-currency pricing',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Upload color-specific image for a product
 */
const uploadColorImage = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const productId = req.params.id;
    const { colorName, colorIndex } = req.body;

    // Validate required parameters
    if (!colorName && colorIndex === undefined) {
      return res.status(400).json({
        success: false,
        message: 'Either colorName or colorIndex is required'
      });
    }

    // Check if image was uploaded
    if (!req.fileUrl) {
      return res.status(400).json({
        success: false,
        message: 'No image file uploaded'
      });
    }

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Find product
    const product = await Product.findOne({ 
      _id: productId, 
      vendor: vendor._id 
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Find the color to update
    let colorToUpdate = null;
    let colorIndexToUpdate = -1;

    if (colorIndex !== undefined) {
      // Update by index
      const index = parseInt(colorIndex);
      if (index >= 0 && index < product.colors.length) {
        colorToUpdate = product.colors[index];
        colorIndexToUpdate = index;
      }
    } else if (colorName) {
      // Update by name
      colorIndexToUpdate = product.colors.findIndex(
        color => color.name.toLowerCase() === colorName.toLowerCase()
      );
      if (colorIndexToUpdate !== -1) {
        colorToUpdate = product.colors[colorIndexToUpdate];
      }
    }

    if (!colorToUpdate) {
      // Clean up uploaded image if color not found
      try {
        await imageUpload.deleteImage(req.cloudinaryPublicId || req.fileUrl);
      } catch (deleteError) {
        console.error('Error deleting uploaded image:', deleteError);
      }

      return res.status(404).json({
        success: false,
        message: colorName 
          ? `Color '${colorName}' not found in product` 
          : `Color at index ${colorIndex} not found`
      });
    }

    // Delete old color image if exists
    if (colorToUpdate.image) {
      try {
        await imageUpload.deleteImage(colorToUpdate.image);
      } catch (deleteError) {
        console.error('Error deleting old color image:', deleteError);
      }
    }

    // Update color with new image
    product.colors[colorIndexToUpdate].image = req.fileUrl;
    product.lastModified = new Date();
    product.modifiedBy = vendorId;

    // Save product
    await product.save();

    console.log(`Color image updated for product ${productId}, color: ${colorToUpdate.name}`);

    res.json({
      success: true,
      message: `Image uploaded successfully for color '${colorToUpdate.name}'`,
      data: {
        product: {
          _id: product._id,
          name: product.name,
          sku: product.sku
        },
        color: {
          name: colorToUpdate.name,
          hexCode: colorToUpdate.hexCode,
          image: req.fileUrl,
          index: colorIndexToUpdate
        }
      }
    });

  } catch (error) {
    console.error('Upload color image error:', error);

    // Clean up uploaded image on error
    if (req.fileUrl && req.cloudinaryPublicId) {
      try {
        await imageUpload.deleteImage(req.cloudinaryPublicId);
      } catch (deleteError) {
        console.error('Error deleting image on error:', deleteError);
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to upload color image',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Remove color-specific image from a product
 */
const removeColorImage = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const productId = req.params.id;
    const { colorName, colorIndex } = req.body;

    // Validate required parameters
    if (!colorName && colorIndex === undefined) {
      return res.status(400).json({
        success: false,
        message: 'Either colorName or colorIndex is required'
      });
    }

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Find product
    const product = await Product.findOne({ 
      _id: productId, 
      vendor: vendor._id 
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Find the color to update
    let colorToUpdate = null;
    let colorIndexToUpdate = -1;

    if (colorIndex !== undefined) {
      // Update by index
      const index = parseInt(colorIndex);
      if (index >= 0 && index < product.colors.length) {
        colorToUpdate = product.colors[index];
        colorIndexToUpdate = index;
      }
    } else if (colorName) {
      // Update by name
      colorIndexToUpdate = product.colors.findIndex(
        color => color.name.toLowerCase() === colorName.toLowerCase()
      );
      if (colorIndexToUpdate !== -1) {
        colorToUpdate = product.colors[colorIndexToUpdate];
      }
    }

    if (!colorToUpdate) {
      return res.status(404).json({
        success: false,
        message: colorName 
          ? `Color '${colorName}' not found in product` 
          : `Color at index ${colorIndex} not found`
      });
    }

    if (!colorToUpdate.image) {
      return res.status(400).json({
        success: false,
        message: `Color '${colorToUpdate.name}' does not have an image to remove`
      });
    }

    // Delete color image
    try {
      await imageUpload.deleteImage(colorToUpdate.image);
    } catch (deleteError) {
      console.error('Error deleting color image:', deleteError);
    }

    // Remove image from color
    product.colors[colorIndexToUpdate].image = '';
    product.lastModified = new Date();
    product.modifiedBy = vendorId;

    // Save product
    await product.save();

    console.log(`Color image removed for product ${productId}, color: ${colorToUpdate.name}`);

    res.json({
      success: true,
      message: `Image removed successfully for color '${colorToUpdate.name}'`,
      data: {
        product: {
          _id: product._id,
          name: product.name,
          sku: product.sku
        },
        color: {
          name: colorToUpdate.name,
          hexCode: colorToUpdate.hexCode,
          image: '',
          index: colorIndexToUpdate
        }
      }
    });

  } catch (error) {
    console.error('Remove color image error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove color image',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get all colors with their images for a product
 */
const getProductColors = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const productId = req.params.id;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Find product
    const product = await Product.findOne({ 
      _id: productId, 
      vendor: vendor._id 
    }).select('_id name sku colors');

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    res.json({
      success: true,
      data: {
        product: {
          _id: product._id,
          name: product.name,
          sku: product.sku
        },
        colors: product.colors.map((color, index) => ({
          index,
          name: color.name,
          hexCode: color.hexCode,
          image: color.image || null,
          available: color.available,
          stock: color.stock
        }))
      }
    });

  } catch (error) {
    console.error('Get product colors error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch product colors',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Simple test product creation - MINIMAL VALIDATION FOR TESTING
 */
const createTestProduct = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    console.log('Creating test product for user:', vendorId);

    // Find or create vendor profile
    let vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      console.log('Creating test vendor profile...');
      
      // Create a minimal vendor profile for testing
      vendor = new Vendor({
        user: vendorId,
        businessName: `Test Business ${Date.now()}`,
        businessType: 'individual',
        businessAddress: {
          street: 'Test Street',
          city: 'Test City',
          state: 'Test State',
          zipCode: '12345',
          country: 'Test Country'
        },
        contactInfo: {
          businessPhone: '+*********0',
          businessEmail: '<EMAIL>'
        },
        bankDetails: {
          accountHolderName: 'Test User',
          bankName: 'Test Bank',
          accountNumber: '*********0',
          routingNumber: '*********',
          accountType: 'checking'
        },
        status: 'active',
        verification: {
          status: 'verified'
        }
      });
      
      await vendor.save();
      console.log('Test vendor created:', vendor._id);
    }

    // Generate unique SKU
    const sku = `TEST-${Date.now()}-${Math.random().toString(36).substr(2, 5).toUpperCase()}`;

    // Create minimal product data
    const productData = {
      vendor: vendor._id,
      name: req.body.name || `Test Product ${Date.now()}`,
      description: req.body.description || 'Test product description',
      sku: sku,
      pricing: {
        basePrice: parseFloat(req.body.price) || 19.99,
        currency: 'USD',
        taxClass: 'standard',
        taxRate: 0
      },
      inventory: {
        trackQuantity: true,
        quantity: parseInt(req.body.quantity) || 10,
        lowStockThreshold: 5,
        allowBackorders: false
      },
      status: 'active',
      visibility: 'public',
      modifiedBy: vendorId
    };

    console.log('Creating product with data:', productData);

    // Create product
    const product = new Product(productData);
    await product.save();

    console.log('Test product created:', product._id);

    res.status(201).json({
      success: true,
      message: 'Test product created successfully',
      data: {
        product: {
          _id: product._id,
          name: product.name,
          sku: product.sku,
          price: product.pricing.basePrice,
          quantity: product.inventory.quantity,
          status: product.status,
          vendor: vendor.businessName,
          createdAt: product.createdAt
        }
      }
    });

  } catch (error) {
    console.error('Create test product error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create test product',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

module.exports = {
  createProduct,
  getProducts,
  getProduct,
  updateProduct,
  deleteProduct,
  updateStock,
  bulkOperation,
  removeImage,
  getProductStats,
  submitForApproval,
  getApprovalHistory,
  updateMultiCurrencyPricing,
  getMultiCurrencyPricing,
  uploadColorImage,
  removeColorImage,
  getProductColors,
  createTestProduct
};
