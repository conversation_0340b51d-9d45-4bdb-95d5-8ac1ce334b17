const { Notification, User, Vendor, Product } = require('../models');

class NotificationService {
  /**
   * Create and send a notification
   */
  static async createNotification(notificationData) {
    try {
      const notification = await Notification.createNotification(notificationData);
      return notification;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Send product approval request notification to admin
   */
  static async notifyProductApprovalRequest(product, vendor) {
    try {
      // Get all admin users
      const admins = await User.find({ role: 'admin' });
      
      const notifications = admins.map(admin => ({
        recipient: admin._id,
        type: 'product_approval_request',
        title: 'New Product Approval Request',
        message: `${vendor.businessName} has submitted "${product.name}" for approval.`,
        data: {
          productId: product._id,
          vendorId: vendor._id,
          productName: product.name,
          vendorName: vendor.businessName
        },
        relatedEntity: {
          entityType: 'Product',
          entityId: product._id
        },
        priority: 'medium',
        channels: ['in_app', 'email'],
        actionUrl: `/admin/products/pending-approval`,
        actionText: 'Review Product'
      }));

      const createdNotifications = await Promise.all(
        notifications.map(notif => this.createNotification(notif))
      );

      return createdNotifications;
    } catch (error) {
      console.error('Error sending product approval request notification:', error);
      throw error;
    }
  }

  /**
   * Send product approved notification to vendor
   */
  static async notifyProductApproved(product, vendor, admin, notes = '') {
    try {
      const notification = await this.createNotification({
        recipient: vendor.user,
        sender: admin._id,
        type: 'product_approved',
        title: 'Product Approved',
        message: `Great news! Your product "${product.name}" has been approved and is now live on the platform.${notes ? ` Admin notes: ${notes}` : ''}`,
        data: {
          productId: product._id,
          productName: product.name,
          adminNotes: notes
        },
        relatedEntity: {
          entityType: 'Product',
          entityId: product._id
        },
        priority: 'high',
        channels: ['in_app', 'email'],
        actionUrl: `/vendor/products/${product._id}`,
        actionText: 'View Product'
      });

      return notification;
    } catch (error) {
      console.error('Error sending product approved notification:', error);
      throw error;
    }
  }

  /**
   * Send product rejected notification to vendor
   */
  static async notifyProductRejected(product, vendor, admin, reason, notes = '') {
    try {
      const notification = await this.createNotification({
        recipient: vendor.user,
        sender: admin._id,
        type: 'product_rejected',
        title: 'Product Rejected',
        message: `Your product "${product.name}" has been rejected. Reason: ${reason}.${notes ? ` Additional notes: ${notes}` : ''}`,
        data: {
          productId: product._id,
          productName: product.name,
          rejectionReason: reason,
          adminNotes: notes
        },
        relatedEntity: {
          entityType: 'Product',
          entityId: product._id
        },
        priority: 'high',
        channels: ['in_app', 'email'],
        actionUrl: `/vendor/products/${product._id}`,
        actionText: 'View Product'
      });

      return notification;
    } catch (error) {
      console.error('Error sending product rejected notification:', error);
      throw error;
    }
  }

  /**
   * Send product changes requested notification to vendor
   */
  static async notifyProductChangesRequested(product, vendor, admin, changes, notes = '') {
    try {
      const changesList = changes.map(change => `${change.field}: ${change.message}`).join(', ');
      
      const notification = await this.createNotification({
        recipient: vendor.user,
        sender: admin._id,
        type: 'product_changes_requested',
        title: 'Product Changes Requested',
        message: `Please make the following changes to your product "${product.name}": ${changesList}.${notes ? ` Additional notes: ${notes}` : ''}`,
        data: {
          productId: product._id,
          productName: product.name,
          changesRequested: changes,
          adminNotes: notes
        },
        relatedEntity: {
          entityType: 'Product',
          entityId: product._id
        },
        priority: 'medium',
        channels: ['in_app', 'email'],
        actionUrl: `/vendor/products/${product._id}/edit`,
        actionText: 'Edit Product'
      });

      return notification;
    } catch (error) {
      console.error('Error sending product changes requested notification:', error);
      throw error;
    }
  }

  /**
   * Send vendor verification request notification to admin
   */
  static async notifyVendorVerificationRequest(vendor) {
    try {
      // Get all admin users
      const admins = await User.find({ role: 'admin' });
      
      const notifications = admins.map(admin => ({
        recipient: admin._id,
        type: 'vendor_verification_request',
        title: 'New Vendor Verification Request',
        message: `${vendor.businessName} has submitted their documents for verification.`,
        data: {
          vendorId: vendor._id,
          vendorName: vendor.businessName,
          businessType: vendor.businessType
        },
        relatedEntity: {
          entityType: 'Vendor',
          entityId: vendor._id
        },
        priority: 'medium',
        channels: ['in_app', 'email'],
        actionUrl: `/admin/vendors/pending-verification`,
        actionText: 'Review Vendor'
      }));

      const createdNotifications = await Promise.all(
        notifications.map(notif => this.createNotification(notif))
      );

      return createdNotifications;
    } catch (error) {
      console.error('Error sending vendor verification request notification:', error);
      throw error;
    }
  }

  /**
   * Send vendor verified notification
   */
  static async notifyVendorVerified(vendor, admin, notes = '') {
    try {
      const notification = await this.createNotification({
        recipient: vendor.user,
        sender: admin._id,
        type: 'vendor_verified',
        title: 'Vendor Account Verified',
        message: `Congratulations! Your vendor account has been verified. You can now start selling on our platform.${notes ? ` Admin notes: ${notes}` : ''}`,
        data: {
          vendorId: vendor._id,
          vendorName: vendor.businessName,
          adminNotes: notes
        },
        relatedEntity: {
          entityType: 'Vendor',
          entityId: vendor._id
        },
        priority: 'high',
        channels: ['in_app', 'email'],
        actionUrl: `/vendor/dashboard`,
        actionText: 'Go to Dashboard'
      });

      return notification;
    } catch (error) {
      console.error('Error sending vendor verified notification:', error);
      throw error;
    }
  }

  /**
   * Send vendor rejected notification
   */
  static async notifyVendorRejected(vendor, admin, reason, notes = '') {
    try {
      const notification = await this.createNotification({
        recipient: vendor.user,
        sender: admin._id,
        type: 'vendor_rejected',
        title: 'Vendor Application Rejected',
        message: `Your vendor application has been rejected. Reason: ${reason}.${notes ? ` Additional notes: ${notes}` : ''}`,
        data: {
          vendorId: vendor._id,
          vendorName: vendor.businessName,
          rejectionReason: reason,
          adminNotes: notes
        },
        relatedEntity: {
          entityType: 'Vendor',
          entityId: vendor._id
        },
        priority: 'high',
        channels: ['in_app', 'email'],
        actionUrl: `/vendor/profile`,
        actionText: 'Update Profile'
      });

      return notification;
    } catch (error) {
      console.error('Error sending vendor rejected notification:', error);
      throw error;
    }
  }

  /**
   * Send payout processed notification
   */
  static async notifyPayoutProcessed(vendor, amount, method, transactionId) {
    try {
      const notification = await this.createNotification({
        recipient: vendor.user,
        type: 'payout_processed',
        title: 'Payout Processed',
        message: `Your payout of $${amount.toFixed(2)} has been processed via ${method.replace('_', ' ')}.${transactionId ? ` Transaction ID: ${transactionId}` : ''}`,
        data: {
          vendorId: vendor._id,
          amount: amount,
          method: method,
          transactionId: transactionId
        },
        relatedEntity: {
          entityType: 'Vendor',
          entityId: vendor._id
        },
        priority: 'medium',
        channels: ['in_app', 'email'],
        actionUrl: `/vendor/payouts`,
        actionText: 'View Payouts'
      });

      return notification;
    } catch (error) {
      console.error('Error sending payout processed notification:', error);
      throw error;
    }
  }

  /**
   * Send commission earned notification
   */
  static async notifyCommissionEarned(vendor, amount, orderId) {
    try {
      const notification = await this.createNotification({
        recipient: vendor.user,
        type: 'commission_earned',
        title: 'Commission Earned',
        message: `You've earned $${amount.toFixed(2)} in commission from order #${orderId}.`,
        data: {
          vendorId: vendor._id,
          amount: amount,
          orderId: orderId
        },
        relatedEntity: {
          entityType: 'Vendor',
          entityId: vendor._id
        },
        priority: 'low',
        channels: ['in_app'],
        actionUrl: `/vendor/orders/${orderId}`,
        actionText: 'View Order'
      });

      return notification;
    } catch (error) {
      console.error('Error sending commission earned notification:', error);
      throw error;
    }
  }

  /**
   * Get user notifications
   */
  static async getUserNotifications(userId, options = {}) {
    try {
      const notifications = await Notification.getUserNotifications(userId, options);
      return notifications;
    } catch (error) {
      console.error('Error getting user notifications:', error);
      throw error;
    }
  }

  /**
   * Mark notification as read
   */
  static async markAsRead(notificationId, userId) {
    try {
      const notification = await Notification.findOne({
        _id: notificationId,
        recipient: userId
      });

      if (!notification) {
        throw new Error('Notification not found');
      }

      await notification.markAsRead();
      return notification;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  /**
   * Get notification statistics
   */
  static async getNotificationStats(userId) {
    try {
      const stats = await Notification.getStatistics(userId);
      return stats;
    } catch (error) {
      console.error('Error getting notification statistics:', error);
      throw error;
    }
  }
}

module.exports = NotificationService;
