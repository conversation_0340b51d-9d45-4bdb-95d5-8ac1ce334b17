import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Badge,
  Progress,
  Alert,
  Spin,
  Typography,
  Space,
  Button,
  Select,
  DatePicker,
  notification,
  Tag,
  Avatar
} from 'antd';
import {
  DollarOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  ShopOutlined,
  RiseOutlined,
  WarningOutlined,
  ReloadOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  ShoppingOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  TeamOutlined
} from '@ant-design/icons';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { dashboardApi, ordersApi, usersApi, vendorsApi, productsApi } from '../../services/adminApi';
import dayjs from 'dayjs';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const { Title: AntTitle, Text } = Typography;
const { RangePicker } = DatePicker;

const ResponsiveAdminDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [realTimeData, setRealTimeData] = useState(null);
  const [recentOrders, setRecentOrders] = useState([]);
  const [recentUsers, setRecentUsers] = useState([]);
  const [pendingApprovals, setPendingApprovals] = useState([]);
  const [vendorStats, setVendorStats] = useState(null);
  const [productStats, setProductStats] = useState(null);
  const [alerts, setAlerts] = useState([]);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');

  useEffect(() => {
    initializeDashboard();
  }, []);

  const initializeDashboard = async () => {
    try {
      setLoading(true);

      // For demo purposes, let's create some mock data if API fails
      try {
        // Fetch all dashboard data
        const [
          statsResponse,
          ordersResponse,
          usersResponse,
          vendorStatsResponse,
          productStatsResponse,
          pendingProductsResponse,
          pendingVendorsResponse
        ] = await Promise.all([
          dashboardApi.getStats(),
          ordersApi.getOrders({ limit: 5, sortBy: 'createdAt', sortOrder: 'desc' }),
          usersApi.getUsers({ limit: 5, sortBy: 'createdAt', sortOrder: 'desc' }),
          vendorsApi.getStatistics(),
          productsApi.getStats(),
          productsApi.getPendingApproval({ limit: 5 }),
          vendorsApi.getPendingVerification({ limit: 5 })
        ]);

        setDashboardData(statsResponse.data.data);
        setRecentOrders(ordersResponse.data.data.orders);
        setRecentUsers(usersResponse.data.data.users);
        setVendorStats(vendorStatsResponse.data.data);
        setProductStats(productStatsResponse.data.data);

        // Combine pending approvals
        const pendingItems = [
          ...(pendingProductsResponse.data.data.products || []).map(p => ({
            type: 'product',
            id: p._id,
            name: p.name,
            vendor: p.vendor?.businessName,
            submittedAt: p.approval?.submittedAt
          })),
          ...(pendingVendorsResponse.data.data.vendors || []).map(v => ({
            type: 'vendor',
            id: v._id,
            name: v.businessName,
            email: v.user?.email,
            submittedAt: v.createdAt
          }))
        ];
        setPendingApprovals(pendingItems);

        // Set up alerts based on dashboard data
        const newAlerts = [];
        const stats = statsResponse.data.data;
        
        if (stats.alerts?.lowStockProducts > 0) {
          newAlerts.push({
            type: 'warning',
            message: `${stats.alerts.lowStockProducts} products are low in stock`,
            action: 'View Products'
          });
        }
        
        if (stats.alerts?.pendingVendors > 10) {
          newAlerts.push({
            type: 'info',
            message: `${stats.alerts.pendingVendors} vendor applications pending review`,
            action: 'Review Vendors'
          });
        }
        
        if (stats.alerts?.pendingOrders > 20) {
          newAlerts.push({
            type: 'warning',
            message: `${stats.alerts.pendingOrders} orders pending processing`,
            action: 'View Orders'
          });
        }

        setAlerts(newAlerts);
        
        // Fetch analytics data
        await fetchAnalyticsData(selectedPeriod);
        
      } catch (apiError) {
        console.log('API not available, using mock data for demo');
        
        // Mock data for demonstration
        setDashboardData({
          overview: {
            orders: {
              totalOrders: 1250,
              totalRevenue: 125000,
              pendingOrders: 15,
              confirmedOrders: 45,
              processingOrders: 30,
              shippedOrders: 25,
              deliveredOrders: 1100,
              cancelledOrders: 35
            },
            users: {
              totalUsers: 5420,
              activeUsers: 4890,
              inactiveUsers: 530
            },
            vendors: {
              totalVendors: 125,
              activeVendors: 98,
              pendingVendors: 12
            }
          }
        });

        setRecentOrders([
          {
            _id: '1',
            orderNumber: 'ORD240101001',
            customer: { firstName: 'John', lastName: 'Doe' },
            status: 'delivered',
            pricing: { total: 299.99 },
            createdAt: new Date().toISOString()
          },
          {
            _id: '2',
            orderNumber: 'ORD240101002',
            customer: { firstName: 'Jane', lastName: 'Smith' },
            status: 'processing',
            pricing: { total: 149.50 },
            createdAt: new Date(Date.now() - 86400000).toISOString()
          }
        ]);

        setRecentUsers([
          {
            _id: '1',
            firstName: 'Alice',
            lastName: 'Johnson',
            email: '<EMAIL>',
            role: 'customer',
            status: 'active',
            createdAt: new Date().toISOString()
          },
          {
            _id: '2',
            firstName: 'Bob',
            lastName: 'Wilson',
            email: '<EMAIL>',
            role: 'vendor',
            status: 'active',
            createdAt: new Date(Date.now() - 86400000).toISOString()
          }
        ]);

        setAnalyticsData({
          analytics: [
            { _id: '2024-01-01', revenue: 5000, orders: 25 },
            { _id: '2024-01-02', revenue: 7500, orders: 35 },
            { _id: '2024-01-03', revenue: 6200, orders: 28 },
            { _id: '2024-01-04', revenue: 8900, orders: 42 },
            { _id: '2024-01-05', revenue: 12000, orders: 55 }
          ]
        });

        setProductStats({
          totalProducts: 250,
          activeProducts: 220,
          pendingProducts: 15,
          lowStockProducts: 10,
          outOfStockProducts: 5
        });
      }
      
    } catch (error) {
      console.error('Error loading dashboard:', error);
      notification.error({
        message: 'Error',
        description: 'Failed to load dashboard data. Using demo data instead.'
      });
    } finally {
      setLoading(false);
    }
  };


  const fetchAnalyticsData = async (period) => {
    try {
      const response = await dashboardApi.getAnalytics({ period, type: 'revenue' });
      setAnalyticsData(response.data.data);
    } catch (error) {
      console.error('Error fetching analytics:', error);
    }
  };

  const fetchRecentOrders = async () => {
    try {
      const response = await ordersApi.getOrders({ limit: 5, sortBy: 'createdAt', sortOrder: 'desc' });
      setRecentOrders(response.data.data.orders);
    } catch (error) {
      console.error('Error fetching recent orders:', error);
    }
  };

  const fetchRecentUsers = async () => {
    try {
      const response = await usersApi.getUsers({ limit: 5, sortBy: 'createdAt', sortOrder: 'desc' });
      setRecentUsers(response.data.data.users);
    } catch (error) {
      console.error('Error fetching recent users:', error);
    }
  };

  const handleRefresh = () => {
    initializeDashboard();
  };

  const handlePeriodChange = (period) => {
    setSelectedPeriod(period);
    fetchAnalyticsData(period);
  };

  // Chart configurations
  const revenueChartData = {
    labels: analyticsData?.analytics?.map(item => item._id) || [],
    datasets: [
      {
        label: 'Revenue',
        data: analyticsData?.analytics?.map(item => item.revenue) || [],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.1
      },
      {
        label: 'Orders',
        data: analyticsData?.analytics?.map(item => item.orders) || [],
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        tension: 0.1,
        yAxisID: 'y1'
      }
    ]
  };

  const revenueChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    scales: {
      y: {
        type: 'linear',
        display: true,
        position: 'left',
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        grid: {
          drawOnChartArea: false,
        },
      },
    },
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Revenue & Orders Trend'
      }
    }
  };

  const orderStatusData = {
    labels: ['Pending', 'Confirmed', 'Processing', 'Shipped', 'Delivered', 'Cancelled'],
    datasets: [
      {
        data: dashboardData ? [
          dashboardData.overview?.orders?.pendingOrders || 0,
          dashboardData.overview?.orders?.confirmedOrders || 0,
          dashboardData.overview?.orders?.processingOrders || 0,
          dashboardData.overview?.orders?.shippedOrders || 0,
          dashboardData.overview?.orders?.deliveredOrders || 0,
          dashboardData.overview?.orders?.cancelledOrders || 0
        ] : [],
        backgroundColor: [
          '#ffa940',
          '#52c41a',
          '#1890ff',
          '#722ed1',
          '#13c2c2',
          '#f5222d'
        ]
      }
    ]
  };

  // Table columns
  const orderColumns = [
    {
      title: 'Order #',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      render: (text) => <Text strong>{text}</Text>
    },
    {
      title: 'Customer',
      dataIndex: 'customer',
      key: 'customer',
      render: (customer) => customer ? `${customer.firstName} ${customer.lastName}` : 'N/A'
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Badge 
          status={status === 'delivered' ? 'success' : status === 'cancelled' ? 'error' : 'processing'} 
          text={status.charAt(0).toUpperCase() + status.slice(1)} 
        />
      )
    },
    {
      title: 'Total',
      dataIndex: ['pricing', 'total'],
      key: 'total',
      render: (total) => `$${total?.toFixed(2) || '0.00'}`
    },
    {
      title: 'Date',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => dayjs(date).format('MMM DD, YYYY')
    }
  ];

  const userColumns = [
    {
      title: 'Name',
      key: 'name',
      render: (record) => `${record.firstName} ${record.lastName}`
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email'
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      render: (role) => (
        <Badge 
          color={role === 'admin' ? 'red' : role === 'vendor' ? 'blue' : 'green'} 
          text={role.charAt(0).toUpperCase() + role.slice(1)} 
        />
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Badge 
          status={status === 'active' ? 'success' : 'error'} 
          text={status.charAt(0).toUpperCase() + status.slice(1)} 
        />
      )
    },
    {
      title: 'Joined',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => dayjs(date).format('MMM DD, YYYY')
    }
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div style={{ padding: '16px', background: '#f0f2f5', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'flex-start',
        marginBottom: '24px',
        flexWrap: 'wrap',
        gap: '16px'
      }}>
        <div style={{ flex: 1, minWidth: '280px' }}>
          <AntTitle level={2} style={{ margin: 0, color: '#262626', fontSize: 'clamp(20px, 4vw, 28px)' }}>
            Dashboard
          </AntTitle>
          <Text type="secondary" style={{ fontSize: '14px', display: 'block', marginTop: '4px' }}>
            Welcome back, Rahul Raaj! Here's what's happening with your store.
          </Text>
        </div>
        <Space wrap>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            Last updated: {realTimeData?.timestamp ? dayjs(realTimeData.timestamp).format('HH:mm:ss') : 'Never'}
          </Text>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={handleRefresh}
            loading={loading}
            type="primary"
            size="small"
          >
            Refresh
          </Button>
        </Space>
      </div>

      {/* Installation Notice */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col span={24}>
          <Alert
            message="Demo Mode Active"
            description="This dashboard is showing demo data. Connect to your backend API and database for real-time functionality."
            type="info"
            showIcon
            closable
          />
        </Col>
      </Row>

      {/* Alerts */}
      {alerts.length > 0 && (
        <Row gutter={[16, 16]} className="mb-6">
          <Col span={24}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {alerts.map((alert, index) => (
                <Alert
                  key={index}
                  message={alert.message}
                  type={alert.type}
                  action={
                    <Button size="small" type="link">
                      {alert.action}
                    </Button>
                  }
                  closable
                />
              ))}
            </Space>
          </Col>
        </Row>
      )}

      {/* Key Metrics Row */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Revenue"
              value={dashboardData?.overview?.orders?.totalRevenue || 0}
              precision={2}
              prefix={<DollarOutlined />}
              suffix="USD"
              valueStyle={{ color: '#3f8600' }}
            />
            {realTimeData && (
              <Text type="secondary" className="text-xs">
                Today: ${realTimeData.todayRevenue?.toFixed(2) || '0.00'}
              </Text>
            )}
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Orders"
              value={dashboardData?.overview?.orders?.totalOrders || 0}
              prefix={<ShoppingCartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
            {realTimeData && (
              <Text type="secondary" className="text-xs">
                Today: {realTimeData.todayOrders || 0}
              </Text>
            )}
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Users"
              value={dashboardData?.overview?.users?.totalUsers || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
            {realTimeData && (
              <Text type="secondary" className="text-xs">
                Active: {realTimeData.activeUsers || 0}
              </Text>
            )}
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Vendors"
              value={vendorStats?.totalVendors || dashboardData?.overview?.vendors?.totalVendors || 0}
              prefix={<ShopOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
            <Text type="secondary" className="text-xs">
              Active: {vendorStats?.activeVendors || dashboardData?.overview?.vendors?.activeVendors || 0}
            </Text>
          </Card>
        </Col>
      </Row>

      {/* Additional Metrics Row */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Products"
              value={productStats?.totalProducts || 0}
              prefix={<ShoppingOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
            <Text type="secondary" className="text-xs">
              Active: {productStats?.activeProducts || 0}
            </Text>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Pending Approvals"
              value={pendingApprovals.length}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
            <Text type="secondary" className="text-xs">
              Products: {pendingApprovals.filter(p => p.type === 'product').length} |
              Vendors: {pendingApprovals.filter(p => p.type === 'vendor').length}
            </Text>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Low Stock Items"
              value={productStats?.lowStockProducts || 0}
              prefix={<WarningOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
            <Text type="secondary" className="text-xs">
              Requires attention
            </Text>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Commission Pending"
              value={vendorStats?.totalPendingCommission || 0}
              precision={2}
              prefix={<DollarOutlined />}
              suffix="USD"
              valueStyle={{ color: '#1890ff' }}
            />
            <Text type="secondary" className="text-xs">
              Awaiting payout
            </Text>
          </Card>
        </Col>
      </Row>

      {/* Charts Row */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} lg={16}>
          <Card 
            title="Revenue & Orders Trend" 
            extra={
              <Select 
                value={selectedPeriod} 
                onChange={handlePeriodChange}
                style={{ width: 120 }}
              >
                <Select.Option value="7d">Last 7 days</Select.Option>
                <Select.Option value="30d">Last 30 days</Select.Option>
                <Select.Option value="90d">Last 90 days</Select.Option>
                <Select.Option value="1y">Last year</Select.Option>
              </Select>
            }
          >
            <div style={{ height: '300px' }}>
              <Line data={revenueChartData} options={revenueChartOptions} />
            </div>
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="Order Status Distribution">
            <div style={{ height: '300px' }}>
              <Doughnut 
                data={orderStatusData} 
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom'
                    }
                  }
                }}
              />
            </div>
          </Card>
        </Col>
      </Row>

      {/* Pending Approvals Section */}
      {pendingApprovals.length > 0 && (
        <Row gutter={[16, 16]} className="mb-6">
          <Col span={24}>
            <Card
              title={
                <Space>
                  <ClockCircleOutlined />
                  Pending Approvals
                  <Badge count={pendingApprovals.length} />
                </Space>
              }
              extra={<Button type="link" icon={<EyeOutlined />}>View All</Button>}
            >
              <Table
                dataSource={pendingApprovals}
                pagination={false}
                size="small"
                rowKey="id"
                columns={[
                  {
                    title: 'Type',
                    dataIndex: 'type',
                    key: 'type',
                    render: (type) => (
                      <Tag color={type === 'product' ? 'blue' : 'green'}>
                        {type.toUpperCase()}
                      </Tag>
                    )
                  },
                  {
                    title: 'Name',
                    dataIndex: 'name',
                    key: 'name'
                  },
                  {
                    title: 'Details',
                    key: 'details',
                    render: (_, record) => (
                      <Text type="secondary">
                        {record.type === 'product' ? `by ${record.vendor}` : record.email}
                      </Text>
                    )
                  },
                  {
                    title: 'Submitted',
                    dataIndex: 'submittedAt',
                    key: 'submittedAt',
                    render: (date) => new Date(date).toLocaleDateString()
                  },
                  {
                    title: 'Actions',
                    key: 'actions',
                    render: (_, record) => (
                      <Space>
                        <Button
                          type="primary"
                          size="small"
                          onClick={() => {
                            if (record.type === 'product') {
                              window.location.href = '/admin/products/pending-approval';
                            } else {
                              window.location.href = '/admin/vendors/pending-verification';
                            }
                          }}
                        >
                          Review
                        </Button>
                      </Space>
                    )
                  }
                ]}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Tables Row */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card
            title="Recent Orders"
            extra={<Button type="link" icon={<EyeOutlined />}>View All</Button>}
          >
            <Table
              dataSource={recentOrders}
              columns={orderColumns}
              pagination={false}
              size="small"
              rowKey="_id"
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card
            title="Recent Users"
            extra={<Button type="link" icon={<EyeOutlined />}>View All</Button>}
          >
            <Table
              dataSource={recentUsers}
              columns={userColumns}
              pagination={false}
              size="small"
              rowKey="_id"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ResponsiveAdminDashboard;