import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, InputNumber, Row, Col, Typography, Space, Tag, Button, Tooltip, Switch } from 'antd';
import { PlusOutlined, DeleteOutlined, GlobalOutlined, DollarCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { validateMultiCurrencyPrices } from '../../utils/priceValidation';
import { SUPPORTED_CURRENCIES, getCurrencyInfo, DEFAULT_CURRENCY } from '../../constants/currencies';

const { Title, Text } = Typography;

const MultiCurrencyPriceInput = React.memo(({
  value = {},
  onChange,
  required = false,
  disabled = false,
  showAdvanced = false
}) => {
  const [prices, setPrices] = useState({ [DEFAULT_CURRENCY]: { basePrice: 0 } });
  const [enabledCurrencies, setEnabledCurrencies] = useState([DEFAULT_CURRENCY]);
  const [showMultiCurrency, setShowMultiCurrency] = useState(false);
  const [initialized, setInitialized] = useState(false);

  // Initialize state from props only once
  useEffect(() => {
    if (!initialized && value && typeof value === 'object' && Object.keys(value).length > 0) {
      let newPrices = { [DEFAULT_CURRENCY]: { basePrice: 0 } };
      let currencies = [DEFAULT_CURRENCY];
      let multiCurrency = false;

      // Handle both old format (basePrice, salePrice) and new multi-currency format
      if (value.basePrice !== undefined || value.salePrice !== undefined) {
        // Old format - convert to multi-currency
        newPrices = {
          [DEFAULT_CURRENCY]: {
            basePrice: value.basePrice || 0,
            salePrice: value.salePrice || undefined
          }
        };
        
        if (value.multiCurrency && typeof value.multiCurrency === 'object') {
          Object.keys(value.multiCurrency).forEach(currency => {
            if (value.multiCurrency[currency]?.basePrice) {
              newPrices[currency] = {
                basePrice: value.multiCurrency[currency].basePrice,
                salePrice: value.multiCurrency[currency].salePrice || undefined
              };
            }
          });
        }
        
        currencies = Object.keys(newPrices);
        multiCurrency = currencies.length > 1;
      } else if (Object.keys(value).some(key => value[key]?.basePrice)) {
        // New multi-currency format
        newPrices = { ...value };
        currencies = Object.keys(value).filter(key => value[key]?.basePrice);
        multiCurrency = currencies.length > 1;
      }

      setPrices(newPrices);
      setEnabledCurrencies(currencies.length > 0 ? currencies : [DEFAULT_CURRENCY]);
      setShowMultiCurrency(multiCurrency);
      setInitialized(true);
    } else if (!initialized) {
      setInitialized(true);
    }
  }, [value, initialized]);

  // Memoized callback for onChange to prevent infinite re-renders
  const handlePriceUpdate = useCallback((newPrices) => {
    if (onChange && initialized) {
      const primaryCurrency = newPrices.INR || newPrices[Object.keys(newPrices)[0]];
      const result = {
        basePrice: primaryCurrency?.basePrice || 0,
        salePrice: primaryCurrency?.salePrice,
        currency: 'INR',
        multiCurrency: newPrices
      };
      onChange(result);
    }
  }, [onChange, initialized]);

  // Update parent when prices change with debouncing
  useEffect(() => {
    if (initialized) {
      const timeoutId = setTimeout(() => {
        handlePriceUpdate(prices);
      }, 100);
      return () => clearTimeout(timeoutId);
    }
  }, [prices, initialized, handlePriceUpdate]);

  // Utilize utility functions for better modularity
  const handlePriceChange = useCallback((currency, type, value) => {
    setPrices(prev => ({
      ...prev,
      [currency]: {
        ...prev[currency],
        [type]: value || undefined
      }
    }));
  }, []);

  const addCurrency = useCallback((currency) => {
    if (!enabledCurrencies.includes(currency)) {
      setEnabledCurrencies(prev => [...prev, currency]);
      setPrices(prev => ({
        ...prev,
        [currency]: { basePrice: 0 }
      }));
      setShowMultiCurrency(true);
    }
  }, [enabledCurrencies]);

  const removeCurrency = useCallback((currency) => {
    if (currency === DEFAULT_CURRENCY) return; // Cannot remove default currency
    
    setEnabledCurrencies(prev => prev.filter(c => c !== currency));
    setPrices(prev => {
      const newPrices = { ...prev };
      delete newPrices[currency];
      return newPrices;
    });
    
    setEnabledCurrencies(prev => {
      if (prev.length <= 2) {
        setShowMultiCurrency(false);
      }
      return prev;
    });
  }, []);

  // Memoized helper functions - use the imported getCurrencyInfo
  const validatePrice = useCallback((currency, basePrice, salePrice) => {
    const errors = [];
    if (required && (!basePrice || basePrice <= 0)) {
      errors.push(`${currency} base price is required`);
    }
    if (salePrice && basePrice && salePrice >= basePrice) {
      errors.push(`${currency} sale price must be less than base price`);
    }
    return errors;
  }, [required]);

  // Memoized available currencies to add
  const availableToAdd = useMemo(() => {
    return SUPPORTED_CURRENCIES.filter(c => !enabledCurrencies.includes(c.code));
  }, [enabledCurrencies]);

  const renderCurrencyInput = (currency) => {
    const currencyInfo = getCurrencyInfo(currency);
    const currentPrices = prices[currency] || {};
    const errors = validatePrice(currency, currentPrices.basePrice, currentPrices.salePrice);

    return (
      <Card
        key={currency}
        size="small"
        title={
          <Space>
            <Tag color={currency === 'INR' ? 'green' : 'blue'}>
              {currencyInfo.symbol} {currency}
            </Tag>
            <Text type="secondary">{currencyInfo.name}</Text>
            {currency === 'INR' && <Tag color="gold">Default</Tag>}
          </Space>
        }
        extra={
          currency !== 'INR' && (
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={() => removeCurrency(currency)}
              disabled={disabled}
            />
          )
        }
        style={{ marginBottom: 16 }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <div style={{ marginBottom: 8 }}>
              <Text strong>Base Price *</Text>
            </div>
            <InputNumber
              placeholder="Enter base price"
              value={currentPrices.basePrice}
              onChange={(value) => handlePriceChange(currency, 'basePrice', value)}
              min={0}
              step={0.01}
              style={{ width: '100%' }}
              disabled={disabled}
              status={errors.some(e => e.includes('base price')) ? 'error' : ''}
              addonBefore={currencyInfo.symbol}
            />
          </Col>
          <Col span={12}>
            <div style={{ marginBottom: 8 }}>
              <Space>
                <Text strong>Sale Price</Text>
                <Tooltip title="Optional. Must be less than base price">
                  <InfoCircleOutlined style={{ color: '#999' }} />
                </Tooltip>
              </Space>
            </div>
            <InputNumber
              placeholder="Enter sale price"
              value={currentPrices.salePrice}
              onChange={(value) => handlePriceChange(currency, 'salePrice', value)}
              min={0}
              step={0.01}
              style={{ width: '100%' }}
              disabled={disabled}
              status={errors.some(e => e.includes('sale price')) ? 'error' : ''}
              addonBefore={currencyInfo.symbol}
            />
          </Col>
        </Row>
        {errors.length > 0 && (
          <div style={{ marginTop: 8 }}>
            {errors.map((error, index) => (
              <Text key={index} type="danger" style={{ fontSize: '12px', display: 'block' }}>
                {error}
              </Text>
            ))}
          </div>
        )}
      </Card>
    );
  };

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Title level={5} style={{ margin: 0 }}>
            <GlobalOutlined /> Product Pricing
          </Title>
          {showAdvanced && (
            <Switch
              checked={showMultiCurrency}
              onChange={setShowMultiCurrency}
              checkedChildren="Multi-Currency"
              unCheckedChildren="Single Currency"
              disabled={disabled}
            />
          )}
        </Space>
      </div>

      {/* Default INR Currency - Always visible */}
      {renderCurrencyInput('INR')}

      {/* Multi-currency section */}
      {(showMultiCurrency || enabledCurrencies.length > 1) && (
        <div>
          {enabledCurrencies
            .filter(currency => currency !== 'INR')
            .map(currency => renderCurrencyInput(currency))}

          {/* Add currency buttons */}
          {availableToAdd.length > 0 && !disabled && (
            <div style={{ marginTop: 16 }}>
              <Text strong style={{ marginRight: 16 }}>Add Currency:</Text>
              <Space wrap>
                {availableToAdd.map(currency => (
                  <Button
                    key={currency.code}
                    type="dashed"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={() => addCurrency(currency.code)}
                  >
                    {currency.symbol} {currency.code}
                  </Button>
                ))}
              </Space>
            </div>
          )}
        </div>
      )}

      {/* Pricing summary for enabled currencies */}
      {enabledCurrencies.length > 1 && (
        <Card
          title={
            <Space>
              <DollarCircleOutlined />
              <Text>Pricing Summary</Text>
            </Space>
          }
          size="small"
          style={{ marginTop: 16, backgroundColor: '#fafafa' }}
        >
          <Row gutter={16}>
            {enabledCurrencies.map(currency => {
              const currencyInfo = getCurrencyInfo(currency);
              const currentPrices = prices[currency] || {};
              const currentPrice = currentPrices.salePrice || currentPrices.basePrice;
              
              return (
                <Col key={currency} span={8}>
                  <div style={{ textAlign: 'center' }}>
                    <Text type="secondary">{currency}</Text>
                    <div>
                      <Text strong>
                        {currencyInfo.symbol}{currentPrice?.toFixed(2) || '0.00'}
                      </Text>
                    </div>
                    {currentPrices.salePrice && currentPrices.basePrice && (
                      <Text type="success" style={{ fontSize: '12px' }}>
                        {Math.round(((currentPrices.basePrice - currentPrices.salePrice) / currentPrices.basePrice) * 100)}% off
                      </Text>
                    )}
                  </div>
                </Col>
              );
            })}
          </Row>
        </Card>
      )}
    </div>
  );
});

export default MultiCurrencyPriceInput;
