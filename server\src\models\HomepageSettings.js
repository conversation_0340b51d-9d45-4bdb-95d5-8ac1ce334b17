const mongoose = require('mongoose');

const homepageSettingsSchema = new mongoose.Schema({
  // Carousel Images Section
  carouselImages: [{
    title: {
      type: String,
      trim: true,
      maxlength: [100, 'Title cannot exceed 100 characters']
    },
    description: {
      type: String,
      trim: true,
      maxlength: [200, 'Description cannot exceed 200 characters']
    },
    imageUrl: {
      type: String,
      required: true,
      validate: {
        validator: function(v) {
          // Validate that it's a valid URL (basic validation)
          return /^https?:\/\/.+/.test(v);
        },
        message: 'Image URL must be a valid HTTP/HTTPS URL'
      }
    },
    linkUrl: {
      type: String,
      trim: true
    },
    isActive: {
      type: Boolean,
      default: true
    },
    sortOrder: {
      type: Number,
      default: 0
    }
  }],

  // Featured Categories Section
  featuredCategories: [{
    category: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Category',
      required: true
    },
    displayName: {
      type: String,
      trim: true,
      maxlength: [50, 'Display name cannot exceed 50 characters']
    },
    imageUrl: {
      type: String,
      required: true
    },
    cloudinaryPublicId: {
      type: String,
      required: true
    },
    isActive: {
      type: Boolean,
      default: true
    },
    sortOrder: {
      type: Number,
      default: 0
    }
  }],

  // Promotion Images Section (simplified to only title and image)
  promotionImages: [{
    title: {
      type: String,
      trim: true,
      maxlength: [100, 'Title cannot exceed 100 characters']
    },
    imageUrl: {
      type: String,
      required: true,
      validate: {
        validator: function(v) {
          // Validate that it's a valid URL (basic validation)
          return /^https?:\/\/.+/.test(v);
        },
        message: 'Image URL must be a valid HTTP/HTTPS URL'
      }
    },
    position: {
      type: String,
      enum: ['sidebar', 'banner', 'popup', 'footer'],
      default: 'sidebar'
    },
    isActive: {
      type: Boolean,
      default: true
    },
    sortOrder: {
      type: Number,
      default: 0
    }
  }],

  // Featured Category IDs (simple array of category IDs)
  featuredCategoryIds: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category'
  }],

  // All Categories Modal Configuration
  allCategoriesModal: {
    // Main categories grid (6 columns)
    mainCategories: [{
      name: {
        type: String,
        required: true,
        trim: true,
        maxlength: [50, 'Category name cannot exceed 50 characters']
      },
      icon: {
        type: String,
        required: true,
        trim: true
      },
      isActive: {
        type: Boolean,
        default: true
      },
      sortOrder: {
        type: Number,
        default: 0
      },
      linkUrl: {
        type: String,
        trim: true,
        default: ''
      }
    }],
    // Popular categories (5 per row, 10 total)
    popularCategories: [{
      name: {
        type: String,
        required: true,
        trim: true,
        maxlength: [50, 'Category name cannot exceed 50 characters']
      },
      isActive: {
        type: Boolean,
        default: true
      },
      sortOrder: {
        type: Number,
        default: 0
      },
      linkUrl: {
        type: String,
        trim: true,
        default: ''
      }
    }]
  },

  // General Settings
  settings: {
    autoPlayCarousel: {
      type: Boolean,
      default: true
    },
    carouselSpeed: {
      type: Number,
      default: 3000,
      min: 1000,
      max: 10000
    },
    showPromotions: {
      type: Boolean,
      default: true
    },
    maxCarouselImages: {
      type: Number,
      default: 10,
      min: 1,
      max: 20
    },
    maxPromotionImages: {
      type: Number,
      default: 5,
      min: 1,
      max: 10
    }
  },

  // Metadata
  lastUpdatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
homepageSettingsSchema.index({ 'carouselImages.isActive': 1 });
homepageSettingsSchema.index({ 'carouselImages.sortOrder': 1 });
homepageSettingsSchema.index({ 'featuredCategories.isActive': 1 });
homepageSettingsSchema.index({ 'featuredCategories.sortOrder': 1 });
homepageSettingsSchema.index({ 'promotionImages.isActive': 1 });
homepageSettingsSchema.index({ 'promotionImages.position': 1 });
homepageSettingsSchema.index({ 'promotionImages.startDate': 1, 'promotionImages.endDate': 1 });

// Virtual for active carousel images
homepageSettingsSchema.virtual('activeCarouselImages').get(function() {
  return this.carouselImages
    .filter(img => img.isActive)
    .sort((a, b) => a.sortOrder - b.sortOrder);
});

// Virtual for active featured categories
homepageSettingsSchema.virtual('activeFeaturedCategories').get(function() {
  return this.featuredCategories
    .filter(cat => cat.isActive)
    .sort((a, b) => a.sortOrder - b.sortOrder);
});

// Virtual for active promotion images by position
homepageSettingsSchema.virtual('activePromotionsByPosition').get(function() {
  const now = new Date();
  return this.promotionImages
    .filter(promo => {
      if (!promo.isActive) return false;
      if (promo.startDate && promo.startDate > now) return false;
      if (promo.endDate && promo.endDate < now) return false;
      return true;
    })
    .reduce((acc, promo) => {
      if (!acc[promo.position]) acc[promo.position] = [];
      acc[promo.position].push(promo);
      return acc;
    }, {});
});

// Virtual for active main categories
homepageSettingsSchema.virtual('activeMainCategories').get(function() {
  if (!this.allCategoriesModal || !this.allCategoriesModal.mainCategories) return [];
  return this.allCategoriesModal.mainCategories
    .filter(cat => cat.isActive)
    .sort((a, b) => a.sortOrder - b.sortOrder);
});

// Virtual for active popular categories
homepageSettingsSchema.virtual('activePopularCategories').get(function() {
  if (!this.allCategoriesModal || !this.allCategoriesModal.popularCategories) return [];
  return this.allCategoriesModal.popularCategories
    .filter(cat => cat.isActive)
    .sort((a, b) => a.sortOrder - b.sortOrder);
});

// Static method to get or create homepage settings
homepageSettingsSchema.statics.getSettings = async function() {
  let settings = await this.findOne();
  if (!settings) {
    settings = await this.create({
      carouselImages: [],
      featuredCategories: [],
      promotionImages: [],
      settings: {}
    });
  }
  return settings;
};

// Instance method to add carousel image
homepageSettingsSchema.methods.addCarouselImage = function(imageData) {
  this.carouselImages.push({
    ...imageData,
    sortOrder: this.carouselImages.length
  });
  return this.save();
};

// Instance method to add promotion image
homepageSettingsSchema.methods.addPromotionImage = function(imageData) {
  this.promotionImages.push({
    ...imageData,
    sortOrder: this.promotionImages.length
  });
  return this.save();
};

// Instance method to add featured category
homepageSettingsSchema.methods.addFeaturedCategory = function(categoryData) {
  this.featuredCategories.push({
    ...categoryData,
    sortOrder: this.featuredCategories.length
  });
  return this.save();
};

// Instance method to add main category to all categories modal
homepageSettingsSchema.methods.addMainCategory = function(categoryData) {
  if (!this.allCategoriesModal) {
    this.allCategoriesModal = { mainCategories: [], popularCategories: [] };
  }
  if (!this.allCategoriesModal.mainCategories) {
    this.allCategoriesModal.mainCategories = [];
  }
  
  this.allCategoriesModal.mainCategories.push({
    ...categoryData,
    sortOrder: this.allCategoriesModal.mainCategories.length
  });
  return this.save();
};

// Instance method to add popular category to all categories modal
homepageSettingsSchema.methods.addPopularCategory = function(categoryData) {
  if (!this.allCategoriesModal) {
    this.allCategoriesModal = { mainCategories: [], popularCategories: [] };
  }
  if (!this.allCategoriesModal.popularCategories) {
    this.allCategoriesModal.popularCategories = [];
  }
  
  this.allCategoriesModal.popularCategories.push({
    ...categoryData,
    sortOrder: this.allCategoriesModal.popularCategories.length
  });
  return this.save();
};

// Instance method to initialize default categories
homepageSettingsSchema.methods.initializeDefaultCategories = function() {
  if (!this.allCategoriesModal) {
    this.allCategoriesModal = {
      mainCategories: [
        { name: 'Apparel & Accessories', icon: 'ShopOutlined', isActive: true, sortOrder: 0, linkUrl: '' },
        { name: 'Consumer Electronics', icon: 'MobileOutlined', isActive: true, sortOrder: 1, linkUrl: '' },
        { name: 'Sports & Entertainment', icon: 'CrownOutlined', isActive: true, sortOrder: 2, linkUrl: '' },
        { name: 'Jewelry, Eyewear & Watches', icon: 'HeartOutlined', isActive: true, sortOrder: 3, linkUrl: '' },
        { name: 'Shoes & Accessories', icon: 'CarOutlined', isActive: true, sortOrder: 4, linkUrl: '' },
        { name: 'Home & Garden', icon: 'HomeOutlined', isActive: true, sortOrder: 5, linkUrl: '' },
        { name: 'Beauty', icon: 'SkinOutlined', isActive: true, sortOrder: 6, linkUrl: '' },
        { name: 'Luggage, Bags & Cases', icon: 'ShoppingOutlined', isActive: true, sortOrder: 7, linkUrl: '' },
        { name: 'Packaging & Printing', icon: 'PrinterOutlined', isActive: true, sortOrder: 8, linkUrl: '' },
        { name: 'Parents, Kids & Toys', icon: 'BugOutlined', isActive: true, sortOrder: 9, linkUrl: '' },
        { name: 'Personal Care & Home Care', icon: 'SmileOutlined', isActive: true, sortOrder: 10, linkUrl: '' },
        { name: 'Health & Medical', icon: 'MedicineBoxOutlined', isActive: true, sortOrder: 11, linkUrl: '' },
        { name: 'Gifts & Crafts', icon: 'GiftOutlined', isActive: true, sortOrder: 12, linkUrl: '' },
        { name: 'Furniture', icon: 'TableOutlined', isActive: true, sortOrder: 13, linkUrl: '' },
        { name: 'Lights & Lighting', icon: 'BulbOutlined', isActive: true, sortOrder: 14, linkUrl: '' },
        { name: 'Home Appliances', icon: 'SettingOutlined', isActive: true, sortOrder: 15, linkUrl: '' },
        { name: 'Safety & Security', icon: 'SafetyOutlined', isActive: true, sortOrder: 16, linkUrl: '' },
        { name: 'View All', icon: 'AppstoreOutlined', isActive: true, sortOrder: 17, linkUrl: '' }
      ],
      popularCategories: [
        { name: 'Consumer Electronics', isActive: true, sortOrder: 0, linkUrl: '' },
        { name: 'Apparel & Accessories', isActive: true, sortOrder: 1, linkUrl: '' },
        { name: 'Home & Garden', isActive: true, sortOrder: 2, linkUrl: '' },
        { name: 'Sports & Entertainment', isActive: true, sortOrder: 3, linkUrl: '' },
        { name: 'Beauty', isActive: true, sortOrder: 4, linkUrl: '' },
        { name: 'Jewelry, Eyewear & Watches', isActive: true, sortOrder: 5, linkUrl: '' },
        { name: 'Shoes & Accessories', isActive: true, sortOrder: 6, linkUrl: '' },
        { name: 'Health & Medical', isActive: true, sortOrder: 7, linkUrl: '' },
        { name: 'Furniture', isActive: true, sortOrder: 8, linkUrl: '' },
        { name: 'Home Appliances', isActive: true, sortOrder: 9, linkUrl: '' }
      ]
    };
  }
  return this.save();
};

const HomepageSettings = mongoose.model('HomepageSettings', homepageSettingsSchema);

module.exports = HomepageSettings;
