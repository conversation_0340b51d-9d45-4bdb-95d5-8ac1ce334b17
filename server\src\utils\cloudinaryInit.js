const { validateCloudinaryConfig } = require('../config/cloudinary');

/**
 * Initialize and validate Cloudinary configuration
 */
const initializeCloudinary = () => {
  console.log('🔧 Initializing Cloudinary configuration...');
  
  const isConfigured = validateCloudinaryConfig();
  
  if (isConfigured) {
    console.log('✅ Cloudinary configuration is valid');
    console.log('📁 Image uploads will be stored in Cloudinary');
    console.log('📷 Automatic image optimization enabled');
  } else {
    console.warn('⚠️  Cloudinary configuration is missing or incomplete');
    console.warn('📁 Image uploads will fall back to local storage');
    console.warn('💡 To enable Cloudinary, set the following environment variables:');
    console.warn('   - CLOUDINARY_CLOUD_NAME');
    console.warn('   - CLOUDINARY_API_KEY');
    console.warn('   - CLOUDINARY_API_SECRET');
  }
  
  return isConfigured;
};

/**
 * Get Cloudinary setup instructions
 */
const getCloudinaryInstructions = () => {
  return {
    message: 'To set up Cloudinary for image uploads:',
    steps: [
      '1. Create a free account at https://cloudinary.com',
      '2. Go to your dashboard and copy the credentials',
      '3. Add these to your .env file:',
      '   CLOUDINARY_CLOUD_NAME=your_cloud_name',
      '   CLOUDINARY_API_KEY=your_api_key',
      '   CLOUDINARY_API_SECRET=your_api_secret',
      '4. Restart your server'
    ],
    benefits: [
      '• Automatic image optimization and compression',
      '• CDN delivery for faster loading',
      '• Multiple image format support (WebP, AVIF)',
      '• Automatic responsive image generation',
      '• Cloud storage with high availability'
    ]
  };
};

module.exports = {
  initializeCloudinary,
  getCloudinaryInstructions
};
