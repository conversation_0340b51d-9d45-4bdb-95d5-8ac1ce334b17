/* Auth Page Styles */
.auth-tabs .ant-tabs-nav {
  margin-bottom: 2rem !important;
}

.auth-tabs .ant-tabs-tab {
  padding: 12px 24px !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
  margin: 0 8px !important;
  transition: all 0.3s ease !important;
}

.auth-tabs .ant-tabs-tab:hover {
  color: #ff6b35 !important;
}

.auth-tabs .ant-tabs-tab-active {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%) !important;
  color: white !important;
  border: none !important;
}

.auth-tabs .ant-tabs-tab-active:hover {
  color: white !important;
}

.auth-tabs .ant-tabs-ink-bar {
  display: none !important;
}

/* Auth Segment Styles */
.auth-segment .ant-segmented-item {
  padding: 12px 24px !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

.auth-segment .ant-segmented-item-selected {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%) !important;
  color: white !important;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3) !important;
}

/* Auth Input Styles */
.auth-input.ant-input-affix-wrapper {
  padding: 12px 16px !important;
  font-size: 16px !important;
}

.auth-input.ant-input-affix-wrapper:focus,
.auth-input.ant-input-affix-wrapper-focused {
  border-color: #ff6b35 !important;
  box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.1) !important;
}

.auth-input.ant-input-password {
  padding: 12px 16px !important;
  font-size: 16px !important;
}

/* Auth Button Styles */
.auth-button.ant-btn {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
}

.auth-button.primary:hover {
  background: linear-gradient(135deg, #e55a2b 0%, #e0841a 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4) !important;
}

.auth-button.secondary:hover {
  background: #ff6b35 !important;
  color: white !important;
  transform: translateY(-1px) !important;
}

.auth-button.ghost:hover {
  border-color: #ff6b35 !important;
  color: #ff6b35 !important;
  transform: translateY(-1px) !important;
}

/* Form Styles */
.ant-form-item {
  margin-bottom: 1.5rem !important;
}

.ant-form-item-label > label {
  font-weight: 600 !important;
  color: #374151 !important;
}

.ant-checkbox-wrapper {
  font-size: 14px !important;
}

.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #ff6b35 !important;
  border-color: #ff6b35 !important;
}

.ant-checkbox:hover .ant-checkbox-inner {
  border-color: #ff6b35 !important;
}

/* Select Styles */
.ant-select-selector {
  border-radius: 8px !important;
  border: 1px solid #d9d9d9 !important;
  padding: 8px 12px !important;
  height: 48px !important;
}

.ant-select-focused .ant-select-selector {
  border-color: #ff6b35 !important;
  box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.1) !important;
}

/* Divider Styles */
.ant-divider-horizontal.ant-divider-with-text {
  margin: 24px 0 !important;
}

.ant-divider-inner-text {
  color: #6b7280 !important;
  font-size: 14px !important;
}

/* Result Styles */
.ant-result-title {
  color: #374151 !important;
  font-weight: 700 !important;
}

.ant-result-subtitle {
  color: #6b7280 !important;
}

/* Responsive Styles */
@media (max-width: 640px) {
  .auth-tabs .ant-tabs-tab {
    padding: 10px 16px !important;
    margin: 0 4px !important;
    font-size: 14px !important;
  }
  
  .auth-segment .ant-segmented-item {
    padding: 10px 16px !important;
    font-size: 14px !important;
  }
  
  .auth-input.ant-input-affix-wrapper,
  .auth-input.ant-input-password {
    padding: 10px 14px !important;
    font-size: 14px !important;
  }
  
  .auth-button.ant-btn {
    height: 44px !important;
    font-size: 14px !important;
  }
}

/* Loading States */
.ant-btn-loading-icon {
  margin-right: 8px !important;
}

/* Animation for smooth transitions */
.auth-input,
.auth-button,
.auth-segment {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Focus states for accessibility */
.auth-button:focus-visible {
  outline: 2px solid #ff6b35 !important;
  outline-offset: 2px !important;
}

.auth-input:focus-visible {
  outline: 2px solid #ff6b35 !important;
  outline-offset: 2px !important;
}