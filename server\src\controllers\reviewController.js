const Review = require('../models/Review');
const ReviewReply = require('../models/ReviewReply');
const Product = require('../models/Product');
const User = require('../models/User');
const mongoose = require('mongoose');

class ReviewController {
  // Create a new review (Customer only)
  async createReview(req, res) {
    try {
      const { productId, rating, comment } = req.body;
      const customerId = req.user._id || req.user.userId;

      // Enhanced input validation
      if (!productId || !rating || !comment) {
        return res.status(400).json({
          success: false,
          message: 'Product ID, rating, and comment are required'
        });
      }

      if (rating < 1 || rating > 5) {
        return res.status(400).json({
          success: false,
          message: 'Rating must be between 1 and 5'
        });
      }

      if (comment.trim().length < 10) {
        return res.status(400).json({
          success: false,
          message: 'Review comment must be at least 10 characters long'
        });
      }

      // Validate user is a customer
      const customer = await User.findById(customerId);
      if (!customer || customer.userType !== 'customer') {
        return res.status(403).json({
          success: false,
          message: 'Only customers can write reviews'
        });
      }

      // Check if product exists and is active
      const product = await Product.findById(productId).populate('vendor');
      if (!product) {
        return res.status(404).json({
          success: false,
          message: 'Product not found'
        });
      }

      if (!product.vendor) {
        return res.status(400).json({
          success: false,
          message: 'Product vendor information is missing'
        });
      }

      // Check if customer can review this product
      const canReview = await Review.canCustomerReview(customerId, productId);
      if (!canReview) {
        return res.status(400).json({
          success: false,
          message: 'You have already reviewed this product'
        });
      }

      // Create the review
      const review = new Review({
        product: productId,
        customer: customerId,
        vendor: product.vendor._id,
        rating: parseInt(rating),
        comment: comment.trim()
      });

      await review.save();

      // Update product rating
      const productRating = await Review.getProductRating(productId);
      await Product.findByIdAndUpdate(productId, {
        'reviews.averageRating': productRating.averageRating,
        'reviews.totalReviews': productRating.totalReviews,
        'reviews.ratingDistribution': productRating.ratingDistribution
      });

      // Populate the review for response
      await review.populate('customer', 'firstName lastName avatar');
      await review.populate('product', 'name');

      res.status(201).json({
        success: true,
        message: 'Review created successfully',
        data: {
          review,
          productRating
        }
      });

    } catch (error) {
      console.error('Create review error:', error);
      
      // Handle duplicate key error (customer already reviewed)
      if (error.code === 11000) {
        return res.status(400).json({
          success: false,
          message: 'You have already reviewed this product'
        });
      }
      
      if (error.name === 'ValidationError') {
        const errors = Object.values(error.errors).map(err => err.message);
        return res.status(400).json({
          success: false,
          message: 'Validation error',
          errors
        });
      }

      if (error.name === 'CastError') {
        return res.status(400).json({
          success: false,
          message: 'Invalid product ID format'
        });
      }

      res.status(500).json({
        success: false,
        message: 'Failed to create review'
      });
    }
  }

  // Get reviews for a product
  async getProductReviews(req, res) {
    try {
      const { productId } = req.params;
      const { page = 1, limit = 10, sortBy = 'newest' } = req.query;

      const skip = (page - 1) * limit;
      let sortOption = { createdAt: -1 };

      switch (sortBy) {
        case 'oldest':
          sortOption = { createdAt: 1 };
          break;
        case 'highest-rating':
          sortOption = { rating: -1, createdAt: -1 };
          break;
        case 'lowest-rating':
          sortOption = { rating: 1, createdAt: -1 };
          break;
        default:
          sortOption = { createdAt: -1 };
      }

      const reviews = await Review.find({ 
        product: productId, 
        status: 'active' 
      })
        .populate('customer', 'firstName lastName avatar')
        .populate('replies')
        .sort(sortOption)
        .skip(skip)
        .limit(parseInt(limit));

      const totalReviews = await Review.countDocuments({ 
        product: productId, 
        status: 'active' 
      });

      const productRating = await Review.getProductRating(productId);

      res.json({
        success: true,
        data: {
          reviews,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(totalReviews / limit),
            totalReviews,
            hasNextPage: page * limit < totalReviews,
            hasPrevPage: page > 1
          },
          rating: productRating
        }
      });

    } catch (error) {
      console.error('Get product reviews error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch reviews'
      });
    }
  }

  // Get reviews for vendor dashboard
  async getVendorReviews(req, res) {
    try {
      const vendorId = req.user._id || req.user.userId;
      const { page = 1, limit = 10, status = 'active' } = req.query;

      // Validate user is a vendor
      const vendor = await User.findById(vendorId);
      if (!vendor || vendor.userType !== 'vendor') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Vendor account required.'
        });
      }

      const reviews = await Review.getVendorReviews(vendorId, {
        page: parseInt(page),
        limit: parseInt(limit),
        status
      });

      const totalReviews = await Review.countDocuments({ 
        vendor: vendorId, 
        status 
      });

      res.json({
        success: true,
        data: {
          reviews,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(totalReviews / limit),
            totalReviews,
            hasNextPage: page * limit < totalReviews,
            hasPrevPage: page > 1
          }
        }
      });

    } catch (error) {
      console.error('Get vendor reviews error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch vendor reviews'
      });
    }
  }

  // Reply to a review (Vendor only)
  async replyToReview(req, res) {
    try {
      const { reviewId } = req.params;
      const { message } = req.body;
      const vendorId = req.user._id || req.user.userId;

      // Validate user is a vendor
      const vendor = await User.findById(vendorId);
      if (!vendor || vendor.userType !== 'vendor') {
        return res.status(403).json({
          success: false,
          message: 'Only vendors can reply to reviews'
        });
      }

      // Check if review exists and belongs to vendor's product
      const review = await Review.findById(reviewId).populate('product');
      if (!review) {
        return res.status(404).json({
          success: false,
          message: 'Review not found'
        });
      }

      // Verify vendor owns the product
      if (review.vendor.toString() !== vendorId) {
        return res.status(403).json({
          success: false,
          message: 'You can only reply to reviews on your products'
        });
      }

      // Check if vendor already replied
      const existingReply = await ReviewReply.findOne({ 
        review: reviewId, 
        vendor: vendorId 
      });
      
      if (existingReply) {
        return res.status(400).json({
          success: false,
          message: 'You have already replied to this review'
        });
      }

      // Create the reply
      const reply = new ReviewReply({
        review: reviewId,
        vendor: vendorId,
        message: message.trim()
      });

      await reply.save();

      // Add reply reference to the review
      review.replies.push(reply._id);
      await review.save();

      // Populate reply for response
      await reply.populate('vendor', 'businessName avatar');

      res.status(201).json({
        success: true,
        message: 'Reply posted successfully',
        data: reply
      });

    } catch (error) {
      console.error('Reply to review error:', error);
      
      if (error.name === 'ValidationError') {
        const errors = Object.values(error.errors).map(err => err.message);
        return res.status(400).json({
          success: false,
          message: 'Validation error',
          errors
        });
      }

      res.status(500).json({
        success: false,
        message: 'Failed to post reply'
      });
    }
  }

  // Update review reply (Vendor only)
  async updateReply(req, res) {
    try {
      const { replyId } = req.params;
      const { message } = req.body;
      const vendorId = req.user._id || req.user.userId;

      const reply = await ReviewReply.findById(replyId);
      if (!reply) {
        return res.status(404).json({
          success: false,
          message: 'Reply not found'
        });
      }

      // Verify vendor owns the reply
      if (reply.vendor.toString() !== vendorId) {
        return res.status(403).json({
          success: false,
          message: 'You can only edit your own replies'
        });
      }

      reply.message = message.trim();
      await reply.save();

      await reply.populate('vendor', 'businessName avatar');

      res.json({
        success: true,
        message: 'Reply updated successfully',
        data: reply
      });

    } catch (error) {
      console.error('Update reply error:', error);
      
      if (error.name === 'ValidationError') {
        const errors = Object.values(error.errors).map(err => err.message);
        return res.status(400).json({
          success: false,
          message: 'Validation error',
          errors
        });
      }

      res.status(500).json({
        success: false,
        message: 'Failed to update reply'
      });
    }
  }

  // Delete review reply (Vendor only)
  async deleteReply(req, res) {
    try {
      const { replyId } = req.params;
      const vendorId = req.user._id || req.user.userId;

      const reply = await ReviewReply.findById(replyId);
      if (!reply) {
        return res.status(404).json({
          success: false,
          message: 'Reply not found'
        });
      }

      // Verify vendor owns the reply
      if (reply.vendor.toString() !== vendorId) {
        return res.status(403).json({
          success: false,
          message: 'You can only delete your own replies'
        });
      }

      // Remove reply reference from review
      await Review.findByIdAndUpdate(reply.review, {
        $pull: { replies: replyId }
      });

      // Delete the reply
      await ReviewReply.findByIdAndDelete(replyId);

      res.json({
        success: true,
        message: 'Reply deleted successfully'
      });

    } catch (error) {
      console.error('Delete reply error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete reply'
      });
    }
  }

  // Get customer's reviews
  async getCustomerReviews(req, res) {
    try {
      const customerId = req.user._id || req.user.userId;
      const { page = 1, limit = 10 } = req.query;

      const skip = (page - 1) * limit;

      const reviews = await Review.find({ 
        customer: customerId, 
        status: 'active' 
      })
        .populate('product', 'name images')
        .populate('replies')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit));

      const totalReviews = await Review.countDocuments({ 
        customer: customerId, 
        status: 'active' 
      });

      res.json({
        success: true,
        data: {
          reviews,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(totalReviews / limit),
            totalReviews,
            hasNextPage: page * limit < totalReviews,
            hasPrevPage: page > 1
          }
        }
      });

    } catch (error) {
      console.error('Get customer reviews error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch customer reviews'
      });
    }
  }

  // Check if customer can review a product
  async canReviewProduct(req, res) {
    try {
      const { productId } = req.params;
      const customerId = req.user._id || req.user.userId;

      const canReview = await Review.canCustomerReview(customerId, productId);

      res.json({
        success: true,
        data: { canReview }
      });

    } catch (error) {
      console.error('Can review product error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to check review eligibility'
      });
    }
  }
}

module.exports = new ReviewController();
