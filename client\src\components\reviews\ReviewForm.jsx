import React, { useState } from 'react';
import { Rate, Button, Form, Input, Spin, notification } from 'antd';
import { StarFilled } from '@ant-design/icons';

const { TextArea } = Input;

const ReviewForm = ({ productId, productName, onReviewSubmit, loading = false, onSuccess }) => {
  const [form] = Form.useForm();
  const [rating, setRating] = useState(0);
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async (values) => {
    if (rating === 0) {
      notification.error({
        message: 'Rating Required',
        description: 'Please select a rating before submitting your review',
        placement: 'topRight',
      });
      return;
    }

    setSubmitting(true);
    try {
      const reviewData = {
        productId,
        rating,
        comment: values.comment.trim()
      };

      await onReviewSubmit(reviewData);
      
      // Reset form after successful submission
      form.resetFields();
      setRating(0);
      
      // Call onSuccess callback if provided
      if (onSuccess) onSuccess();
      
    } catch (error) {
      notification.error({
        message: 'Review Submission Failed',
        description: error.message || 'Failed to submit review. Please try again.',
        placement: 'topRight',
        duration: 5,
      });
    } finally {
      setSubmitting(false);
    }
  };

  const ratingDescriptions = {
    1: 'Poor',
    2: 'Fair', 
    3: 'Good',
    4: 'Very Good',
    5: 'Excellent'
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      {/* Header */}
      <div className="flex items-center gap-2 px-4 py-3 border-b border-gray-200">
        <StarFilled className="text-blue-600 text-lg" />
        <span className="font-medium text-gray-900">Write a Review</span>
      </div>

      {/* Form Content */}
      <div className="p-4">
        <Spin spinning={loading || submitting}>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            className="space-y-4"
          >
            {/* Rating Section */}
            <Form.Item
              label={<span className="text-gray-700 font-medium text-sm">Rating</span>}
              required
              className="mb-3"
            >
              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <Rate
                  value={rating}
                  onChange={setRating}
                  allowHalf={false}
                  className="text-lg"
                />
                {rating > 0 && (
                  <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium">
                    {ratingDescriptions[rating]}
                  </span>
                )}
              </div>
            </Form.Item>

            {/* Comment Section */}
            <Form.Item
              label={<span className="text-gray-700 font-medium text-sm">Your Review</span>}
              name="comment"
              className="mb-3"
              rules={[
                { required: true, message: 'Please write your review' },
                { min: 10, message: 'Review must be at least 10 characters long' },
                { max: 300, message: 'Review cannot exceed 300 characters' }
              ]}
            >
              <TextArea
                rows={3}
                placeholder="Share your experience with this product..."
                showCount
                maxLength={300}
                className="rounded-lg"
              />
            </Form.Item>

            {/* Submit Button */}
            <Form.Item className="mb-0">
              <Button 
                type="primary" 
                htmlType="submit"
                loading={submitting}
                disabled={rating === 0}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {submitting ? 'Submitting...' : 'Submit Review'}
              </Button>
            </Form.Item>
          </Form>
        </Spin>
      </div>
    </div>
  );
};

export default ReviewForm;
