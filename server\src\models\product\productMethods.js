// Instance methods for Product model
function addProductMethods(productSchema) {
  // Instance method to update stock
  productSchema.methods.updateStock = function(quantity, operation = 'set') {
    if (!this.inventory.trackQuantity) return this;
    
    switch (operation) {
      case 'add':
        this.inventory.quantity += quantity;
        break;
      case 'subtract':
        this.inventory.quantity = Math.max(0, this.inventory.quantity - quantity);
        break;
      case 'set':
      default:
        this.inventory.quantity = Math.max(0, quantity);
        break;
    }
    
    return this.save();
  };

  // Instance method to update sales data
  productSchema.methods.recordSale = function(quantity, price) {
    this.sales.totalSold += quantity;
    this.sales.totalRevenue += (price * quantity);
    this.sales.lastSaleDate = new Date();
    
    // Update stock if tracking
    if (this.inventory.trackQuantity) {
      this.inventory.quantity = Math.max(0, this.inventory.quantity - quantity);
    }
    
    return this.save();
  };

  // Instance method to update review data
  productSchema.methods.updateReviews = function(rating) {
    const currentTotal = this.reviews.averageRating * this.reviews.totalReviews;
    this.reviews.totalReviews += 1;
    this.reviews.averageRating = (currentTotal + rating) / this.reviews.totalReviews;
    this.reviews.ratingDistribution[rating] += 1;

    return this.save();
  };

  // Instance method to submit for approval
  productSchema.methods.submitForApproval = function(userId) {
    this.status = 'pending_approval';
    this.approval.status = 'pending';
    this.approval.submittedAt = new Date();
    this.approval.history.push({
      action: 'submitted',
      performedBy: userId,
      timestamp: new Date()
    });

    return this.save();
  };

  // Instance method to approve product
  productSchema.methods.approveProduct = function(adminId, notes = '') {
    this.status = 'active';
    this.approval.status = 'approved';
    this.approval.reviewedAt = new Date();
    this.approval.reviewedBy = adminId;
    this.approval.adminNotes = notes;
    this.publishedAt = new Date();
    this.approval.history.push({
      action: 'approved',
      performedBy: adminId,
      timestamp: new Date()
    });

    return this.save();
  };

  // Instance method to reject product
  productSchema.methods.rejectProduct = function(adminId, reason, notes = '') {
    this.status = 'rejected';
    this.approval.status = 'rejected';
    this.approval.reviewedAt = new Date();
    this.approval.reviewedBy = adminId;
    this.approval.rejectionReason = reason;
    this.approval.adminNotes = notes;
    this.approval.history.push({
      action: 'rejected',
      performedBy: adminId,
      reason: reason,
      timestamp: new Date()
    });

    return this.save();
  };

  // Instance method to request changes
  productSchema.methods.requestChanges = function(adminId, changes, notes = '') {
    this.status = 'draft';
    this.approval.status = 'requires_changes';
    this.approval.reviewedAt = new Date();
    this.approval.reviewedBy = adminId;
    this.approval.adminNotes = notes;
    this.approval.changesRequested = changes.map(change => ({
      field: change.field,
      message: change.message,
      resolved: false,
      createdAt: new Date()
    }));
    this.approval.history.push({
      action: 'changes_requested',
      performedBy: adminId,
      timestamp: new Date()
    });

    return this.save();
  };

  // Instance method to track product view
  productSchema.methods.trackView = function(isUnique = false) {
    this.analytics.views.total += 1;
    if (isUnique) {
      this.analytics.views.unique += 1;
    }

    // Update daily stats
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    let dailyStat = this.analytics.views.daily.find(d =>
      d.date.getTime() === today.getTime()
    );

    if (!dailyStat) {
      dailyStat = {
        date: today,
        views: 0,
        uniqueViews: 0
      };
      this.analytics.views.daily.push(dailyStat);
    }

    dailyStat.views += 1;
    if (isUnique) {
      dailyStat.uniqueViews += 1;
    }

    // Keep only last 30 days
    this.analytics.views.daily = this.analytics.views.daily
      .sort((a, b) => b.date - a.date)
      .slice(0, 30);

    return this.save();
  };
}

module.exports = { addProductMethods };
