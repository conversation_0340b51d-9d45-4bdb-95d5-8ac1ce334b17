import React, { useMemo } from 'react';
import { Card, Row, Col, Badge } from 'antd';
import { Line, Bar, Pie, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import useResponsive from '../../../hooks/useResponsive';
import {
  prepareSalesPerformanceData,
  prepareOrderAnalyticsData,
  prepareProductPerformanceData,
  prepareRevenueBreakdownData,
  getChartOptions,
  getDoughnutOptions
} from './chartDataUtils';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,  
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const ChartCard = ({ title, children, extra, height, loading = false }) => {
  const { isMobile } = useResponsive();
  
  return (
    <Card 
      title={title}
      extra={extra}
      style={{ height }}
      size={isMobile ? 'small' : 'default'}
      loading={loading}
    >
      {children}
    </Card>
  );
};

const ChartsSection = ({ analyticsData, dashboardData, loading }) => {
  const { isMobile, isTablet } = useResponsive();
  
  const cardHeight = isMobile ? '280px' : isTablet ? '320px' : '400px';
  const chartHeight = isMobile ? '180px' : isTablet ? '220px' : '300px';
  
  // Memoized chart data
  const salesPerformanceData = useMemo(() => 
    prepareSalesPerformanceData(analyticsData), 
    [analyticsData]
  );
  
  const orderAnalyticsData = useMemo(() => 
    prepareOrderAnalyticsData(analyticsData), 
    [analyticsData]
  );
  
  const productPerformanceData = useMemo(() => 
    prepareProductPerformanceData(dashboardData), 
    [dashboardData]
  );
  
  const revenueBreakdownData = useMemo(() => 
    prepareRevenueBreakdownData(dashboardData), 
    [dashboardData]
  );
  
  // Memoized chart options
  const chartOptions = useMemo(() => getChartOptions(isMobile), [isMobile]);
  const doughnutOptions = useMemo(() => getDoughnutOptions(isMobile), [isMobile]);
  
  return (
    <>
      {/* Charts Row 1 - Performance Charts */}
      <Row gutter={[8, 8]} style={{ marginBottom: 16 }}>
        <Col xs={24} lg={12}>
          <ChartCard 
            title="Sales Performance" 
            extra={<Badge status="processing" text={isMobile ? "Live" : "Live Data"} />}
            height={cardHeight}
            loading={loading}
          >
            <div style={{ height: chartHeight }}>
              <Line data={salesPerformanceData} options={chartOptions} />
            </div>
          </ChartCard>
        </Col>
        <Col xs={24} lg={12}>
          <ChartCard 
            title="Orders Analytics" 
            extra={<Badge status="success" text="Updated" />}
            height={cardHeight}
            loading={loading}
          >
            <div style={{ height: chartHeight }}>
              <Bar data={orderAnalyticsData} options={chartOptions} />
            </div>
          </ChartCard>
        </Col>
      </Row>
      
      {/* Charts Row 2 - Analysis Charts */}
      <Row gutter={[8, 8]} style={{ marginBottom: 16 }}>
        <Col xs={24} lg={12}>
          <ChartCard 
            title="Product Performance" 
            extra={<Badge status="processing" text="Top Products" />}
            height={cardHeight}
            loading={loading}
          >
            <div style={{ height: chartHeight }}>
              <Doughnut data={productPerformanceData} options={doughnutOptions} />
            </div>
          </ChartCard>
        </Col>
        <Col xs={24} lg={12}>
          <ChartCard 
            title="Revenue Breakdown" 
            extra={<Badge status="default" text="This Period" />}
            height={cardHeight}
            loading={loading}
          >
            <div style={{ height: chartHeight }}>
              <Pie data={revenueBreakdownData} options={doughnutOptions} />
            </div>
          </ChartCard>
        </Col>
      </Row>
    </>
  );
};

export default ChartsSection;
