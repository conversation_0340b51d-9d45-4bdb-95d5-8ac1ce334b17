// Product database indexes
function addProductIndexes(productSchema) {
  // Basic indexes (slug and sku indexes are automatically created by unique: true)
  productSchema.index({ vendor: 1 });
  productSchema.index({ category: 1 });
  productSchema.index({ name: 'text', description: 'text', tags: 'text' });
  productSchema.index({ status: 1 });
  productSchema.index({ featured: 1 });
  productSchema.index({ 'pricing.basePrice': 1 });
  productSchema.index({ 'reviews.averageRating': -1 });
  productSchema.index({ 'sales.totalSold': -1 });
  productSchema.index({ createdAt: -1 });
  productSchema.index({ publishedAt: -1 });

  // Approval system and analytics indexes
  productSchema.index({ 'approval.status': 1 });
  productSchema.index({ 'approval.submittedAt': -1 });
  productSchema.index({ 'approval.reviewedBy': 1 });
  productSchema.index({ 'analytics.views.total': -1 });
  productSchema.index({ 'analytics.conversionRate': -1 });

  // Enhanced fields indexes
  productSchema.index({ 'colors.available': 1 });
  productSchema.index({ 'colors.name': 1 });
  productSchema.index({ 'sizes.available': 1 });
  productSchema.index({ 'sizes.name': 1 });
  productSchema.index({ 'shippingOptions.shippingDays': 1 });
  productSchema.index({ 'shippingOptions.processingDays': 1 });
  productSchema.index({ 'deliveryCharges.standard.charge': 1 });
  productSchema.index({ 'deliveryCharges.express.available': 1 });
  productSchema.index({ 'returnPolicy.returnsAccepted': 1 });
  productSchema.index({ 'returnPolicy.returnWindow': 1 });
  productSchema.index({ 'warranty.hasWarranty': 1 });
  productSchema.index({ 'warranty.warrantyPeriod': 1 });
  productSchema.index({ 'specifications.features.name': 1 });
  productSchema.index({ 'highlights': 1 });

  // Compound indexes for common queries
  productSchema.index({ vendor: 1, status: 1 });
  productSchema.index({ vendor: 1, 'colors.available': 1 });
  productSchema.index({ vendor: 1, 'warranty.hasWarranty': 1 });
  productSchema.index({ status: 1, 'shippingOptions.shippingDays': 1 });
}

module.exports = { addProductIndexes };
