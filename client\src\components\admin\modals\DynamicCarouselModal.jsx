import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, Space, message, Divider } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';

const { TextArea } = Input;

const DynamicCarouselModal = ({ 
  visible, 
  onCancel, 
  onSubmit, 
  loading = false,
  editingImages = null 
}) => {
  const [form] = Form.useForm();
  const [imageInputs, setImageInputs] = useState([{ id: 1 }]);

  useEffect(() => {
    if (visible) {
      if (editingImages && editingImages.length > 0) {
        // If editing, populate with existing images
        const inputs = editingImages.map((img, index) => ({
          id: index + 1,
          title: img.title || '',
          description: img.description || '',
          imageUrl: img.imageUrl || '',
          linkUrl: img.linkUrl || ''
        }));
        setImageInputs(inputs);
        
        // Set form values
        const formValues = {};
        inputs.forEach((input, index) => {
          formValues[`title_${input.id}`] = input.title;
          formValues[`description_${input.id}`] = input.description;
          formValues[`imageUrl_${input.id}`] = input.imageUrl;
          formValues[`linkUrl_${input.id}`] = input.linkUrl;
        });
        form.setFieldsValue(formValues);
      } else {
        // If adding new, start with one input
        setImageInputs([{ id: 1 }]);
        form.resetFields();
      }
    }
  }, [visible, editingImages, form]);

  const addImageInput = () => {
    if (imageInputs.length >= 10) {
      message.warning('Maximum 10 carousel images allowed');
      return;
    }
    
    const newId = Math.max(...imageInputs.map(input => input.id)) + 1;
    setImageInputs([...imageInputs, { id: newId }]);
  };

  const removeImageInput = (idToRemove) => {
    if (imageInputs.length <= 1) {
      message.warning('At least one image is required');
      return;
    }
    
    setImageInputs(imageInputs.filter(input => input.id !== idToRemove));
    
    // Clear form fields for removed input
    const fieldsToRemove = [
      `title_${idToRemove}`,
      `description_${idToRemove}`,
      `imageUrl_${idToRemove}`,
      `linkUrl_${idToRemove}`
    ];
    
    const currentValues = form.getFieldsValue();
    fieldsToRemove.forEach(field => {
      delete currentValues[field];
    });
    form.setFieldsValue(currentValues);
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      // Transform form values into array of image objects
      const images = imageInputs.map(input => ({
        title: values[`title_${input.id}`] || '',
        description: values[`description_${input.id}`] || '',
        imageUrl: values[`imageUrl_${input.id}`] || '',
        linkUrl: values[`linkUrl_${input.id}`] || ''
      })).filter(img => img.imageUrl.trim() !== ''); // Only include images with URLs
      
      if (images.length === 0) {
        message.error('Please provide at least one image URL');
        return;
      }

      await onSubmit(images);
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setImageInputs([{ id: 1 }]);
    onCancel();
  };

  return (
    <Modal
      title={editingImages ? 'Edit Carousel Images' : 'Add Carousel Images'}
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={loading}
      width={800}
      okText={editingImages ? 'Update Images' : 'Add Images'}
      cancelText="Cancel"
    >
      <div style={{ marginBottom: 16 }}>
        <p style={{ color: '#666', marginBottom: 8 }}>
          Add up to 10 carousel images using direct Cloudinary links. At least one image URL is required.
        </p>
        <Button 
          type="dashed" 
          onClick={addImageInput}
          icon={<PlusOutlined />}
          disabled={imageInputs.length >= 10}
          style={{ marginBottom: 16 }}
        >
          Add Another Image ({imageInputs.length}/10)
        </Button>
      </div>

      <Form form={form} layout="vertical">
        {imageInputs.map((input, index) => (
          <div key={input.id} style={{ marginBottom: 24, padding: 16, border: '1px solid #f0f0f0', borderRadius: 8 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
              <h4 style={{ margin: 0 }}>Image {index + 1}</h4>
              {imageInputs.length > 1 && (
                <Button 
                  type="text" 
                  danger 
                  icon={<DeleteOutlined />}
                  onClick={() => removeImageInput(input.id)}
                  size="small"
                >
                  Remove
                </Button>
              )}
            </div>

            <Form.Item
              name={`imageUrl_${input.id}`}
              label="Image URL"
              rules={[
                { 
                  pattern: /^https?:\/\/.+/, 
                  message: 'Please enter a valid HTTP/HTTPS URL' 
                }
              ]}
            >
              <Input 
                placeholder="https://res.cloudinary.com/your-cloud/image/upload/..." 
                style={{ fontFamily: 'monospace' }}
              />
            </Form.Item>

            <Form.Item
              name={`title_${input.id}`}
              label="Title (Optional)"
            >
              <Input placeholder="Enter image title" />
            </Form.Item>

            <Form.Item
              name={`description_${input.id}`}
              label="Description (Optional)"
            >
              <TextArea rows={2} placeholder="Enter image description" />
            </Form.Item>

            <Form.Item
              name={`linkUrl_${input.id}`}
              label="Link URL (Optional)"
            >
              <Input placeholder="https://example.com (where to redirect when clicked)" />
            </Form.Item>

            {index < imageInputs.length - 1 && <Divider />}
          </div>
        ))}
      </Form>
    </Modal>
  );
};

export default DynamicCarouselModal;
