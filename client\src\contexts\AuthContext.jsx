import React, { createContext, useReducer, useEffect, useContext } from 'react';
import { authAPI } from '../utils/authApi';

// Initial state
const initialState = {
  user: null,
  token: null,
  userType: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

// Action types
const AUTH_ACTIONS = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  SET_LOADING: 'SET_LOADING',
  CLEAR_ERROR: 'CLEAR_ERROR',
  UPDATE_PROFILE: 'UPDATE_PROFILE',
  RESTORE_SESSION: 'RESTORE_SESSION',
};

// Helper function to check if token is expired
const isTokenExpired = (token) => {
  if (!token) return true;
  
  try {
    // Simple JWT decode without verification (just for expiry check)
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true;
  }
};

// Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        userType: action.payload.userType,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case AUTH_ACTIONS.LOGIN_FAILURE:
      return {
        ...state,
        user: null,
        token: null,
        userType: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      };
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...initialState,
        isLoading: false,
      };
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
      };
    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };
    case AUTH_ACTIONS.UPDATE_PROFILE:
      return {
        ...state,
        user: { ...state.user, ...action.payload },
      };
    default:
      return state;
  }
};

// Create context
const AuthContext = createContext();

// Export AuthContext as named export as well for consistency
export { AuthContext };

// Auth provider component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Helper function to get current token
  const getCurrentToken = () => {
    return state.token || localStorage.getItem('authToken');
  };

  // Load user from localStorage on app start
  useEffect(() => {
    const loadStoredAuth = async () => {
      console.log('🔄 Loading stored authentication...');
      
      try {
        const token = localStorage.getItem('authToken');
        const userString = localStorage.getItem('authUser');
        const userType = localStorage.getItem('authUserType');

        console.log('📋 Stored auth data:', { hasToken: !!token, hasUser: !!userString, userType });

        if (token && userString) {
          // First check if token is expired
          if (isTokenExpired(token)) {
            console.log('⏰ Token is expired, clearing auth data');
            clearAuthData();
            dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
            return;
          }

          const user = JSON.parse(userString);
          
          // Restore session immediately with stored data
          console.log('✅ Restoring session with valid token');
          dispatch({
            type: AUTH_ACTIONS.LOGIN_SUCCESS,
            payload: {
              token,
              user,
              userType: userType || user.userType || 'customer',
            },
          });
          
          // Then verify token in background and update user data if needed
          try {
            console.log('🔍 Verifying token with server...');
            const response = await authAPI.getProfile(token);
            
            if (response.success) {
              console.log('✅ Token verification successful');
              const userData = response.data.user || response.data;

              // Ensure user has proper name fields
              if (!userData.name && userData.firstName && userData.lastName) {
                userData.name = `${userData.firstName} ${userData.lastName}`;
              } else if (!userData.name) {
                userData.name = userData.firstName || userData.lastName || userData.email || 'User';
              }

              // Update stored user data with fresh data from server if different
              if (JSON.stringify(user) !== JSON.stringify(userData)) {
                console.log('📝 Updating user data with fresh server data');
                storeAuthData(token, userData, userData.userType || userType);
                dispatch({
                  type: AUTH_ACTIONS.UPDATE_PROFILE,
                  payload: userData,
                });
              }
            } else {
              console.log('❌ Token verification failed, but keeping session (server may be down)');
              // Don't clear auth data immediately - server might be down
              // Only clear if we get a specific unauthorized response
              if (response.status === 401 || response.status === 403) {
                console.log('🔒 Unauthorized response, clearing auth data');
                clearAuthData();
                dispatch({ type: AUTH_ACTIONS.LOGOUT });
              }
            }
          } catch (error) {
            console.error('⚠️ Token verification failed, but keeping session:', error);
            // Don't clear auth data on network errors - user should stay logged in
            // Only clear on specific auth errors
            if (error.response?.status === 401 || error.response?.status === 403) {
              console.log('🔒 Auth error, clearing auth data');
              clearAuthData();
              dispatch({ type: AUTH_ACTIONS.LOGOUT });
            }
          }
        } else {
          console.log('❌ No stored auth data found');
          dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
        }
      } catch (error) {
        console.error('❌ Error loading stored auth:', error);
        // Only clear on parsing errors, not network errors
        if (error instanceof SyntaxError) {
          console.log('🧹 JSON parsing error, clearing corrupted auth data');
          clearAuthData();
        }
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      }
    };

    loadStoredAuth();
  }, []);

  // Store auth data in localStorage
  const storeAuthData = (token, user, userType) => {
    localStorage.setItem('authToken', token);
    localStorage.setItem('authUser', JSON.stringify(user));
    localStorage.setItem('authUserType', userType);
  };

  // Clear auth data from localStorage
  const clearAuthData = () => {
    console.log('🧹 Clearing localStorage auth data...');
    localStorage.removeItem('authToken');
    localStorage.removeItem('authUser');
    localStorage.removeItem('authUserType');
    localStorage.removeItem('refreshToken');
    
    // Also clear any other auth-related items that might exist
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.startsWith('auth') || key.startsWith('user') || key.startsWith('token'))) {
        keysToRemove.push(key);
      }
    }
    
    keysToRemove.forEach(key => {
      console.log(`🗑️ Removing localStorage key: ${key}`);
      localStorage.removeItem(key);
    });
    
    console.log('✅ localStorage cleared');
  };

  // Login function
  const login = async (credentials, userType) => {
    console.log('🔑 AuthContext login called with:', { email: credentials.email, userType, hasOTP: !!credentials.otp });
    dispatch({ type: AUTH_ACTIONS.LOGIN_START });

    try {
      const loginData = { ...credentials, userType };
      const response = await authAPI.login(loginData);
      console.log('📡 AuthAPI login response:', response);

      if (response.success) {
        const { token, refreshToken, user } = response.data;

        // Use the userType from the backend response, not the frontend request
        const actualUserType = user.userType || user.role || 'customer';

        // Ensure user has proper name fields
        if (!user.name && user.firstName && user.lastName) {
          user.name = `${user.firstName} ${user.lastName}`;
        } else if (!user.name) {
          user.name = user.firstName || user.lastName || user.email || 'User';
        }

        storeAuthData(token, user, actualUserType);
        if (refreshToken) {
          localStorage.setItem('refreshToken', refreshToken);
        }

        dispatch({
          type: AUTH_ACTIONS.LOGIN_SUCCESS,
          payload: { token, user, userType: actualUserType },
        });

        return { success: true, data: response.data };
      } else if (response.requiresOTP) {
        // Handle OTP requirement - this is not an error, just needs OTP verification
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
        return {
          success: false,
          requiresOTP: true,
          message: response.message,
          data: response.data
        };
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error) {
      let errorMessage = error.response?.data?.message || error.message || 'Login failed';
      
      // Check if the error response contains OTP requirement
      if (error.response?.data?.requiresOTP) {
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
        return {
          success: false,
          requiresOTP: true,
          message: error.response.data.message,
          data: error.response.data.data
        };
      }
      
      // Handle cross-account type error specifically
      if (error.response?.status === 403 && error.response?.data?.accountType) {
        const { accountType, attemptedType } = error.response.data;
        errorMessage = error.response.data.message;
      }
      
      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: errorMessage,
      });
      return { 
        success: false, 
        error: errorMessage,
        accountType: error.response?.data?.accountType,
        attemptedType: error.response?.data?.attemptedType
      };
    }
  };

  // Register function
  const register = async (userData, userType) => {
    dispatch({ type: AUTH_ACTIONS.LOGIN_START });

    try {
      const registerData = { ...userData, userType };
      const response = await authAPI.register(registerData);

      if (response.success) {
        const { token, refreshToken, user } = response.data;
        
        storeAuthData(token, user, userType);
        if (refreshToken) {
          localStorage.setItem('refreshToken', refreshToken);
        }
        
        dispatch({
          type: AUTH_ACTIONS.LOGIN_SUCCESS,
          payload: { token, user, userType },
        });

        return { success: true, data: response.data };
      } else {
        throw new Error(response.message || 'Registration failed');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Registration failed';
      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: errorMessage,
      });
      return { success: false, error: errorMessage };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      console.log('🔄 Logout process started');
      const token = getCurrentToken();
      if (token) {
        console.log('📡 Calling logout API...');
        await authAPI.logout(token);
        console.log('✅ Logout API call successful');
      }
    } catch (error) {
      console.error('❌ Logout API error:', error);
    } finally {
      console.log('🧹 Clearing auth data...');
      clearAuthData();
      console.log('🔄 Dispatching logout action...');
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
      console.log('✅ Logout process completed');
    }
  };

  // Social login function
  const socialLogin = async (provider, userType, token) => {
    dispatch({ type: AUTH_ACTIONS.LOGIN_START });

    try {
      const response = await authAPI.socialLogin(provider, userType, token);
      
      if (response.success) {
        const { token: authToken, user } = response.data;
        
        storeAuthData(authToken, user, userType);
        
        dispatch({
          type: AUTH_ACTIONS.LOGIN_SUCCESS,
          payload: { token: authToken, user, userType },
        });

        return { success: true, data: response.data };
      } else {
        throw new Error(response.message || 'Social login failed');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Social login failed';
      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: errorMessage,
      });
      return { success: false, error: errorMessage };
    }
  };

  // Update profile function
  const updateProfile = async (profileData) => {
    try {
      console.log('=== AuthContext updateProfile START ===');
      
      const token = getCurrentToken();
      
      if (!token) {
        console.error('No authentication token available');
        return { success: false, error: 'Not authenticated - please login again' };
      }
      
      // Check if profileData is FormData or regular object
      const isFormData = profileData instanceof FormData;
      
      if (!isFormData && (!profileData || typeof profileData !== 'object')) {
        console.error('Invalid profile data provided');
        return { success: false, error: 'Invalid profile data provided' };
      }
      
      if (!isFormData && Object.keys(profileData).length === 0) {
        console.error('Empty profile data provided');
        return { success: false, error: 'No profile data provided' };
      }
      
      console.log('Calling authAPI.updateProfile...');
      const response = await authAPI.updateProfile(token, profileData);
      console.log('API response received:', response);
      
      if (response && response.success) {
        const updatedUser = response.data;
        console.log('Profile update successful, updating state...');
        
        // Update localStorage
        const currentUser = JSON.parse(localStorage.getItem('authUser') || '{}');
        const mergedUser = { ...currentUser, ...updatedUser };
        localStorage.setItem('authUser', JSON.stringify(mergedUser));
        
        dispatch({
          type: AUTH_ACTIONS.UPDATE_PROFILE,
          payload: updatedUser,
        });

        console.log('=== AuthContext updateProfile SUCCESS ===');
        return { success: true, data: response.data };
      } else {
        console.error('API returned unsuccessful response:', response);
        throw new Error(response?.message || 'Profile update failed');
      }
    } catch (error) {
      console.error('=== AuthContext updateProfile ERROR ===');
      console.error('Error object:', error);
      console.error('Error message:', error.message);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      
      const errorMessage = error.response?.data?.message || error.message || 'Profile update failed';
      return { success: false, error: errorMessage };
    }
  };

  // Update profile picture function
  const updateProfilePicture = async (imageFile) => {
    try {
      const token = getCurrentToken();
      
      if (!token) {
        return { success: false, error: 'Not authenticated - please login again' };
      }
      
      if (!imageFile) {
        return { success: false, error: 'No image file provided' };
      }
      
      const response = await authAPI.updateProfilePicture(token, imageFile);
      
      if (response && response.success) {
        const { avatar } = response.data;
        
        // Update localStorage
        const currentUser = JSON.parse(localStorage.getItem('authUser') || '{}');
        const updatedUser = { ...currentUser, avatar };
        localStorage.setItem('authUser', JSON.stringify(updatedUser));
        
        dispatch({
          type: AUTH_ACTIONS.UPDATE_PROFILE,
          payload: { avatar },
        });
        
        return { success: true, data: response.data };
      } else {
        throw new Error(response?.message || 'Profile picture update failed');
      }
    } catch (error) {
      console.error('Error updating profile picture:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Profile picture update failed';
      return { success: false, error: errorMessage };
    }
  };

  // Delete profile picture function
  const deleteProfilePicture = async () => {
    try {
      const token = getCurrentToken();
      
      if (!token) {
        return { success: false, error: 'Not authenticated - please login again' };
      }
      
      const response = await authAPI.deleteProfilePicture(token);
      
      if (response && response.success) {
        // Update localStorage
        const currentUser = JSON.parse(localStorage.getItem('authUser') || '{}');
        const updatedUser = { ...currentUser, avatar: null };
        localStorage.setItem('authUser', JSON.stringify(updatedUser));
        
        dispatch({
          type: AUTH_ACTIONS.UPDATE_PROFILE,
          payload: { avatar: null },
        });
        
        return { success: true };
      } else {
        throw new Error(response?.message || 'Profile picture deletion failed');
      }
    } catch (error) {
      console.error('Error deleting profile picture:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Profile picture deletion failed';
      return { success: false, error: errorMessage };
    }
  };

  // Change password function
  const changePassword = async (passwordData) => {
    try {
      console.log('AuthContext changePassword called');
      const token = getCurrentToken();
      console.log('Token exists:', !!token);
      
      if (!token) {
        return { success: false, error: 'Not authenticated - please login again' };
      }
      
      const response = await authAPI.changePassword(token, passwordData);
      
      if (response.success) {
        return { success: true, data: response.data };
      } else {
        throw new Error(response.message || 'Password change failed');
      }
    } catch (error) {
      console.error('AuthContext changePassword error:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Password change failed';
      return { success: false, error: errorMessage };
    }
  };

  // Clear error function
  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
  };

  // Context value
  const value = {
    ...state,
    login,
    register,
    logout,
    socialLogin,
    updateProfile,
    updateProfilePicture,
    deleteProfilePicture,
    changePassword,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the AuthContext
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;