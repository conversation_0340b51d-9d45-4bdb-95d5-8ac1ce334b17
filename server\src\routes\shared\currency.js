const express = require('express');
const router = express.Router();
const { verifyToken, optionalAuth } = require('../../middleware/auth/authMiddleware');
const {
  getSupportedCurrenciesList,
  updateUserCurrency,
  getUserCurrency
} = require('../../controllers/shared/currencyController');

// Public routes
// Root endpoint - returns supported currencies list
router.get('/', getSupportedCurrenciesList);
router.get('/supported', getSupportedCurrenciesList);

// Protected routes (require authentication)
router.use(verifyToken);

// Get user's current currency preference (for both endpoints frontend might call)
router.get('/user-preference', getUserCurrency);
router.get('/preference', getUserCurrency);

// Update user's currency preference
router.put('/user-preference', updateUserCurrency);
router.put('/preference', updateUserCurrency);

module.exports = router;
