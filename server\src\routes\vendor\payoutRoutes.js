const express = require('express');
const router = express.Router();
const {
  getPayoutDashboard,
  requestPayout,
  getPayouts,
  getPayoutDetails,
  cancelPayout,
  getPayoutMethods
} = require('../../controllers/vendor/payoutController');
const PayoutValidator = require('../../validators/payoutValidator');
const { authenticate } = require('../../middleware/auth');
const { requireVendor } = require('../../middleware/vendorAuth');

// Apply authentication and vendor middleware to all routes
router.use(authenticate);
router.use(requireVendor);

/**
 * @route   GET /api/vendor/payouts/dashboard
 * @desc    Get vendor payout dashboard with balance and statistics
 * @access  Private (Vendor only)
 */
router.get('/dashboard', getPayoutDashboard);

/**
 * @route   GET /api/vendor/payouts/methods
 * @desc    Get available payout methods for the vendor
 * @access  Private (Vendor only)
 */
router.get('/methods', getPayoutMethods);

/**
 * @route   GET /api/vendor/payouts
 * @desc    Get all payouts for the vendor with pagination and filtering
 * @access  Private (Vendor only)
 */
router.get('/', PayoutValidator.validatePayoutQuery(), getPayouts);

/**
 * @route   POST /api/vendor/payouts/request
 * @desc    Request a new payout
 * @access  Private (Vendor only)
 */
router.post('/request', PayoutValidator.validatePayoutRequest(), requestPayout);

/**
 * @route   GET /api/vendor/payouts/:payoutId
 * @desc    Get detailed information about a specific payout
 * @access  Private (Vendor only)
 */
router.get('/:payoutId', PayoutValidator.validatePayoutId(), getPayoutDetails);

/**
 * @route   DELETE /api/vendor/payouts/:payoutId/cancel
 * @desc    Cancel a pending payout request
 * @access  Private (Vendor only)
 */
router.delete('/:payoutId/cancel', PayoutValidator.validatePayoutId(), cancelPayout);

module.exports = router;
