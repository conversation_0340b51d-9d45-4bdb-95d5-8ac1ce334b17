import React from 'react';
import { useNavigate } from 'react-router-dom';
import { message } from 'antd';
import { ShoppingCartOutlined, StarFilled, EnvironmentOutlined, HeartOutlined } from '@ant-design/icons';
import { getAbsoluteImageUrl } from '../../utils/imageUtils';
import { useCurrency } from '../../contexts/CurrencyContext';

const ProductItem = ({ product, className = '', viewMode = 'grid' }) => {
  const navigate = useNavigate();
  const { getProductPriceInCurrency, getCurrencySymbol } = useCurrency();

  // Get product image URL
  const getProductImageUrl = (product) => {
    if (!product || !product.images || !Array.isArray(product.images) || product.images.length === 0) {
      return null;
    }

    // Try to find primary image first
    const primaryImage = product.images.find(img => img && img.isPrimary);
    if (primaryImage && primaryImage.url) {
      return getAbsoluteImageUrl(primaryImage.url);
    }

    // Fallback to first image
    const firstImage = product.images[0];
    if (firstImage) {
      const url = typeof firstImage === 'string' ? firstImage : firstImage.url;
      return getAbsoluteImageUrl(url);
    }

    return null;
  };

  const handleProductClick = () => {
    navigate(`/product/${product._id}`);
  };

  const handleCartClick = (e) => {
    e.stopPropagation();
    console.log('Added to cart:', product._id);
    message.success('Added to cart');
  };

  const handleWishlistClick = (e) => {
    e.stopPropagation();
    console.log('Added to wishlist:', product._id);
    message.success('Added to wishlist');
  };

  const getPrice = () => {
    const priceData = getProductPriceInCurrency(product);
    if (!priceData) {
      return {
        salePrice: 0,
        originalPrice: null,
        hasDiscount: false,
        currency: 'INR'
      };
    }
    
    const { basePrice, salePrice, currency } = priceData;
    if (salePrice && basePrice && salePrice < basePrice) {
      return {
        salePrice,
        originalPrice: basePrice,
        hasDiscount: true,
        currency
      };
    }
    return {
      salePrice: basePrice || salePrice || 0,
      originalPrice: null,
      hasDiscount: false,
      currency
    };
  };

  const { salePrice, originalPrice, hasDiscount, currency } = getPrice();
  const currencySymbol = getCurrencySymbol(currency);

  // List view layout
  if (viewMode === 'list') {
    return (
      <div
        className={`bg-white rounded-lg overflow-hidden shadow-sm border border-gray-100 hover:shadow-md hover:border-gray-200 transition-all duration-300 cursor-pointer flex ${className}`}
        onClick={handleProductClick}
      >
        {/* Image container for list view */}
        <div className="relative w-32 h-32 sm:w-40 sm:h-40 bg-gray-100 flex-shrink-0">
          <img
            src={getProductImageUrl(product) || 'https://via.placeholder.com/400x400?text=No+Image'}
            alt={product.name}
            className="w-full h-full object-cover"
            onError={(e) => {
              e.target.src = 'https://via.placeholder.com/400x400?text=Image+Error';
            }}
          />
          
          {/* Badges */}
          <div className="absolute top-2 left-2 flex flex-col gap-1">
            {hasDiscount && (
              <div className="bg-green-500 text-white px-1 py-0.5 text-xs rounded">
                Sale
              </div>
            )}
          </div>
        </div>

        {/* Content for list view */}
        <div className="flex-1 p-4 flex flex-col justify-between">
          <div>
            {/* Title */}
            <h3 className="text-lg font-semibold text-gray-800 mb-2 line-clamp-2">
              {product.name}
            </h3>
            
            {/* Description if available */}
            {product.description && (
              <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                {product.description}
              </p>
            )}
            
            {/* Rating and Reviews */}
            <div className="flex items-center mb-2">
              <StarFilled className="text-yellow-400 text-sm" />
              <span className="ml-1 text-sm text-gray-600">{product.reviews?.averageRating?.toFixed(1) || '0.0'}</span>
              <span className="ml-1 text-sm text-gray-500">({product.reviews?.totalReviews || 0} reviews)</span>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              {/* Price */}
              <div className="mb-2">
                {hasDiscount ? (
                  <div className="flex items-center space-x-2">
                    <span className="text-xl font-bold text-[#ed2b2a]">
                      {currencySymbol}{salePrice}
                    </span>
                    <span className="text-base text-gray-500 line-through">
                      {currencySymbol}{originalPrice}
                    </span>
                    <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                      {Math.round(((originalPrice - salePrice) / originalPrice) * 100)}% OFF
                    </span>
                  </div>
                ) : (
                  <span className="text-xl font-bold text-[#ed2b2a]">
                    {currencySymbol}{salePrice}
                  </span>
                )}
              </div>
              
              {/* Vendor and Stock */}
              <div className="flex items-center gap-4 text-sm text-gray-500">
                <span className="flex items-center">
                  <EnvironmentOutlined className="mr-1" />
                  {product.vendor?.businessName || 'Store'}
                </span>
                <span className={`${product.inventory?.quantity > 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {product.inventory?.quantity > 0 ? 'In Stock' : 'Out of Stock'}
                </span>
              </div>
            </div>
            
            {/* Actions */}
            <div className="flex items-center gap-2">
              <button
                onClick={handleWishlistClick}
                className="p-2 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors"
                title="Add to Wishlist"
              >
                <HeartOutlined className="text-gray-600" />
              </button>
              <button 
                onClick={handleCartClick}
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors flex items-center gap-2"
                title="Add to Cart"
                disabled={product.inventory?.quantity <= 0}
              >
                <ShoppingCartOutlined />
                Add to Cart
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Grid view layout (default)
  return (
    <div
      className={`bg-white rounded-lg overflow-hidden shadow-sm border border-gray-100 hover:shadow-md hover:border-gray-200 transition-all duration-300 transform hover:-translate-y-1 cursor-pointer ${className}`}
      onClick={handleProductClick}
    >
      {/* Image container: square */}
      <div className="relative w-full pb-[100%] bg-gray-100">
        <img
          src={getProductImageUrl(product) || 'https://via.placeholder.com/400x400?text=No+Image'}
          alt={product.name}
          className="absolute inset-0 w-full h-full object-cover"
          onError={(e) => {
            e.target.src = 'https://via.placeholder.com/400x400?text=Image+Error';
          }}
        />
        
        {/* Badges */}
        <div className="absolute top-2 left-2 flex flex-col gap-1">
          {hasDiscount && (
            <div className="bg-green-500 text-white px-2 py-1 text-xs rounded">
              Sale
            </div>
          )}
          {product.inventory?.quantity <= 0 && (
            <div className="bg-gray-500 text-white px-2 py-1 text-xs rounded">
              Out of Stock
            </div>
          )}
        </div>

        {/* Wishlist button */}
        <button
          onClick={handleWishlistClick}
          className="absolute top-2 right-2 bg-white rounded-full p-2 shadow-md hover:bg-gray-50 transition-colors"
        >
          <HeartOutlined className="text-gray-400 hover:text-red-500" />
        </button>
      </div>

      {/* Content */}
      <div className="p-3 flex flex-col justify-between h-44">
        <div>
          {/* Title: clamp to 2 lines */}
          <h3 
            className="text-sm font-medium text-gray-800 leading-tight mb-1" 
            style={{
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              minHeight: '2.5em'
            }}
          >
            {product.name}
          </h3>
          
          {/* Price */}
          <div className="mb-1">
            {hasDiscount ? (
              <div className="flex items-center space-x-2">
                <span className="text-base font-bold text-[#ed2b2a]">
                  {currencySymbol}{salePrice}
                </span>
                <span className="text-sm text-gray-500 line-through">
                  {currencySymbol}{originalPrice}
                </span>
                <span className="text-xs bg-green-100 text-green-800 px-1 rounded">
                  {Math.round(((originalPrice - salePrice) / originalPrice) * 100)}% OFF
                </span>
              </div>
            ) : (
              <span className="text-base font-bold text-[#ed2b2a]">
                {currencySymbol}{salePrice}
              </span>
            )}
          </div>
          
          {/* Rating and Stock */}
          <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
            <div className="flex items-center">
              <StarFilled className="text-yellow-400 text-xs" />
              <span className="ml-1">{product.reviews?.averageRating?.toFixed(1) || '0.0'}</span>
              <span className="ml-1">({product.reviews?.totalReviews || 0})</span>
            </div>
            <span className={`${product.inventory?.quantity > 0 ? 'text-green-600' : 'text-red-600'}`}>
              {product.inventory?.quantity > 0 ? 'In Stock' : 'Out of Stock'}
            </span>
          </div>
        </div>

        {/* Footer row: vendor + actions */}
        <div className="flex items-center justify-between mt-auto pt-2 border-t border-gray-50">
          <span className="text-xs text-gray-500 flex items-center truncate flex-1 mr-2">
            <EnvironmentOutlined className="mr-1 text-xs flex-shrink-0" />
            <span className="truncate">{product.vendor?.businessName || 'Store'}</span>
          </span>
          <div className="flex space-x-2 flex-shrink-0">
            <button 
              onClick={handleCartClick}
              className="text-gray-400 hover:text-blue-600 cursor-pointer transition-colors duration-200 p-1 rounded hover:bg-gray-50"
              title="Add to Cart"
              disabled={product.inventory?.quantity <= 0}
            >
              <ShoppingCartOutlined className="text-sm" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductItem;
