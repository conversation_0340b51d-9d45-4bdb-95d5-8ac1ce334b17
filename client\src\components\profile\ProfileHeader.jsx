import React from 'react';
import { 
  UserOutlined, 
  ShopOutlined, 
  MailOutlined, 
  SafetyCertificateOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined,
  EditOutlined
} from '@ant-design/icons';

const ProfileHeader = ({ user, userType, isEditing, onEdit, onSave, onCancel, saving }) => {
  const getStatusIcon = () => {
    if (user?.emailVerification?.isVerified) {
      return <CheckCircleOutlined className="text-green-500" />;
    }
    return <CloseCircleOutlined className="text-red-500" />;
  };

  const getStatusText = () => {
    if (user?.emailVerification?.isVerified) {
      return 'Verified Account';
    }
    return 'Unverified Account';
  };

  const getUserTypeIcon = () => {
    return userType === 'vendor' ? (
      <ShopOutlined className="text-orange-500 text-2xl" />
    ) : (
      <UserOutlined className="text-blue-500 text-2xl" />
    );
  };

  const getUserDisplayName = () => {
    if (userType === 'vendor') {
      return user?.businessName || user?.contactPerson || 'Vendor Account';
    }
    
    if (!user) return 'User Account';
    
    const firstName = user.firstName || '';
    const lastName = user.lastName || '';
    const fullName = `${firstName} ${lastName}`.trim();
    
    return fullName || user.email || 'User Account';
  };

  return (
    <div className="bg-gradient-to-r from-orange-500 to-pink-500 rounded-lg p-6 text-white mb-6">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex items-center space-x-4">
          <div className="bg-white/20 p-3 rounded-full">
            {getUserTypeIcon()}
          </div>
          <div>
            <h1 className="text-2xl font-bold">{getUserDisplayName()}</h1>
            <div className="flex items-center space-x-2 mt-1">
              <MailOutlined />
              <span className="text-orange-100">{user?.email}</span>
            </div>
            <div className="flex items-center space-x-2 mt-1">
              {getStatusIcon()}
              <span className="text-sm text-orange-100">{getStatusText()}</span>
            </div>
            <div className="flex items-center space-x-2 mt-1">
              <SafetyCertificateOutlined />
              <span className="text-sm text-orange-100 capitalize">
                {userType} Account
              </span>
            </div>
          </div>
        </div>
        
        <div className="flex space-x-3">
          {!isEditing ? (
            <button
              onClick={onEdit}
              className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-md transition-colors duration-200 flex items-center space-x-2"
            >
              <EditOutlined />
              <span>Edit Profile</span>
            </button>
          ) : (
            <>
              <button
                onClick={onCancel}
                disabled={saving}
                className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-md transition-colors duration-200 disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={onSave}
                disabled={saving}
                className="bg-white hover:bg-gray-100 text-orange-600 px-4 py-2 rounded-md transition-colors duration-200 disabled:opacity-50 font-medium"
              >
                {saving ? 'Saving...' : 'Save Changes'}
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfileHeader;
