import React from 'react';

const Footer = () => {

  const footerSections = [
    {
      title: "Get support",
      links: [
        { name: "Help Center", href: "/help" },
        { name: "Live chat", href: "/chat" },
        { name: "Check order status", href: "/track" },
         { name: "Refunds", href: "/refunds" },
      ]
    },
    {
      title: "Payments and protections",
      links: [
        { name: "Safe and easy payments", href: "/payments-page" },
        { name: "On-time shipping", href: "/shipping-page" },
        { name: "Money-back policy", href: "/money-back" },
      ]
    },
    
    {
      title: "Sell on Alicartify",
      links: [
        { name: "Start selling", href: "/start-selling" },
        { name: "Become a Verified Vendor", href: "/become-vendor" },
        { name: "Download App", href: "/app-coming-soon" }
      ]
    },
    {
      title: "Get to know us",
      links: [
        { name: "About Alicartify", href: "/about-us" },
        { name: "About Agency", href: "/about-agency" },
      ]
    },
    
  ];

  return (
    <footer className="bg-white text-black">
      {/* Main Footer Content - Centered */}
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 justify-center">
          {footerSections.map((section, index) => (
            <div key={index} className="text-center">
              <h4 className="text-lg font-semibold mb-4 text-black">{section.title}</h4>
              <ul className="space-y-2">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <a
                      href={link.href}
                      className="text-gray-600 hover:text-black transition-colors duration-200 text-sm"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>

      {/* Payment Methods Icons Bar */}
      <div className="bg-white py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            
            {/* Payment Methods - in a single row */}
            <div className="flex items-center justify-center gap-2">
              <img src="https://s.alicdn.com/@img/imgextra/i1/O1CN01L00bAM1TmF3L42KkI_!!6000000002424-2-tps-286-80.png" alt="MasterCard" className="h-8" />
              <img src="https://s.alicdn.com/@img/imgextra/i4/O1CN013pymTh1OIrZGMQ6iO_!!6000000001683-2-tps-93-80.png" alt="ID Check" className="h-8" />
              <img src="https://s.alicdn.com/@img/imgextra/i3/O1CN01CoqZOX1E5uCoNiJIr_!!6000000000301-2-tps-75-80.png" alt="RapidSSL" className="h-8" />
              <img src="https://s.alicdn.com/@img/imgextra/i1/O1CN01ba6iSo1PHJqZo1Gba_!!6000000001815-2-tps-81-80.png" alt="Verified" className="h-8" />
              <img src="https://s.alicdn.com/@img/imgextra/i4/O1CN01dsw9V61Lbh0D1f9JG_!!6000000001318-2-tps-205-112.png" alt="VISA" className="h-8" />
              <img src="https://s.alicdn.com/@img/imgextra/i4/O1CN01sXbha020agNJcLC4l_!!6000000006866-2-tps-148-112.png" alt="American Express" className="h-8" />
              <img src="https://s.alicdn.com/@img/imgextra/i1/O1CN01F2dH281hwEJACdKgv_!!6000000004341-2-tps-113-112.png" alt="PayPal" className="h-8" />
              <img src="https://s.alicdn.com/@img/imgextra/i1/O1CN017IIzE71MpGLv2nxMd_!!6000000001483-2-tps-260-112.png" alt="Apple Pay" className="h-8" />
              <img src="https://s.alicdn.com/@img/imgextra/i4/O1CN01yLWgha1BtsZXZDDih_!!6000000000004-2-tps-158-112.png" alt="Discover" className="h-8" />
              <img src="https://s.alicdn.com/@img/imgextra/i3/O1CN01EXUFiW1EgXt8o8Ev9_!!6000000000381-2-tps-125-112.png" alt="Diners Club" className="h-8" />
              <img src="https://s.alicdn.com/@img/imgextra/i3/O1CN01tkTNhl1ZaEMHoGWsA_!!6000000003210-2-tps-137-112.png" alt="JCB" className="h-8" />
              <img src="https://i.ibb.co/d0HtHMKp/unnamed.png" alt="Razorpay" className="h-8" />
              <img src="https://s.alicdn.com/@img/imgextra/i4/O1CN010I5eGr1aDcQ82EcRH_!!6000000003296-2-tps-169-112.png" alt="Union Pay" className="h-8" />
              <img src="https://s.alicdn.com/@img/imgextra/i1/O1CN01EF6Zjm21spgURRwKI_!!6000000007041-2-tps-138-112.png" alt="T/T" className="h-8" />
            </div>

            {/* App Store Links with Text */}
            <div className="flex flex-col items-center gap-2">
              <span className="text-sm text-gray-600">Trade on the go with the <span className="font-bold">Alicartify app</span></span>
              <div className="flex gap-4">
                <a href="/app-coming-soon">
                  <img 
                    src="https://s.alicdn.com/@img/imgextra/i4/O1CN018KnDNq1JleFgkjLRq_!!6000000001069-2-tps-447-132.png" 
                    alt="App Store" 
                    className="h-10"
                  />
                </a>
                <a href="/app-coming-soon">
                  <img 
                    src="https://s.alicdn.com/@img/imgextra/i4/O1CN01i9Aj641atkjJJ9I6y_!!6000000003388-2-tps-396-132.png" 
                    alt="Google Play" 
                    className="h-10"
                  />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Policy Links Section */}
      <div className="py-3" style={{backgroundColor: '#E5E7EB'}}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center items-center gap-4 text-xs">
            <a href="/policies-rules" className="text-gray-600 hover:text-black transition-colors duration-200">
              Policies and rules
            </a>
            <span className="text-gray-400">|</span>
            <a href="/legal-notice" className="text-gray-600 hover:text-black transition-colors duration-200">
              Legal Notice
            </a>
            <span className="text-gray-400">|</span>
            <a href="/product-listing-policy" className="text-gray-600 hover:text-black transition-colors duration-200">
              Product Listing Policy
            </a>
            <span className="text-gray-400">|</span>
            <a href="/intellectual-property" className="text-gray-600 hover:text-black transition-colors duration-200">
              Intellectual Property Protection
            </a>
            <span className="text-gray-400">|</span>
            <a href="/privacy-policy" className="text-gray-600 hover:text-black transition-colors duration-200">
              Privacy Policy
            </a>
            <span className="text-gray-400">|</span>
            <a href="/terms-of-use" className="text-gray-600 hover:text-black transition-colors duration-200">
              Terms of Use
            </a>
            <span className="text-gray-400">|</span>
            <a href="/integrity-compliance" className="text-gray-600 hover:text-black transition-colors duration-200">
              Integrity Compliance
            </a>
          </div>
        </div>
      </div>

      {/* Copyright Section */}
      <div className="py-2" style={{backgroundColor: '#E5E7EB'}}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-sm text-gray-700">Alicartify 2025 Copyright Reserve</p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
