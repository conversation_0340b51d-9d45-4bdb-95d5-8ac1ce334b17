import React, { useState, useEffect } from 'react';
import { Card, Button, Alert, Space, Typography, Descriptions } from 'antd';
import { BugOutlined } from '@ant-design/icons';
import { dashboardApi } from '../../../services/vendorApi';

const { Text, Paragraph } = Typography;

const AnalyticsDebug = () => {
  const [debugInfo, setDebugInfo] = useState({});
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState({});

  useEffect(() => {
    // Collect debug information
    const info = {
      authToken: localStorage.getItem('authToken') ? 'Present' : 'Missing',
      authUser: localStorage.getItem('authUser') ? 'Present' : 'Missing',
      apiUrl: import.meta.env.VITE_API_URL || 'Using default',
      timestamp: new Date().toISOString()
    };
    
    if (localStorage.getItem('authUser')) {
      try {
        const user = JSON.parse(localStorage.getItem('authUser'));
        info.userType = user.userType || 'Unknown';
        info.userId = user._id || 'Unknown';
        info.vendorId = user.vendorId || 'Unknown';
      } catch (e) {
        info.userParseError = 'Failed to parse user data';
      }
    }
    
    setDebugInfo(info);
  }, []);

  const testEndpoints = async () => {
    setTesting(true);
    setResults({});
    
    try {
      // Test dashboard stats endpoint
      try {
        console.log('🧪 Testing dashboard stats endpoint...');
        const dashboardResponse = await dashboardApi.getStats();
        console.log('Dashboard test response:', dashboardResponse);
        
        setResults(prev => ({
          ...prev,
          dashboard: {
            status: 'Success',
            data: dashboardResponse.data,
            timestamp: new Date().toISOString()
          }
        }));
      } catch (dashError) {
        console.error('Dashboard test error:', dashError);
        setResults(prev => ({
          ...prev,
          dashboard: {
            status: 'Error',
            error: dashError.message,
            statusCode: dashError.response?.status,
            timestamp: new Date().toISOString()
          }
        }));
      }

      // Test analytics endpoint
      try {
        console.log('🧪 Testing analytics endpoint...');
        const analyticsResponse = await dashboardApi.getAnalytics({ period: '30d', type: 'revenue' });
        console.log('Analytics test response:', analyticsResponse);
        
        setResults(prev => ({
          ...prev,
          analytics: {
            status: 'Success',
            data: analyticsResponse.data,
            timestamp: new Date().toISOString()
          }
        }));
      } catch (analyticsError) {
        console.error('Analytics test error:', analyticsError);
        setResults(prev => ({
          ...prev,
          analytics: {
            status: 'Error',
            error: analyticsError.message,
            statusCode: analyticsError.response?.status,
            timestamp: new Date().toISOString()
          }
        }));
      }
      
    } catch (error) {
      console.error('General test error:', error);
    } finally {
      setTesting(false);
    }
  };

  return (
    <Card 
      title={
        <Space>
          <BugOutlined />
          Analytics Debug Panel
        </Space>
      }
      style={{ marginBottom: 16 }}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <Alert
          message="Debug Information"
          description="This panel helps diagnose API issues. Remove this component after debugging."
          type="info"
          showIcon
        />
        
        <Descriptions title="Environment Info" bordered size="small">
          <Descriptions.Item label="Auth Token">{debugInfo.authToken}</Descriptions.Item>
          <Descriptions.Item label="Auth User">{debugInfo.authUser}</Descriptions.Item>
          <Descriptions.Item label="User Type">{debugInfo.userType || 'N/A'}</Descriptions.Item>
          <Descriptions.Item label="User ID">{debugInfo.userId || 'N/A'}</Descriptions.Item>
          <Descriptions.Item label="Vendor ID">{debugInfo.vendorId || 'N/A'}</Descriptions.Item>
          <Descriptions.Item label="API URL">{debugInfo.apiUrl}</Descriptions.Item>
        </Descriptions>

        <Button 
          type="primary" 
          onClick={testEndpoints} 
          loading={testing}
          icon={<BugOutlined />}
        >
          Test API Endpoints
        </Button>

        {Object.keys(results).length > 0 && (
          <div>
            <Text strong>Test Results:</Text>
            {results.dashboard && (
              <Card size="small" title="Dashboard Stats API" style={{ marginTop: 8 }}>
                <Space direction="vertical">
                  <Text>Status: <Text type={results.dashboard.status === 'Success' ? 'success' : 'danger'}>{results.dashboard.status}</Text></Text>
                  {results.dashboard.error && <Text type="danger">Error: {results.dashboard.error}</Text>}
                  {results.dashboard.statusCode && <Text>Status Code: {results.dashboard.statusCode}</Text>}
                  {results.dashboard.data && (
                    <Paragraph>
                      <Text code>{JSON.stringify(results.dashboard.data, null, 2)}</Text>
                    </Paragraph>
                  )}
                </Space>
              </Card>
            )}
            
            {results.analytics && (
              <Card size="small" title="Analytics API" style={{ marginTop: 8 }}>
                <Space direction="vertical">
                  <Text>Status: <Text type={results.analytics.status === 'Success' ? 'success' : 'danger'}>{results.analytics.status}</Text></Text>
                  {results.analytics.error && <Text type="danger">Error: {results.analytics.error}</Text>}
                  {results.analytics.statusCode && <Text>Status Code: {results.analytics.statusCode}</Text>}
                  {results.analytics.data && (
                    <Paragraph>
                      <Text code>{JSON.stringify(results.analytics.data, null, 2)}</Text>
                    </Paragraph>
                  )}
                </Space>
              </Card>
            )}
          </div>
        )}
      </Space>
    </Card>
  );
};

export default AnalyticsDebug;
