const mongoose = require('mongoose');

const inventorySchema = new mongoose.Schema({
  trackQuantity: {
    type: Boolean,
    default: true
  },
  quantity: {
    type: Number,
    required: false, // Remove conditional validation that causes issues in updates
    min: [0, 'Quantity cannot be negative'],
    default: 0
  },
  lowStockThreshold: {
    type: Number,
    min: 0,
    default: 5
  },
  allowBackorders: {
    type: Boolean,
    default: false
  },
  stockStatus: {
    type: String,
    enum: ['in_stock', 'out_of_stock', 'on_backorder'],
    default: 'in_stock'
  }
}, { _id: false });

module.exports = { inventorySchema };
