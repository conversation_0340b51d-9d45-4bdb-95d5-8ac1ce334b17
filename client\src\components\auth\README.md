# Authentication System

This directory contains a comprehensive authentication system for the multi-vendor eCommerce platform with beautiful, responsive UI components.

## Features

### 🎨 UI Components
- **AuthSegment**: Block segment component to switch between User and Vendor registration/login
- **AuthInput**: Reusable input component with validation states and icons
- **AuthButton**: Customizable button component with multiple variants and loading states
- **LoginForm**: Complete login form with email/password and social login options
- **SignUpForm**: Registration form with different fields for users vs vendors
- **ForgotPasswordForm**: Password reset form with email verification flow

### 🔐 Authentication Features
- **Dual User Types**: Separate authentication flows for Users and Vendors
- **Form Validation**: Client-side validation with real-time feedback
- **Social Login**: Integration-ready for Google, Facebook, and GitHub
- **Password Reset**: Complete forgot password flow with email verification
- **Remember Me**: Persistent login sessions
- **Terms & Conditions**: Checkbox validation for registration

### 📱 Responsive Design
- **Mobile-First**: Optimized for all screen sizes
- **Touch-Friendly**: Proper touch targets for mobile devices
- **Adaptive Layout**: Different layouts for desktop and mobile
- **Accessibility**: WCAG compliant with proper focus states

### 🎯 User Experience
- **Smooth Animations**: CSS transitions for all interactions
- **Loading States**: Visual feedback during API calls
- **Error Handling**: User-friendly error messages
- **Success Feedback**: Confirmation messages for successful actions

## Component Structure

```
auth/
├── LoginForm.jsx          # Login form component
├── SignUpForm.jsx         # Registration form component
├── ForgotPasswordForm.jsx # Password reset form
└── README.md             # This file

ui/
├── AuthSegment.jsx       # User/Vendor selector
├── AuthInput.jsx         # Styled input component
└── AuthButton.jsx        # Styled button component
```

## Usage

### Basic Implementation

```jsx
import AuthPage from '../pages/AuthPage';

// Use in your routing
<Route path="/auth" element={<AuthPage />} />
```

### With Authentication Context

```jsx
import { useAuth } from '../contexts/AuthContext';

function MyComponent() {
  const { login, register, isAuthenticated, user } = useAuth();
  
  // Use authentication methods
}
```

### Custom Form Implementation

```jsx
import LoginForm from '../components/auth/LoginForm';

<LoginForm
  userType="user" // or "vendor"
  onLogin={handleLogin}
  onForgotPassword={handleForgotPassword}
  onSocialLogin={handleSocialLogin}
/>
```

## Styling

The authentication system uses a combination of:
- **Tailwind CSS**: For utility classes and responsive design
- **Ant Design**: For base components and consistent styling
- **Custom CSS**: For enhanced animations and brand-specific styling

### Color Scheme
- Primary: Orange gradient (#ff6b35 to #f7931e)
- Secondary: Gray tones for text and borders
- Success: Green for positive feedback
- Error: Red for validation errors

### Typography
- Headings: Bold, large sizes for hierarchy
- Body: Clean, readable fonts
- Labels: Medium weight for form labels

## Validation

### Client-Side Validation
- Email format validation
- Password strength requirements
- Phone number format
- Required field validation
- Password confirmation matching

### Password Requirements
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- Special characters recommended

## API Integration

The system is designed to work with RESTful APIs:

```javascript
// Login endpoint
POST /api/auth/login
POST /api/auth/vendor/login

// Registration endpoint
POST /api/auth/register
POST /api/auth/vendor/register

// Password reset
POST /api/auth/forgot-password
POST /api/auth/reset-password

// Social login
POST /api/auth/social/{provider}
```

## Customization

### Themes
Modify the CSS variables in `auth.css` to change colors:

```css
:root {
  --auth-primary: #ff6b35;
  --auth-secondary: #f7931e;
  --auth-text: #374151;
}
```

### Form Fields
Add custom fields by extending the form components:

```jsx
// In SignUpForm.jsx
const renderCustomFields = () => (
  <Form.Item name="customField">
    <AuthInput placeholder="Custom Field" />
  </Form.Item>
);
```

### Validation Rules
Extend validation in the form components:

```jsx
const customRules = [
  { required: true, message: 'Custom validation message' },
  { pattern: /regex/, message: 'Pattern validation' }
];
```

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance

- Lazy loading for form components
- Optimized bundle size with tree shaking
- Minimal re-renders with React hooks
- Efficient form validation

## Security Considerations

- Client-side validation only (server validation required)
- No sensitive data stored in localStorage
- HTTPS required for production
- CSRF protection recommended
- Rate limiting on API endpoints

## Future Enhancements

- [ ] Two-factor authentication
- [ ] Biometric login support
- [ ] OAuth 2.0 integration
- [ ] Progressive Web App features
- [ ] Offline support
- [ ] Multi-language support