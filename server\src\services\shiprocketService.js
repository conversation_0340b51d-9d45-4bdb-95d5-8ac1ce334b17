const axios = require('axios');
const Vendor = require('../models/Vendor');

class ShiprocketService {
  constructor() {
    this.baseURL = 'https://apiv2.shiprocket.in/v1/external';
    this.token = null;
    this.tokenExpiry = null;
  }

  // Get authentication token
  async authenticate() {
    try {
      const email = String(process.env.SHIPROCKET_EMAIL || '').trim();
      const password = String(process.env.SHIPROCKET_PASSWORD || '').trim();

      const response = await axios.post(`${this.baseURL}/auth/login`, {
        email,
        password
      });

      this.token = response.data.token;
      this.tokenExpiry = new Date(Date.now() + (9 * 24 * 60 * 60 * 1000));
      return this.token;
    } catch (error) {
      console.error('Shiprocket authentication failed:', error.response?.data || error.message);
      throw new Error('Failed to authenticate with Shiprocket');
    }
  }

  // Get headers with authentication
  async getAuthHeaders() {
    if (!this.token || new Date() >= this.tokenExpiry) {
      await this.authenticate();
    }
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`
    };
  }

  async fetchPickupLocations() {
    const headers = await this.getAuthHeaders();
    const response = await axios.get(`${this.baseURL}/settings/company/pickup`, { headers });
    const list = Array.isArray(response.data?.data) ? response.data.data : [];
    return list;
  }

  async addPickupLocationForVendor(vendor) {
    if (!vendor) {
      throw new Error('Vendor required to add pickup location');
    }
    const headers = await this.getAuthHeaders();
    const payload = {
      pickup_location: String(vendor.shiprocket?.pickupLocationName || vendor.businessName || 'Primary').trim(),
      name: String(vendor.businessName || '').trim(),
      email: String(vendor.contactInfo?.businessEmail || process.env.SHIPROCKET_EMAIL || '').trim(),
      phone: String(vendor.contactInfo?.businessPhone || '').trim(),
      address: String(vendor.businessAddress?.street || '').trim(),
      address_2: '',
      city: String(vendor.businessAddress?.city || '').trim(),
      state: String(vendor.businessAddress?.state || '').trim(),
      country: String(vendor.businessAddress?.country || 'India').trim(),
      pin_code: String(vendor.businessAddress?.zipCode || '').trim()
    };

    const response = await axios.post(`${this.baseURL}/settings/company/addpickup`, payload, { headers });
    // Best-effort persist of the configured name
    try {
      vendor.shiprocket = vendor.shiprocket || {};
      vendor.shiprocket.pickupLocationName = payload.pickup_location;
      await vendor.save();
    } catch (_) {}
    return response.data;
  }

  async resolvePickupLocationName(preferredName, vendor) {
    // If an explicit env is provided, try to use it if it exists
    const envName = String(preferredName || '').trim();
    const locations = await this.fetchPickupLocations();
    const names = new Set(locations.map(loc => String(loc.pickup_location || '').trim()).filter(Boolean));

    if (envName && names.has(envName)) return envName;

    // If vendor business name exists and matches, use it
    const vendorName = vendor ? String(vendor.businessName || '').trim() : '';
    if (vendorName && names.has(vendorName)) return vendorName;

    // Attempt to auto-create a pickup location for this vendor using their business address
    if (vendorName) {
      try {
        await this.addPickupLocationForVendor(vendor);
        // Re-fetch and validate
        const updated = await this.fetchPickupLocations();
        const updatedNames = new Set(updated.map(loc => String(loc.pickup_location || '').trim()).filter(Boolean));
        if (updatedNames.has(vendorName)) return vendorName;
      } catch (e) {
        // If creation fails due to permissions, we'll fall back below
      }
    }

    // Fallback to the first available pickup location
    const first = locations.find(l => l && l.pickup_location);
    if (first?.pickup_location) return String(first.pickup_location);

    throw new Error('No valid Shiprocket pickup locations found. Please configure one in your Shiprocket dashboard.');
  }

  // Create order with vendor's address as pickup
  async createOrder(orderData) {
    try {
      const headers = await this.getAuthHeaders();

      // Get first vendor from items to use their address
      const firstItem = orderData.items[0];
      const vendor = await Vendor.findById(firstItem.vendor);

      // Resolve a valid pickup location name from Shiprocket account
      const pickupLocationName = await this.resolvePickupLocationName(process.env.SHIPROCKET_PICKUP_LOCATION, vendor);
      
      const shiprocketOrder = {
        order_id: orderData.orderNumber,
        order_date: new Date().toISOString().split('T')[0],
        // Use a validated pickup location name
        pickup_location: pickupLocationName,
        billing_customer_name: `${orderData.billing.firstName} ${orderData.billing.lastName}`,
        billing_address: orderData.billing.address.street,
        billing_city: orderData.billing.address.city,
        billing_pincode: String(orderData.billing.address.zipCode),
        billing_state: orderData.billing.address.state,
        billing_country: orderData.billing.address.country || 'India',
        billing_email: orderData.billing.email,
        billing_phone: String(orderData.billing.phone),
        shipping_is_billing: false,
        shipping_customer_name: `${orderData.shipping.firstName} ${orderData.shipping.lastName}`,
        shipping_address: orderData.shipping.address.street,
        shipping_city: orderData.shipping.address.city,
        shipping_pincode: String(orderData.shipping.address.zipCode),
        shipping_state: orderData.shipping.address.state,
        shipping_country: orderData.shipping.address.country || 'India',
        shipping_phone: String(orderData.shipping.phone || orderData.billing.phone),
        order_items: orderData.items.map(item => ({
          name: item.name,
          sku: item.sku,
          units: item.quantity,
          selling_price: parseFloat(item.unitPrice || item.totalPrice / item.quantity),
        })),
        payment_method: orderData.payment.method === 'cod' ? 'COD' : 'Prepaid',
        shipping_charges: parseFloat(orderData.pricing.shipping || 0),
        sub_total: parseFloat(orderData.pricing.subtotal),
        length: 10,
        breadth: 10,
        height: 10,
        weight: 0.5
      };

      const response = await axios.post(`${this.baseURL}/orders/create/adhoc`, shiprocketOrder, { headers });
      return response.data;
    } catch (error) {
      console.error('Failed to create Shiprocket order:', error.response?.data || error.message);
      throw new Error(`Failed to create order in Shiprocket: ${error.response?.data?.message || error.message}`);
    }
  }

  // Create separate Shiprocket orders per vendor using each vendor's pickup location
  async createOrdersForVendors(orderData) {
    try {
      const headers = await this.getAuthHeaders();

      // Group items by vendor
      const vendorToItemsMap = new Map();
      for (const item of orderData.items) {
        const vendorId = String(item.vendor);
        if (!vendorToItemsMap.has(vendorId)) vendorToItemsMap.set(vendorId, []);
        vendorToItemsMap.get(vendorId).push(item);
      }

      const results = [];
      let vendorIndex = 0;
      for (const [vendorId, vendorItems] of vendorToItemsMap.entries()) {
        vendorIndex += 1;
        const vendor = await Vendor.findById(vendorId);

        // Prefer explicit vendor pickup setting, else vendor business name, else env
        const preferredName = (vendor?.shiprocket?.pickupLocationName) || (vendor?.businessName) || process.env.SHIPROCKET_PICKUP_LOCATION;
        const pickupLocationName = await this.resolvePickupLocationName(preferredName, vendor);

        // Ensure unique order_id per vendor
        const vendorOrderId = `${orderData.orderNumber}-${vendorIndex}`;

        const shiprocketOrder = {
          order_id: vendorOrderId,
          order_date: new Date().toISOString().split('T')[0],
          pickup_location: pickupLocationName,
          billing_customer_name: `${orderData.billing.firstName} ${orderData.billing.lastName}`,
          billing_address: orderData.billing.address.street,
          billing_city: orderData.billing.address.city,
          billing_pincode: String(orderData.billing.address.zipCode),
          billing_state: orderData.billing.address.state,
          billing_country: orderData.billing.address.country || 'India',
          billing_email: orderData.billing.email,
          billing_phone: String(orderData.billing.phone),
          shipping_is_billing: false,
          shipping_customer_name: `${orderData.shipping.firstName} ${orderData.shipping.lastName}`,
          shipping_address: orderData.shipping.address.street,
          shipping_city: orderData.shipping.address.city,
          shipping_pincode: String(orderData.shipping.address.zipCode),
          shipping_state: orderData.shipping.address.state,
          shipping_country: orderData.shipping.address.country || 'India',
          shipping_phone: String(orderData.shipping.phone || orderData.billing.phone),
          order_items: vendorItems.map(item => ({
            name: item.name,
            sku: item.sku,
            units: item.quantity,
            selling_price: parseFloat(item.unitPrice || item.totalPrice / item.quantity),
          })),
          payment_method: orderData.payment.method === 'cod' ? 'COD' : 'Prepaid',
          shipping_charges: 0,
          sub_total: parseFloat(vendorItems.reduce((acc, it) => acc + (it.unitPrice || (it.totalPrice || 0) / Math.max(1, it.quantity)), 0)),
          length: 10,
          breadth: 10,
          height: 10,
          weight: 0.5
        };

        try {
          const response = await axios.post(`${this.baseURL}/orders/create/adhoc`, shiprocketOrder, { headers });
          results.push({ vendorId, pickupLocation: pickupLocationName, response: response.data });
        } catch (error) {
          const errMsg = error.response?.data?.message || error.message;
          console.error('Failed to create Shiprocket order for vendor:', vendorId, error.response?.data || error.message);
          results.push({ vendorId, pickupLocation: pickupLocationName, error: errMsg, status: error.response?.status });
        }
      }

      return results;
    } catch (error) {
      console.error('Failed to create Shiprocket orders per vendor:', error.response?.data || error.message);
      throw new Error(`Failed to create Shiprocket orders: ${error.response?.data?.message || error.message}`);
    }
  }
  // Track shipment
  async trackShipment(awb) {
    try {
      const headers = await this.getAuthHeaders();
      const response = await axios.get(`${this.baseURL}/courier/track/awb/${awb}`, { headers });
      return response.data;
    } catch (error) {
      console.error('Failed to track shipment:', error.response?.data || error.message);
      throw new Error(`Failed to track shipment: ${error.response?.data?.message || error.message}`);
    }
  }
}

module.exports = new ShiprocketService();
