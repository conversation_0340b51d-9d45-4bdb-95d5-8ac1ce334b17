const axios = require('axios');
const Vendor = require('../models/Vendor');

class ShiprocketService {
  constructor() {
    this.baseURL = 'https://apiv2.shiprocket.in/v1/external';
    this.token = null;
    this.tokenExpiry = null;
  }

  // Get authentication token
  async authenticate() {
    try {
      const email = String(process.env.SHIPROCKET_EMAIL || '').trim();
      const password = String(process.env.SHIPROCKET_PASSWORD || '').trim();

      const response = await axios.post(`${this.baseURL}/auth/login`, {
        email,
        password
      });

      this.token = response.data.token;
      this.tokenExpiry = new Date(Date.now() + (9 * 24 * 60 * 60 * 1000));
      return this.token;
    } catch (error) {
      console.error('Shiprocket authentication failed:', error.response?.data || error.message);
      throw new Error('Failed to authenticate with Shiprocket');
    }
  }

  // Get headers with authentication
  async getAuthHeaders() {
    if (!this.token || new Date() >= this.tokenExpiry) {
      await this.authenticate();
    }
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`
    };
  }

  async fetchPickupLocations() {
    const headers = await this.getAuthHeaders();
    const response = await axios.get(`${this.baseURL}/settings/company/pickup`, { headers });
    const list = Array.isArray(response.data?.data) ? response.data.data : [];
    return list;
  }

  // Validate vendor data for pickup location creation
  validateVendorForPickup(vendor) {
    const errors = [];

    if (!vendor) {
      errors.push('Vendor is required');
      return errors;
    }

    if (!vendor.businessName || vendor.businessName.trim() === '') {
      errors.push('Business name is required');
    }

    if (!vendor.businessAddress) {
      errors.push('Business address is required');
    } else {
      if (!vendor.businessAddress.street || vendor.businessAddress.street.trim() === '') {
        errors.push('Business address street is required');
      }
      if (!vendor.businessAddress.city || vendor.businessAddress.city.trim() === '') {
        errors.push('Business address city is required');
      }
      if (!vendor.businessAddress.state || vendor.businessAddress.state.trim() === '') {
        errors.push('Business address state is required');
      }
      if (!vendor.businessAddress.zipCode || vendor.businessAddress.zipCode.trim() === '') {
        errors.push('Business address zip code is required');
      }
    }

    if (!vendor.contactInfo) {
      errors.push('Contact information is required');
    } else {
      if (!vendor.contactInfo.businessPhone || vendor.contactInfo.businessPhone.trim() === '') {
        errors.push('Business phone number is required');
      }
    }

    return errors;
  }

  async addPickupLocationForVendor(vendor) {
    // Validate vendor data
    const validationErrors = this.validateVendorForPickup(vendor);
    if (validationErrors.length > 0) {
      throw new Error(`Cannot create pickup location for vendor ${vendor?.businessName || 'Unknown'}: ${validationErrors.join(', ')}`);
    }

    const headers = await this.getAuthHeaders();

    // Create unique pickup location name to avoid conflicts
    const baseLocationName = String(vendor.businessName || 'Vendor').trim();
    const uniqueLocationName = `${baseLocationName}-${vendor._id.toString().slice(-6)}`;

    const payload = {
      pickup_location: uniqueLocationName,
      name: String(vendor.businessName || '').trim(),
      email: String(vendor.contactInfo?.businessEmail || process.env.SHIPROCKET_EMAIL || '').trim(),
      phone: String(vendor.contactInfo?.businessPhone || '').trim(),
      address: String(vendor.businessAddress?.street || '').trim(),
      address_2: '',
      city: String(vendor.businessAddress?.city || '').trim(),
      state: String(vendor.businessAddress?.state || '').trim(),
      country: String(vendor.businessAddress?.country || 'India').trim(),
      pin_code: String(vendor.businessAddress?.zipCode || '').trim()
    };

    console.log(`Creating Shiprocket pickup location for vendor ${vendor.businessName}:`, payload);

    try {
      const response = await axios.post(`${this.baseURL}/settings/company/addpickup`, payload, { headers });

      // Persist the configured name and code to vendor
      vendor.shiprocket = vendor.shiprocket || {};
      vendor.shiprocket.pickupLocationName = uniqueLocationName;
      vendor.shiprocket.pickupLocationCode = response.data?.pickup_id || null;
      await vendor.save();

      console.log(`✅ Successfully created pickup location for vendor ${vendor.businessName}: ${uniqueLocationName}`);
      return response.data;
    } catch (error) {
      console.error(`❌ Failed to create pickup location for vendor ${vendor.businessName}:`, error.response?.data || error.message);
      throw new Error(`Failed to create pickup location for vendor ${vendor.businessName}: ${error.response?.data?.message || error.message}`);
    }
  }

  async resolvePickupLocationName(preferredName, vendor) {
    console.log(`🔍 Resolving pickup location for vendor: ${vendor?.businessName || 'Unknown'}`);

    try {
      const locations = await this.fetchPickupLocations();
      const names = new Set(locations.map(loc => String(loc.pickup_location || '').trim()).filter(Boolean));

      console.log(`📍 Available pickup locations: [${Array.from(names).join(', ')}]`);

      // Priority 1: Check if vendor already has a configured pickup location
      if (vendor?.shiprocket?.pickupLocationName) {
        const vendorPickupName = String(vendor.shiprocket.pickupLocationName).trim();
        if (names.has(vendorPickupName)) {
          console.log(`✅ Using existing vendor pickup location: ${vendorPickupName}`);
          return vendorPickupName;
        } else {
          console.log(`⚠️  Vendor's configured pickup location '${vendorPickupName}' not found in Shiprocket`);
        }
      }

      // Priority 2: Check if vendor business name matches existing location
      const vendorName = vendor ? String(vendor.businessName || '').trim() : '';
      if (vendorName && names.has(vendorName)) {
        console.log(`✅ Using vendor business name as pickup location: ${vendorName}`);
        return vendorName;
      }

      // Priority 3: Check environment variable preference
      const envName = String(preferredName || '').trim();
      if (envName && names.has(envName)) {
        console.log(`✅ Using environment preferred pickup location: ${envName}`);
        return envName;
      }

      // Priority 4: Auto-create pickup location for vendor
      if (vendor && vendorName) {
        console.log(`🔧 Attempting to create new pickup location for vendor: ${vendorName}`);
        try {
          const createResult = await this.addPickupLocationForVendor(vendor);

          // Re-fetch locations to get the newly created one
          const updatedLocations = await this.fetchPickupLocations();
          const updatedNames = new Set(updatedLocations.map(loc => String(loc.pickup_location || '').trim()).filter(Boolean));

          // Check for the unique location name we created
          const uniqueLocationName = `${vendorName}-${vendor._id.toString().slice(-6)}`;
          if (updatedNames.has(uniqueLocationName)) {
            console.log(`✅ Successfully created and using new pickup location: ${uniqueLocationName}`);
            return uniqueLocationName;
          }

          console.log(`⚠️  Created pickup location but couldn't find it in updated list`);
        } catch (createError) {
          console.error(`❌ Failed to create pickup location for vendor ${vendorName}:`, createError.message);
        }
      }

      // Priority 5: Fallback to first available pickup location
      const first = locations.find(l => l && l.pickup_location);
      if (first?.pickup_location) {
        const fallbackName = String(first.pickup_location);
        console.log(`⚠️  Using fallback pickup location: ${fallbackName}`);
        return fallbackName;
      }

      // If we reach here, there are no pickup locations at all
      throw new Error('No valid Shiprocket pickup locations found. Please configure at least one pickup location in your Shiprocket dashboard.');

    } catch (error) {
      console.error('❌ Error in resolvePickupLocationName:', error.message);
      throw error;
    }
  }

  // Create order with vendor's address as pickup (single vendor order)
  async createOrder(orderData) {
    console.log(`🚀 Creating single Shiprocket order: ${orderData.orderNumber}`);

    try {
      const headers = await this.getAuthHeaders();

      // Get first vendor from items to use their address
      const firstItem = orderData.items[0];
      if (!firstItem || !firstItem.vendor) {
        throw new Error('No vendor found in order items');
      }

      const vendor = await Vendor.findById(firstItem.vendor);
      if (!vendor) {
        throw new Error(`Vendor not found: ${firstItem.vendor}`);
      }

      console.log(`📋 Processing single order for vendor: ${vendor.businessName}`);

      // Resolve a valid pickup location name from Shiprocket account
      const preferredName = (vendor?.shiprocket?.pickupLocationName) || (vendor?.businessName) || process.env.SHIPROCKET_PICKUP_LOCATION;
      const pickupLocationName = await this.resolvePickupLocationName(preferredName, vendor);

      const shiprocketOrder = {
        order_id: orderData.orderNumber,
        order_date: new Date().toISOString().split('T')[0],
        pickup_location: pickupLocationName,
        billing_customer_name: `${orderData.billing.firstName} ${orderData.billing.lastName}`,
        billing_address: orderData.billing.address.street,
        billing_city: orderData.billing.address.city,
        billing_pincode: String(orderData.billing.address.zipCode),
        billing_state: orderData.billing.address.state,
        billing_country: orderData.billing.address.country || 'India',
        billing_email: orderData.billing.email,
        billing_phone: String(orderData.billing.phone),
        shipping_is_billing: false,
        shipping_customer_name: `${orderData.shipping.firstName} ${orderData.shipping.lastName}`,
        shipping_address: orderData.shipping.address.street,
        shipping_city: orderData.shipping.address.city,
        shipping_pincode: String(orderData.shipping.address.zipCode),
        shipping_state: orderData.shipping.address.state,
        shipping_country: orderData.shipping.address.country || 'India',
        shipping_phone: String(orderData.shipping.phone || orderData.billing.phone),
        order_items: orderData.items.map(item => ({
          name: item.name,
          sku: item.sku,
          units: item.quantity,
          selling_price: parseFloat(item.unitPrice || item.totalPrice / item.quantity),
        })),
        payment_method: orderData.payment.method === 'cod' ? 'COD' : 'Prepaid',
        shipping_charges: parseFloat(orderData.pricing.shipping || 0),
        sub_total: parseFloat(orderData.pricing.subtotal),
        length: 10,
        breadth: 10,
        height: 10,
        weight: 0.5
      };

      console.log(`📤 Creating Shiprocket order with pickup location: ${pickupLocationName}`);
      const response = await axios.post(`${this.baseURL}/orders/create/adhoc`, shiprocketOrder, { headers });

      console.log(`✅ Successfully created Shiprocket order: ${response.data?.order_id}`);
      return response.data;

    } catch (error) {
      console.error('❌ Failed to create Shiprocket order:', error.response?.data || error.message);
      throw new Error(`Failed to create order in Shiprocket: ${error.response?.data?.message || error.message}`);
    }
  }

  // Create separate Shiprocket orders per vendor using each vendor's pickup location
  async createOrdersForVendors(orderData) {
    console.log(`🚀 Creating Shiprocket orders for order: ${orderData.orderNumber}`);

    try {
      const headers = await this.getAuthHeaders();

      // Group items by vendor
      const vendorToItemsMap = new Map();
      for (const item of orderData.items) {
        const vendorId = String(item.vendor);
        if (!vendorToItemsMap.has(vendorId)) vendorToItemsMap.set(vendorId, []);
        vendorToItemsMap.get(vendorId).push(item);
      }

      console.log(`📦 Processing ${vendorToItemsMap.size} vendor(s) for order ${orderData.orderNumber}`);

      const results = [];
      let vendorIndex = 0;

      for (const [vendorId, vendorItems] of vendorToItemsMap.entries()) {
        vendorIndex += 1;
        console.log(`\n🏪 Processing vendor ${vendorIndex}/${vendorToItemsMap.size}: ${vendorId}`);

        try {
          const vendor = await Vendor.findById(vendorId);
          if (!vendor) {
            const error = `Vendor not found: ${vendorId}`;
            console.error(`❌ ${error}`);
            results.push({ vendorId, error, status: 'vendor_not_found' });
            continue;
          }

          console.log(`📋 Vendor details: ${vendor.businessName} (${vendor.contactInfo?.businessEmail})`);

          // Resolve pickup location for this specific vendor
          const preferredName = (vendor?.shiprocket?.pickupLocationName) || (vendor?.businessName) || process.env.SHIPROCKET_PICKUP_LOCATION;
          const pickupLocationName = await this.resolvePickupLocationName(preferredName, vendor);

          // Ensure unique order_id per vendor
          const vendorOrderId = `${orderData.orderNumber}-V${vendorIndex}`;

          const shiprocketOrder = {
            order_id: vendorOrderId,
            order_date: new Date().toISOString().split('T')[0],
            pickup_location: pickupLocationName,
            billing_customer_name: `${orderData.billing.firstName} ${orderData.billing.lastName}`,
            billing_address: orderData.billing.address.street,
            billing_city: orderData.billing.address.city,
            billing_pincode: String(orderData.billing.address.zipCode),
            billing_state: orderData.billing.address.state,
            billing_country: orderData.billing.address.country || 'India',
            billing_email: orderData.billing.email,
            billing_phone: String(orderData.billing.phone),
            shipping_is_billing: false,
            shipping_customer_name: `${orderData.shipping.firstName} ${orderData.shipping.lastName}`,
            shipping_address: orderData.shipping.address.street,
            shipping_city: orderData.shipping.address.city,
            shipping_pincode: String(orderData.shipping.address.zipCode),
            shipping_state: orderData.shipping.address.state,
            shipping_country: orderData.shipping.address.country || 'India',
            shipping_phone: String(orderData.shipping.phone || orderData.billing.phone),
            order_items: vendorItems.map(item => ({
              name: item.name,
              sku: item.sku,
              units: item.quantity,
              selling_price: parseFloat(item.unitPrice || item.totalPrice / item.quantity),
            })),
            payment_method: orderData.payment.method === 'cod' ? 'COD' : 'Prepaid',
            shipping_charges: 0,
            sub_total: parseFloat(vendorItems.reduce((acc, it) => acc + (it.unitPrice || (it.totalPrice || 0) / Math.max(1, it.quantity)), 0)),
            length: 10,
            breadth: 10,
            height: 10,
            weight: 0.5
          };

          console.log(`📤 Creating Shiprocket order for vendor ${vendor.businessName} with pickup location: ${pickupLocationName}`);

          const response = await axios.post(`${this.baseURL}/orders/create/adhoc`, shiprocketOrder, { headers });

          console.log(`✅ Successfully created Shiprocket order for vendor ${vendor.businessName}: ${response.data?.order_id}`);
          results.push({
            vendorId,
            vendorName: vendor.businessName,
            pickupLocation: pickupLocationName,
            response: response.data
          });

        } catch (error) {
          const errMsg = error.response?.data?.message || error.message;
          console.error(`❌ Failed to create Shiprocket order for vendor ${vendorId}:`, error.response?.data || error.message);
          results.push({
            vendorId,
            pickupLocation: null,
            error: errMsg,
            status: error.response?.status || 'unknown_error'
          });
        }
      }

      console.log(`\n📊 Shiprocket order creation summary: ${results.filter(r => !r.error).length}/${results.length} successful`);
      return results;

    } catch (error) {
      console.error('❌ Critical error in createOrdersForVendors:', error.response?.data || error.message);
      throw new Error(`Failed to create Shiprocket orders: ${error.response?.data?.message || error.message}`);
    }
  }
  // Track shipment
  async trackShipment(awb) {
    try {
      const headers = await this.getAuthHeaders();
      const response = await axios.get(`${this.baseURL}/courier/track/awb/${awb}`, { headers });
      return response.data;
    } catch (error) {
      console.error('Failed to track shipment:', error.response?.data || error.message);
      throw new Error(`Failed to track shipment: ${error.response?.data?.message || error.message}`);
    }
  }
}

module.exports = new ShiprocketService();
