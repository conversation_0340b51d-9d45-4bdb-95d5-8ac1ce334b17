require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./src/models/User');
const Vendor = require('./src/models/Vendor');

async function findVendorUsers() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('📊 Connected to database');
    
    console.log('\n=== Finding Vendor Users ===');
    
    // Find users with vendor type
    const vendorUsers = await User.find({ userType: 'vendor' })
      .select('email firstName lastName userType status isEmailVerified')
      .limit(10);
    
    console.log(`Found ${vendorUsers.length} vendor users:`);
    
    if (vendorUsers.length === 0) {
      console.log('No vendor users found. Let\'s create a test vendor user...');
      
      // Create a test vendor user
      const testVendor = new User({
        firstName: 'Test',
        lastName: 'Vendor', 
        email: '<EMAIL>',
        password: 'password123', // This will be hashed by the model
        userType: 'vendor',
        status: 'active',
        isEmailVerified: true
      });
      
      await testVendor.save();
      console.log('✅ Created test vendor user:');
      console.log('Email: <EMAIL>');
      console.log('Password: password123');
      
    } else {
      vendorUsers.forEach((user, index) => {
        console.log(`${index + 1}. Email: ${user.email}`);
        console.log(`   Name: ${user.firstName} ${user.lastName}`);
        console.log(`   Status: ${user.status}`);
        console.log(`   Email Verified: ${user.isEmailVerified}`);
        console.log('');
      });
      
      // Check if these users have vendor records
      console.log('\n=== Checking Vendor Records ===');
      
      for (const user of vendorUsers) {
        const vendorRecord = await Vendor.findOne({ user: user._id });
        if (vendorRecord) {
          console.log(`✅ ${user.email} has vendor record - Status: ${vendorRecord.status}, Verification: ${vendorRecord.verification.status}`);
        } else {
          console.log(`❌ ${user.email} has NO vendor record`);
        }
      }
      
      // Use the first vendor user for testing
      if (vendorUsers.length > 0) {
        console.log(`\n=== Use this for testing ===`);
        console.log(`Email: ${vendorUsers[0].email}`);
        console.log(`Password: You'll need to know their password or reset it`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n📊 Disconnected from database');
  }
}

findVendorUsers();
