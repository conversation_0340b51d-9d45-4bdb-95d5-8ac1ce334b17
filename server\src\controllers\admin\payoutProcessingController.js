const Payout = require('../../models/Payout');
const Vendor = require('../../models/Vendor');
const { validationResult } = require('express-validator');

/**
 * Admin Payout Processing Operations Controller
 * Handles approval, rejection, and completion of payout requests
 */
class PayoutProcessingController {

  /**
   * Approve a payout request
   */
  static async approvePayout(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      const { payoutId } = req.params;
      const { notes, processingFee } = req.body;
      const adminId = req.user._id;

      // Find and approve
      const payout = await Payout.findOne({ _id: payoutId, status: 'pending' }).populate('vendor');
      if (!payout) {
        return res.status(404).json({
          success: false,
          message: 'Pending payout not found'
        });
      }

      if (processingFee !== undefined) {
        payout.processingFee = processingFee;
      }

      await payout.approve(adminId, notes);

      res.json({
        success: true,
        message: 'Payout approved successfully',
        data: {
          payoutId: payout._id,
          status: payout.status,
          processedDate: payout.processedDate
        }
      });

    } catch (error) {
      console.error('Error approving payout:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  /**
   * Reject a payout request
   */
  static async rejectPayout(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      const { payoutId } = req.params;
      const { reason, notes } = req.body;
      const adminId = req.user._id;

      const payout = await Payout.findOne({ _id: payoutId, status: 'pending' }).populate('vendor');
      if (!payout) {
        return res.status(404).json({
          success: false,
          message: 'Pending payout not found'
        });
      }

      await payout.reject(adminId, reason, notes);

      const vendor = await Vendor.findById(payout.vendor._id);
      vendor.commission.pendingAmount += payout.requestedAmount;
      await vendor.save();

      res.json({
        success: true,
        message: 'Payout rejected successfully',
        data: {
          payoutId: payout._id,
          status: payout.status,
          processedDate: payout.processedDate
        }
      });

    } catch (error) {
      console.error('Error rejecting payout:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  /**
   * Mark payout as processing, preparing for execution
   */
  static async markAsProcessing(req, res) {
    try {
      const { payoutId } = req.params;
      const { transactionId } = req.body;

      const payout = await Payout.findOne({ _id: payoutId, status: 'approved' });
      if (!payout) {
        return res.status(404).json({
          success: false,
          message: 'Approved payout not found'
        });
      }

      await payout.startProcessing(transactionId);

      res.json({
        success: true,
        message: 'Payout marked as processing',
        data: {
          payoutId: payout._id,
          status: payout.status,
          transactionId: payout.transactionId
        }
      });

    } catch (error) {
      console.error('Error marking payout as processing:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  /**
   * Complete a payout and update records
   */
  static async completePayout(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      const { payoutId } = req.params;
      const { transactionId } = req.body;

      const payout = await Payout.findOne({ _id: payoutId, status: { $in: ['approved', 'processing'] } }).populate('vendor');
      if (!payout) {
        return res.status(404).json({
          success: false,
          message: 'Payout not found or not in processable state'
        });
      }

      await payout.complete(transactionId);

      const vendor = await Vendor.findById(payout.vendor._id);
      await vendor.recordPayout(payout.netAmount, payout.method, transactionId);

      res.json({
        success: true,
        message: 'Payout completed successfully',
        data: {
          payoutId: payout._id,
          status: payout.status,
          completedDate: payout.completedDate,
          transactionId: payout.transactionId
        }
      });

    } catch (error) {
      console.error('Error completing payout:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  /**
   * Mark a payout as failed and restore balance
   */
  static async markAsFailed(req, res) {
    try {
      const { payoutId } = req.params;
      const { reason } = req.body;

      const payout = await Payout.findOne({ _id: payoutId, status: { $in: ['approved', 'processing'] } }).populate('vendor');
      if (!payout) {
        return res.status(404).json({
          success: false,
          message: 'Payout not found or not in processable state'
        });
      }

      await payout.markAsFailed(reason);

      const vendor = await Vendor.findById(payout.vendor._id);
      vendor.commission.pendingAmount += payout.requestedAmount;
      await vendor.save();

      res.json({
        success: true,
        message: 'Payout marked as failed',
        data: {
          payoutId: payout._id,
          status: payout.status,
          rejectionReason: payout.rejectionReason
        }
      });

    } catch (error) {
      console.error('Error marking payout as failed:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  /**
   * Bulk approval of multiple payouts
   */
  static async bulkApprovePayout(req, res) {
    try {
      const { payoutIds, notes } = req.body;
      const adminId = req.user._id;

      if (!Array.isArray(payoutIds) || payoutIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Payout IDs array is required'
        });
      }

      const payouts = await Payout.find({ _id: { $in: payoutIds }, status: 'pending' });

      const results = { approved: [], failed: [] };

      for (const payout of payouts) {
        try {
          await payout.approve(adminId, notes);
          results.approved.push(payout._id);
        } catch (error) {
          results.failed.push({ payoutId: payout._id, error: error.message });
        }
      }

      res.json({
        success: true,
        message: `Bulk approval completed. ${results.approved.length} approved, ${results.failed.length} failed.`,
        data: results
      });

    } catch (error) {
      console.error('Error bulk approving payouts:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }
}

module.exports = PayoutProcessingController;
