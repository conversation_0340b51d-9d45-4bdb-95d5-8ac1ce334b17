import React, { useState } from 'react';
import { Modal, Form, Input, Select, Row, Col, Upload, Button, message } from 'antd';
import { UploadOutlined, PlusOutlined } from '@ant-design/icons';

const { Option } = Select;
const { TextArea } = Input;

const AgencyVendorModal = ({
  visible,
  onOk,
  onCancel,
  editingVendor,
  loading
}) => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState([]);
  const [imageUrl, setImageUrl] = useState('');

  // Set form values when editing vendor changes
  React.useEffect(() => {
    if (editingVendor) {
      form.setFieldsValue(editingVendor);
      // Set existing image if available
      if (editingVendor.businessImage) {
        setImageUrl(editingVendor.businessImage);
        setFileList([{
          uid: '-1',
          name: 'business-image.png',
          status: 'done',
          url: editingVendor.businessImage,
        }]);
      }
    } else {
      form.resetFields();
      setFileList([]);
      setImageUrl('');
    }
  }, [editingVendor, form]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      // Set isVendorApproved to true by default for agency-added vendors
      values.isVendorApproved = true;
      
      // Include image data in the form values
      if (imageUrl) {
        values.businessImage = imageUrl;
      }
      
      await onOk(values);
      
      // Reset form and state after successful submission
      form.resetFields();
      setFileList([]);
      setImageUrl('');
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setFileList([]);
    setImageUrl('');
    onCancel();
  };

  const businessCategories = [
    'Electronics & Technology',
    'Fashion & Apparel',
    'Home & Garden',
    'Health & Beauty',
    'Sports & Outdoors',
    'Books & Media',
    'Toys & Games',
    'Automotive',
    'Food & Beverages',
    'Jewelry & Accessories',
    'Arts & Crafts',
    'Pet Supplies',
    'Office Supplies',
    'Baby Products',
    'Travel & Luggage',
    'Other'
  ];

  return (
    <Modal
      title={editingVendor ? 'Edit Vendor' : 'Add New Vendor'}
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={900}
      confirmLoading={loading}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          isVendorApproved: true
        }}
      >
        {/* Personal Information */}
        <div style={{ marginBottom: 16 }}>
          <h3 style={{ margin: '0 0 16px 0', color: '#1890ff' }}>Personal Information</h3>
        </div>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="firstName"
              label="First Name"
              rules={[{ required: true, message: 'Please enter first name' }]}
            >
              <Input placeholder="Enter first name" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="lastName"
              label="Last Name"
              rules={[{ required: true, message: 'Please enter last name' }]}
            >
              <Input placeholder="Enter last name" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="email"
              label="Email ID"
              rules={[
                { required: true, message: 'Please enter email' },
                { type: 'email', message: 'Please enter valid email' }
              ]}
            >
              <Input placeholder="Enter email address" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="phone"
              label="Phone Number"
              rules={[{ required: true, message: 'Please enter phone number' }]}
            >
              <Input placeholder="Enter phone number" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="password"
          label="Password"
          rules={[
            { required: !editingVendor, message: 'Please enter password' },
            { min: 6, message: 'Password must be at least 6 characters' }
          ]}
        >
          <Input.Password placeholder="Enter password" />
        </Form.Item>

        {/* Business Information */}
        <div style={{ marginBottom: 16, marginTop: 24 }}>
          <h3 style={{ margin: '0 0 16px 0', color: '#1890ff' }}>Business Information</h3>
        </div>

        <Form.Item
          name="businessName"
          label="Business Name"
          rules={[{ required: true, message: 'Please enter business name' }]}
        >
          <Input placeholder="Enter business name" />
        </Form.Item>

        <Form.Item
          name="businessCategory"
          label="Business Category"
          rules={[{ required: true, message: 'Please select business category' }]}
        >
          <Select placeholder="Select business category">
            {businessCategories.map(category => (
              <Option key={category} value={category}>{category}</Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="businessImage"
          label="Business Image"
          rules={[{ required: true, message: 'Please upload business image' }]}
        >
          <Upload
            listType="picture-card"
            maxCount={1}
            beforeUpload={(file) => {
              // File type validation
              const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
              if (!isJpgOrPng) {
                message.error('You can only upload JPG/PNG file!');
                return Upload.LIST_IGNORE;
              }
              
              // File size validation (2MB)
              const isLt2M = file.size / 1024 / 1024 < 2;
              if (!isLt2M) {
                message.error('Image must be smaller than 2MB!');
                return Upload.LIST_IGNORE;
              }

              // Convert to base64 for preview and storage
              const reader = new FileReader();
              reader.onload = (e) => {
                const base64 = e.target.result;
                setImageUrl(base64);
                setFileList([{
                  uid: '-1',
                  name: file.name,
                  status: 'done',
                  url: base64,
                  originFileObj: file
                }]);
                form.setFieldsValue({ businessImage: base64 });
                message.success('Image uploaded successfully!');
              };
              reader.readAsDataURL(file);
              return false; // Prevent automatic upload
            }}
            fileList={fileList}
            onChange={({ fileList: newFileList }) => {
              setFileList(newFileList);
              if (newFileList.length === 0) {
                setImageUrl('');
                form.setFieldsValue({ businessImage: '' });
              }
            }}
            onPreview={(file) => {
              const url = file.url || file.preview;
              if (url) {
                window.open(url, '_blank');
              }
            }}
          >
            {fileList.length < 1 && (
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>Upload Image</div>
              </div>
            )}
          </Upload>
          <div style={{ marginTop: 8, color: '#666', fontSize: '12px' }}>
            Support JPG, PNG. Max size: 2MB
          </div>
        </Form.Item>

        {/* Financial Information */}
        <div style={{ marginBottom: 16, marginTop: 24 }}>
          <h3 style={{ margin: '0 0 16px 0', color: '#1890ff' }}>Financial Information</h3>
        </div>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="bankAccount"
              label="Bank Account Number"
              rules={[{ required: true, message: 'Please enter bank account number' }]}
            >
              <Input placeholder="Enter bank account number" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="gstin"
              label="GSTIN Number (Optional)"
            >
              <Input placeholder="Enter GSTIN number" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="description"
          label="Business Description"
        >
          <TextArea rows={3} placeholder="Enter business description" />
        </Form.Item>

        {/* Hidden field for vendor approval status */}
        <Form.Item name="isVendorApproved" hidden>
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AgencyVendorModal;
