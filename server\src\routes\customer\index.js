const express = require('express');
const router = express.Router();

// Import route modules
const authRoutes = require('./auth');
const profileRoutes = require('./profile');
const cartRoutes = require('./cart');
const orderRoutes = require('./orders');
const productRoutes = require('./products');

// Register routes
router.use('/auth', authRoutes);
router.use('/profile', profileRoutes);
router.use('/cart', cartRoutes);
router.use('/orders', orderRoutes);
router.use('/products', productRoutes);

module.exports = router;