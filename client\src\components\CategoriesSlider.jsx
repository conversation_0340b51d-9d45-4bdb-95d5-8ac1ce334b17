import React, { useRef, useState, useEffect } from 'react';
import { 
    RightOutlined, 
    LeftOutlined,
    ShopOutlined,
    MobileOutlined,
    CrownOutlined,
    HeartOutlined,
    CarOutlined,
    HomeOutlined,
    SkinOutlined,
    ShoppingOutlined,
    PrinterOutlined,
    BugOutlined,
    SmileOutlined,
    MedicineBoxOutlined,
    GiftOutlined,
    ScissorOutlined,
    BookOutlined,
    TableOutlined,
    BulbOutlined,
    SettingOutlined,
    ThunderboltOutlined,
    SafetyOutlined
} from '@ant-design/icons';

// Categories with their corresponding icons
const categories = [
    { name: 'Apparel & Accessories', icon: ShopOutlined },
    { name: 'Consumer Electronics', icon: MobileOutlined },
    { name: 'Sports & Entertainment', icon: CrownOutlined },
    { name: 'Jewelry, Eyewear & Watches', icon: HeartOutlined },
    { name: 'Shoes & Accessories', icon: CarOutlined },
    { name: 'Home & Garden', icon: HomeOutlined },
    { name: 'Beauty', icon: SkinOutlined },
    { name: 'Luggage, Bags & Cases', icon: ShoppingOutlined },
    { name: 'Packaging & Printing', icon: PrinterOutlined },
    { name: 'Parents, Kids & Toys', icon: BugOutlined },
    { name: 'Personal Care & Home Care', icon: SmileOutlined },
    { name: 'Health & Medical', icon: MedicineBoxOutlined },
    { name: 'Gifts & Crafts', icon: GiftOutlined },
    { name: 'Pet Supplies', icon: ScissorOutlined },
    { name: 'School & Office Supplies', icon: BookOutlined },
    { name: 'Furniture', icon: TableOutlined },
    { name: 'Lights & Lighting', icon: BulbOutlined },
    { name: 'Home Appliances', icon: SettingOutlined },
    { name: 'Electrical Equipment & Supplies', icon: ThunderboltOutlined }
];

const CategoriesSlider = () => {
    const scrollRef = useRef(null);
    const [showLeftArrow, setShowLeftArrow] = useState(false);
    const [showRightArrow, setShowRightArrow] = useState(true);

    const checkScrollPosition = () => {
        const { current } = scrollRef;
        if (current) {
            setShowLeftArrow(current.scrollLeft > 0);
            setShowRightArrow(
                current.scrollLeft < current.scrollWidth - current.clientWidth
            );
        }
    };

    useEffect(() => {
        const { current } = scrollRef;
        if (current) {
            current.addEventListener('scroll', checkScrollPosition);
            checkScrollPosition(); // Initial check
            return () => current.removeEventListener('scroll', checkScrollPosition);
        }
    }, []);

    const scroll = (direction) => {
        const { current } = scrollRef;
        if (current) {
            const scrollAmount = 600; // Adjust for 2-row layout
            if (direction === 'left') {
                current.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
            } else {
                current.scrollBy({ left: scrollAmount, behavior: 'smooth' });
            }
        }
    };

    const handleCategoryClick = (categoryName) => {
        console.log('Category clicked:', categoryName);
        // Add navigation logic here
    };

    return (
        <div className="relative w-full py-8 bg-white">
            {/* Left Arrow */}
            {showLeftArrow && (
                <button
                    onClick={() => scroll('left')}
                    className="absolute left-2 top-1/2 transform -translate-y-1/2 z-10 bg-white rounded-full shadow-lg border border-gray-200 p-2 hover:bg-gray-50 transition-colors duration-200"
                >
                    <LeftOutlined className="text-gray-600 text-lg" />
                </button>
            )}

            {/* Categories Container */}
            <div
                ref={scrollRef}
                className="flex justify-center overflow-x-auto scrollbar-hide px-8 scroll-smooth"
            >
                {/* Two Rows of Categories - Centered */}
                <div className="flex flex-col items-center gap-6 mt-4 mb-4">
                    {Array.from({ length: 2 }, (_, rowIndex) => (
                        <div className="flex justify-center gap-8" key={rowIndex}>
                            {categories.slice(rowIndex * 8, rowIndex * 8 + 8).map((category, index) => {
                                const IconComponent = category.icon;
                                return (
                                    <div
                                        key={index + rowIndex * 8}
                                        onClick={() => handleCategoryClick(category.name)}
                                        className="flex flex-col items-center cursor-pointer group hover:scale-105 transition-transform duration-200 min-w-[80px]"
                                    >
                                        {/* Circular Icon Container - Bigger size */}
                                        <div className="w-16 h-16 bg-gradient-to-br from-orange-100 to-orange-200 rounded-full flex items-center justify-center mb-2 group-hover:from-orange-200 group-hover:to-orange-300 transition-all duration-200 shadow-md">
                                            <IconComponent className="text-orange-600 text-2xl" />
                                        </div>

                                        {/* Category Name */}
                                        <div className="text-xs text-gray-700 text-center max-w-20 leading-tight group-hover:text-orange-600 transition-colors duration-200">
                                            {category.name}
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    ))}
                </div>
            </div>

            {/* Right Arrow */}
            {showRightArrow && (
                <button
                    onClick={() => scroll('right')}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 z-10 bg-white rounded-full shadow-lg border border-gray-200 p-2 hover:bg-gray-50 transition-colors duration-200"
                >
                    <RightOutlined className="text-gray-600 text-lg" />
                </button>
            )}
        </div>
    );
};

export default CategoriesSlider;
