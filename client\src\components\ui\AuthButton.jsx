import React from 'react';
import { Button } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

const AuthButton = ({ 
  children, 
  loading = false, 
  variant = 'primary', 
  className = "",
  ...props 
}) => {
  const getButtonStyle = () => {
    const baseStyle = {
      height: '48px',
      borderRadius: '8px',
      fontWeight: '600',
      fontSize: '16px',
      transition: 'all 0.3s ease',
      border: 'none',
    };

    switch (variant) {
      case 'primary':
        return {
          ...baseStyle,
          background: 'linear-gradient(135deg, #ff6b35 0%, #f7931e 100%)',
          color: 'white',
          boxShadow: '0 4px 15px rgba(255, 107, 53, 0.3)',
        };
      case 'secondary':
        return {
          ...baseStyle,
          background: 'transparent',
          color: '#ff6b35',
          border: '2px solid #ff6b35',
          boxShadow: 'none',
        };
      case 'ghost':
        return {
          ...baseStyle,
          background: 'transparent',
          color: '#666',
          border: '1px solid #d9d9d9',
          boxShadow: 'none',
        };
      default:
        return baseStyle;
    }
  };

  return (
    <Button
      {...props}
      loading={loading}
      className={`auth-button ${variant} ${className}`}
      style={getButtonStyle()}
      icon={loading ? <LoadingOutlined /> : props.icon}
    >
      {children}
    </Button>
  );
};

export default AuthButton;