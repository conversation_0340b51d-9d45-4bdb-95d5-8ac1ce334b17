const express = require('express');
const router = express.Router();
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');
const {
  getCart,
  addToCart,
  updateCartItem,
  removeFromCart,
  clearCart,
  getCartSummary,
  bulkAddToCart,
  bulkUpdateCart
} = require('../../controllers/cartController');

// All routes are protected and for customers only
router.use(verifyToken);
router.use(requireUserType(['customer']));

// @route   GET /api/customer/cart
// @desc    Get customer's cart with all product details
// @access  Private (Customer)
router.get('/', getCart);

// @route   GET /api/customer/cart/summary
// @desc    Get cart summary (count, total amount)
// @access  Private (Customer)
router.get('/summary', getCartSummary);

// @route   POST /api/customer/cart/add
// @desc    Add item to cart (real-time update)
// @access  Private (Customer)
router.post('/add', addToCart);

// @route   PUT /api/customer/cart/update
// @desc    Update cart item quantity (real-time update)
// @access  Private (Customer)
router.put('/update', updateCartItem);

// @route   DELETE /api/customer/cart/remove/:productId
// @desc    Remove specific item from cart
// @access  Private (Customer)
router.delete('/remove/:productId', removeFromCart);

// @route   DELETE /api/customer/cart/clear
// @desc    Clear entire cart
// @access  Private (Customer)
router.delete('/clear', clearCart);

// @route   POST /api/customer/cart/bulk-add
// @desc    Bulk add items to cart
// @access  Private (Customer)
router.post('/bulk-add', bulkAddToCart);

// @route   PUT /api/customer/cart/bulk-update
// @desc    Bulk update cart items
// @access  Private (Customer)
router.put('/bulk-update', bulkUpdateCart);

module.exports = router;
