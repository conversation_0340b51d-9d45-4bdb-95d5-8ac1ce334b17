import React, { useState, useEffect } from 'react';
import { message } from 'antd';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { authAPI } from '../../utils/authApi';
import { useAuth } from '../../hooks/useAuth';

const EmailVerificationForm = () => {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const { isAuthenticated, user, updateProfile } = useAuth();
    const [loading, setLoading] = useState(false);
    const [verifying, setVerifying] = useState(false);
    const [verified, setVerified] = useState(false);
    const [error, setError] = useState(null);
    const [email, setEmail] = useState('');

    const token = searchParams.get('token');
    const emailParam = searchParams.get('email');

    useEffect(() => {
        if (token && emailParam) {
            setEmail(emailParam);
            verifyEmail();
        }
    }, [token, emailParam]);

    const verifyEmail = async () => {
        if (!token || !emailParam) {
            setError('Invalid verification link');
            return;
        }

        setVerifying(true);
        try {
            const response = await authAPI.verifyEmail(token, emailParam);
            if (response.success) {
                setVerified(true);
                message.success('Email verified successfully!');
                
                // If user is already authenticated and this is their email, update their profile
                if (isAuthenticated && user && user.email === emailParam) {
                    // Update the user's verification status in the context
                    if (response.data && response.data.user) {
                        await updateProfile(response.data.user);
                    } else {
                        await updateProfile({ isEmailVerified: true });
                    }
                    // Navigate to profile page for authenticated users
                    setTimeout(() => {
                        navigate('/profile');
                    }, 2000);
                } else {
                    // Navigate to auth page for non-authenticated users
                    setTimeout(() => {
                        navigate('/auth');
                    }, 3000);
                }
            } else {
                setError(response.message || 'Verification failed');
            }
        } catch (error) {
            console.error('Email verification error:', error);
            setError(error.response?.data?.message || 'Verification failed');
        } finally {
            setVerifying(false);
        }
    };

    const resendVerification = async () => {
        if (!email) {
            message.error('Email address is required');
            return;
        }

        setLoading(true);
        try {
            const response = await authAPI.resendVerificationEmail(email);
            if (response.success) {
                message.success('Verification email sent! Please check your inbox.');
            } else {
                message.error(response.message || 'Failed to send verification email');
            }
        } catch (error) {
            console.error('Resend verification error:', error);
            message.error(error.response?.data?.message || 'Failed to send verification email');
        } finally {
            setLoading(false);
        }
    };

    const handleEmailSubmit = async (e) => {
        e.preventDefault();
        const formData = new FormData(e.target);
        const emailValue = formData.get('email');
        setEmail(emailValue);
        
        if (!emailValue) {
            message.error('Please enter your email address');
            return;
        }
        
        if (!/\S+@\S+\.\S+/.test(emailValue)) {
            message.error('Please enter a valid email address');
            return;
        }
        
        await resendVerification();
    };

    if (verifying) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-50 via-white to-orange-50">
                <div className="max-w-md w-full mx-auto p-8 bg-white rounded-2xl shadow-xl text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">
                        Verifying Your Email
                    </h2>
                    <p className="text-gray-600">
                        Please wait while we verify your email address...
                    </p>
                </div>
            </div>
        );
    }

    if (verified) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-50 via-white to-orange-50">
                <div className="max-w-md w-full mx-auto p-8 bg-white rounded-2xl shadow-xl text-center">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">
                        Email Verified Successfully!
                    </h2>
                    <p className="text-gray-600 mb-6">
                        {isAuthenticated ? 
                            'Your email has been verified. You will be redirected to your profile shortly.' :
                            'Your email has been verified. You will be redirected to the login page shortly.'
                        }
                    </p>
                    <button
                        onClick={() => isAuthenticated ? navigate('/profile') : navigate('/auth')}
                        className="w-full bg-gradient-to-r from-orange-500 to-orange-600 text-white py-3 px-4 rounded-lg font-medium hover:from-orange-600 hover:to-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-all duration-200"
                    >
                        {isAuthenticated ? 'Continue to Profile' : 'Go to Login'}
                    </button>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-50 via-white to-orange-50">
                <div className="max-w-md w-full mx-auto p-8 bg-white rounded-2xl shadow-xl">
                    <div className="text-center mb-6">
                        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </div>
                        <h2 className="text-2xl font-bold text-gray-800 mb-2">
                            Verification Failed
                        </h2>
                        <p className="text-red-600 mb-6">
                            {error}
                        </p>
                    </div>

                    <div className="mb-6">
                        <p className="text-gray-600 mb-4">
                            Enter your email address to receive a new verification link:
                        </p>
                        <form onSubmit={handleEmailSubmit} className="space-y-4">
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                                    </svg>
                                </div>
                                <input
                                    type="email"
                                    name="email"
                                    placeholder="Enter your email"
                                    defaultValue={email}
                                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
                                    required
                                />
                            </div>
                            <button
                                type="submit"
                                disabled={loading}
                                className="w-full bg-gradient-to-r from-orange-500 to-orange-600 text-white py-3 px-4 rounded-lg font-medium hover:from-orange-600 hover:to-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {loading ? (
                                    <div className="flex items-center justify-center">
                                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                                        Sending...
                                    </div>
                                ) : (
                                    'Resend Verification Email'
                                )}
                            </button>
                        </form>
                    </div>

                    <button
                        onClick={() => navigate('/auth')}
                        className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200"
                    >
                        Back to Login
                    </button>
                </div>
            </div>
        );
    }

    // Default state - manual email verification
    return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-50 via-white to-orange-50">
            <div className="max-w-md w-full mx-auto p-8 bg-white rounded-2xl shadow-xl">
                <div className="text-center mb-8">
                    <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="h-8 w-8 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                        </svg>
                    </div>
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">
                        Verify Your Email
                    </h2>
                    <p className="text-gray-600">
                        Enter your email address to receive a verification link
                    </p>
                </div>

                <form onSubmit={handleEmailSubmit} className="space-y-6">
                    <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                            </svg>
                        </div>
                        <input
                            type="email"
                            name="email"
                            placeholder="Enter your email"
                            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
                            required
                        />
                    </div>

                    <button
                        type="submit"
                        disabled={loading}
                        className="w-full bg-gradient-to-r from-orange-500 to-orange-600 text-white py-3 px-4 rounded-lg font-medium hover:from-orange-600 hover:to-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {loading ? (
                            <div className="flex items-center justify-center">
                                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                                Sending...
                            </div>
                        ) : (
                            'Send Verification Email'
                        )}
                    </button>
                </form>

                <div className="text-center mt-6">
                    <button
                        onClick={() => navigate('/auth')}
                        className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200"
                    >
                        Back to Login
                    </button>
                </div>
            </div>
        </div>
    );
};

export default EmailVerificationForm;