import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Modal,
  Form,
  message,
  Popconfirm,
  Drawer,
  Descriptions,
  Statistic,
  Row,
  Col,
  Avatar,
  Typography,
  Badge,
  Tooltip
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  CalendarOutlined,
  ShoppingCartOutlined,
  DollarOutlined
} from '@ant-design/icons';
import { usersApi } from '../../../services/adminApi';
import dayjs from 'dayjs';

const { Search } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

const UsersManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [filters, setFilters] = useState({
    search: '',
    role: '',
    status: '',
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });
  const [selectedUser, setSelectedUser] = useState(null);
  const [userDetailVisible, setUserDetailVisible] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [statistics, setStatistics] = useState(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const [createForm] = Form.useForm();
  const [editForm] = Form.useForm();

  useEffect(() => {
    fetchUsers();
    fetchStatistics();
  }, [pagination.current, pagination.pageSize, filters]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        ...filters
      };

      const response = await usersApi.getUsers(params);
      const { users: userData, pagination: paginationData } = response.data.data;

      setUsers(userData);
      setPagination(prev => ({
        ...prev,
        total: paginationData.totalUsers
      }));
    } catch (error) {
      message.error('Failed to fetch users');
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await usersApi.getUserStatistics();
      setStatistics(response.data.data);
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  const handleTableChange = (paginationData, filters, sorter) => {
    setPagination(prev => ({
      ...prev,
      current: paginationData.current,
      pageSize: paginationData.pageSize
    }));

    if (sorter.field) {
      setFilters(prev => ({
        ...prev,
        sortBy: sorter.field,
        sortOrder: sorter.order === 'ascend' ? 'asc' : 'desc'
      }));
    }
  };

  const handleSearch = (value) => {
    setFilters(prev => ({ ...prev, search: value }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleViewUser = async (userId) => {
    try {
      const response = await usersApi.getUserById(userId);
      setSelectedUser(response.data.data);
      setUserDetailVisible(true);
    } catch (error) {
      message.error('Failed to fetch user details');
    }
  };

  const handleCreateUser = async (values) => {
    try {
      await usersApi.createUser(values);
      message.success('User created successfully');
      setCreateModalVisible(false);
      createForm.resetFields();
      fetchUsers();
      fetchStatistics();
    } catch (error) {
      message.error('Failed to create user');
      console.error('Error creating user:', error);
    }
  };

  const handleEditUser = async (values) => {
    try {
      await usersApi.updateUser(selectedUser.user._id, values);
      message.success('User updated successfully');
      setEditModalVisible(false);
      editForm.resetFields();
      setSelectedUser(null);
      fetchUsers();
    } catch (error) {
      message.error('Failed to update user');
      console.error('Error updating user:', error);
    }
  };

  const handleStatusChange = async (userId, status) => {
    try {
      await usersApi.updateUserStatus(userId, status);
      message.success('User status updated successfully');
      fetchUsers();
      fetchStatistics();
    } catch (error) {
      message.error('Failed to update user status');
    }
  };

  const handleDeleteUser = async (userId) => {
    try {
      await usersApi.deleteUser(userId);
      message.success('User deleted successfully');
      fetchUsers();
      fetchStatistics();
    } catch (error) {
      message.error('Failed to delete user');
    }
  };

  const handleBulkStatusUpdate = async (status) => {
    try {
      await usersApi.bulkUpdateUsers(selectedRowKeys, { status });
      message.success(`${selectedRowKeys.length} users updated successfully`);
      setSelectedRowKeys([]);
      fetchUsers();
      fetchStatistics();
    } catch (error) {
      message.error('Failed to update users');
    }
  };

  const columns = [
    {
      title: 'User',
      key: 'user',
      render: (record) => (
        <Space>
          <Avatar 
            src={record.avatar} 
            icon={<UserOutlined />}
            size="large"
          />
          <div>
            <div className="font-medium">
              {record.firstName} {record.lastName}
            </div>
            <Text type="secondary" className="text-sm">
              {record.email}
            </Text>
          </div>
        </Space>
      )
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      render: (role, record) => {
        // Fallback to userType if role is missing, then default to 'customer'
        const userRole = role || record.userType || 'customer';
        return (
          <Tag color={userRole === 'admin' ? 'red' : userRole === 'vendor' ? 'blue' : 'green'}>
            {userRole.toUpperCase()}
          </Tag>
        );
      },
      filters: [
        { text: 'Customer', value: 'customer' },
        { text: 'Vendor', value: 'vendor' },
        { text: 'Admin', value: 'admin' }
      ]
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => (
        <Select
          value={status}
          size="small"
          style={{ width: 100 }}
          onChange={(value) => handleStatusChange(record._id, value)}
        >
          <Option value="active">
            <Badge status="success" text="Active" />
          </Option>
          <Option value="inactive">
            <Badge status="default" text="Inactive" />
          </Option>
          <Option value="suspended">
            <Badge status="error" text="Suspended" />
          </Option>
        </Select>
      ),
      filters: [
        { text: 'Active', value: 'active' },
        { text: 'Inactive', value: 'inactive' },
        { text: 'Suspended', value: 'suspended' }
      ]
    },
    {
      title: 'Orders',
      key: 'orders',
      render: (record) => (
        <div>
          <div>{record.stats?.totalOrders || 0}</div>
          <Text type="secondary" className="text-xs">
            ${(record.stats?.totalSpent || 0).toFixed(2)}
          </Text>
        </div>
      ),
      sorter: true
    },
    {
      title: 'Joined',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => (
        <Tooltip title={dayjs(date).format('YYYY-MM-DD HH:mm:ss')}>
          {dayjs(date).format('MMM DD, YYYY')}
        </Tooltip>
      ),
      sorter: true
    },
    {
      title: 'Last Login',
      dataIndex: 'lastLogin',
      key: 'lastLogin',
      render: (date) => date ? (
        <Tooltip title={dayjs(date).format('YYYY-MM-DD HH:mm:ss')}>
          {dayjs(date).fromNow()}
        </Tooltip>
      ) : (
        <Text type="secondary">Never</Text>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record) => (
        <Space>
          <Tooltip title="View Details">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewUser(record._id)}
            />
          </Tooltip>
          <Tooltip title="Edit User">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => {
                setSelectedUser({ user: record });
                editForm.setFieldsValue(record);
                setEditModalVisible(true);
              }}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this user?"
            onConfirm={() => handleDeleteUser(record._id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete User">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE
    ]
  };

  return (
    <div>
      {/* Statistics Cards */}
      {statistics && (
        <Row gutter={[16, 16]} className="mb-6">
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Users"
                value={statistics.overview.totalUsers}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Active Users"
                value={statistics.overview.activeUsers}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="New This Month"
                value={statistics.registrationTrends?.thisMonth || 0}
                prefix={<CalendarOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Top Customers"
                value={statistics.topCustomers?.length || 0}
                prefix={<ShoppingCartOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Main Content */}
      <Card>
        <div className="flex justify-between items-center mb-4">
          <Title level={3} className="mb-0">Users Management</Title>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            Add User
          </Button>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-4 mb-4">
          <Search
            placeholder="Search users..."
            allowClear
            style={{ width: 300 }}
            onSearch={handleSearch}
          />
          <Select
            placeholder="Filter by role"
            allowClear
            style={{ width: 150 }}
            onChange={(value) => handleFilterChange('role', value)}
          >
            <Option value="customer">Customer</Option>
            <Option value="vendor">Vendor</Option>
            <Option value="admin">Admin</Option>
          </Select>
          <Select
            placeholder="Filter by status"
            allowClear
            style={{ width: 150 }}
            onChange={(value) => handleFilterChange('status', value)}
          >
            <Option value="active">Active</Option>
            <Option value="inactive">Inactive</Option>
            <Option value="suspended">Suspended</Option>
          </Select>
        </div>

        {/* Bulk Actions */}
        {selectedRowKeys.length > 0 && (
          <div className="mb-4 p-3 bg-blue-50 rounded">
            <Space>
              <Text>{selectedRowKeys.length} users selected</Text>
              <Button
                size="small"
                onClick={() => handleBulkStatusUpdate('active')}
              >
                Activate
              </Button>
              <Button
                size="small"
                onClick={() => handleBulkStatusUpdate('inactive')}
              >
                Deactivate
              </Button>
              <Button
                size="small"
                danger
                onClick={() => handleBulkStatusUpdate('suspended')}
              >
                Suspend
              </Button>
            </Space>
          </div>
        )}

        {/* Table */}
        <Table
          columns={columns}
          dataSource={users}
          rowKey="_id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} users`
          }}
          onChange={handleTableChange}
          rowSelection={rowSelection}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Create User Modal */}
      <Modal
        title="Create New User"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          createForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateUser}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="firstName"
                label="First Name"
                rules={[{ required: true, message: 'Please enter first name' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="lastName"
                label="Last Name"
                rules={[{ required: true, message: 'Please enter last name' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="email"
            label="Email"
            rules={[
              { required: true, message: 'Please enter email' },
              { type: 'email', message: 'Please enter valid email' }
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="password"
            label="Password"
            rules={[
              { required: true, message: 'Please enter password' },
              { min: 6, message: 'Password must be at least 6 characters' }
            ]}
          >
            <Input.Password />
          </Form.Item>
          <Form.Item name="phone" label="Phone">
            <Input />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="role"
                label="Role"
                rules={[{ required: true, message: 'Please select role' }]}
              >
                <Select>
                  <Option value="customer">Customer</Option>
                  <Option value="vendor">Vendor</Option>
                  <Option value="admin">Admin</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="Status"
                initialValue="active"
                rules={[{ required: true, message: 'Please select status' }]}
              >
                <Select>
                  <Option value="active">Active</Option>
                  <Option value="inactive">Inactive</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item className="mb-0 text-right">
            <Space>
              <Button onClick={() => setCreateModalVisible(false)}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                Create User
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Edit User Modal */}
      <Modal
        title="Edit User"
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          editForm.resetFields();
          setSelectedUser(null);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleEditUser}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="firstName"
                label="First Name"
                rules={[{ required: true, message: 'Please enter first name' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="lastName"
                label="Last Name"
                rules={[{ required: true, message: 'Please enter last name' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="email"
            label="Email"
            rules={[
              { required: true, message: 'Please enter email' },
              { type: 'email', message: 'Please enter valid email' }
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item name="phone" label="Phone">
            <Input />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="role"
                label="Role"
                rules={[{ required: true, message: 'Please select role' }]}
              >
                <Select>
                  <Option value="customer">Customer</Option>
                  <Option value="vendor">Vendor</Option>
                  <Option value="admin">Admin</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="Status"
                rules={[{ required: true, message: 'Please select status' }]}
              >
                <Select>
                  <Option value="active">Active</Option>
                  <Option value="inactive">Inactive</Option>
                  <Option value="suspended">Suspended</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item className="mb-0 text-right">
            <Space>
              <Button onClick={() => setEditModalVisible(false)}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                Update User
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* User Detail Drawer */}
      <Drawer
        title="User Details"
        placement="right"
        width={600}
        open={userDetailVisible}
        onClose={() => {
          setUserDetailVisible(false);
          setSelectedUser(null);
        }}
      >
        {selectedUser && (
          <div>
            <div className="text-center mb-6">
              <Avatar
                size={80}
                src={selectedUser.user.avatar}
                icon={<UserOutlined />}
              />
              <Title level={4} className="mt-2 mb-1">
                {selectedUser.user.firstName} {selectedUser.user.lastName}
              </Title>
              <Tag color={selectedUser.user.role === 'admin' ? 'red' : selectedUser.user.role === 'vendor' ? 'blue' : 'green'}>
                {(selectedUser.user.role || selectedUser.user.userType || 'customer').toUpperCase()}
              </Tag>
            </div>

            <Descriptions column={1} bordered>
              <Descriptions.Item label="Email" icon={<MailOutlined />}>
                {selectedUser.user.email}
              </Descriptions.Item>
              <Descriptions.Item label="Phone" icon={<PhoneOutlined />}>
                {selectedUser.user.phone || 'Not provided'}
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                <Badge
                  status={selectedUser.user.status === 'active' ? 'success' : 'error'}
                  text={selectedUser.user.status.charAt(0).toUpperCase() + selectedUser.user.status.slice(1)}
                />
              </Descriptions.Item>
              <Descriptions.Item label="Email Verified">
                <Badge
                  status={selectedUser.user.emailVerified ? 'success' : 'error'}
                  text={selectedUser.user.emailVerified ? 'Verified' : 'Not Verified'}
                />
              </Descriptions.Item>
              <Descriptions.Item label="Joined">
                {dayjs(selectedUser.user.createdAt).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label="Last Login">
                {selectedUser.user.lastLogin
                  ? dayjs(selectedUser.user.lastLogin).format('YYYY-MM-DD HH:mm:ss')
                  : 'Never'
                }
              </Descriptions.Item>
            </Descriptions>

            {/* Order Statistics */}
            <Title level={5} className="mt-6 mb-4">Order Statistics</Title>
            <Row gutter={16}>
              <Col span={8}>
                <Statistic
                  title="Total Orders"
                  value={selectedUser.statistics?.totalOrders || 0}
                  prefix={<ShoppingCartOutlined />}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="Total Spent"
                  value={selectedUser.statistics?.totalSpent || 0}
                  prefix={<DollarOutlined />}
                  precision={2}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="Avg Order Value"
                  value={selectedUser.statistics?.averageOrderValue || 0}
                  prefix={<DollarOutlined />}
                  precision={2}
                />
              </Col>
            </Row>

            {/* Recent Orders */}
            {selectedUser.recentOrders && selectedUser.recentOrders.length > 0 && (
              <>
                <Title level={5} className="mt-6 mb-4">Recent Orders</Title>
                <div className="space-y-2">
                  {selectedUser.recentOrders.map(order => (
                    <div key={order._id} className="p-3 border rounded">
                      <div className="flex justify-between items-center">
                        <div>
                          <Text strong>{order.orderNumber}</Text>
                          <br />
                          <Text type="secondary">
                            {dayjs(order.createdAt).format('MMM DD, YYYY')}
                          </Text>
                        </div>
                        <div className="text-right">
                          <div>${order.pricing.total.toFixed(2)}</div>
                          <Badge
                            status={order.status === 'delivered' ? 'success' : 'processing'}
                            text={order.status}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default UsersManagement;