const { verifyToken, requireRole, requireEmailVerification } = require('./authMiddleware');

// Admin-specific authentication middleware
const authenticateAdmin = [
    verifyToken,
    requireRole(['admin']),
    requireEmailVerification
];

// Super admin authentication
const authenticateSuperAdmin = [
    verifyToken,
    requireRole(['super_admin']),
    requireEmailVerification
];

// Admin authentication without email verification requirement
const authenticateAdminBasic = [
    verifyToken,
    requireRole(['admin'])
];

module.exports = {
    authenticateAdmin,
    authenticateSuperAdmin,
    authenticateAdminBasic,
    // Export individual functions for flexibility
    verifyToken,
    requireRole,
    requireEmailVerification
};