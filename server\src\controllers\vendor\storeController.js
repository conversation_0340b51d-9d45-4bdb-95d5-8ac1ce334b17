const { Vendor, User } = require('../../models');
const shiprocketService = require('../../services/shiprocketService');

/**
 * Get vendor store profile
 */
const getProfile = async (req, res) => {
  try {
    const vendorId = req.user.userId;

    // Get vendor with user details
    const vendor = await Vendor.findOne({ user: vendorId })
      .populate('user', 'firstName lastName email phone countryCode avatar preferences address city state zipCode country');

    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    res.json({
      success: true,
      data: vendor
    });

  } catch (error) {
    console.error('Get vendor profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch vendor profile',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update vendor store profile
 */
const updateProfile = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const {
      // Business fields
      businessName,
      businessDescription,
      businessType,
      contactInfo,
      businessAddress,
      settings,
      taxId,
      businessRegistrationNumber,
      businessLicense,
      // Profile fields (for user)
      firstName,
      lastName,
      email,
      phone,
      countryCode,
      address,
      city,
      state,
      zipCode,
      country,
      preferences
    } = req.body;

    console.log('Update profile request for vendor:', vendorId);
    console.log('Request body:', req.body);

    // Find vendor
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      console.log('Vendor not found for user:', vendorId);
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    console.log('Found vendor:', vendor.businessName);

    // Update vendor profile if business data provided
    if (businessName || businessDescription || businessType || contactInfo || businessAddress || settings || taxId || businessRegistrationNumber || businessLicense) {
      const vendorUpdateData = {};
      if (businessName) vendorUpdateData.businessName = businessName;
      if (businessDescription) vendorUpdateData.businessDescription = businessDescription;
      if (businessType) vendorUpdateData.businessType = businessType;
      if (contactInfo) {
        vendorUpdateData.contactInfo = {
          ...(vendor.contactInfo || {}),
          ...contactInfo
        };
      }
      if (businessAddress) {
        vendorUpdateData.businessAddress = {
          ...(vendor.businessAddress || {}),
          ...businessAddress
        };
      }
      if (settings) {
        vendorUpdateData.settings = {
          ...(vendor.settings || {}),
          ...settings
        };
      }
      if (taxId) vendorUpdateData.taxId = taxId;
      if (businessRegistrationNumber || businessLicense) {
        vendorUpdateData.businessRegistrationNumber = businessRegistrationNumber || businessLicense;
      }

      console.log('Updating vendor with data:', vendorUpdateData);

      await Vendor.findByIdAndUpdate(
        vendor._id,
        { $set: vendorUpdateData },
        { new: true, runValidators: true }
      );
    }

    // Update user profile if user data provided
    if (firstName || lastName || email || phone || countryCode || address || city || state || zipCode || country || preferences) {
      const user = await User.findById(vendorId);
      const userUpdateData = {};
      if (firstName) userUpdateData.firstName = firstName;
      if (lastName) userUpdateData.lastName = lastName;
      if (email) userUpdateData.email = email;
      if (phone) userUpdateData.phone = phone;
      if (countryCode) userUpdateData.countryCode = countryCode;
      if (address) userUpdateData.address = address;
      if (city) userUpdateData.city = city;
      if (state) userUpdateData.state = state;
      if (zipCode) userUpdateData.zipCode = zipCode;
      if (country) userUpdateData.country = country;
      if (preferences) {
        userUpdateData.preferences = {
          ...(user?.preferences || {}),
          ...preferences
        };
      }

      console.log('Updating user with data:', userUpdateData);

      await User.findByIdAndUpdate(
        vendorId,
        { $set: userUpdateData },
        { new: true, runValidators: true }
      );
    }

    // Get updated vendor with user details
    const updatedVendor = await Vendor.findOne({ user: vendorId })
      .populate('user', 'firstName lastName email phone countryCode avatar preferences address city state zipCode country');

    console.log('Profile update successful');

    res.json({
      success: true,
      data: updatedVendor,
      message: 'Profile updated successfully'
    });

  } catch (error) {
    console.error('Update vendor profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update vendor profile',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update vendor store settings
 */
const updateSettings = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const {
      storeSettings,
      notificationSettings,
      paymentSettings,
      shippingSettings,
      security
    } = req.body;

    // Find vendor
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Handle password reset if security data provided
    if (security && security.currentPassword && security.newPassword) {
      const user = await User.findById(vendorId).select('+password');
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Verify current password
      const isCurrentPasswordValid = await user.comparePassword(security.currentPassword);
      if (!isCurrentPasswordValid) {
        return res.status(400).json({
          success: false,
          message: 'Current password is incorrect'
        });
      }

      // Update password
      user.password = security.newPassword;
      await user.save();

      return res.json({
        success: true,
        message: 'Password updated successfully'
      });
    }

    // Update other settings
    const updateData = {};
    if (storeSettings) updateData.storeSettings = storeSettings;
    if (notificationSettings) updateData.notificationSettings = notificationSettings;
    if (paymentSettings) updateData.paymentSettings = paymentSettings;
    if (shippingSettings) updateData.shippingSettings = shippingSettings;

    const updatedVendor = await Vendor.findByIdAndUpdate(
      vendor._id,
      { $set: updateData },
      { new: true, runValidators: true }
    );

    res.json({
      success: true,
      data: updatedVendor,
      message: 'Settings updated successfully'
    });

  } catch (error) {
    console.error('Update vendor settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update vendor settings',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Upload store logo
 */
const uploadLogo = async (req, res) => {
  try {
    const vendorId = req.user.userId;

    console.log('Logo upload request received for vendor:', vendorId);
    console.log('File info:', req.file ? {
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size
    } : 'No file');
    console.log('File URL:', req.fileUrl);

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No logo file provided'
      });
    }

    // Find vendor
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    console.log('Found vendor:', vendor.businessName);

    // Update vendor with new logo
    const updatedVendor = await Vendor.findByIdAndUpdate(
      vendor._id,
      { $set: { logo: req.fileUrl } },
      { new: true }
    );

    console.log('Logo updated successfully:', req.fileUrl);

    res.json({
      success: true,
      data: {
        logo: updatedVendor.logo
      },
      message: 'Logo uploaded successfully'
    });

  } catch (error) {
    console.error('Upload logo error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload logo',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Upload store banner
 */
const uploadBanner = async (req, res) => {
  try {
    const vendorId = req.user.userId;

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No banner file provided'
      });
    }

    // Find vendor
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Update vendor with new banner
    const updatedVendor = await Vendor.findByIdAndUpdate(
      vendor._id,
      { $set: { banner: req.fileUrl } },
      { new: true }
    );

    res.json({
      success: true,
      data: {
        banner: updatedVendor.banner
      },
      message: 'Banner uploaded successfully'
    });

  } catch (error) {
    console.error('Upload banner error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload banner',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Upload user avatar
 */
const uploadAvatar = async (req, res) => {
  try {
    const vendorId = req.user.userId;

    console.log('Avatar upload request received for vendor:', vendorId);
    console.log('File info:', req.file ? {
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size
    } : 'No file');
    console.log('File URL:', req.fileUrl);

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No avatar file provided'
      });
    }

    // Update user with new avatar
    const updatedUser = await User.findByIdAndUpdate(
      vendorId,
      { $set: { avatar: req.fileUrl } },
      { new: true }
    );

    if (!updatedUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    console.log('Avatar updated successfully:', req.fileUrl);

    res.json({
      success: true,
      data: {
        avatar: updatedUser.avatar
      },
      message: 'Avatar uploaded successfully'
    });

  } catch (error) {
    console.error('Upload avatar error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload avatar',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Setup Shiprocket pickup location for vendor
 */
const setupShiprocketPickup = async (req, res) => {
  try {
    const vendorId = req.user.userId;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Validate vendor data for pickup location
    const validationErrors = shiprocketService.validateVendorForPickup(vendor);
    if (validationErrors.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot setup pickup location',
        errors: validationErrors
      });
    }

    // Check if pickup location already exists
    if (vendor.shiprocket?.pickupLocationName) {
      try {
        const locations = await shiprocketService.fetchPickupLocations();
        const existingLocation = locations.find(loc =>
          loc.pickup_location === vendor.shiprocket.pickupLocationName
        );

        if (existingLocation) {
          return res.json({
            success: true,
            message: 'Pickup location already configured',
            data: {
              pickupLocationName: vendor.shiprocket.pickupLocationName,
              pickupLocationCode: vendor.shiprocket.pickupLocationCode,
              status: 'existing'
            }
          });
        }
      } catch (error) {
        console.log('Error checking existing pickup locations:', error.message);
      }
    }

    // Create new pickup location
    const result = await shiprocketService.addPickupLocationForVendor(vendor);

    res.json({
      success: true,
      message: 'Pickup location created successfully',
      data: {
        pickupLocationName: vendor.shiprocket.pickupLocationName,
        pickupLocationCode: vendor.shiprocket.pickupLocationCode,
        status: 'created',
        shiprocketResponse: result
      }
    });

  } catch (error) {
    console.error('Setup Shiprocket pickup error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to setup pickup location',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get Shiprocket pickup location status for vendor
 */
const getShiprocketPickupStatus = async (req, res) => {
  try {
    const vendorId = req.user.userId;

    // Get vendor details
    const vendor = await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Validate vendor data
    const validationErrors = shiprocketService.validateVendorForPickup(vendor);
    const isValidForPickup = validationErrors.length === 0;

    let pickupLocationStatus = 'not_configured';
    let pickupLocationExists = false;

    if (vendor.shiprocket?.pickupLocationName) {
      try {
        const locations = await shiprocketService.fetchPickupLocations();
        pickupLocationExists = locations.some(loc =>
          loc.pickup_location === vendor.shiprocket.pickupLocationName
        );
        pickupLocationStatus = pickupLocationExists ? 'configured' : 'missing';
      } catch (error) {
        console.log('Error fetching pickup locations:', error.message);
        pickupLocationStatus = 'error';
      }
    }

    res.json({
      success: true,
      data: {
        isValidForPickup,
        validationErrors,
        pickupLocationName: vendor.shiprocket?.pickupLocationName || null,
        pickupLocationCode: vendor.shiprocket?.pickupLocationCode || null,
        pickupLocationStatus,
        pickupLocationExists,
        businessAddress: vendor.businessAddress,
        contactInfo: {
          businessPhone: vendor.contactInfo?.businessPhone,
          businessEmail: vendor.contactInfo?.businessEmail
        }
      }
    });

  } catch (error) {
    console.error('Get Shiprocket pickup status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get pickup location status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getProfile,
  updateProfile,
  updateSettings,
  uploadLogo,
  uploadBanner,
  uploadAvatar,
  setupShiprocketPickup,
  getShiprocketPickupStatus
};
