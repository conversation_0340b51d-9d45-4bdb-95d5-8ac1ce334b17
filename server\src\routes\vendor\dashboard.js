const express = require('express');
const router = express.Router();
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');
const ensureVendorRecord = require('../../middleware/vendor/ensureVendorRecord');
const {
  getDashboardStats,
  getAnalytics
} = require('../../controllers/vendor/dashboardController');

// Apply authentication middleware to all routes
router.use(verifyToken);
router.use(requireUserType(['vendor']));
router.use(ensureVendorRecord);
// Note: Not requiring vendor approval for dashboard access

/**
 * @route   GET /api/vendor/dashboard/stats
 * @desc    Get vendor dashboard statistics
 * @access  Private (Vendor)
 */
router.get('/stats', getDashboardStats);

/**
 * @route   GET /api/vendor/dashboard/analytics
 * @desc    Get vendor analytics data
 * @access  Private (Vendor)
 */
router.get('/analytics', getAnalytics);

/**
 * @route   GET /api/vendor/dashboard/test
 * @desc    Test endpoint to verify authentication
 * @access  Private (Vendor)
 */
router.get('/test', (req, res) => {
  console.log('Vendor test endpoint - req.user:', req.user);
  console.log('Vendor test endpoint - req.vendor:', req.vendor);

  res.json({
    success: true,
    message: 'Vendor authentication working',
    user: {
      userId: req.user?.userId,
      userType: req.user?.userType,
      role: req.user?.role,
      email: req.user?.email,
      vendor: req.vendor ? {
        id: req.vendor._id,
        businessName: req.vendor.businessName,
        status: req.vendor.status
      } : null
    }
  });
});

module.exports = router;
