const mongoose = require('mongoose');
require('dotenv').config();

const Review = require('./src/models/Review');
const User = require('./src/models/User');

async function checkUserReviews() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    const PRODUCT_ID = '6881fd806652576061a0be95';
    
    // Find all reviews for this product
    const reviews = await Review.find({ product: PRODUCT_ID })
      .populate('customer', 'firstName lastName email')
      .sort({ createdAt: -1 });

    console.log(`\nReviews for product ${PRODUCT_ID}:`);
    reviews.forEach((review, index) => {
      console.log(`${index + 1}. ${review.customer.firstName} ${review.customer.lastName} (${review.customer.email})`);
      console.log(`   Customer ID: ${review.customer._id}`);
      console.log(`   Rating: ${review.rating}/5`);
      console.log(`   Comment: "${review.comment}"`);
      console.log(`   Created: ${review.createdAt}\n`);
    });

    // Show current user from token (if we can decode it from localStorage)
    console.log('To fix the issue, the current user should not see the review form if they already reviewed.');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
  }
}

checkUserReviews();
