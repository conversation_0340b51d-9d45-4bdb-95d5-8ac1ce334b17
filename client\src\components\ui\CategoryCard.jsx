import React from 'react';
import ItemCard from './ItemCard';

const CategoryCard = ({ 
  title = "Category Title", 
  icon = null, 
  items = [] 
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-4 w-full">
      <div className="flex items-center mb-3">
        {icon && <span className="mr-2">{icon}</span>}
        <h2 className="text-base font-medium text-gray-800">{title}</h2>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
        {items.map((item, index) => (
          <ItemCard 
            key={item.id || index}
            image={item.image}
            price={item.price}
            badgeText={item.badgeText || "Trending Now"}
            alt={item.alt || `Item ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default CategoryCard;