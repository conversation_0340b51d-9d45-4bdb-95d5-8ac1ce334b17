import axios from 'axios';
import { API_BASE_URL } from '../config/api';

// Create axios instance for shared APIs
const sharedApiClient = axios.create({
  baseURL: API_BASE_URL.endsWith('/api') ? `${API_BASE_URL}/shared` : `${API_BASE_URL}/api/shared`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
sharedApiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Handle response errors
sharedApiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('authToken');
      localStorage.removeItem('authUser');
      localStorage.removeItem('authUserType');
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      // Don't redirect to auth here as it might disrupt the flow
    }
    return Promise.reject(error);
  }
);

// Currency APIs
export const sharedApi = {
  // Get all supported currencies
  getSupportedCurrencies: () => sharedApiClient.get('/currencies'),
  
  // Update user's preferred currency (requires auth)
  updateUserCurrency: (currency) => sharedApiClient.put('/currencies/preference', { currency }),
  
  // Get user's current currency preference (requires auth)
  getUserCurrency: () => sharedApiClient.get('/currencies/preference'),
};

export default sharedApiClient;
