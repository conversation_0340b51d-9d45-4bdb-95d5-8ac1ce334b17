import React, { useEffect } from 'react';
import { useCurrency } from '../contexts/CurrencyContext';
import { useSearch } from '../contexts/SearchContext';

const CurrencyDebugComponent = () => {
  const { currentCurrency, filterProductsByCurrency, getProductPriceInCurrency, getCurrencySymbol } = useCurrency();
  const { searchResults, searchTerm } = useSearch();

  const testProduct = {
    _id: "test123",
    name: "<PERSON><PERSON>",
    description: "This is demo",
    brand: "Samsung",
    sku: "SMSNG",
    pricing: {
      basePrice: 45000,
      salePrice: 4400,
      currency: "INR"
    },
    multiCurrency: {
      INR: {
        basePrice: 45000,
        salePrice: 4400
      },
      USD: {
        basePrice: 5400,
        salePrice: 4144
      }
    },
    inventory: {
      quantity: 10
    }
  };

  useEffect(() => {
    console.log('=== CURRENCY DEBUG COMPONENT ===');
    console.log('Current Currency:', currentCurrency);
    console.log('Search Term:', searchTerm);
    console.log('Search Results:', searchResults);
    
    // Test filtering with test product
    const testFiltered = filterProductsByCurrency([testProduct]);
    console.log('Test product filtered result:', testFiltered);
    
    // Test price retrieval
    const priceData = getProductPriceInCurrency(testProduct);
    console.log('Price data for current currency:', priceData);
    
    // Test with actual search results if available
    if (searchResults && searchResults.length > 0) {
      console.log('First search result:', searchResults[0]);
      const firstProductPrice = getProductPriceInCurrency(searchResults[0]);
      console.log('First product price data:', firstProductPrice);
    }
  }, [currentCurrency, searchResults, searchTerm]);

  return (
    <div style={{ 
      position: 'fixed', 
      bottom: '10px', 
      right: '10px', 
      background: 'white', 
      border: '2px solid red', 
      padding: '10px', 
      zIndex: 9999,
      fontSize: '12px',
      maxWidth: '300px'
    }}>
      <div><strong>Currency Debug</strong></div>
      <div>Current: {currentCurrency}</div>
      <div>Symbol: {getCurrencySymbol()}</div>
      <div>Search Term: {searchTerm}</div>
      <div>Results: {searchResults?.length || 0}</div>
      <div>Test Product Filter Result: {filterProductsByCurrency([testProduct]).length}</div>
      {searchResults?.length > 0 && (
        <div>
          <div>First Result: {searchResults[0]?.name}</div>
          <div>Has multiCurrency: {searchResults[0]?.multiCurrency ? 'Yes' : 'No'}</div>
        </div>
      )}
    </div>
  );
};

export default CurrencyDebugComponent;
