const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Verify JWT token middleware
const verifyToken = async (req, res, next) => {
    try {
        const token = req.header('Authorization')?.replace('Bearer ', '') || req.header('x-auth-token');
        
        if (!token) {
            return res.status(401).json({
                success: false,
                message: 'No token, authorization denied'
            });
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Find user to make sure they still exist
        const user = await User.findById(decoded.userId);
        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'Token is not valid'
            });
        }

        req.userId = decoded.userId;
        req.userType = decoded.userType;
        req.user = { userId: decoded.userId, userType: decoded.userType };
        next();
    } catch (error) {
        console.error('Token verification error:', error);
        res.status(401).json({
            success: false,
            message: 'Token is not valid'
        });
    }
};

// Simple rate limiting (basic implementation)
const authRateLimit = (maxAttempts = 5, windowMs = 15 * 60 * 1000) => {
    const attempts = new Map();
    
    return (req, res, next) => {
        const ip = req.ip || req.connection.remoteAddress;
        const now = Date.now();
        
        if (!attempts.has(ip)) {
            attempts.set(ip, { count: 1, resetTime: now + windowMs });
            return next();
        }
        
        const userAttempts = attempts.get(ip);
        
        if (now > userAttempts.resetTime) {
            attempts.set(ip, { count: 1, resetTime: now + windowMs });
            return next();
        }
        
        if (userAttempts.count >= maxAttempts) {
            return res.status(429).json({
                success: false,
                message: 'Too many attempts, please try again later'
            });
        }
        
        userAttempts.count++;
        next();
    };
};

// Require email verification middleware
const requireEmailVerification = (req, res, next) => {
    // For now, just pass through - can implement later if needed
    next();
};

module.exports = {
    verifyToken,
    requireEmailVerification,
    authRateLimit
};
