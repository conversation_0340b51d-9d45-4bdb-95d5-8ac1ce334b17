// Product middleware (pre/post hooks)
function addProductMiddleware(productSchema) {
  // Pre-save middleware to generate slug and handle various updates
  productSchema.pre('save', function(next) {
    // Generate slug from name if modified and slug doesn't exist
    if (this.isModified('name') && !this.slug) {
      this.slug = this.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '');
    }
    
    // Update lastModified
    this.lastModified = new Date();
    
    // Ensure only one primary image
    if (this.images && this.images.length > 0) {
      const primaryImages = this.images.filter(img => img.isPrimary);
      if (primaryImages.length > 1) {
        this.images.forEach((img, index) => {
          if (index > 0 && img.isPrimary) {
            img.isPrimary = false;
          }
        });
      } else if (primaryImages.length === 0) {
        this.images[0].isPrimary = true;
      }
    }
    
    // Update stock status based on quantity
    if (this.inventory.trackQuantity) {
      if (this.inventory.quantity === 0) {
        this.inventory.stockStatus = this.inventory.allowBackorders ? 'on_backorder' : 'out_of_stock';
      } else {
        this.inventory.stockStatus = 'in_stock';
      }
    }
    
    next();
  });
}

module.exports = { addProductMiddleware };
