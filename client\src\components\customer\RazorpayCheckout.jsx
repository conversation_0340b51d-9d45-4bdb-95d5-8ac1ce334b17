import React, { useState } from 'react';
import { Button, message } from 'antd';
import axios from 'axios';

const RazorpayCheckout = ({ orderData, onSuccess, onFailure }) => {
  const [loading, setLoading] = useState(false);

  const handlePayment = async () => {
    try {
      setLoading(true);

      // Create order
      const { data } = await axios.post('/api/payments/razorpay/create-order', orderData);
      
      if (!data.success) {
        throw new Error(data.message);
      }

      const { order_id, razorpay_order_id, amount, key } = data.data;

      // Razorpay options
      const options = {
        key,
        amount,
        currency: 'INR',
        name: 'Alicartify',
        order_id: razorpay_order_id,
        handler: async (response) => {
          try {
            // Verify payment
            const verifyData = await axios.post('/api/payments/razorpay/verify', {
              ...response,
              order_id
            });

            if (verifyData.data.success) {
              message.success('Payment successful!');
              onSuccess && onSuccess(verifyData.data);
            } else {
              throw new Error('Verification failed');
            }
          } catch (error) {
            message.error('Payment verification failed');
            onFailure && onFailure(error);
          }
        },
        prefill: {
          name: `${orderData.billing.firstName} ${orderData.billing.lastName}`,
          email: orderData.billing.email,
          contact: orderData.billing.phone
        },
        theme: { color: '#1890ff' },
        modal: {
          ondismiss: () => {
            setLoading(false);
            onFailure && onFailure(new Error('Payment cancelled'));
          }
        }
      };

      // Open Razorpay checkout
      const razorpay = new window.Razorpay(options);
      razorpay.open();

    } catch (error) {
      console.error('Payment error:', error);
      message.error(error.message || 'Payment failed');
      onFailure && onFailure(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button 
      type="primary" 
      size="large" 
      onClick={handlePayment}
      loading={loading}
      block
    >
      Pay with Razorpay
    </Button>
  );
};

export default RazorpayCheckout;
