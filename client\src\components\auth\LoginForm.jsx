import React, { useState } from 'react';
import { Form, Checkbox, message } from 'antd';
import { MailOutlined, LockOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import OTPVerificationForm from './OTPVerificationForm';

const LoginForm = ({ userType, onForgotPassword, onLogin, onSendOTP, onVerifyOTP }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [showOTPForm, setShowOTPForm] = useState(false);
  const [otpData, setOtpData] = useState(null);
  const [loginData, setLoginData] = useState(null);

  const handleSubmit = async (values) => {
    setLoading(true);
    setError('');
    try {
      if (onLogin) {
        const currentLoginData = { ...values, rememberMe, userType };
        setLoginData(currentLoginData);
        
        const result = await onLogin(currentLoginData);
        console.log('🔍 Login result:', result);
        
        if (result.requiresOTP) {
          // OTP is required, show OTP form
          console.log('📱 OTP required, showing OTP form');
          setOtpData({
            phone: result.data.phone,
            email: result.data.email,
            expiresIn: result.data.expiresIn
          });
          setShowOTPForm(true);
          message.info(result.message);
        } else if (!result.success) {
          let errorMessage = result.error || 'Login failed. Please check your credentials.';
          
          // Handle cross-account type error with more specific messaging
          if (result.accountType && result.attemptedType) {
            const accountTypeDisplay = result.accountType === 'vendor' ? 'Vendor' : 'Customer';
            const attemptedTypeDisplay = result.attemptedType === 'vendor' ? 'Vendor' : 'Customer';
            
            errorMessage = `This email is registered as a ${accountTypeDisplay} account. Please select "${accountTypeDisplay}" to login, or use a different email for ${attemptedTypeDisplay} login.`;
          }
          
          setError(errorMessage);
          message.error(errorMessage);
        }
        // If result.success is true, the login was successful and handled by parent
      } else {
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('Login values:', { ...values, userType, rememberMe });
      }
    } catch (error) {
      console.error('Login error:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Login failed. Please try again.';
      setError(errorMessage);
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleOTPVerify = async (otpVerificationData) => {
    try {
      // Add OTP to the original login data and retry login
      const loginWithOTP = {
        ...loginData,
        otp: otpVerificationData.otp
      };
      
      const result = await onLogin(loginWithOTP);
      
      if (result.success) {
        setShowOTPForm(false);
        message.success('Login successful!');
        return { success: true };
      } else {
        return { 
          success: false, 
          message: result.message || 'OTP verification failed' 
        };
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'OTP verification failed'
      };
    }
  };

  const handleOTPResend = async () => {
    try {
      if (onSendOTP && otpData) {
        const result = await onSendOTP({
          phone: otpData.phone,
          purpose: 'login'
        });
        return result;
      }
      return { success: false, message: 'Unable to resend OTP' };
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to resend OTP'
      };
    }
  };

  const handleBackToLogin = () => {
    setShowOTPForm(false);
    setOtpData(null);
    setLoginData(null);
    setError('');
  };

  // Show OTP form if OTP verification is required
  if (showOTPForm && otpData) {
    return (
      <OTPVerificationForm
        phone={otpData.phone}
        purpose="login"
        onVerify={handleOTPVerify}
        onResend={handleOTPResend}
        onBack={handleBackToLogin}
        initialTimer={otpData.expiresIn * 60} // Convert minutes to seconds
      />
    );
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Welcome back
        </h2>
        <p className="text-gray-600">
          Sign in to your {userType} account
        </p>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      <Form
        form={form}
        onFinish={handleSubmit}
        layout="vertical"
        requiredMark={false}
        className="space-y-6"
      >
        <Form.Item
          name="email"
          rules={[
            { required: true, message: 'Please enter your email' },
            { type: 'email', message: 'Please enter a valid email' }
          ]}
        >
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MailOutlined className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="email"
              placeholder="Enter your email"
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
            />
          </div>
        </Form.Item>

        <Form.Item
          name="password"
          rules={[
            { required: true, message: 'Please enter your password' },
            { min: 6, message: 'Password must be at least 6 characters' }
          ]}
        >
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <LockOutlined className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type={showPassword ? "text" : "password"}
              placeholder="Enter your password"
              className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              {showPassword ? (
                <EyeInvisibleOutlined className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              ) : (
                <EyeOutlined className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              )}
            </button>
          </div>
        </Form.Item>

        <div className="flex items-center justify-between">
          <Checkbox
            checked={rememberMe}
            onChange={(e) => setRememberMe(e.target.checked)}
            className="text-gray-600"
          >
            Remember me
          </Checkbox>
          <button
            type="button"
            onClick={onForgotPassword}
            className="text-orange-600 hover:text-orange-700 text-sm font-medium transition-colors"
          >
            Forgot password?
          </button>
        </div>

        <Form.Item>
          <button
            type="submit"
            disabled={loading}
            className="w-full bg-gradient-to-r from-orange-500 to-orange-600 text-white py-3 px-4 rounded-lg font-medium hover:from-orange-600 hover:to-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Signing in...
              </div>
            ) : (
              'Sign in'
            )}
          </button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default LoginForm;