import React from 'react';
import { useAuth } from '../hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import AuthDebugPanel from '../components/AuthDebugPanel';
import { ArrowLeftOutlined } from '@ant-design/icons';

const DebugPage = () => {
    const { isAuthenticated, isLoading } = useAuth();
    const navigate = useNavigate();

    if (isLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="mb-8">
                    <button
                        onClick={() => navigate(-1)}
                        className="inline-flex items-center text-gray-600 hover:text-gray-900 mb-4"
                    >
                        <ArrowLeftOutlined className="mr-2" />
                        Back
                    </button>
                    <h1 className="text-3xl font-bold text-gray-900">Authentication Debug</h1>
                    <p className="mt-2 text-gray-600">
                        Diagnose and fix authentication issues that may be causing cart errors
                    </p>
                </div>

                {/* Debug Panel */}
                <AuthDebugPanel />

                {/* Additional Information */}
                <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 className="text-lg font-medium text-gray-900 mb-4">About This Error</h2>
                    <div className="space-y-4 text-sm text-gray-600">
                        <p>
                            The "Access denied. Please check your permissions" error typically occurs when:
                        </p>
                        <ul className="list-disc list-inside space-y-2 ml-4">
                            <li>You're logged in with a vendor or admin account instead of a customer account</li>
                            <li>Your authentication token has expired</li>
                            <li>There's a mismatch between your stored user type and what the server expects</li>
                            <li>Your session data is corrupted or incomplete</li>
                        </ul>
                        <p>
                            <strong>Quick Fix:</strong> If you're trying to use cart functionality, make sure you're logged in with a customer account, not a vendor or admin account.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DebugPage;