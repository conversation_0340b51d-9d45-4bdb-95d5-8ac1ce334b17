import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from 'react-router-dom'
import './index.css'
import './styles/auth.css'
import App from './App.jsx'
import { AuthProvider } from './contexts/AuthContext.jsx'

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <BrowserRouter>
      <AuthProvider>
        <App />
      </AuthProvider>
    </BrowserRouter>
  </StrictMode>
)
