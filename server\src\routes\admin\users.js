const express = require('express');
const router = express.Router();
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');
const {
  getUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  updateUserStatus,
  resetUserPassword,
  getUserStatistics,
  bulkUpdateUsers
} = require('../../controllers/admin/usersController');

// Apply authentication middleware to all routes
router.use(verifyToken);
router.use(requireUserType(['admin']));

// User management routes
router.get('/', getUsers);
router.get('/statistics', getUserStatistics);
router.get('/:id', getUserById);
router.post('/', createUser);
router.put('/:id', updateUser);
router.patch('/:id/status', updateUserStatus);
router.patch('/:id/reset-password', resetUserPassword);
router.patch('/bulk-update', bulkUpdateUsers);
router.delete('/:id', deleteUser);

module.exports = router;