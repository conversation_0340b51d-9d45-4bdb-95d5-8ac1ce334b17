---
type: "always_apply"
---

# These are some rule that you have to to follow strictly to make your code more readable and maintainable

## 1. Use meaningful variable names
- Use descriptive names that reflect the purpose of the variable
- Avoid single-letter variable names
- Use camelCase for variable names
- Use underscores for multi-word variable names
- Avoid using reserved keywords as variable names

## 2. Try to avoid write long lines of code
- Follow Monolith Principle and keep your code as short as possible
- Break up long lines of code into smaller, more manageable components
- Write clean, modular, readable, reusable and easy to maintain code
- Avoid using excessive indentation or nesting
- Use indentation to make the code easier to read
- Use comments to explain complex code

## 3. Try to avoid write Every time complex code
- Sometime there is no need to write complex code, make it simple as much as possible and easy to understand
- Use built-in functions and libraries whenever possible
- Use built-in data structures and algorithms whenever possible
- Use built-in methods and functions whenever possible
- Use built-in operators and functions whenever possible
- Use built-in functions and methods whenever possible

## 4. Write ones and never touch again 
- You have to write code in that way, developer never ever change or modify the code. i mean perfect piece of code use your perfect mind and write code in that way.
- I want code like senior developer write code that has decade of experience, not junior developer write code.
- You have to write code like production ready and match the market standards.
- also write test case after every code you write, because test case is the most important thing in software development (you have to do in the last of task).
- Try to avoid import, syntax, duplicate or any other common errors.
- First check the codebase file, Is that their is already code available that can used in task and if yes then use that code.
- Try to reuse the existing code not to write new code.