import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { message } from 'antd';
import { productsApi, searchApi } from '../services/publicApi';
import { useCurrency } from './CurrencyContext';

const SearchContext = createContext();

export const useSearch = () => {
  const context = useContext(SearchContext);
  if (!context) {
    throw new Error('useSearch must be used within a SearchProvider');
  }
  return context;
};

export const SearchProvider = ({ children }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const { filterProductsByCurrency, currentCurrency } = useCurrency();
  const [filters, setFilters] = useState({
    sortBy: 'relevance', // relevance, price-low-high, price-high-low, rating, newest
    minPrice: '',
    maxPrice: '',
    inStock: false
  });

  // Search products
  const searchProducts = useCallback(async (query, customFilters = {}) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setLoading(true);
      const searchFilters = { ...filters, ...customFilters };
      
      // Map client sort values to server sort values
      const mapSortValue = (sortBy) => {
        switch (sortBy) {
          case 'price-low-high':
            return 'price_asc';
          case 'price-high-low':
            return 'price_desc';
          case 'rating':
            return 'rating';
          case 'newest':
            return 'newest';
          case 'relevance':
          default:
            return 'relevance';
        }
      };
      
      // Build search parameters
      const params = {
        limit: 50,
        ...searchFilters,
        sortBy: mapSortValue(searchFilters.sortBy),
        inStock: searchFilters.inStock ? 'true' : undefined // Convert boolean to string or undefined
      };

      // Remove empty values
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key];
        }
      });

      const response = await productsApi.searchProducts(query, params);
      
      console.log('🔍 Full search API response:', response.data);
      
      if (response.data.success) {
        let results = response.data.data.products || response.data.data || [];
        console.log('🔍 Search API returned products:', results.length, results);
        
        // Log first product structure for debugging
        if (results.length > 0) {
          console.log('📦 First product structure:', JSON.stringify(results[0], null, 2));
        }
        
        // Apply currency filtering
        const beforeFilterCount = results.length;
        results = filterProductsByCurrency(results);
        console.log('💰 After currency filtering:', results.length, 'from', beforeFilterCount, 'products');
        console.log('💰 Filtered results:', results);
        
        setSearchResults(results);
      } else {
        console.log('❌ Search API returned no success');
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      
      // More detailed error logging
      if (error.code === 'ERR_NETWORK') {
        console.error('Network error - check if server is running on http://localhost:5000');
        message.error('Network error: Unable to connect to server. Please check if the server is running.');
      } else if (error.response?.status === 0) {
        console.error('CORS error - preflight request failed');
        message.error('CORS error: Cross-origin request blocked. Please check server CORS configuration.');
      } else if (error.response) {
        console.error('Server responded with error:', error.response.status, error.response.data);
        message.error(`Server error: ${error.response.status} - ${error.response.statusText}`);
      } else {
        console.error('Unknown error:', error.message);
        message.error('Failed to search products');
      }
      
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  }, [filters, filterProductsByCurrency, currentCurrency]);

  // Get search suggestions
  const getSuggestions = useCallback(async (query) => {
    if (!query.trim() || query.length < 2) {
      setSuggestions([]);
      return;
    }

    try {
      const response = await searchApi.getSuggestions(query);
      if (response.data.success) {
        setSuggestions(response.data.data || []);
      }
    } catch (error) {
      console.error('Suggestions error:', error);
      setSuggestions([]);
    }
  }, []);


  // Update filters
  const updateFilters = useCallback((newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Re-run search when currency changes
  useEffect(() => {
    if (searchTerm && searchTerm.trim()) {
      console.log('🔄 Currency changed, re-running search for:', searchTerm, 'with currency:', currentCurrency);
      // Use setTimeout to avoid infinite loops and ensure state is settled
      const timeoutId = setTimeout(() => {
        searchProducts(searchTerm);
      }, 100);
      return () => clearTimeout(timeoutId);
    }
  }, [currentCurrency, searchTerm]); // Include searchTerm to ensure we have a term to search

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchTerm('');
    setSearchResults([]);
    setSuggestions([]);
    setShowSuggestions(false);
    setFilters({
      sortBy: 'relevance',
      minPrice: '',
      maxPrice: '',
      inStock: false
    });
  }, []);

  const value = {
    // State
    searchTerm,
    searchResults,
    loading,
    suggestions,
    showSuggestions,
    filters,
    
    // Actions
    setSearchTerm,
    setShowSuggestions,
    searchProducts,
    getSuggestions,
    updateFilters,
    clearSearch
  };

  return (
    <SearchContext.Provider value={value}>
      {children}
    </SearchContext.Provider>
  );
};
