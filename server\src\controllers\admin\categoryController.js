const Category = require('../../models/Category');
const Product = require('../../models/Product');
const imageUpload = require('../../middleware/upload/imageUpload');

/**
 * Get all categories with pagination and filters
 */
const getCategories = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const search = req.query.search;
    const status = req.query.status;
    const level = req.query.level;

    // Build filter
    const filter = {};
    if (status) filter.status = status;
    if (level !== undefined) filter.level = parseInt(level);
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const [categories, totalCategories] = await Promise.all([
      Category.find(filter)
        .populate('parent', 'name slug')
        .sort({ level: 1, sortOrder: 1, name: 1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Category.countDocuments(filter)
    ]);

    const totalPages = Math.ceil(totalCategories / limit);

    res.json({
      success: true,
      data: {
        categories,
        pagination: {
          currentPage: page,
          totalPages,
          totalCategories,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
          limit
        }
      }
    });

  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch categories',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get category tree structure
 */
const getCategoryTree = async (req, res) => {
  try {
    const tree = await Category.getTree();
    
    res.json({
      success: true,
      data: { tree }
    });

  } catch (error) {
    console.error('Get category tree error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch category tree',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get single category by ID
 */
const getCategory = async (req, res) => {
  try {
    const categoryId = req.params.id;

    const category = await Category.findById(categoryId)
      .populate('parent', 'name slug level')
      .populate('children');

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    // Get hierarchy
    const hierarchy = await Category.getHierarchy(categoryId);

    res.json({
      success: true,
      data: {
        category,
        hierarchy
      }
    });

  } catch (error) {
    console.error('Get category error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch category',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Create new category
 */
const createCategory = async (req, res) => {
  try {
    const categoryData = {
      ...req.body,
      image: req.fileUrls?.[0] || null
    };

    // Validate parent category if provided
    if (categoryData.parent) {
      const parentCategory = await Category.findById(categoryData.parent);
      if (!parentCategory) {
        return res.status(400).json({
          success: false,
          message: 'Parent category not found'
        });
      }
      
      // Check maximum nesting level (5 levels deep)
      if (parentCategory.level >= 4) {
        return res.status(400).json({
          success: false,
          message: 'Maximum category nesting level (5) exceeded'
        });
      }
    }

    const category = new Category(categoryData);
    await category.save();

    // Populate parent for response
    await category.populate('parent', 'name slug');

    res.status(201).json({
      success: true,
      message: 'Category created successfully',
      data: { category }
    });

  } catch (error) {
    console.error('Create category error:', error);
    
    // Clean up uploaded image on error
    if (req.fileUrls?.[0]) {
      try {
        await imageUpload.deleteImage(req.cloudinaryPublicIds?.[0]);
      } catch (deleteError) {
        console.error('Error deleting image:', deleteError);
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create category',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update category
 */
const updateCategory = async (req, res) => {
  try {
    const categoryId = req.params.id;
    const category = await Category.findById(categoryId);

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    const updateData = { ...req.body };

    // Handle image upload
    if (req.fileUrls?.[0]) {
      // Delete old image if exists
      if (category.image) {
        try {
          await imageUpload.deleteImage(category.image);
        } catch (deleteError) {
          console.error('Error deleting old image:', deleteError);
        }
      }
      updateData.image = req.fileUrls[0];
    }

    // Validate parent change
    if (updateData.parent && updateData.parent !== category.parent?.toString()) {
      const newParent = await Category.findById(updateData.parent);
      if (!newParent) {
        return res.status(400).json({
          success: false,
          message: 'New parent category not found'
        });
      }

      // Prevent circular reference
      const descendants = await category.getDescendants();
      if (descendants.some(d => d._id.toString() === updateData.parent)) {
        return res.status(400).json({
          success: false,
          message: 'Cannot set a descendant category as parent'
        });
      }

      // Check nesting level
      if (newParent.level >= 4) {
        return res.status(400).json({
          success: false,
          message: 'Maximum category nesting level (5) exceeded'
        });
      }
    }

    Object.assign(category, updateData);
    await category.save();

    await category.populate('parent', 'name slug');

    res.json({
      success: true,
      message: 'Category updated successfully',
      data: { category }
    });

  } catch (error) {
    console.error('Update category error:', error);
    
    // Clean up uploaded image on error
    if (req.fileUrls?.[0]) {
      try {
        await imageUpload.deleteImage(req.cloudinaryPublicIds?.[0]);
      } catch (deleteError) {
        console.error('Error deleting image:', deleteError);
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update category',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Delete category
 */
const deleteCategory = async (req, res) => {
  try {
    const categoryId = req.params.id;
    const category = await Category.findById(categoryId);

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    // Check if category has children
    const childrenCount = await Category.countDocuments({ parent: categoryId });
    if (childrenCount > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete category with subcategories. Delete or move subcategories first.'
      });
    }

    // Check if category has products
    const productCount = await Product.countDocuments({ category: categoryId });
    if (productCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete category with ${productCount} products. Move or delete products first.`
      });
    }

    // Delete category image if exists
    if (category.image) {
      try {
        await imageUpload.deleteImage(category.image);
      } catch (deleteError) {
        console.error('Error deleting category image:', deleteError);
      }
    }

    await Category.findByIdAndDelete(categoryId);

    res.json({
      success: true,
      message: 'Category deleted successfully'
    });

  } catch (error) {
    console.error('Delete category error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete category',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get category statistics
 */
const getCategoryStats = async (req, res) => {
  try {
    const stats = await Category.getStatistics();
    const popularCategories = await Category.getPopular(5);

    res.json({
      success: true,
      data: {
        stats,
        popularCategories
      }
    });

  } catch (error) {
    console.error('Get category stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch category statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getCategories,
  getCategoryTree,
  getCategory,
  createCategory,
  updateCategory,
  deleteCategory,
  getCategoryStats
};
