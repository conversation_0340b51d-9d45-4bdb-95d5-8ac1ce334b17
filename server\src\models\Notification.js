const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
  recipient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  type: {
    type: String,
    enum: [
      'product_approval_request',
      'product_approved',
      'product_rejected',
      'product_changes_requested',
      'vendor_verification_request',
      'vendor_verified',
      'vendor_rejected',
      'order_placed',
      'order_updated',
      'payout_processed',
      'commission_earned',
      'low_stock_alert',
      'system_announcement',
      'review_received',
      'message_received'
    ],
    required: true
  },
  title: {
    type: String,
    required: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  message: {
    type: String,
    required: true,
    maxlength: [1000, 'Message cannot exceed 1000 characters']
  },
  data: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  relatedEntity: {
    entityType: {
      type: String,
      enum: ['Product', 'Vendor', 'Order', 'User', 'Review'],
    },
    entityId: {
      type: mongoose.Schema.Types.ObjectId
    }
  },
  status: {
    type: String,
    enum: ['unread', 'read', 'archived'],
    default: 'unread'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  channels: [{
    type: String,
    enum: ['in_app', 'email', 'sms', 'push'],
    default: 'in_app'
  }],
  deliveryStatus: {
    in_app: {
      sent: { type: Boolean, default: false },
      sentAt: Date,
      read: { type: Boolean, default: false },
      readAt: Date
    },
    email: {
      sent: { type: Boolean, default: false },
      sentAt: Date,
      delivered: { type: Boolean, default: false },
      deliveredAt: Date,
      opened: { type: Boolean, default: false },
      openedAt: Date
    },
    sms: {
      sent: { type: Boolean, default: false },
      sentAt: Date,
      delivered: { type: Boolean, default: false },
      deliveredAt: Date
    },
    push: {
      sent: { type: Boolean, default: false },
      sentAt: Date,
      delivered: { type: Boolean, default: false },
      deliveredAt: Date,
      clicked: { type: Boolean, default: false },
      clickedAt: Date
    }
  },
  scheduledFor: Date,
  expiresAt: Date,
  actionUrl: String,
  actionText: String,
  metadata: {
    source: String,
    campaign: String,
    tags: [String]
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
notificationSchema.index({ recipient: 1, status: 1 });
notificationSchema.index({ type: 1 });
notificationSchema.index({ createdAt: -1 });
notificationSchema.index({ scheduledFor: 1 });
notificationSchema.index({ expiresAt: 1 });
notificationSchema.index({ 'relatedEntity.entityType': 1, 'relatedEntity.entityId': 1 });

// Virtual for checking if notification is expired
notificationSchema.virtual('isExpired').get(function() {
  return this.expiresAt && this.expiresAt < new Date();
});

// Static method to create notification
notificationSchema.statics.createNotification = async function(notificationData) {
  const notification = new this(notificationData);
  await notification.save();

  // TODO: Trigger notification delivery based on channels
  await notification.deliver();

  return notification;
};

// Instance method to mark as read
notificationSchema.methods.markAsRead = function() {
  this.status = 'read';
  this.deliveryStatus.in_app.read = true;
  this.deliveryStatus.in_app.readAt = new Date();
  return this.save();
};

// Instance method to archive notification
notificationSchema.methods.archive = function() {
  this.status = 'archived';
  return this.save();
};

// Instance method to deliver notification
notificationSchema.methods.deliver = async function() {
  const deliveryPromises = [];

  for (const channel of this.channels) {
    switch (channel) {
      case 'in_app':
        this.deliveryStatus.in_app.sent = true;
        this.deliveryStatus.in_app.sentAt = new Date();
        break;
      case 'email':
        // TODO: Implement email delivery
        deliveryPromises.push(this.sendEmail());
        break;
      case 'sms':
        // TODO: Implement SMS delivery
        deliveryPromises.push(this.sendSMS());
        break;
      case 'push':
        // TODO: Implement push notification delivery
        deliveryPromises.push(this.sendPush());
        break;
    }
  }

  await Promise.all(deliveryPromises);
  return this.save();
};

// Instance method to send email (placeholder)
notificationSchema.methods.sendEmail = async function() {
  // TODO: Implement email sending logic
  this.deliveryStatus.email.sent = true;
  this.deliveryStatus.email.sentAt = new Date();
  return Promise.resolve();
};

// Instance method to send SMS (placeholder)
notificationSchema.methods.sendSMS = async function() {
  // TODO: Implement SMS sending logic
  this.deliveryStatus.sms.sent = true;
  this.deliveryStatus.sms.sentAt = new Date();
  return Promise.resolve();
};

// Instance method to send push notification (placeholder)
notificationSchema.methods.sendPush = async function() {
  // TODO: Implement push notification sending logic
  this.deliveryStatus.push.sent = true;
  this.deliveryStatus.push.sentAt = new Date();
  return Promise.resolve();
};

// Static method to get user notifications
notificationSchema.statics.getUserNotifications = function(userId, options = {}) {
  const {
    status = 'unread',
    limit = 20,
    skip = 0,
    type
  } = options;

  const query = { recipient: userId };

  if (status !== 'all') {
    query.status = status;
  }

  if (type) {
    query.type = type;
  }

  return this.find(query)
    .populate('sender', 'firstName lastName avatar')
    .sort({ createdAt: -1 })
    .limit(limit)
    .skip(skip);
};

// Static method to get notification statistics
notificationSchema.statics.getStatistics = async function(userId) {
  const stats = await this.aggregate([
    { $match: { recipient: mongoose.Types.ObjectId(userId) } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);

  const result = {
    unread: 0,
    read: 0,
    archived: 0,
    total: 0
  };

  stats.forEach(stat => {
    result[stat._id] = stat.count;
    result.total += stat.count;
  });

  return result;
};

module.exports = mongoose.model('Notification', notificationSchema);