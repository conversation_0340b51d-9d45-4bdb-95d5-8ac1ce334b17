/**
 * Client-side Refresh Service - Replacement for Socket.IO
 * Handles auto-refresh and manual refresh functionality
 */

class RefreshService {
  constructor() {
    this.intervals = new Map();
    this.callbacks = new Map();
    console.log('🔄 Client refresh service initialized');
  }

  /**
   * Start auto-refresh for a specific data type
   * @param {string} key - Unique identifier for the refresh
   * @param {function} callback - Function to call for refresh
   * @param {number} interval - Refresh interval in milliseconds (default: 30 seconds)
   */
  startAutoRefresh(key, callback, interval = 30000) {
    // Clear existing interval if any
    this.stopAutoRefresh(key);

    // Store callback
    this.callbacks.set(key, callback);

    // Set up interval
    const intervalId = setInterval(() => {
      try {
        callback();
      } catch (error) {
        console.error(`Error in auto-refresh for ${key}:`, error);
      }
    }, interval);

    this.intervals.set(key, intervalId);
    console.log(`🔄 Auto-refresh started for ${key} (${interval}ms interval)`);
  }

  /**
   * Stop auto-refresh for a specific data type
   * @param {string} key - Unique identifier for the refresh
   */
  stopAutoRefresh(key) {
    const intervalId = this.intervals.get(key);
    if (intervalId) {
      clearInterval(intervalId);
      this.intervals.delete(key);
      this.callbacks.delete(key);
      console.log(`🛑 Auto-refresh stopped for ${key}`);
    }
  }

  /**
   * Stop all auto-refresh intervals
   */
  stopAllAutoRefresh() {
    this.intervals.forEach((intervalId, key) => {
      clearInterval(intervalId);
      console.log(`🛑 Auto-refresh stopped for ${key}`);
    });
    this.intervals.clear();
    this.callbacks.clear();
  }

  /**
   * Manual refresh for a specific data type
   * @param {string} key - Unique identifier for the refresh
   */
  manualRefresh(key) {
    const callback = this.callbacks.get(key);
    if (callback) {
      try {
        callback();
        console.log(`🔄 Manual refresh triggered for ${key}`);
      } catch (error) {
        console.error(`Error in manual refresh for ${key}:`, error);
      }
    } else {
      console.warn(`No callback found for refresh key: ${key}`);
    }
  }

  /**
   * Refresh all registered callbacks
   */
  refreshAll() {
    this.callbacks.forEach((callback, key) => {
      try {
        callback();
        console.log(`🔄 Refreshed ${key}`);
      } catch (error) {
        console.error(`Error refreshing ${key}:`, error);
      }
    });
  }

  /**
   * Get active refresh keys
   */
  getActiveRefreshKeys() {
    return Array.from(this.intervals.keys());
  }

  /**
   * Check if auto-refresh is active for a key
   */
  isAutoRefreshActive(key) {
    return this.intervals.has(key);
  }

  /**
   * Update refresh interval for existing auto-refresh
   */
  updateRefreshInterval(key, newInterval) {
    const callback = this.callbacks.get(key);
    if (callback) {
      this.stopAutoRefresh(key);
      this.startAutoRefresh(key, callback, newInterval);
    }
  }

  /**
   * Dashboard-specific refresh methods
   */
  startDashboardRefresh(refreshCallback, interval = 60000) {
    this.startAutoRefresh('dashboard', refreshCallback, interval);
  }

  startOrdersRefresh(refreshCallback, interval = 30000) {
    this.startAutoRefresh('orders', refreshCallback, interval);
  }

  startVendorStatsRefresh(refreshCallback, interval = 45000) {
    this.startAutoRefresh('vendorStats', refreshCallback, interval);
  }

  startNotificationsRefresh(refreshCallback, interval = 15000) {
    this.startAutoRefresh('notifications', refreshCallback, interval);
  }

  /**
   * Cleanup method to be called when component unmounts
   */
  cleanup() {
    this.stopAllAutoRefresh();
  }
}

// Create singleton instance
const refreshService = new RefreshService();

export default refreshService;