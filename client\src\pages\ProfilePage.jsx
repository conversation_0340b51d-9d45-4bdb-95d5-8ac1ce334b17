import React, { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import { message, Tabs, Spin, Modal, Upload, Select, notification } from 'antd';
import { 
  UserOutlined, 
  EnvironmentOutlined, 
  BellOutlined, 
  SafetyCertificateOutlined, 
  SettingOutlined, 
  CameraOutlined, 
  UploadOutlined, 
  CloseOutlined, 
  CheckOutlined, 
 CreditCardOutlined, 
  ShoppingOutlined, 
  HeartOutlined,
  PhoneOutlined,
  DollarOutlined,
  CarOutlined
} from '@ant-design/icons';
import { orderTrackingApi } from '../services/orderTrackingApi';
import ProfileHeader from '../components/profile/ProfileHeader';
import PersonalInfoSection from '../components/profile/PersonalInfoSection';
import AddressInfoSection from '../components/profile/AddressInfoSection';

const { TabPane } = Tabs;

const ProfilePage = () => {
  const { user, isAuthenticated, userType, updateProfile, isLoading } = useAuth();
  const navigate = useNavigate();
  
  // State for edit mode
  const [isEditing, setIsEditing] = useState(false);
  
  // State for form data - Simplified with only essential fields
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'United States',
    preferences: {
      currency: 'USD'
    }
  });
  
  // State for tracking orders
  const [trackingOrders, setTrackingOrders] = useState([]);
  const [trackingLoading, setTrackingLoading] = useState(false);
  
  // Available currencies based on product currencies
  const availableCurrencies = [
    { value: 'USD', label: 'US Dollar ($)', symbol: '$' },
    { value: 'EUR', label: 'Euro (€)', symbol: '€' },
    { value: 'GBP', label: 'British Pound (£)', symbol: '£' },
    { value: 'INR', label: 'Indian Rupee (₹)', symbol: '₹' },
    { value: 'JPY', label: 'Japanese Yen (¥)', symbol: '¥' },
    { value: 'CAD', label: 'Canadian Dollar (C$)', symbol: 'C$' },
    { value: 'AUD', label: 'Australian Dollar (A$)', symbol: 'A$' },
    { value: 'CHF', label: 'Swiss Franc (CHF)', symbol: 'CHF' },
    { value: 'CNY', label: 'Chinese Yuan (¥)', symbol: '¥' },
    { value: 'KRW', label: 'Korean Won (₩)', symbol: '₩' }
  ];
  
  // State for profile picture
  const [avatar, setAvatar] = useState(null);
  const [avatarPreview, setAvatarPreview] = useState(null);
  const [showAvatarModal, setShowAvatarModal] = useState(false);
  
  // State for saving
  const [saving, setSaving] = useState(false);
  
  // State for active tab
  const [activeTab, setActiveTab] = useState('1');

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/auth');
    }
  }, [isAuthenticated, isLoading, navigate]);

  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: user.email || '',
        phone: user.phone || '',
        address: user.address || '',
        city: user.city || '',
        state: user.state || '',
        zipCode: user.zipCode || '',
        country: user.country || 'United States',
        preferences: user.preferences || {
          currency: 'USD'
        }
      });
      
      // Set avatar preview if user has an avatar
      if (user.avatar) {
        setAvatarPreview(user.avatar);
      }
    }
  }, [user]);
  
  // Fetch user's tracking orders
  const fetchTrackingOrders = async () => {
    if (!user || userType !== 'customer') return;
    
    setTrackingLoading(true);
    try {
      const response = await orderTrackingApi.getCustomerTrackings();
      if (response.success) {
        setTrackingOrders(response.data || []);
      }
    } catch (error) {
      console.error('Error fetching tracking orders:', error);
    } finally {
      setTrackingLoading(false);
    }
  };
  
  useEffect(() => {
    if (user && userType === 'customer') {
      fetchTrackingOrders();
    }
  }, [user, userType]);

  const handleSubmit = async (e) => {
    if (e) e.preventDefault();
    setSaving(true);
    
    try {
      // Create form data for file upload
      const profileData = new FormData();
      
      // Add all form fields to form data
      Object.keys(formData).forEach(key => {
        if (key !== 'preferences') {
          profileData.append(key, formData[key]);
        }
      });
      
      // Add preferences as JSON string
      profileData.append('preferences', JSON.stringify(formData.preferences));
      
      // Add avatar if changed - backend will handle Cloudinary upload
      if (avatar) {
        profileData.append('avatar', avatar);
      }
      
      const result = await updateProfile(profileData);
      
      if (result.success) {
        message.success('Profile updated successfully!');
        setIsEditing(false);
        setAvatar(null); // Reset avatar state after successful update
      } else {
        message.error(result.error || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Update error:', error);
      message.error('Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  const handleChange = (field, value) => {
    if (field.includes('.')) {
      // Handle nested fields (e.g., preferences.notifications.email)
      const fields = field.split('.');
      setFormData(prev => {
        const newData = { ...prev };
        let current = newData;
        
        for (let i = 0; i < fields.length - 1; i++) {
          current = current[fields[i]];
        }
        
        current[fields[fields.length - 1]] = value;
        return newData;
      });
    } else {
      // Handle top-level fields
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };
  
  const handleAvatarChange = (info) => {
    if (info.file) {
      // Check file size (limit to 2MB)
      if (info.file.size > 2 * 1024 * 1024) {
        message.error('Image must be smaller than 2MB!');
        return;
      }
      
      // Check file type
      if (!['image/jpeg', 'image/png', 'image/gif'].includes(info.file.type)) {
        message.error('You can only upload JPG, PNG, or GIF files!');
        return;
      }
      
      // Set avatar file
      setAvatar(info.file);
      
      // Create preview URL
      const reader = new FileReader();
      reader.onload = () => {
        setAvatarPreview(reader.result);
      };
      reader.readAsDataURL(info.file);
      
      // Close modal
      setShowAvatarModal(false);
    }
  };
  
  const handleEditToggle = () => {
    setIsEditing(!isEditing);
    
    // If canceling edit, reset form data to user data
    if (isEditing) {
      if (user) {
        setFormData({
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          email: user.email || '',
          phone: user.phone || '',
          address: user.address || '',
          city: user.city || '',
          state: user.state || '',
          zipCode: user.zipCode || '',
          country: user.country || 'United States',
          preferences: user.preferences || {
            currency: 'USD'
          }
        });
        
        // Reset avatar preview
        if (user.avatar) {
          setAvatarPreview(user.avatar);
        } else {
          setAvatarPreview(null);
        }
        
        // Reset avatar
        setAvatar(null);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-5xl mx-auto px-4">
        {/* Profile Header with Avatar */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-8">
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-6">
            <div className="relative">
              <div className="h-32 w-32 rounded-full border-4 border-gray-200 bg-white shadow-md overflow-hidden">
                {avatarPreview ? (
                  <img 
                    src={avatarPreview} 
                    alt="Profile" 
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <div className="h-full w-full flex items-center justify-center bg-gray-100">
                    <UserOutlined className="text-6xl text-gray-400" />
                  </div>
                )}
              </div>
              
              {isEditing && (
                <button 
                  onClick={() => setShowAvatarModal(true)}
                  className="absolute bottom-0 right-0 bg-orange-500 text-white p-2 rounded-full shadow-md hover:bg-orange-600 transition-colors duration-200"
                  title="Upload Profile Picture"
                >
                  <CameraOutlined />
                </button>
              )}
            </div>
            
            <div className="flex-1">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {user.userType === 'vendor' ? (
                      user.businessName || user.contactPerson || 'Vendor Account'
                    ) : (
                      (() => {
                        if (!user) return 'User Account';
                        const firstName = user.firstName || '';
                        const lastName = user.lastName || '';
                        const fullName = `${firstName} ${lastName}`.trim();
                        return fullName || user.email || 'User Account';
                      })()
                    )}
                  </h1>
                  <p className="text-gray-500">{user.email}</p>
                  <div className="flex items-center mt-2 text-sm text-gray-500">
                    <SafetyCertificateOutlined className="mr-1" />
                    <span className="capitalize">
                      {userType === 'admin' ? (
                        <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-semibold">
                          Admin Account
                        </span>
                      ) : userType === 'vendor' ? (
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-semibold">
                          Vendor Account
                        </span>
                      ) : (
                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-semibold">
                          Customer Account
                        </span>
                      )}
                    </span>
                    {user.emailVerification?.isVerified && (
                      <>
                        <span className="mx-2">•</span>
                        <span className="text-green-600">✓ Verified</span>
                      </>
                    )}
                    <span className="mx-2">•</span>
                    <span>
                      {user.createdAt && !isNaN(new Date(user.createdAt)) ? (
                        `Member since ${new Date(user.createdAt).toLocaleDateString()}`
                      ) : (
                        userType === 'admin' ? 'Admin Account' : 'Member'
                      )}
                    </span>
                  </div>
                </div>
                
                <div className="flex space-x-3 mt-4 sm:mt-0">
                  {!isEditing ? (
                    <button
                      onClick={handleEditToggle}
                      className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md transition-colors duration-200 flex items-center space-x-2"
                    >
                      <SettingOutlined />
                      <span>Edit Profile</span>
                    </button>
                  ) : (
                    <>
                      <button
                        onClick={handleEditToggle}
                        disabled={saving}
                        className="border border-gray-300 hover:bg-gray-100 text-gray-700 px-4 py-2 rounded-md transition-colors duration-200 disabled:opacity-50"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={handleSubmit}
                        disabled={saving}
                        className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50"
                      >
                        {saving ? (
                          <>
                            <Spin size="small" />
                            <span>Saving...</span>
                          </>
                        ) : (
                          <>
                            <CheckOutlined />
                            <span>Save Changes</span>
                          </>
                        )}
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Profile Content */}
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <Tabs 
            activeKey={activeTab} 
            onChange={setActiveTab}
            className="profile-tabs"
            tabBarStyle={{ padding: '0 24px', borderBottom: '1px solid #f0f0f0' }}
          >
            <TabPane 
              tab={
                <span className="flex items-center space-x-2 py-4">
                  <UserOutlined />
                  <span>Personal Info</span>
                </span>
              } 
              key="1"
            >
              <div className="p-6">
                <PersonalInfoSection 
                  user={user} 
                  userType={userType} 
                  editData={formData} 
                  isEditing={isEditing} 
                  onInputChange={handleChange} 
                />
              </div>
            </TabPane>
            
            <TabPane 
              tab={
                <span className="flex items-center space-x-2 py-4">
                  <EnvironmentOutlined />
                  <span>Address</span>
                </span>
              } 
              key="2"
            >
              <div className="p-6">
                <AddressInfoSection 
                  user={user} 
                  editData={formData} 
                  isEditing={isEditing} 
                  onInputChange={handleChange} 
                />
              </div>
            </TabPane>
            
            {userType !== 'admin' && (
              <TabPane 
                tab={
                  <span className="flex items-center space-x-2 py-4">
                    <DollarOutlined />
                    <span>Currency</span>
                  </span>
                } 
                key="3"
              >
                <div className="p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Currency Preferences</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Preferred Currency
                      </label>
                      <select
                        value={formData.preferences?.currency || 'USD'}
                        onChange={(e) => handleChange('preferences.currency', e.target.value)}
                        disabled={!isEditing}
                        className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200 ${!isEditing ? 'bg-gray-100' : ''}`}
                      >
                        {availableCurrencies.map(currency => (
                          <option key={currency.value} value={currency.value}>
                            {currency.label}
                          </option>
                        ))}
                      </select>
                      <p className="text-sm text-gray-500 mt-2">
                        This will be used as your default currency for viewing product prices.
                      </p>
                    </div>
                  </div>
                </div>
              </TabPane>
            )}
            
            {userType === 'customer' && (
              <TabPane 
                tab={
                  <span className="flex items-center space-x-2 py-4">
                    <CarOutlined />
                    <span>Track Orders</span>
                  </span>
                } 
                key="4"
              >
                <div className="p-6">
                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Order Tracking</h3>
                    <p className="text-gray-500 mb-4">Track your orders and view delivery status</p>
                    <button 
                      onClick={() => navigate('/track-order')}
                      className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md transition-colors duration-200 flex items-center space-x-2"
                    >
                      <CarOutlined />
                      <span>Go to Order Tracking</span>
                    </button>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-800 mb-3">Recent Tracking Orders</h4>
                    {trackingLoading ? (
                      <div className="text-center py-4">
                        <Spin size="small" />
                        <p className="text-gray-500 mt-2">Loading tracking information...</p>
                      </div>
                    ) : trackingOrders.length > 0 ? (
                      <div className="space-y-3">
                        {trackingOrders.slice(0, 3).map((order, index) => (
                          <div key={index} className="border border-gray-200 rounded-lg p-3">
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="font-medium text-gray-900">
                                  Order #{order.orderId || 'N/A'}
                                </p>
                                <p className="text-sm text-gray-500">
                                  Tracking: {order.trackingNumber || 'N/A'}
                                </p>
                                <p className="text-sm text-gray-500">
                                  Status: <span className="capitalize">{order.status || 'Processing'}</span>
                                </p>
                              </div>
                              <button
                                onClick={() => navigate(`/track-order/${order.trackingNumber}`)}
                                className="text-orange-500 hover:text-orange-600 text-sm font-medium"
                              >
                                View Details
                              </button>
                            </div>
                          </div>
                        ))}
                        {trackingOrders.length > 3 && (
                          <div className="text-center">
                            <button
                              onClick={() => navigate('/track-order')}
                              className="text-orange-500 hover:text-orange-600 text-sm font-medium"
                            >
                              View All Tracking Orders ({trackingOrders.length})
                            </button>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <CarOutlined className="text-4xl text-gray-300 mb-3" />
                        <p className="text-gray-500">No tracking orders found</p>
                        <p className="text-sm text-gray-400 mt-2">Your order tracking information will appear here</p>
                      </div>
                    )}
                  </div>
                </div>
              </TabPane>
            )}
            
            {userType !== 'admin' && (
              <TabPane 
                tab={
                  <span className="flex items-center space-x-2 py-4">
                    <ShoppingOutlined />
                    <span>Orders</span>
                  </span>
                } 
                key="5"
              >
                <div className="p-6">
                  <div className="text-center py-8">
                    <ShoppingOutlined className="text-6xl text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Your Order History</h3>
                    <p className="text-gray-500 mb-4">View and track all your orders in one place</p>
                    <button 
                      onClick={() => navigate('/orders')}
                      className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md transition-colors duration-200"
                    >
                      View Orders
                    </button>
                  </div>
                </div>
              </TabPane>
            )}
            
                      </Tabs>
        </div>
      </div>
      
      {/* Avatar Upload Modal */}
      <Modal
        title="Update Profile Picture"
        open={showAvatarModal}
        onCancel={() => setShowAvatarModal(false)}
        footer={null}
      >
        <div className="py-4">
          <div className="text-center mb-6">
            <div className="h-32 w-32 mx-auto rounded-full border-4 border-gray-200 overflow-hidden">
              {avatarPreview ? (
                <img 
                  src={avatarPreview} 
                  alt="Profile Preview" 
                  className="h-full w-full object-cover"
                />
              ) : (
                <div className="h-full w-full flex items-center justify-center bg-gray-100">
                  <UserOutlined className="text-6xl text-gray-400" />
                </div>
              )}
            </div>
          </div>
          
          <Upload
            name="avatar"
            listType="picture"
            showUploadList={false}
            beforeUpload={(file) => {
              handleAvatarChange({ file });
              return false;
            }}
          >
            <div className="text-center">
              <button className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md transition-colors duration-200 flex items-center space-x-2 mx-auto">
                <UploadOutlined />
                <span>Upload New Picture</span>
              </button>
              <p className="text-xs text-gray-500 mt-2">
                JPG, PNG or GIF. Max size 2MB.
              </p>
            </div>
          </Upload>
          
          {avatarPreview && (
            <div className="mt-4 text-center">
              <button 
                onClick={() => {
                  setAvatarPreview(null);
                  setAvatar(null);
                  setShowAvatarModal(false);
                }}
                className="text-red-500 hover:text-red-700 transition-colors duration-200 flex items-center space-x-1 mx-auto"
              >
                <CloseOutlined />
                <span>Remove Picture</span>
              </button>
            </div>
          )}
        </div>
      </Modal>
      
      {/* Add custom styles for the profile page */}
      <style jsx>{`
        .profile-tabs .ant-tabs-nav {
          margin-bottom: 0;
        }
        
        .profile-tabs .ant-tabs-tab {
          padding: 12px 0;
          margin: 0 16px 0 0;
        }
        
        .profile-tabs .ant-tabs-tab-active {
          font-weight: 500;
        }
        
        .profile-tabs .ant-tabs-ink-bar {
          background-color: #f97316;
          height: 3px;
        }
      `}</style>
    </div>
  );
};

export default ProfilePage;