import React from "react";
import { Routes, Route, useLocation } from "react-router-dom";
import { CartProvider } from "./contexts/CartContext";
import { SearchProvider } from "./contexts/SearchContext";
import { CurrencyProvider } from "./contexts/CurrencyContext";
import { NavbarProvider } from "./contexts/NavbarContext";
import Navbar from "./components/Navbar";
import ErrorBoundary from "./components/ErrorBoundary";
import LandingPage from "./pages/LandingPage";
import HomePage from "./pages/HomePage";
import ProductsPage from "./pages/ProductsPage";
import ProductDetailPage from "./pages/ProductDetailPage";
import OrdersPage from "./pages/OrdersPage";
import CartPage from "./pages/CartPage";
import CheckoutPage from "./pages/CheckoutPage";
import AuthPage from "./pages/AuthPage";
import ProfilePage from "./pages/ProfilePage";
import SettingsPage from "./pages/SettingsPage";
import DebugPage from "./pages/DebugPage";
import ProductDebugPage from "./pages/ProductDebugPage";
import OrderTrackingPage from "./pages/OrderTrackingPage";
import OrderSuccessPage from "./pages/OrderSuccessPage";
import SignUp from "./components/SignUp";
import EmailVerificationForm from "./components/auth/EmailVerificationForm";
import ResetPasswordForm from "./components/auth/ResetPasswordForm";
import ForgotPasswordForm from "./components/auth/ForgotPasswordForm";
import TestPanelsPage from "./pages/TestPanelsPage";
import SearchPage from "./pages/SearchPage";
import ProtectedRoute from "./components/ProtectedRoute";
import AdminProtectedRoute from "./components/AdminProtectedRoute";
import VendorProtectedRoute from "./components/VendorProtectedRoute";

// Admin Components
import AdminDashboardPage from "./pages/admin/AdminDashboardPage";
import AdminAuthPage from "./pages/admin/AdminAuthPage";

// Vendor Components
import VendorDashboardPage from "./pages/vendor/VendorDashboardPage";

// Agency Components
import AgencyDashboardPage from "./pages/agency/AgencyDashboardPage";

function AppContent() {
  const location = useLocation();
  
  // Pages where we don't want to show the navbar
  const hideNavbarPaths = [
    '/',
    '/auth',
    '/login', 
    '/signup',
    '/verify-email',
    '/reset-password',
    '/forgot-password'
  ];
  
  // Check if current path starts with admin, vendor, or agency routes
  const isAdminOrVendorOrAgencyPage = location.pathname.startsWith('/admin') || location.pathname.startsWith('/vendor') || location.pathname.startsWith('/agency');
  
  const shouldShowNavbar = !hideNavbarPaths.includes(location.pathname) && !isAdminOrVendorOrAgencyPage;

  return (
    <>
      {shouldShowNavbar && <Navbar />}
      <Routes>
        {/* Landing Page - Public Route */}
        <Route path="/" element={<LandingPage />} />
        
        {/* Protected Home Route - Requires Authentication */}
        <Route path="/home" element={
          <ProtectedRoute>
            <HomePage />
          </ProtectedRoute>
        } />
        
        {/* Protected Routes - Require Authentication */}
        <Route path="/page" element={
          <ProtectedRoute>
            <ProductsPage />
          </ProtectedRoute>
        } />
        <Route path="/product/:id" element={
          <ProtectedRoute>
            <ProductDetailPage />
          </ProtectedRoute>
        } />
        <Route path="/orders" element={
          <ProtectedRoute>
            <OrdersPage />
          </ProtectedRoute>
        } />
        <Route path="/cart" element={
          <ProtectedRoute>
            <CartPage />
          </ProtectedRoute>
        } />
        <Route path="/checkout" element={
          <ProtectedRoute>
            <CheckoutPage />
          </ProtectedRoute>
        } />
        <Route path="/track-order" element={
          <ProtectedRoute>
            <OrderTrackingPage />
          </ProtectedRoute>
        } />
        <Route path="/track-order/:trackingNumber" element={
          <ProtectedRoute>
            <OrderTrackingPage />
          </ProtectedRoute>
        } />
        <Route path="/search" element={
          <ProtectedRoute>
            <SearchPage />
          </ProtectedRoute>
        } />
        <Route path="/profile" element={
          <ProtectedRoute>
            <ProfilePage />
          </ProtectedRoute>
        } />
        <Route path="/settings" element={
          <ProtectedRoute>
            <SettingsPage />
          </ProtectedRoute>
        } />
        <Route path="/debug" element={
          <ProtectedRoute>
            <DebugPage />
          </ProtectedRoute>
        } />
        <Route path="/debug/products" element={<ProductDebugPage />} />
        <Route path="/test-panels" element={
          <ProtectedRoute>
            <TestPanelsPage />
          </ProtectedRoute>
        } />
        <Route path="/order-success" element={
          <ProtectedRoute>
            <OrderSuccessPage />
          </ProtectedRoute>
        } />

        {/* Public Authentication Routes */}
        <Route path="/auth" element={<AuthPage />} />
        <Route path="/login" element={<AuthPage />} />
        <Route path="/signup" element={<SignUp />} />
        
        {/* Authentication Routes */}
        <Route path="/verify-email" element={<EmailVerificationForm />} />
        <Route path="/reset-password" element={<ResetPasswordForm />} />
        <Route path="/forgot-password" element={<ForgotPasswordForm onBackToLogin={() => window.location.href = '/auth'} />} />
        
        {/* Admin Routes */}
        <Route path="/admin/auth" element={<AdminAuthPage />} />
        <Route path="/admin" element={
          <AdminProtectedRoute>
            <AdminDashboardPage />
          </AdminProtectedRoute>
        } />
        <Route path="/admin/dashboard" element={
          <AdminProtectedRoute>
            <AdminDashboardPage />
          </AdminProtectedRoute>
        } />

        {/* Vendor Routes */}
        <Route path="/vendor" element={
          <VendorProtectedRoute>
            <VendorDashboardPage />
          </VendorProtectedRoute>
        } />
        <Route path="/vendor/dashboard" element={
          <VendorProtectedRoute>
            <VendorDashboardPage />
          </VendorProtectedRoute>
        } />

        {/* Agency Routes */}
        <Route path="/agency/dashboard" element={
          // <ProtectedRoute>
            <AgencyDashboardPage />
          // </ProtectedRoute>
        } />
        
        {/* 404 Route */}
        <Route path="*" element={
          <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <h1 className="text-6xl font-bold text-gray-900">404</h1>
              <p className="text-xl text-gray-600 mt-4">Page Not Found</p>
              <a href="/" className="text-orange-500 hover:text-orange-600 mt-4 inline-block">
                Go back home
              </a>
            </div>
          </div>
        } />
      </Routes>
    </>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <CurrencyProvider>
        <CartProvider>
          <SearchProvider>
            <NavbarProvider>
              <AppContent />
            </NavbarProvider>
          </SearchProvider>
        </CartProvider>
      </CurrencyProvider>
    </ErrorBoundary>
  );
}

export default App