/**
 * Currency configuration constants
 */

export const SUPPORTED_CURRENCIES = [
  { code: 'INR', name: 'Indian Rupee', symbol: '₹', isDefault: true },
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' }
];

export const DEFAULT_CURRENCY = 'INR';

export const getCurrencyInfo = (code) => {
  return SUPPORTED_CURRENCIES.find(c => c.code === code) || { 
    code, 
    name: code, 
    symbol: code 
  };
};
