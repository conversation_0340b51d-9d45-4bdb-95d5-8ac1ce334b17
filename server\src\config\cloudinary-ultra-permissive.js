const cloudinary = require('cloudinary').v2;
const { CloudinaryStorage } = require('multer-storage-cloudinary');
const multer = require('multer');

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Track if warning has been shown
let warningShown = false;

// Validate Cloudinary configuration
const validateCloudinaryConfig = () => {
  const { cloud_name, api_key, api_secret } = cloudinary.config();

  if (!cloud_name || !api_key || !api_secret) {
    if (!warningShown) {
      console.warn('Cloudinary configuration is missing. Please set CLOUDINARY_CLOUD_NAME, CLOUDINARY_API_KEY, and CLOUDINARY_API_SECRET in your .env file');
      warningShown = true;
    }
    return false;
  }

  return true;
};

// Create ultra-permissive Cloudinary storage
const createCloudinaryStorage = (folder, transformation = {}) => {
  return new CloudinaryStorage({
    cloudinary: cloudinary,
    params: {
      folder: folder,
      resource_type: 'auto', // Let Cloudinary auto-detect everything
      // No allowed_formats restriction at all
      transformation: {
        quality: 'auto',
        fetch_format: 'auto',
        ...transformation
      },
      public_id: (req, file) => {
        // Generate unique filename
        const timestamp = Date.now();
        const randomString = Math.random().toString(36).substring(2, 15);
        return `${timestamp}-${randomString}`;
      }
    }
  });
};

// Storage configurations for different image types
const storageConfigs = {
  // Product images storage
  productImages: createCloudinaryStorage('ecommerce/products', {
    width: 1200,
    height: 1200,
    crop: 'limit',
    quality: 'auto:good'
  }),

  // User avatars storage
  userAvatars: createCloudinaryStorage('ecommerce/users/avatars', {
    width: 300,
    height: 300,
    crop: 'fill',
    gravity: 'face',
    quality: 'auto:good'
  }),

  // Vendor logos storage
  vendorLogos: createCloudinaryStorage('ecommerce/vendors/logos', {
    width: 500,
    height: 500,
    crop: 'limit',
    quality: 'auto:good'
  }),

  // Category images storage
  categoryImages: createCloudinaryStorage('ecommerce/categories', {
    width: 800,
    height: 600,
    crop: 'limit',
    quality: 'auto:good'
  }),

  // Carousel images storage - Ultra permissive
  carouselImages: createCloudinaryStorage('ecommerce/homepage/carousel', {
    width: 1920,
    height: 800,
    crop: 'limit',
    quality: 'auto:good'
  }),

  // Promotion images storage
  promotionImages: createCloudinaryStorage('ecommerce/homepage/promotions', {
    width: 800,
    height: 600,
    crop: 'limit',
    quality: 'auto:good'
  })
};

// Ultra permissive file filter - accept everything
const fileFilter = (req, file, cb) => {
  console.log('🔍 Ultra permissive file filter:', {
    fieldname: file.fieldname,
    originalname: file.originalname,
    mimetype: file.mimetype,
    size: file.size
  });
  
  console.log('✅ Accepting ALL files - let Cloudinary handle validation');
  cb(null, true);
};

// Special carousel file filter - absolutely no restrictions
const carouselFileFilter = (req, file, cb) => {
  console.log('🎠 Carousel upload - NO RESTRICTIONS:', {
    fieldname: file.fieldname,
    originalname: file.originalname,
    mimetype: file.mimetype,
    size: file.size
  });
  
  console.log('✅ Carousel: Accepting file unconditionally');
  cb(null, true);
};

// Create multer instances for different image types
const createMulterInstance = (storageType, options = {}) => {
  const defaultOptions = {
    storage: storageConfigs[storageType],
    fileFilter: fileFilter,
    limits: {
      fileSize: 50 * 1024 * 1024, // 50MB limit - very generous
      files: 10
    }
  };

  return multer({
    ...defaultOptions,
    ...options
  });
};

// Create special carousel multer instance with no file filter
const createCarouselMulterInstance = () => {
  return multer({
    storage: storageConfigs.carouselImages,
    fileFilter: carouselFileFilter, // Use special carousel filter
    limits: {
      fileSize: 50 * 1024 * 1024, // 50MB limit
      files: 1
    }
  });
};

// Multer instances for different use cases
const uploaders = {
  // Product images uploader (multiple files)
  productImages: createMulterInstance('productImages'),

  // User avatar uploader (single file)
  userAvatar: createMulterInstance('userAvatars', {
    limits: { fileSize: 10 * 1024 * 1024, files: 1 }
  }),

  // Vendor logo uploader (single file)
  vendorLogo: createMulterInstance('vendorLogos', {
    limits: { fileSize: 10 * 1024 * 1024, files: 1 }
  }),

  // Category image uploader (single file)
  categoryImage: createMulterInstance('categoryImages', {
    limits: { fileSize: 10 * 1024 * 1024, files: 1 }
  }),

  // Carousel image uploader - ULTRA PERMISSIVE
  carouselImage: createCarouselMulterInstance(),

  // Promotion image uploader (single file)
  promotionImage: createMulterInstance('promotionImages', {
    limits: { fileSize: 10 * 1024 * 1024, files: 1 }
  })
};

// Helper function to delete image from Cloudinary
const deleteImage = async (publicId) => {
  try {
    if (!publicId) return false;
    
    const result = await cloudinary.uploader.destroy(publicId);
    return result.result === 'ok';
  } catch (error) {
    console.error('Error deleting image from Cloudinary:', error);
    return false;
  }
};

// Helper function to extract public ID from Cloudinary URL
const extractPublicId = (url) => {
  try {
    if (!url || typeof url !== 'string') return null;
    
    // More permissive regex for extracting public ID
    const matches = url.match(/\/v\d+\/(.+)\.([a-zA-Z0-9]+)$/i);
    return matches ? matches[1] : null;
  } catch (error) {
    console.error('Error extracting public ID from URL:', error);
    return null;
  }
};

// Helper function to get optimized image URL
const getOptimizedImageUrl = (publicId, options = {}) => {
  try {
    if (!publicId) return null;
    
    const defaultOptions = {
      quality: 'auto',
      fetch_format: 'auto'
    };
    
    return cloudinary.url(publicId, {
      ...defaultOptions,
      ...options
    });
  } catch (error) {
    console.error('Error generating optimized image URL:', error);
    return null;
  }
};

module.exports = {
  cloudinary,
  uploaders,
  storageConfigs,
  deleteImage,
  extractPublicId,
  getOptimizedImageUrl,
  validateCloudinaryConfig
};