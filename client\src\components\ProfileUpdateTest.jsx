import React, { useState } from 'react';
import { useAuth } from '../hooks/useAuth';

const ProfileUpdateTest = () => {
    const { updateProfile, user } = useAuth();
    const [loading, setLoading] = useState(false);
    const [result, setResult] = useState(null);

    const testUpdate = async () => {
        setLoading(true);
        setResult(null);
        
        const testData = {
            firstName: 'TestFirst',
            lastName: 'TestLast',
            country: 'United States'
        };
        
        console.log('=== FRONTEND TEST START ===');
        console.log('Sending test data:', testData);
        
        try {
            const response = await updateProfile(testData);
            console.log('Test response:', response);
            setResult(response);
        } catch (error) {
            console.error('Test error:', error);
            setResult({ success: false, error: error.message });
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="fixed top-4 left-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-sm">
            <h3 className="font-bold text-sm mb-2">Profile Update Test</h3>
            
            <div className="text-xs space-y-2 mb-3">
                <div><strong>User:</strong> {user?.email || 'Not logged in'}</div>
                <div><strong>Type:</strong> {user?.userType || 'Unknown'}</div>
            </div>
            
            <button 
                onClick={testUpdate}
                disabled={loading || !user}
                className="w-full bg-blue-500 text-white px-3 py-2 rounded text-sm disabled:opacity-50"
            >
                {loading ? 'Testing...' : 'Test Profile Update'}
            </button>
            
            {result && (
                <div className={`mt-2 p-2 rounded text-xs ${
                    result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                    <div><strong>Success:</strong> {result.success ? 'Yes' : 'No'}</div>
                    {result.error && <div><strong>Error:</strong> {result.error}</div>}
                    {result.success && <div><strong>Message:</strong> Profile updated!</div>}
                </div>
            )}
            
            <div className="mt-2 text-xs text-gray-600">
                Check browser console and server logs for detailed debug info.
            </div>
        </div>
    );
};

export default ProfileUpdateTest;