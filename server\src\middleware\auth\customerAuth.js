const { verifyToken, requireUserType, requireEmailVerification } = require('./authMiddleware');

// Customer-specific authentication middleware
const authenticateCustomer = [
    verifyToken,
    requireUserType(['customer']),
    requireEmailVerification
];

// Customer authentication without email verification requirement
const authenticateCustomerBasic = [
    verifyToken,
    requireUserType(['customer'])
];

module.exports = {
    authenticateCustomer,
    authenticateCustomerBasic,
    // Export individual functions for flexibility
    verifyToken,
    requireUserType,
    requireEmailVerification
};