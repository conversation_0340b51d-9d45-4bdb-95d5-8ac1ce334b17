import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Tag,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Steps,
  Drawer,
  Timeline,
  Descriptions,
  Typography,
  Row,
  Col,
  Statistic,
  Alert,
  Progress,
  Tooltip,
  notification
} from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  TruckOutlined,
  PackageOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  EditOutlined,
  SendOutlined,
  TrackingOutlined
} from '@ant-design/icons';
import { ordersApi } from '../../services/vendorApi';
import dayjs from 'dayjs';

const { Option } = Select;
const { Step } = Steps;
const { Title, Text } = Typography;
const { TextArea } = Input;

const OrderFulfillment = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [fulfillmentModalVisible, setFulfillmentModalVisible] = useState(false);
  const [shippingModalVisible, setShippingModalVisible] = useState(false);
  const [trackingModalVisible, setTrackingModalVisible] = useState(false);
  const [orderDetailDrawerVisible, setOrderDetailDrawerVisible] = useState(false);
  const [analytics, setAnalytics] = useState(null);
  const [form] = Form.useForm();
  const [shippingForm] = Form.useForm();
  const [trackingForm] = Form.useForm();

  useEffect(() => {
    fetchOrders();
    fetchAnalytics();
  }, []);

  const fetchOrders = async () => {
    setLoading(true);
    try {
      const response = await ordersApi.getOrders({ 
        limit: 50,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      });
      setOrders(response.data.orders);
    } catch (error) {
      notification.error({
        message: 'Error',
        description: 'Failed to fetch orders'
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchAnalytics = async () => {
    try {
      const response = await ordersApi.getOrderAnalytics('30d');
      setAnalytics(response.data);
    } catch (error) {
      console.error('Failed to fetch analytics:', error);
    }
  };

  const handleOrderAction = async (orderId, action, items = null, shippingInfo = null, note = '') => {
    try {
      await ordersApi.processOrderFulfillment(orderId, action, items, shippingInfo, note);
      notification.success({
        message: 'Success',
        description: `Order ${action}ed successfully`
      });
      fetchOrders();
      setFulfillmentModalVisible(false);
      setShippingModalVisible(false);
    } catch (error) {
      notification.error({
        message: 'Error',
        description: error.message || `Failed to ${action} order`
      });
    }
  };

  const handleCreateShippingTracking = async (orderId, trackingData) => {
    try {
      await ordersApi.createShippingTracking(orderId, trackingData);
      notification.success({
        message: 'Success',
        description: 'Shipping tracking created successfully'
      });
      fetchOrders();
      setShippingModalVisible(false);
      shippingForm.resetFields();
    } catch (error) {
      notification.error({
        message: 'Error',
        description: error.message || 'Failed to create shipping tracking'
      });
    }
  };

  const handleUpdateTrackingStatus = async (trackingId, status, location, description, estimatedDelivery) => {
    try {
      await ordersApi.updateTrackingStatus(trackingId, status, location, description, estimatedDelivery);
      notification.success({
        message: 'Success',
        description: 'Tracking status updated successfully'
      });
      fetchOrders();
      setTrackingModalVisible(false);
      trackingForm.resetFields();
    } catch (error) {
      notification.error({
        message: 'Error',
        description: error.message || 'Failed to update tracking status'
      });
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: 'orange',
      confirmed: 'blue',
      processing: 'cyan',
      shipped: 'purple',
      delivered: 'green',
      cancelled: 'red',
      returned: 'volcano'
    };
    return colors[status] || 'default';
  };

  const getStatusIcon = (status) => {
    const icons = {
      pending: <ClockCircleOutlined />,
      confirmed: <CheckCircleOutlined />,
      processing: <PackageOutlined />,
      shipped: <TruckOutlined />,
      delivered: <CheckCircleOutlined />,
      cancelled: <ExclamationCircleOutlined />,
      returned: <ExclamationCircleOutlined />
    };
    return icons[status] || <ClockCircleOutlined />;
  };

  const getCurrentStep = (status) => {
    const statusOrder = ['pending', 'confirmed', 'processing', 'shipped', 'delivered'];
    return statusOrder.indexOf(status);
  };

  const columns = [
    {
      title: 'Order Number',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      render: (text, record) => (
        <Button 
          type="link" 
          onClick={() => {
            setSelectedOrder(record);
            setOrderDetailDrawerVisible(true);
          }}
        >
          {text}
        </Button>
      )
    },
    {
      title: 'Customer',
      dataIndex: ['customer', 'firstName'],
      key: 'customer',
      render: (text, record) => (
        <div>
          <div>{`${record.customer.firstName} ${record.customer.lastName}`}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.customer.email}
          </Text>
        </div>
      )
    },
    {
      title: 'Items',
      dataIndex: 'vendorItemsCount',
      key: 'vendorItemsCount',
      render: (count, record) => (
        <div>
          <Text strong>{count} items</Text>
          <br />
          <Text type="secondary">
            ${record.vendorTotal?.toFixed(2)}
          </Text>
        </div>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {status.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => dayjs(date).format('MMM DD, YYYY HH:mm')
    },
    {
      title: 'Progress',
      key: 'progress',
      render: (_, record) => {
        const currentStep = getCurrentStep(record.status);
        const progressPercent = currentStep >= 0 ? ((currentStep + 1) / 5) * 100 : 0;
        return (
          <Progress 
            percent={progressPercent} 
            size="small" 
            strokeColor={getStatusColor(record.status)}
          />
        );
      }
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="View Details">
            <Button 
              type="primary" 
              size="small" 
              icon={<EyeOutlined />}
              onClick={() => {
                setSelectedOrder(record);
                setOrderDetailDrawerVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="Process Order">
            <Button 
              size="small" 
              icon={<EditOutlined />}
              onClick={() => {
                setSelectedOrder(record);
                setFulfillmentModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="Create Shipping">
            <Button 
              size="small" 
              icon={<TruckOutlined />}
              onClick={() => {
                setSelectedOrder(record);
                setShippingModalVisible(true);
              }}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>Order Fulfillment</Title>
        <Text type="secondary">
          Manage and process your orders efficiently
        </Text>
      </div>

      {/* Analytics Cards */}
      {analytics && (
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="Total Orders"
                value={analytics.stats.totalOrders}
                prefix={<PackageOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="Processing"
                value={analytics.stats.processingOrders}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="Shipped"
                value={analytics.stats.shippedOrders}
                prefix={<TruckOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="Delivered"
                value={analytics.stats.deliveredOrders}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Orders Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={orders}
          loading={loading}
          rowKey="_id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} of ${total} orders`
          }}
        />
      </Card>

      {/* Order Detail Drawer */}
      <Drawer
        title={`Order ${selectedOrder?.orderNumber}`}
        placement="right"
        size="large"
        onClose={() => setOrderDetailDrawerVisible(false)}
        visible={orderDetailDrawerVisible}
      >
        {selectedOrder && (
          <div>
            <Steps current={getCurrentStep(selectedOrder.status)} style={{ marginBottom: '24px' }}>
              <Step title="Pending" icon={<ClockCircleOutlined />} />
              <Step title="Confirmed" icon={<CheckCircleOutlined />} />
              <Step title="Processing" icon={<PackageOutlined />} />
              <Step title="Shipped" icon={<TruckOutlined />} />
              <Step title="Delivered" icon={<CheckCircleOutlined />} />
            </Steps>

            <Descriptions title="Order Information" bordered>
              <Descriptions.Item label="Order Number" span={2}>
                {selectedOrder.orderNumber}
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                <Tag color={getStatusColor(selectedOrder.status)}>
                  {selectedOrder.status.toUpperCase()}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Customer" span={3}>
                {`${selectedOrder.customer.firstName} ${selectedOrder.customer.lastName}`}
                <br />
                <Text type="secondary">{selectedOrder.customer.email}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="Items Count">
                {selectedOrder.vendorItemsCount}
              </Descriptions.Item>
              <Descriptions.Item label="Total">
                ${selectedOrder.vendorTotal?.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="Created">
                {dayjs(selectedOrder.createdAt).format('MMMM DD, YYYY HH:mm')}
              </Descriptions.Item>
            </Descriptions>

            {/* Order Timeline */}
            {selectedOrder.timeline && selectedOrder.timeline.length > 0 && (
              <div style={{ marginTop: '24px' }}>
                <Title level={4}>Order Timeline</Title>
                <Timeline>
                  {selectedOrder.timeline.map((event, index) => (
                    <Timeline.Item 
                      key={index}
                      color={getStatusColor(event.status)}
                      dot={getStatusIcon(event.status)}
                    >
                      <div>
                        <Text strong>{event.status.toUpperCase()}</Text>
                        <br />
                        <Text type="secondary">
                          {dayjs(event.timestamp).format('MMM DD, YYYY HH:mm')}
                        </Text>
                        {event.note && (
                          <>
                            <br />
                            <Text>{event.note}</Text>
                          </>
                        )}
                      </div>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </div>
            )}
          </div>
        )}
      </Drawer>

      {/* Fulfillment Modal */}
      <Modal
        title="Process Order"
        visible={fulfillmentModalVisible}
        onCancel={() => setFulfillmentModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={(values) => {
            handleOrderAction(
              selectedOrder._id,
              values.action,
              values.items,
              values.shippingInfo,
              values.note
            );
          }}
        >
          <Form.Item
            name="action"
            label="Action"
            rules={[{ required: true, message: 'Please select an action' }]}
          >
            <Select placeholder="Select action">
              <Option value="confirm">Confirm Order</Option>
              <Option value="process">Start Processing</Option>
              <Option value="ship">Ship Order</Option>
              <Option value="deliver">Mark as Delivered</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="note"
            label="Note (Optional)"
          >
            <TextArea rows={3} placeholder="Add a note about this action..." />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SendOutlined />}>
                Process Order
              </Button>
              <Button onClick={() => setFulfillmentModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Shipping Modal */}
      <Modal
        title="Create Shipping Tracking"
        visible={shippingModalVisible}
        onCancel={() => setShippingModalVisible(false)}
        footer={null}
        width={700}
      >
        <Form
          form={shippingForm}
          layout="vertical"
          onFinish={(values) => {
            handleCreateShippingTracking(selectedOrder._id, values);
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['carrier', 'name']}
                label="Carrier Name"
                rules={[{ required: true, message: 'Please enter carrier name' }]}
              >
                <Input placeholder="e.g., FedEx, UPS, DHL" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['carrier', 'code']}
                label="Carrier Code"
                rules={[{ required: true, message: 'Please enter carrier code' }]}
              >
                <Input placeholder="e.g., FEDEX, UPS, DHL" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="estimatedDelivery"
            label="Estimated Delivery Date"
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Alert
            message="Shipping Address"
            description="The shipping address will be automatically taken from the order details."
            type="info"
            style={{ marginBottom: '16px' }}
          />

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<TruckOutlined />}>
                Create Tracking
              </Button>
              <Button onClick={() => setShippingModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default OrderFulfillment;
