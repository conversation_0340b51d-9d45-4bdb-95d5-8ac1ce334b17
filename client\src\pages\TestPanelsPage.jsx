import React from 'react';
import { useAuth } from '../hooks/useAuth';

const TestPanelsPage = () => {
  const { user, isAuthenticated } = useAuth();

  const testUsers = [
    {
      role: 'admin',
      email: '<EMAIL>',
      name: 'Test Admin',
      panelUrl: '/admin/dashboard'
    },
    {
      role: 'vendor',
      email: '<EMAIL>', 
      name: 'Test Vendor',
      panelUrl: '/vendor/dashboard'
    }
  ];

  return (
    <div style={{ padding: '2rem', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Admin & Vendor Panel Testing</h1>
      
      {isAuthenticated ? (
        <div style={{ marginBottom: '2rem', padding: '1rem', background: '#f0f8ff', borderRadius: '8px' }}>
          <h3>Current User:</h3>
          <p><strong>Name:</strong> {user?.name}</p>
          <p><strong>Email:</strong> {user?.email}</p>
          <p><strong>Role:</strong> {user?.role}</p>
          
          {user?.role === 'admin' && (
            <div style={{ marginTop: '1rem' }}>
              <a 
                href="/admin/dashboard" 
                style={{ 
                  background: '#007bff', 
                  color: 'white', 
                  padding: '0.5rem 1rem', 
                  textDecoration: 'none', 
                  borderRadius: '4px',
                  display: 'inline-block'
                }}
              >
                Go to Admin Panel
              </a>
            </div>
          )}
          
          {user?.role === 'vendor' && (
            <div style={{ marginTop: '1rem' }}>
              <a 
                href="/vendor/dashboard" 
                style={{ 
                  background: '#28a745', 
                  color: 'white', 
                  padding: '0.5rem 1rem', 
                  textDecoration: 'none', 
                  borderRadius: '4px',
                  display: 'inline-block'
                }}
              >
                Go to Vendor Panel
              </a>
            </div>
          )}
        </div>
      ) : (
        <div style={{ marginBottom: '2rem', padding: '1rem', background: '#fff3cd', borderRadius: '8px' }}>
          <p>You need to be logged in to access the panels.</p>
          <a href="/auth" style={{ color: '#856404' }}>Go to Login</a>
        </div>
      )}

      <div style={{ marginBottom: '2rem' }}>
        <h2>Direct Panel Access (for testing)</h2>
        <p style={{ color: '#666', marginBottom: '1rem' }}>
          You can directly access the panels using these URLs (authentication will be checked):
        </p>
        
        <div style={{ display: 'grid', gap: '1rem' }}>
          <div style={{ padding: '1rem', border: '1px solid #ddd', borderRadius: '8px' }}>
            <h3 style={{ margin: '0 0 0.5rem 0', color: '#007bff' }}>Admin Panel</h3>
            <p style={{ margin: '0 0 0.5rem 0', fontSize: '0.9rem', color: '#666' }}>
              Access admin dashboard and management tools
            </p>
            <a 
              href="/admin/dashboard" 
              style={{ 
                background: '#007bff', 
                color: 'white', 
                padding: '0.5rem 1rem', 
                textDecoration: 'none', 
                borderRadius: '4px',
                display: 'inline-block',
                fontSize: '0.9rem'
              }}
            >
              /admin/dashboard
            </a>
          </div>
          
          <div style={{ padding: '1rem', border: '1px solid #ddd', borderRadius: '8px' }}>
            <h3 style={{ margin: '0 0 0.5rem 0', color: '#28a745' }}>Vendor Panel</h3>
            <p style={{ margin: '0 0 0.5rem 0', fontSize: '0.9rem', color: '#666' }}>
              Access vendor dashboard and store management
            </p>
            <a 
              href="/vendor/dashboard" 
              style={{ 
                background: '#28a745', 
                color: 'white', 
                padding: '0.5rem 1rem', 
                textDecoration: 'none', 
                borderRadius: '4px',
                display: 'inline-block',
                fontSize: '0.9rem'
              }}
            >
              /vendor/dashboard
            </a>
          </div>
        </div>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2>Testing Instructions</h2>
        <ol style={{ lineHeight: '1.6' }}>
          <li>Make sure you have admin or vendor users in your database</li>
          <li>Login with an admin or vendor account</li>
          <li>Click the panel links above to test the interfaces</li>
          <li>Check that role-based access control is working</li>
          <li>Test the navigation and basic functionality</li>
        </ol>
      </div>

      <div style={{ padding: '1rem', background: '#f8f9fa', borderRadius: '8px' }}>
        <h3>Need Test Users?</h3>
        <p style={{ margin: '0 0 1rem 0' }}>
          If you don't have admin/vendor users, you can create them through your backend seeder or manually in the database.
        </p>
        <p style={{ margin: '0', fontSize: '0.9rem', color: '#666' }}>
          Make sure the user's role field is set to 'admin' or 'vendor' in the database.
        </p>
      </div>
    </div>
  );
};

export default TestPanelsPage;