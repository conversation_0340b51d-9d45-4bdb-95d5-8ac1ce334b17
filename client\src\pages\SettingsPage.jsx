import React, { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import { message } from 'antd';
import AuthDebugPanel from '../components/AuthDebugPanel';
import { 
    LockOutlined, 
    MailOutlined, 
    BellOutlined, 
    GlobalOutlined,
    SaveOutlined,
    EyeOutlined,
    EyeInvisibleOutlined,
    SecurityScanOutlined,
    BugOutlined
} from '@ant-design/icons';

const SettingsPage = () => {
    const { user, isAuthenticated, userType, isLoading, updateProfile, changePassword } = useAuth();
    const navigate = useNavigate();
    const [activeTab, setActiveTab] = useState('security');
    const [passwordData, setPasswordData] = useState({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
    });
    const [showPasswords, setShowPasswords] = useState({
        current: false,
        new: false,
        confirm: false
    });
    const [saving, setSaving] = useState(false);
    const [preferences, setPreferences] = useState({
        language: user?.preferences?.language || 'en',
        currency: user?.preferences?.currency || 'USD',
        timezone: user?.preferences?.timezone || 'UTC'
    });

    useEffect(() => {
        if (!isLoading && !isAuthenticated) {
            navigate('/auth');
        }
    }, [isAuthenticated, isLoading, navigate]);

    useEffect(() => {
        if (user) {
            setPreferences({
                language: user.preferences?.language || 'en',
                currency: user.preferences?.currency || 'USD',
                timezone: user.preferences?.timezone || 'UTC'
            });
        }
    }, [user]);

    const handlePasswordChange = async (e) => {
        e.preventDefault();
        
        console.log('Password change form submitted');
        console.log('Password data:', {
            currentPassword: passwordData.currentPassword ? '***' : 'empty',
            newPassword: passwordData.newPassword ? '***' : 'empty',
            confirmPassword: passwordData.confirmPassword ? '***' : 'empty'
        });
        
        if (!passwordData.currentPassword) {
            message.error('Current password is required');
            return;
        }
        
        if (!passwordData.newPassword) {
            message.error('New password is required');
            return;
        }
        
        if (passwordData.newPassword !== passwordData.confirmPassword) {
            message.error('New passwords do not match');
            return;
        }

        if (passwordData.newPassword.length < 8) {
            message.error('Password must be at least 8 characters long');
            return;
        }

        setSaving(true);
        try {
            console.log('Calling changePassword function...');
            const result = await changePassword({
                currentPassword: passwordData.currentPassword,
                newPassword: passwordData.newPassword
            });
            
            console.log('Password change result:', result);
            
            if (result.success) {
                message.success('Password changed successfully!');
                setPasswordData({
                    currentPassword: '',
                    newPassword: '',
                    confirmPassword: ''
                });
            } else {
                message.error(result.error || 'Failed to change password');
            }
        } catch (error) {
            console.error('Password change error:', error);
            message.error('Failed to change password');
        } finally {
            setSaving(false);
        }
    };

    const handlePreferencesSave = async () => {
        setSaving(true);
        try {
            const result = await updateProfile({ preferences });
            if (result.success) {
                message.success('Preferences updated successfully!');
            } else {
                message.error(result.error || 'Failed to update preferences');
            }
        } catch (error) {
            message.error('Failed to update preferences');
        } finally {
            setSaving(false);
        }
    };

    const handleNotificationsSave = async () => {
        setSaving(true);
        try {
            const result = await updateProfile({ 
                preferences: { 
                    ...preferences,
                    notifications 
                } 
            });
            if (result.success) {
                message.success('Notification preferences updated successfully!');
            } else {
                message.error(result.error || 'Failed to update notification preferences');
            }
        } catch (error) {
            message.error('Failed to update notification preferences');
        } finally {
            setSaving(false);
        }
    };

    const togglePasswordVisibility = (field) => {
        setShowPasswords(prev => ({
            ...prev,
            [field]: !prev[field]
        }));
    };

    if (isLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
            </div>
        );
    }

    if (!isAuthenticated || !user) {
        return null;
    }

    const tabs = [
        { id: 'security', name: 'Security', icon: SecurityScanOutlined },
        { id: 'notifications', name: 'Notifications', icon: BellOutlined },
        { id: 'preferences', name: 'Preferences', icon: GlobalOutlined },
    ];

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-gray-900">Account Settings</h1>
                    <p className="mt-2 text-gray-600">Manage your account preferences and security settings</p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    {/* Sidebar */}
                    <div className="lg:col-span-1">
                        <nav className="space-y-1">
                            {tabs.map((tab) => {
                                const Icon = tab.icon;
                                return (
                                    <button
                                        key={tab.id}
                                        onClick={() => setActiveTab(tab.id)}
                                        className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                                            activeTab === tab.id
                                                ? 'bg-orange-100 text-orange-700 border-r-2 border-orange-500'
                                                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                                        }`}
                                    >
                                        <Icon className="mr-3 h-5 w-5" />
                                        {tab.name}
                                    </button>
                                );
                            })}
                        </nav>
                    </div>

                    {/* Content */}
                    <div className="lg:col-span-3">
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                            {/* Security Tab */}
                            {activeTab === 'security' && (
                                <div className="p-6">
                                    <div className="mb-6">
                                        <h2 className="text-lg font-medium text-gray-900 mb-2">Security Settings</h2>
                                        <p className="text-gray-600">Manage your password and account security</p>
                                    </div>

                                    {/* Change Password */}
                                    <div className="mb-8">
                                        <h3 className="text-md font-medium text-gray-900 mb-4">Change Password</h3>
                                        <form onSubmit={handlePasswordChange} className="space-y-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Current Password
                                                </label>
                                                <div className="relative">
                                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                        <LockOutlined className="h-5 w-5 text-gray-400" />
                                                    </div>
                                                    <input
                                                        type={showPasswords.current ? "text" : "password"}
                                                        value={passwordData.currentPassword}
                                                        onChange={(e) => setPasswordData(prev => ({
                                                            ...prev,
                                                            currentPassword: e.target.value
                                                        }))}
                                                        className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                                        placeholder="Enter current password"
                                                        required
                                                    />
                                                    <button
                                                        type="button"
                                                        onClick={() => togglePasswordVisibility('current')}
                                                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                                    >
                                                        {showPasswords.current ? (
                                                            <EyeInvisibleOutlined className="h-5 w-5 text-gray-400" />
                                                        ) : (
                                                            <EyeOutlined className="h-5 w-5 text-gray-400" />
                                                        )}
                                                    </button>
                                                </div>
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    New Password
                                                </label>
                                                <div className="relative">
                                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                        <LockOutlined className="h-5 w-5 text-gray-400" />
                                                    </div>
                                                    <input
                                                        type={showPasswords.new ? "text" : "password"}
                                                        value={passwordData.newPassword}
                                                        onChange={(e) => setPasswordData(prev => ({
                                                            ...prev,
                                                            newPassword: e.target.value
                                                        }))}
                                                        className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                                        placeholder="Enter new password"
                                                        required
                                                    />
                                                    <button
                                                        type="button"
                                                        onClick={() => togglePasswordVisibility('new')}
                                                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                                    >
                                                        {showPasswords.new ? (
                                                            <EyeInvisibleOutlined className="h-5 w-5 text-gray-400" />
                                                        ) : (
                                                            <EyeOutlined className="h-5 w-5 text-gray-400" />
                                                        )}
                                                    </button>
                                                </div>
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Confirm New Password
                                                </label>
                                                <div className="relative">
                                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                        <LockOutlined className="h-5 w-5 text-gray-400" />
                                                    </div>
                                                    <input
                                                        type={showPasswords.confirm ? "text" : "password"}
                                                        value={passwordData.confirmPassword}
                                                        onChange={(e) => setPasswordData(prev => ({
                                                            ...prev,
                                                            confirmPassword: e.target.value
                                                        }))}
                                                        className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                                        placeholder="Confirm new password"
                                                        required
                                                    />
                                                    <button
                                                        type="button"
                                                        onClick={() => togglePasswordVisibility('confirm')}
                                                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                                    >
                                                        {showPasswords.confirm ? (
                                                            <EyeInvisibleOutlined className="h-5 w-5 text-gray-400" />
                                                        ) : (
                                                            <EyeOutlined className="h-5 w-5 text-gray-400" />
                                                        )}
                                                    </button>
                                                </div>
                                            </div>

                                            <button
                                                type="submit"
                                                disabled={saving}
                                                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                            >
                                                {saving ? (
                                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                                ) : (
                                                    <SaveOutlined className="mr-2" />
                                                )}
                                                Change Password
                                            </button>
                                        </form>
                                    </div>

                                    {/* Account Security Info */}
                                    <div className="border-t border-gray-200 pt-6">
                                        <h3 className="text-md font-medium text-gray-900 mb-4">Account Security</h3>
                                        <div className="space-y-3">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center">
                                                    <MailOutlined className="h-5 w-5 text-gray-400 mr-3" />
                                                    <div>
                                                        <p className="text-sm font-medium text-gray-900">Email Verification</p>
                                                        <p className="text-sm text-gray-500">{user.email}</p>
                                                    </div>
                                                </div>
                                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                    user.emailVerification?.isVerified 
                                                        ? 'bg-green-100 text-green-800' 
                                                        : 'bg-red-100 text-red-800'
                                                }`}>
                                                    {user.emailVerification?.isVerified ? 'Verified' : 'Not Verified'}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Notifications Tab */}
                            {activeTab === 'notifications' && (
                                <div className="p-6">
                                    <div className="mb-6">
                                        <h2 className="text-lg font-medium text-gray-900 mb-2">Notification Preferences</h2>
                                        <p className="text-gray-600">Choose how you want to be notified</p>
                                    </div>

                                    <div className="space-y-6">
                                        <div>
                                            <h3 className="text-md font-medium text-gray-900 mb-4">Email Notifications</h3>
                                            <div className="space-y-3">
                                                <label className="flex items-center">
                                                    <input
                                                        type="checkbox"
                                                        checked={notifications.email.orderUpdates}
                                                        onChange={(e) => setNotifications(prev => ({
                                                            ...prev,
                                                            email: { ...prev.email, orderUpdates: e.target.checked }
                                                        }))}
                                                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                                                    />
                                                    <span className="ml-3 text-sm text-gray-700">Order updates and shipping notifications</span>
                                                </label>
                                                <label className="flex items-center">
                                                    <input
                                                        type="checkbox"
                                                        checked={notifications.email.marketing}
                                                        onChange={(e) => setNotifications(prev => ({
                                                            ...prev,
                                                            email: { ...prev.email, marketing: e.target.checked }
                                                        }))}
                                                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                                                    />
                                                    <span className="ml-3 text-sm text-gray-700">Marketing and promotional emails</span>
                                                </label>
                                                <label className="flex items-center">
                                                    <input
                                                        type="checkbox"
                                                        checked={notifications.email.security}
                                                        onChange={(e) => setNotifications(prev => ({
                                                            ...prev,
                                                            email: { ...prev.email, security: e.target.checked }
                                                        }))}
                                                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                                                    />
                                                    <span className="ml-3 text-sm text-gray-700">Security alerts and account changes</span>
                                                </label>
                                            </div>
                                        </div>

                                        <div>
                                            <h3 className="text-md font-medium text-gray-900 mb-4">Push Notifications</h3>
                                            <div className="space-y-3">
                                                <label className="flex items-center">
                                                    <input
                                                        type="checkbox"
                                                        checked={notifications.push.orderUpdates}
                                                        onChange={(e) => setNotifications(prev => ({
                                                            ...prev,
                                                            push: { ...prev.push, orderUpdates: e.target.checked }
                                                        }))}
                                                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                                                    />
                                                    <span className="ml-3 text-sm text-gray-700">Order status updates</span>
                                                </label>
                                                <label className="flex items-center">
                                                    <input
                                                        type="checkbox"
                                                        checked={notifications.push.marketing}
                                                        onChange={(e) => setNotifications(prev => ({
                                                            ...prev,
                                                            push: { ...prev.push, marketing: e.target.checked }
                                                        }))}
                                                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                                                    />
                                                    <span className="ml-3 text-sm text-gray-700">Special offers and deals</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="mt-6 pt-6 border-t border-gray-200">
                                        <button 
                                            onClick={handleNotificationsSave}
                                            disabled={saving}
                                            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                        >
                                            {saving ? (
                                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                            ) : (
                                                <SaveOutlined className="mr-2" />
                                            )}
                                            Save Preferences
                                        </button>
                                    </div>
                                </div>
                            )}

                            {/* Preferences Tab */}
                            {activeTab === 'preferences' && (
                                <div className="p-6">
                                    <div className="mb-6">
                                        <h2 className="text-lg font-medium text-gray-900 mb-2">General Preferences</h2>
                                        <p className="text-gray-600">Customize your experience</p>
                                    </div>

                                    <div className="space-y-6">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Language
                                            </label>
                                            <select
                                                value={preferences.language}
                                                onChange={(e) => setPreferences(prev => ({ ...prev, language: e.target.value }))}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                                            >
                                                <option value="en">English</option>
                                                <option value="es">Spanish</option>
                                                <option value="fr">French</option>
                                                <option value="de">German</option>
                                            </select>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Currency
                                            </label>
                                            <select
                                                value={preferences.currency}
                                                onChange={(e) => setPreferences(prev => ({ ...prev, currency: e.target.value }))}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                                            >
                                                <option value="USD">USD - US Dollar</option>
                                                <option value="EUR">EUR - Euro</option>
                                                <option value="GBP">GBP - British Pound</option>
                                                <option value="CAD">CAD - Canadian Dollar</option>
                                            </select>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Timezone
                                            </label>
                                            <select
                                                value={preferences.timezone}
                                                onChange={(e) => setPreferences(prev => ({ ...prev, timezone: e.target.value }))}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                                            >
                                                <option value="UTC">UTC</option>
                                                <option value="America/New_York">Eastern Time</option>
                                                <option value="America/Chicago">Central Time</option>
                                                <option value="America/Denver">Mountain Time</option>
                                                <option value="America/Los_Angeles">Pacific Time</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div className="mt-6 pt-6 border-t border-gray-200">
                                        <button 
                                            onClick={handlePreferencesSave}
                                            disabled={saving}
                                            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                        >
                                            {saving ? (
                                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                            ) : (
                                                <SaveOutlined className="mr-2" />
                                            )}
                                            Save Preferences
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SettingsPage;