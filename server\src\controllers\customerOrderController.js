const asyncHandler = require('express-async-handler');
const Order = require('../models/Order');
const OrderTracking = require('../models/OrderTracking');
const Cart = require('../models/Cart');
const Product = require('../models/Product');
const { validationResult } = require('express-validator');
const shiprocketService = require('../services/shiprocketService');

// @desc    Create new order
// @route   POST /api/customer/orders
// @access  Private (Customer)
const createOrder = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    console.log('\n=== ORDER VALIDATION ERRORS ===');
    console.log('Request Body:', JSON.stringify(req.body, null, 2));
    console.log('Validation Errors:', JSON.stringify(errors.array(), null, 2));
    console.log('==============================\n');
    
    return res.status(400).json({
      success: false,
      message: 'Validation errors',
      errors: errors.array(),
      details: errors.array().map(err => `${err.path || err.param}: ${err.msg}`).join(', '),
      requestedData: {
        itemsCount: req.body.items?.length || 0,
        hasShipping: !!req.body.shipping,
        hasBilling: !!req.body.billing,
        hasPricing: !!req.body.pricing,
        paymentMethod: req.body.payment?.method,
        billingPhone: req.body.billing?.phone,
        shippingFirstName: req.body.shipping?.firstName,
        shippingLastName: req.body.shipping?.lastName
      }
    });
  }

  const {
    items,
    billing,
    shipping,
    payment,
    pricing,
    customerNotes,
    estimatedDelivery
  } = req.body;

  try {
    // Auto-update user's address if it has changed during checkout
    const User = require('../models/User');
    const currentUser = await User.findById(req.user._id);
    
    if (currentUser && shipping && shipping.address) {
      const needsAddressUpdate = (
        !currentUser.address || 
        !currentUser.city || 
        !currentUser.state || 
        !currentUser.zipCode ||
        currentUser.address !== shipping.address.street ||
        currentUser.city !== shipping.address.city ||
        currentUser.state !== shipping.address.state ||
        currentUser.zipCode !== shipping.address.zipCode ||
        currentUser.country !== shipping.address.country
      );
      
      if (needsAddressUpdate) {
        await User.findByIdAndUpdate(req.user._id, {
          $set: {
            address: shipping.address.street,
            city: shipping.address.city,
            state: shipping.address.state,
            zipCode: shipping.address.zipCode,
            country: shipping.address.country
          }
        });
        console.log('User address updated during order placement');
      }
    }

    // Generate order number manually
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const timestamp = Date.now().toString().slice(-6);
    const orderNumber = `ORD${year}${month}${day}${timestamp}`;

    console.log('Generated order number:', orderNumber);

    // Create the order
    const order = new Order({
      orderNumber: orderNumber,
      customer: req.user._id,
      items,
      billing,
      shipping,
      payment,
      pricing,
      customerNotes,
      status: 'confirmed',
      timeline: [{
        status: 'confirmed',
        timestamp: new Date(),
        note: 'Order has been confirmed',
        updatedBy: req.user._id
      }]
    });

    await order.save();

    console.log('Order created successfully:', order.orderNumber);

    // Create tracking for each vendor's items
    const vendorGroups = {};
    order.items.forEach(item => {
      const vendorId = item.vendor.toString();
      if (!vendorGroups[vendorId]) {
        vendorGroups[vendorId] = [];
      }
      vendorGroups[vendorId].push(item);
    });

    // Create tracking for each vendor
    const trackingPromises = Object.keys(vendorGroups).map(async (vendorId) => {
      const trackingData = {
        orderId: order._id,
        vendorId,
        deliveryAddress: {
          street: shipping.address.street,
          city: shipping.address.city,
          state: shipping.address.state,
          zipCode: shipping.address.zipCode,
          country: shipping.address.country,
          instructions: shipping.instructions || ''
        },
        recipient: {
          name: `${shipping.firstName} ${shipping.lastName}`,
          phone: billing.phone,
          email: billing.email
        },
        estimatedDelivery: estimatedDelivery || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
      };

      return await OrderTracking.createTracking(trackingData);
    });

    const trackings = await Promise.all(trackingPromises);

    // Clear customer's cart after successful order
    try {
      await Cart.findOneAndDelete({ customer: req.user._id });
    } catch (cartError) {
      console.error('Error clearing cart:', cartError);
      // Don't fail the order if cart clearing fails
    }

    // Integrate with Shiprocket (simple approach)
    try {
      const shiprocketResponse = await shiprocketService.createOrder({
        orderNumber: order.orderNumber,
        billing: order.billing,
        shipping: order.shipping,
        items: order.items,
        payment: order.payment,
        pricing: order.pricing
      });

      console.log('Shiprocket order created:', shiprocketResponse);
    } catch (shiprocketError) {
      console.error('Shiprocket integration error:', shiprocketError.message);
      // Don't fail the order creation if Shiprocket fails
    }

    // Populate the order for response
    const populatedOrder = await Order.findById(order._id)
      .populate('customer', 'firstName lastName email')
      .populate('items.product', 'name images price')
      .populate('items.vendor', 'businessName');

    return res.status(201).json({
      success: true,
      message: 'Order created successfully',
      data: {
        order: populatedOrder,
        trackings: trackings.map(tracking => ({
          _id: tracking._id,
          trackingNumber: tracking.trackingNumber,
          vendor: tracking.vendor
        }))
      }
    });

  } catch (error) {
    console.error('Error creating order:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create order',
      error: error.message
    });
  }
});

// @desc    Get customer's orders
// @route   GET /api/customer/orders
// @access  Private (Customer)
const getCustomerOrders = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const status = req.query.status;
  const sortBy = req.query.sortBy || 'createdAt';
  const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

  let query = { customer: req.user._id };

  if (status) {
    query.status = status;
  }

  try {
    const orders = await Order.find(query)
      .populate('items.product', 'name images price')
      .populate('items.vendor', 'businessName')
      .sort({ [sortBy]: sortOrder })
      .limit(limit)
      .skip((page - 1) * limit);

    const total = await Order.countDocuments(query);

    // Get tracking information for each order
    const ordersWithTracking = await Promise.all(
      orders.map(async (order) => {
        const tracking = await OrderTracking.find({ order: order._id })
          .populate('vendor', 'businessName')
          .select('trackingNumber currentStatus vendor progressPercentage');
        
        return {
          ...order.toObject(),
          tracking
        };
      })
    );

    return res.status(200).json({
      success: true,
      data: {
        orders: ordersWithTracking,
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching customer orders:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch orders',
      error: error.message
    });
  }
});

// @desc    Get order by ID
// @route   GET /api/customer/orders/:orderId
// @access  Private (Customer)
const getOrderById = asyncHandler(async (req, res) => {
  const { orderId } = req.params;

  try {
    const order = await Order.findOne({ 
      _id: orderId, 
      customer: req.user._id 
    })
    .populate('customer', 'firstName lastName email')
    .populate('items.product', 'name images price description')
    .populate('items.vendor', 'businessName contactInfo')
    .populate('timeline.updatedBy', 'firstName lastName');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Get tracking information
    const tracking = await OrderTracking.find({ order: orderId })
      .populate('vendor', 'businessName contactInfo');

    return res.status(200).json({
      success: true,
      data: {
        order,
        tracking
      }
    });

  } catch (error) {
    console.error('Error fetching order:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch order',
      error: error.message
    });
  }
});

// @desc    Cancel order
// @route   PUT /api/customer/orders/:orderId/cancel
// @access  Private (Customer)
const cancelOrder = asyncHandler(async (req, res) => {
  const { orderId } = req.params;
  const { reason } = req.body;

  try {
    const order = await Order.findOne({ 
      _id: orderId, 
      customer: req.user._id 
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Check if order can be cancelled
    const cancellableStatuses = ['pending', 'confirmed', 'processing'];
    if (!cancellableStatuses.includes(order.status)) {
      return res.status(400).json({
        success: false,
        message: 'Order cannot be cancelled at this stage'
      });
    }

    // Update order status
    await order.updateStatus('cancelled', reason || 'Cancelled by customer', req.user._id);

    // Update tracking status
    await OrderTracking.updateMany(
      { order: orderId },
      { 
        $set: { currentStatus: 'cancelled' },
        $push: {
          trackingSteps: {
            status: 'cancelled',
            title: 'Order Cancelled',
            description: reason || 'Order has been cancelled by customer',
            timestamp: new Date(),
            updatedBy: {
              type: 'customer',
              user: req.user._id
            }
          }
        }
      }
    );

    return res.status(200).json({
      success: true,
      message: 'Order cancelled successfully'
    });

  } catch (error) {
    console.error('Error cancelling order:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to cancel order',
      error: error.message
    });
  }
});

// @desc    Return order
// @route   PUT /api/customer/orders/:orderId/return
// @access  Private (Customer)
const returnOrder = asyncHandler(async (req, res) => {
  const { orderId } = req.params;
  const { reason, items } = req.body;

  try {
    const order = await Order.findOne({ 
      _id: orderId, 
      customer: req.user._id 
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Check if order can be returned
    if (order.status !== 'delivered') {
      return res.status(400).json({
        success: false,
        message: 'Only delivered orders can be returned'
      });
    }

    // Check return window (30 days)
    const deliveryDate = order.timeline.find(t => t.status === 'delivered')?.timestamp;
    if (deliveryDate) {
      const returnWindow = 30 * 24 * 60 * 60 * 1000; // 30 days
      if (Date.now() - new Date(deliveryDate).getTime() > returnWindow) {
        return res.status(400).json({
          success: false,
          message: 'Return window has expired (30 days from delivery)'
        });
      }
    }

    // Update order status
    await order.updateStatus('returned', reason || 'Returned by customer', req.user._id);

    // Update tracking status
    await OrderTracking.updateMany(
      { order: orderId },
      { 
        $set: { currentStatus: 'returned' },
        $push: {
          trackingSteps: {
            status: 'returned',
            title: 'Return Initiated',
            description: reason || 'Return has been initiated by customer',
            timestamp: new Date(),
            updatedBy: {
              type: 'customer',
              user: req.user._id
            }
          }
        }
      }
    );

    return res.status(200).json({
      success: true,
      message: 'Return initiated successfully'
    });

  } catch (error) {
    console.error('Error initiating return:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to initiate return',
      error: error.message
    });
  }
});

// @desc    Get order statistics
// @route   GET /api/customer/orders/stats
// @access  Private (Customer)
const getOrderStats = asyncHandler(async (req, res) => {
  try {
    const stats = await Order.aggregate([
      { $match: { customer: req.user._id } },
      {
        $group: {
          _id: null,
          totalOrders: { $sum: 1 },
          totalSpent: { $sum: '$pricing.total' },
          pendingOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          },
          confirmedOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'confirmed'] }, 1, 0] }
          },
          processingOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'processing'] }, 1, 0] }
          },
          shippedOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'shipped'] }, 1, 0] }
          },
          deliveredOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'delivered'] }, 1, 0] }
          },
          cancelledOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0] }
          },
          returnedOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'returned'] }, 1, 0] }
          },
          averageOrderValue: { $avg: '$pricing.total' }
        }
      }
    ]);

    const result = stats[0] || {
      totalOrders: 0,
      totalSpent: 0,
      pendingOrders: 0,
      confirmedOrders: 0,
      processingOrders: 0,
      shippedOrders: 0,
      deliveredOrders: 0,
      cancelledOrders: 0,
      returnedOrders: 0,
      averageOrderValue: 0
    };

    return res.status(200).json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Error fetching order stats:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch order statistics',
      error: error.message
    });
  }
});

module.exports = {
  createOrder,
  getCustomerOrders,
  getOrderById,
  cancelOrder,
  returnOrder,
  getOrderStats
};