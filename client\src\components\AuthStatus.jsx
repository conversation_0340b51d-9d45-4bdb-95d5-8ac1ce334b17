import React from 'react';
import { useAuth } from '../hooks/useAuth';

const AuthStatus = () => {
    const { user, token, isAuthenticated, userType } = useAuth();
    
    const localStorageToken = localStorage.getItem('authToken');
    const localStorageUser = localStorage.getItem('authUser');
    
    return (
        <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-sm">
            <h3 className="font-bold text-sm mb-2">Auth Status Debug</h3>
            <div className="text-xs space-y-1">
                <div>
                    <strong>Authenticated:</strong> {isAuthenticated ? '✅ Yes' : '❌ No'}
                </div>
                <div>
                    <strong>User Type:</strong> {userType || 'None'}
                </div>
                <div>
                    <strong>State Token:</strong> {token ? '✅ Exists' : '❌ Missing'}
                </div>
                <div>
                    <strong>localStorage Token:</strong> {localStorageToken ? '✅ Exists' : '❌ Missing'}
                </div>
                <div>
                    <strong>User Email:</strong> {user?.email || 'None'}
                </div>
                <div>
                    <strong>localStorage User:</strong> {localStorageUser ? '✅ Exists' : '❌ Missing'}
                </div>
                {token && (
                    <div>
                        <strong>Token Preview:</strong> {token.substring(0, 20)}...
                    </div>
                )}
            </div>
            <button 
                onClick={() => {
                    console.log('=== AUTH DEBUG INFO ===');
                    console.log('State:', { user, token: token ? token.substring(0, 20) + '...' : null, isAuthenticated, userType });
                    console.log('localStorage token:', localStorageToken ? localStorageToken.substring(0, 20) + '...' : null);
                    console.log('localStorage user:', localStorageUser);
                }}
                className="mt-2 text-xs bg-blue-500 text-white px-2 py-1 rounded"
            >
                Log Debug Info
            </button>
        </div>
    );
};

export default AuthStatus;