const mongoose = require('mongoose');

const reviewReplySchema = new mongoose.Schema({
  review: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Review',
    required: [true, 'Review is required']
  },
  vendor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Vendor is required']
  },
  message: {
    type: String,
    required: [true, 'Reply message is required'],
    trim: true,
    maxlength: [500, 'Reply message cannot exceed 500 characters'],
    minlength: [10, 'Reply message must be at least 10 characters']
  },
  status: {
    type: String,
    enum: ['active', 'hidden', 'flagged'],
    default: 'active'
  },
  isEdited: {
    type: Boolean,
    default: false
  },
  editedAt: {
    type: Date
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
reviewReplySchema.index({ review: 1 });
reviewReplySchema.index({ vendor: 1, createdAt: -1 });
reviewReplySchema.index({ status: 1 });

// Virtual for formatted date
reviewReplySchema.virtual('formattedDate').get(function() {
  return this.createdAt.toDateString();
});

// Pre-save middleware to set editedAt when message is modified
reviewReplySchema.pre('save', function(next) {
  if (this.isModified('message') && !this.isNew) {
    this.isEdited = true;
    this.editedAt = new Date();
  }
  next();
});

// Static method to get replies for a review
reviewReplySchema.statics.getReviewReplies = function(reviewId) {
  return this.find({ review: reviewId, status: 'active' })
    .populate('vendor', 'businessName avatar')
    .sort({ createdAt: 1 });
};

// Static method to get vendor's recent replies
reviewReplySchema.statics.getVendorReplies = function(vendorId, options = {}) {
  const { page = 1, limit = 10, status = 'active' } = options;
  const skip = (page - 1) * limit;

  return this.find({ vendor: vendorId, status })
    .populate('review', 'rating comment customer')
    .populate({
      path: 'review',
      populate: {
        path: 'customer',
        select: 'firstName lastName'
      }
    })
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

module.exports = mongoose.model('ReviewReply', reviewReplySchema);
