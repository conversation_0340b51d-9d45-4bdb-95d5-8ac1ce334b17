const express = require('express');
const router = express.Router();

// Import route modules
const authRoutes = require('./authRoutes');
const adminRoutes = require('./admin');
const vendorRoutes = require('./vendor');
const customerRoutes = require('./customer');
const publicRoutes = require('./public');
const paymentRoutes = require('./paymentRoutes');

// Register routes
router.use('/auth', authRoutes);
router.use('/admin', adminRoutes);
router.use('/vendor', vendorRoutes);
router.use('/customer', customerRoutes);
router.use('/payments', paymentRoutes);

// Mount public routes at both locations for compatibility
router.use('/public', publicRoutes);
router.use('/', publicRoutes);

module.exports = router;