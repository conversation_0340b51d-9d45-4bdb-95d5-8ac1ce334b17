const User = require('../../models/User') || require('../../../schema/userSchema');

/**
 * @desc    Get admin profile
 * @route   GET /api/admin/profile
 * @access  Private (Admin)
 */
const getProfile = async (req, res) => {
  try {
    const admin = await User.findById(req.user.userId || req.user._id).select('-password -passwordReset -emailVerification.verificationToken');
    
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: 'Admin profile not found'
      });
    }

    res.json({
      success: true,
      message: 'Admin profile retrieved successfully',
      data: {
        user: {
          id: admin._id,
          firstName: admin.firstName,
          lastName: admin.lastName,
          email: admin.email,
          phone: admin.phone,
          countryCode: admin.countryCode,
          address: admin.address,
          city: admin.city,
          state: admin.state,
          zipCode: admin.zipCode,
          country: admin.country,
          userType: admin.userType,
          role: admin.role,
          isActive: admin.isActive,
          emailVerified: admin.emailVerification?.isVerified || false,
          createdAt: admin.createdAt,
          lastActiveAt: admin.lastActiveAt,
          twoFactorEnabled: admin.twoFactorAuth?.isEnabled || false
        }
      }
    });
  } catch (error) {
    console.error('Get admin profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while retrieving profile'
    });
  }
};

/**
 * @desc    Update admin profile basic information
 * @route   PUT /api/admin/profile
 * @access  Private (Admin)
 */
const updateProfile = async (req, res) => {
  try {
    const { 
      firstName, 
      lastName, 
      phone, 
      countryCode, 
      address, 
      city, 
      state, 
      zipCode, 
      country 
    } = req.body;

    const userId = req.user.userId || req.user._id;
    const admin = await User.findById(userId);
    
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: 'Admin profile not found'
      });
    }

    // Update only the provided fields
    const updates = {};
    if (firstName !== undefined) updates.firstName = firstName;
    if (lastName !== undefined) updates.lastName = lastName;
    if (phone !== undefined) updates.phone = phone;
    if (countryCode !== undefined) updates.countryCode = countryCode;
    if (address !== undefined) updates.address = address;
    if (city !== undefined) updates.city = city;
    if (state !== undefined) updates.state = state;
    if (zipCode !== undefined) updates.zipCode = zipCode;
    if (country !== undefined) updates.country = country;

    // Update the admin
    const updatedAdmin = await User.findByIdAndUpdate(
      userId,
      { $set: updates },
      { new: true, runValidators: true }
    ).select('-password -passwordReset -emailVerification.verificationToken');

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: {
        user: {
          id: updatedAdmin._id,
          firstName: updatedAdmin.firstName,
          lastName: updatedAdmin.lastName,
          email: updatedAdmin.email,
          phone: updatedAdmin.phone,
          countryCode: updatedAdmin.countryCode,
          address: updatedAdmin.address,
          city: updatedAdmin.city,
          state: updatedAdmin.state,
          zipCode: updatedAdmin.zipCode,
          country: updatedAdmin.country,
          userType: updatedAdmin.userType,
          role: updatedAdmin.role,
          isActive: updatedAdmin.isActive,
          emailVerified: updatedAdmin.emailVerification?.isVerified || false,
          updatedAt: updatedAdmin.updatedAt
        }
      }
    });
  } catch (error) {
    console.error('Update admin profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating profile'
    });
  }
};

/**
 * @desc    Change admin password
 * @route   POST /api/admin/profile/change-password
 * @access  Private (Admin)
 */
const changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    // Get admin with password field
    const userId = req.user.userId || req.user._id;
    const admin = await User.findById(userId).select('+password');
    
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: 'Admin account not found'
      });
    }

    // Verify current password
    const isPasswordValid = await admin.comparePassword(currentPassword);
    if (!isPasswordValid) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }

    // Check if new password is the same as current
    const isSamePassword = await admin.comparePassword(newPassword);
    if (isSamePassword) {
      return res.status(400).json({
        success: false,
        message: 'New password cannot be the same as current password'
      });
    }

    // Update password (will be hashed by pre-save middleware)
    admin.password = newPassword;
    admin.security.passwordChangedAt = new Date();
    
    // Reset login attempts if any
    admin.security.loginAttempts = 0;
    admin.security.lockUntil = undefined;

    await admin.save();

    // Send password change notification email
    await sendPasswordChangeNotification(admin);

    res.json({
      success: true,
      message: 'Password changed successfully',
      data: {
        changedAt: admin.security.passwordChangedAt
      }
    });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while changing password'
    });
  }
};

/**
 * @desc    Update admin profile picture
 * @route   PUT /api/admin/profile/avatar
 * @access  Private (Admin)
 */
const updateProfilePicture = async (req, res) => {
  try {
    const cloudinary = require('../../utils/cloudinaryInit');
    
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No image file provided'
      });
    }

    const userId = req.user.userId || req.user._id;
    const admin = await User.findById(userId);
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: 'Admin profile not found'
      });
    }

    // Delete old profile picture from Cloudinary if exists
    if (admin.profilePicture && admin.profilePicture.public_id) {
      try {
        await cloudinary.uploader.destroy(admin.profilePicture.public_id);
      } catch (error) {
        console.warn('Failed to delete old profile picture:', error);
      }
    }

    // Upload new profile picture to Cloudinary
    const result = await new Promise((resolve, reject) => {
      cloudinary.uploader.upload_stream(
        {
          folder: 'profile-pictures/admin',
          transformation: [{ width: 300, height: 300, crop: 'fill' }],
          format: 'jpg'
        },
        (error, result) => {
          if (error) reject(error);
          else resolve(result);
        }
      ).end(req.file.buffer);
    });

    // Update admin's profile picture
    const updatedAdmin = await User.findByIdAndUpdate(
      userId,
      {
        $set: {
          profilePicture: {
            public_id: result.public_id,
            url: result.secure_url
          }
        }
      },
      { new: true, runValidators: true }
    ).select('-password -passwordReset -emailVerification.verificationToken');

    res.json({
      success: true,
      message: 'Profile picture updated successfully',
      data: {
        profilePicture: updatedAdmin.profilePicture
      }
    });
  } catch (error) {
    console.error('Update profile picture error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating profile picture'
    });
  }
};

/**
 * @desc    Delete admin profile picture
 * @route   DELETE /api/admin/profile/avatar
 * @access  Private (Admin)
 */
const deleteProfilePicture = async (req, res) => {
  try {
    const cloudinary = require('../../utils/cloudinaryInit');
    
    const userId = req.user.userId || req.user._id;
    const admin = await User.findById(userId);
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: 'Admin profile not found'
      });
    }

    if (!admin.profilePicture || !admin.profilePicture.public_id) {
      return res.status(400).json({
        success: false,
        message: 'No profile picture to delete'
      });
    }

    // Delete profile picture from Cloudinary
    try {
      await cloudinary.uploader.destroy(admin.profilePicture.public_id);
    } catch (error) {
      console.warn('Failed to delete profile picture from Cloudinary:', error);
    }

    // Remove profile picture from database
    const updatedAdmin = await User.findByIdAndUpdate(
      userId,
      {
        $unset: { profilePicture: 1 }
      },
      { new: true, runValidators: true }
    ).select('-password -passwordReset -emailVerification.verificationToken');

    res.json({
      success: true,
      message: 'Profile picture deleted successfully'
    });
  } catch (error) {
    console.error('Delete profile picture error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting profile picture'
    });
  }
};

/**
 * Send password change notification email
 */
async function sendPasswordChangeNotification(admin) {
  const emailService = require('../../utils/emailService');
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Changed</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
            .info-box { background: #dbeafe; border: 1px solid #3b82f6; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔐 Password Successfully Changed</h1>
            </div>
            <div class="content">
                <h2>Hello ${admin.firstName || 'Admin'}!</h2>
                <p>Your admin account password has been successfully changed.</p>
                
                <div class="info-box">
                    <strong>🛡️ Security Details:</strong><br>
                    <strong>Account:</strong> ${admin.email}<br>
                    <strong>Changed At:</strong> ${new Date().toLocaleString()}<br>
                    <strong>IP Address:</strong> ${admin.security?.lastLoginIP || 'Not available'}
                </div>
                
                <p><strong>What this means:</strong></p>
                <ul>
                    <li>Your new password is now active</li>
                    <li>Any saved sessions on other devices may be logged out</li>
                    <li>Use your new password for future logins</li>
                </ul>
                
                <p><strong>🚨 Security Notice:</strong> If you didn't make this change, please contact the system administrator immediately and secure your account.</p>
                
                <p>Best regards,<br>The ${process.env.APP_NAME || 'Alicartify'} Team</p>
            </div>
            <div class="footer">
                <p>© ${new Date().getFullYear()} ${process.env.APP_NAME || 'Alicartify'}. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
  `;

  return await emailService.sendEmail(
    admin.email,
    'Your Admin Password Has Been Changed',
    html
  );
}

module.exports = {
  getProfile,
  updateProfile,
  changePassword,
  updateProfilePicture,
  deleteProfilePicture
};
