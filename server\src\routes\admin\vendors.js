const express = require('express');
const router = express.Router();
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');
const {
  getVendors,
  getVendorById,
  updateVendor,
  updateVendorStatus,
  updateVerificationStatus,
  updateCommission,
  getVendorStatistics,
  deleteVendor,
  bulkUpdateVendors,
  getPendingVerification,
  verifyVendor,
  rejectVendorVerification,
  processVendorPayout,
  getVendorCommissionReport,
  approveVendor,
  suspendVendor
} = require('../../controllers/admin/vendorsController');

// Apply authentication middleware to all routes
router.use(verifyToken);
router.use(requireUserType(['admin']));

// Vendor management routes
router.get('/', getVendors);
router.get('/statistics', getVendorStatistics);
router.get('/pending-verification', getPendingVerification);
router.get('/commission-report', getVendorCommissionReport);
router.get('/:id', getVendorById);
router.put('/:id', updateVendor);
router.patch('/:id/status', updateVendorStatus);
router.patch('/:id/verification', updateVerificationStatus);
router.patch('/:id/verify', verifyVendor);
router.patch('/:id/reject-verification', rejectVendorVerification);
router.patch('/:id/approve', approveVendor);
router.patch('/:id/suspend', suspendVendor);
router.patch('/:id/commission', updateCommission);
router.post('/:id/payout', processVendorPayout);
router.patch('/bulk-update', bulkUpdateVendors);
router.delete('/:id', deleteVendor);

module.exports = router;