.reviews-card {
  margin: 20px 0;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
}

.reviews-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.sort-select .ant-select-selector {
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
}

.sort-select:hover .ant-select-selector {
  border-color: #40a9ff;
}

.empty-reviews {
  margin: 20px 0;
  text-align: center;
  color: #999;
}

.rating-info {
  font-size: 14px;
  color: #555;
}

.rating-summary {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 10px;
}

.average-rating {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rating-value {
  font-size: 36px;
  color: #faad14;
}

.rating-stars {
  font-size: 22px;
}

.rating-distribution {
  margin: 20px 0;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border-left: 4px solid #faad14;
}

.rating-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.rating-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #333;
}

.star-count {
  font-size: 16px;
}

.star-icon {
  color: #faad14;
}

.rating-progress {
  width: 60%;
}

.rating-count {
  font-size: 13px;
  color: #777;
}

.review-item {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.review-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.reviewer-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.reviewer-avatar {
  width: 40px;
  height: 40px;
  background: #f5f5f5;
  border-radius: 50%;
}

.reviewer-name {
  font-weight: 500;
  font-size: 15px;
  color: #333;
}

.review-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 13px;
  color: #888;
}

.review-date {
  display: flex;
  align-items: center;
  gap: 4px;
}

.review-date .anticon {
  font-size: 14px;
}

.review-rating {
  font-size: 14px;
}

.verified-badge {
  background-color: #52c41a;
  color: #fff;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.review-text {
  font-size: 14px;
  color: #666;
}

.review-actions {
  margin-top: 8px;
}

.reply-toggle-btn {
  padding: 0;
  font-size: 13px;
  color: #1890ff;
  display: flex;
  align-items: center;
}

.replies-section {
  margin-top: 12px;
  padding: 12px;
  background: #f7f7f7;
  border-radius: 6px;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.reviews-pagination {
  display: inline-block;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .review-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .reviewer-info {
    justify-content: flex-start;
  }

  .rating-summary {
    flex-direction: column;
  }

  .overall-rating {
    text-align: center;
  }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
  .reviews-card {
    border-color: transparent;
    background: #1f1f1f;
  }

  .review-item {
    border-bottom: 1px solid #333;
  }

  .rating-distribution {
    background: #2a2a2a;
  }

  .rating-row {
    color: #ccc;
  }

  .rating-label {
    color: #ddd;
  }

  .review-text {
    color: #bbb;
  }

  .review-meta {
    color: #aaa;
  }

  .reviewer-name {
    color: #ddd;
  }

  .reply-content {
    color: #bbb;
  }

  .rating-info, .empty-reviews, .reply-toggle-btn, .reply-date {
    color: #aaa;
  }

  .overall-rating {
    border-color: #faad14;
  }
}
