/* Phone Input Component Styles */
.phone-input-container {
  width: 100%;
}

.phone-input .ant-input-group-addon {
  padding: 0;
  border: none;
  background: transparent;
}

.phone-input .ant-select-selector {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
  padding: 0 8px;
}

.phone-input .ant-select-selection-item {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.phone-input .ant-input {
  border-left: 1px solid #d1d5db;
}

.phone-input.border-red-500 .ant-input {
  border-color: #ef4444;
}

.phone-input .ant-input:focus {
  border-color: #f97316;
  box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.2);
}

/* Country dropdown styles */
.ant-select-dropdown .ant-select-item-option-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ant-select-dropdown .ant-select-item-option-content span:first-child {
  font-size: 16px;
}

/* Form item styles */
.ant-form-item-label > label {
  font-weight: 500;
  color: #374151;
}

.ant-form-item-explain-error {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .phone-input .ant-select {
    min-width: 80px;
  }
  
  .phone-input .ant-select-selection-item {
    font-size: 12px;
  }
}