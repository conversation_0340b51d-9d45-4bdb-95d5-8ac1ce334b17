# Razorpay Standard Checkout Implementation Guide

## Issue Fixed
❌ **Before**: Frontend redirected to `/checkout` showing custom checkout UI
✅ **After**: Frontend calls Razorpay API and opens Razorpay's standard checkout

## Backend Changes Made

### 1. Added New Payment Endpoint
```
POST /api/payments/razorpay/pay-from-cart
```
This endpoint:
- Gets items from user's cart automatically
- Creates Razorpay order
- Returns order details for Razorpay checkout
- Handles billing/shipping details

### 2. Modified `/checkout` Route
- Now returns instructions instead of serving custom checkout page
- Prevents custom checkout UI from showing
- Guides developers to use proper Razorpay flow

## Frontend Implementation Required

### Step 1: Modify "Proceed from Cart" Button
Instead of navigating to `/checkout`, call the payment API:

```javascript
const proceedToPayment = async () => {
  try {
    // Call backend to create Razorpay order from cart
    const response = await fetch('/api/payments/razorpay/pay-from-cart', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bear<PERSON> ${authToken}` // Your auth token
      },
      body: JSON.stringify({
        // Optional: custom billing/shipping info
        // If not provided, uses user's profile data
        billing: { /* optional custom billing */ },
        shipping: { /* optional custom shipping */ }
      })
    });

    const result = await response.json();

    if (result.success) {
      // Open Razorpay checkout with returned data
      openRazorpayCheckout(result.data);
    } else {
      alert(result.message);
    }
  } catch (error) {
    console.error('Payment error:', error);
    alert('Failed to initiate payment');
  }
};
```

### Step 2: Open Razorpay Standard Checkout
```javascript
const openRazorpayCheckout = (orderData) => {
  const options = {
    key: orderData.key, // Razorpay key from backend
    amount: orderData.amount, // Amount in paise
    currency: 'INR',
    name: 'Your Store Name',
    description: 'Order Payment',
    order_id: orderData.razorpay_order_id,
    handler: function(response) {
      // Payment successful - verify it
      verifyPayment(response, orderData.order_id);
    },
    prefill: {
      email: '<EMAIL>', // Customer email
      contact: '9999999999' // Customer phone
    },
    theme: {
      color: '#3399cc' // Your brand color
    },
    modal: {
      ondismiss: function() {
        console.log('Payment cancelled');
      }
    }
  };

  const rzp = new Razorpay(options);
  rzp.open();
};
```

### Step 3: Verify Payment After Success
```javascript
const verifyPayment = async (paymentResponse, orderId) => {
  try {
    const response = await fetch('/api/payments/razorpay/verify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        razorpay_payment_id: paymentResponse.razorpay_payment_id,
        razorpay_order_id: paymentResponse.razorpay_order_id,
        razorpay_signature: paymentResponse.razorpay_signature,
        order_id: orderId
      })
    });

    const result = await response.json();

    if (result.success) {
      // Payment verified - redirect to success page
      window.location.href = `/order-success?orderId=${result.data.orderId}`;
    } else {
      alert('Payment verification failed');
    }
  } catch (error) {
    console.error('Verification error:', error);
    alert('Payment verification failed');
  }
};
```

## Required Frontend Dependencies

Add Razorpay script to your HTML:
```html
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
```

Or install via npm:
```bash
npm install razorpay
```

## Complete Flow

1. **User clicks "Proceed from Cart"**
2. **Frontend calls** `POST /api/payments/razorpay/pay-from-cart`
3. **Backend creates** Razorpay order from cart items
4. **Frontend opens** Razorpay's checkout window
5. **User completes** payment on Razorpay
6. **Frontend verifies** payment via `POST /api/payments/razorpay/verify`
7. **User redirected** to order success page

## Testing

To test the fix:

1. **Start your server**: `npm run dev`
2. **Try accessing** `http://localhost:5000/checkout` - should return JSON with instructions
3. **Frontend should call** `/api/payments/razorpay/pay-from-cart` instead
4. **Razorpay checkout** should open instead of custom checkout UI

## Environment Variables Required

Make sure these are set in your `.env` file:
```env
RAZORPAY_KEY_ID=rzp_test_your_key_here
RAZORPAY_KEY_SECRET=your_secret_here
RAZORPAY_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

## Benefits of This Fix

✅ **Uses Razorpay's standard checkout** (secure, tested, trusted)  
✅ **No custom checkout UI maintenance** required  
✅ **Automatic cart-to-payment** flow  
✅ **Proper payment verification**  
✅ **Mobile-responsive** Razorpay interface  
✅ **Multiple payment methods** supported by Razorpay  

The issue is now fixed on the backend. Update your frontend code to use the new endpoint instead of navigating to `/checkout`.
