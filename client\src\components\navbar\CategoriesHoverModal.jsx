import React, { useState, useEffect } from 'react';
import {
    ShopOutlined,
    MobileOutlined,
    CrownOutlined,
    HeartOutlined,
    CarOutlined,
    HomeOutlined,
    SkinOutlined,
    ShoppingOutlined,
    PrinterOutlined,
    BugOutlined,
    SmileOutlined,
    MedicineBoxOutlined,
    GiftOutlined,
    TableOutlined,
    BulbOutlined,
    SettingOutlined,
    SecurityScanOutlined,
    AppstoreOutlined,
    BookOutlined,
    CameraOutlined,
    ClockCircleOutlined,
    CoffeeOutlined,
    CompassOutlined,
    DatabaseOutlined,
    DesktopOutlined,
    DollarOutlined,
    EnvironmentOutlined,
    ExperimentOutlined,
    EyeOutlined,
    FileOutlined,
    FireOutlined,
    FlagOutlined,
    FolderOutlined,
    FundOutlined,
    GlobalOutlined,
    GoldOutlined,
    GroupOutlined,
    HddOutlined,
    <PERSON>Outlined,
    IdcardOutlined,
    InfoCircleOutlined,
    KeyOutlined,
    LaptopOutlined,
    LayoutOutlined,
    LikeOutlined,
    LockOutlined,
    MailOutlined,
    MoneyCollectOutlined,
    NotificationOutlined,
    PaperClipOutlined,
    PhoneOutlined,
    PictureOutlined,
    PlayCircleOutlined,
    Plus<PERSON>ircleOutlined,
    ProfileOutlined,
    <PERSON>Outlined,
    <PERSON><PERSON><PERSON>Outlined,
    <PERSON><PERSON>ircleOutlined,
    ReadOutlined,
    <PERSON>Outlined,
    <PERSON>Outlined,
    SaveOutlined,
    ScanOutlined,
    ScheduleOutlined,
    SearchOutlined,
    ShakeOutlined,
    ShareAltOutlined,
    SoundOutlined,
    StarOutlined,
    SyncOutlined,
    TagOutlined,
    TeamOutlined,
    ThunderboltOutlined,
    ToolOutlined,
    TrophyOutlined,
    UsbOutlined,
    UserOutlined,
    VideoCameraOutlined,
    WalletOutlined,
    WifiOutlined,
    ZoomInOutlined
} from '@ant-design/icons';
import { homepageApi } from '../../services/publicApi';

// Icon mapping for dynamic icons
const iconMap = {
    ShopOutlined,
    MobileOutlined,
    CrownOutlined,
    HeartOutlined,
    CarOutlined,
    HomeOutlined,
    SkinOutlined,
    ShoppingOutlined,
    PrinterOutlined,
    BugOutlined,
    SmileOutlined,
    MedicineBoxOutlined,
    GiftOutlined,
    TableOutlined,
    BulbOutlined,
    SettingOutlined,
    SecurityScanOutlined,
    AppstoreOutlined,
    BookOutlined,
    CameraOutlined,
    ClockCircleOutlined,
    CoffeeOutlined,
    CompassOutlined,
    DatabaseOutlined,
    DesktopOutlined,
    DollarOutlined,
    EnvironmentOutlined,
    ExperimentOutlined,
    EyeOutlined,
    FileOutlined,
    FireOutlined,
    FlagOutlined,
    FolderOutlined,
    FundOutlined,
    GlobalOutlined,
    GoldOutlined,
    GroupOutlined,
    HddOutlined,
    HistoryOutlined,
    IdcardOutlined,
    InfoCircleOutlined,
    KeyOutlined,
    LaptopOutlined,
    LayoutOutlined,
    LikeOutlined,
    LockOutlined,
    MailOutlined,
    MoneyCollectOutlined,
    NotificationOutlined,
    PaperClipOutlined,
    PhoneOutlined,
    PictureOutlined,
    PlayCircleOutlined,
    PlusCircleOutlined,
    ProfileOutlined,
    ProjectOutlined,
    PushpinOutlined,
    QuestionCircleOutlined,
    ReadOutlined,
    RocketOutlined,
    SafetyOutlined,
    SaveOutlined,
    ScanOutlined,
    ScheduleOutlined,
    SearchOutlined,
    ShakeOutlined,
    ShareAltOutlined,
    SoundOutlined,
    StarOutlined,
    SyncOutlined,
    TagOutlined,
    TeamOutlined,
    ThunderboltOutlined,
    ToolOutlined,
    TrophyOutlined,
    UsbOutlined,
    UserOutlined,
    VideoCameraOutlined,
    WalletOutlined,
    WifiOutlined,
    ZoomInOutlined
};

const CategoriesHoverModal = ({ showModal, onMouseEnter, onMouseLeave }) => {
    const [categoriesData, setCategoriesData] = useState({
        mainCategories: [],
        popularCategories: []
    });
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (showModal) {
            fetchCategoriesData();
        }
    }, [showModal]);

    const fetchCategoriesData = async () => {
        if (categoriesData.mainCategories.length > 0) return; // Don't fetch if already loaded

        setLoading(true);
        try {
            console.log('🔍 Fetching categories from API...');
            const response = await homepageApi.getAllCategoriesModal();
            console.log('📊 Full API Response:', response);
            
            if (response && response.success && response.data) {
                console.log('✅ Categories data received:', response.data);
                console.log('📋 Main categories count:', response.data.mainCategories?.length || 0);
                console.log('📋 Popular categories count:', response.data.popularCategories?.length || 0);
                
                if (response.data.mainCategories && response.data.mainCategories.length > 0) {
                    console.log('📋 First main category:', response.data.mainCategories[0]);
                    console.log('📋 All main categories:', response.data.mainCategories);
                }
                
                setCategoriesData(response.data);
            } else {
                console.log('❌ Invalid response format, using fallback');
                throw new Error('Invalid response format');
            }
        } catch (error) {
            console.error('Failed to fetch categories data:', error);
            console.log('🔄 Using fallback categories');
            // Fallback to default categories if API fails
            setCategoriesData({
                mainCategories: [
                    { _id: '1', name: 'Apparel & Accessories', icon: 'ShopOutlined', linkUrl: '' },
                    { _id: '2', name: 'Consumer Electronics', icon: 'MobileOutlined', linkUrl: '' },
                    { _id: '3', name: 'Sports & Entertainment', icon: 'CrownOutlined', linkUrl: '' },
                    { _id: '4', name: 'Jewelry, Eyewear & Watches', icon: 'HeartOutlined', linkUrl: '' },
                    { _id: '5', name: 'Shoes & Accessories', icon: 'CarOutlined', linkUrl: '' },
                    { _id: '6', name: 'Home & Garden', icon: 'HomeOutlined', linkUrl: '' },
                    { _id: '7', name: 'Beauty', icon: 'SkinOutlined', linkUrl: '' },
                    { _id: '8', name: 'Luggage, Bags & Cases', icon: 'ShoppingOutlined', linkUrl: '' },
                    { _id: '9', name: 'Packaging & Printing', icon: 'PrinterOutlined', linkUrl: '' },
                    { _id: '10', name: 'Parents, Kids & Toys', icon: 'BugOutlined', linkUrl: '' },
                    { _id: '11', name: 'Personal Care & Home Care', icon: 'SmileOutlined', linkUrl: '' },
                    { _id: '12', name: 'Health & Medical', icon: 'MedicineBoxOutlined', linkUrl: '' },
                    { _id: '13', name: 'Gifts & Crafts', icon: 'GiftOutlined', linkUrl: '' },
                    { _id: '14', name: 'Furniture', icon: 'TableOutlined', linkUrl: '' },
                    { _id: '15', name: 'Lights & Lighting', icon: 'BulbOutlined', linkUrl: '' },
                    { _id: '16', name: 'Home Appliances', icon: 'SettingOutlined', linkUrl: '' },
                    { _id: '17', name: 'Safety & Security', icon: 'SafetyOutlined', linkUrl: '' },
                    { _id: '18', name: 'View All', icon: 'AppstoreOutlined', linkUrl: '' }
                ],
                popularCategories: [
                    { _id: '1', name: 'Consumer Electronics', linkUrl: '' },
                    { _id: '2', name: 'Apparel & Accessories', linkUrl: '' },
                    { _id: '3', name: 'Home & Garden', linkUrl: '' },
                    { _id: '4', name: 'Sports & Entertainment', linkUrl: '' },
                    { _id: '5', name: 'Beauty', linkUrl: '' },
                    { _id: '6', name: 'Jewelry, Eyewear & Watches', linkUrl: '' },
                    { _id: '7', name: 'Shoes & Accessories', linkUrl: '' },
                    { _id: '8', name: 'Health & Medical', linkUrl: '' },
                    { _id: '9', name: 'Furniture', linkUrl: '' },
                    { _id: '10', name: 'Home Appliances', linkUrl: '' }
                ]
            });
        } finally {
            setLoading(false);
        }
    };

    const handleCategoryClick = (category) => {
        console.log('Category clicked:', category.name);
        if (category.linkUrl) {
            window.location.href = category.linkUrl;
        }
        // Add navigation logic here
    };

    if (!showModal) return null;

    return (
        <div
            className="absolute left-0 top-full mt-1 bg-white border border-gray-200 rounded-xl shadow-xl z-50 navbar-hover-modal"
            style={{ width: '900px', maxHeight: '450px' }}
            onMouseEnter={onMouseEnter}
            onMouseLeave={onMouseLeave}
        >
            <div className="p-5">
                {/* Header - More compact like Alibaba */}
                <div className="mb-4 pb-3 border-b border-gray-100">
                    <h3 className="text-base font-medium text-gray-900">All Categories</h3>
                </div>

                {loading ? (
                    <div className="flex items-center justify-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
                        <span className="ml-2 text-gray-600">Loading categories...</span>
                    </div>
                ) : (
                    <>
                        {/* Main Categories Grid - 6 columns */}
                        <div className="grid grid-cols-6 gap-3 mb-5">
                            {categoriesData.mainCategories.map((category, index) => {
                                console.log('🎨 Rendering category:', category.name, 'with icon:', category.icon);
                                const IconComponent = iconMap[category.icon] || AppstoreOutlined;
                                return (
                                    <div
                                        key={category._id || index}
                                        onClick={() => handleCategoryClick(category)}
                                        className="flex flex-col items-center p-2 rounded-md cursor-pointer transition-all duration-200 group border border-gray-200 hover:border-orange-400"
                                    >
                                        <div className="w-10 h-10 flex items-center justify-center mb-2 text-gray-500 group-hover:text-orange-500 transition-colors">
                                            <IconComponent style={{ fontSize: '24px' }} />
                                        </div>
                                        <span className="text-xs text-center text-gray-700 group-hover:text-gray-900 font-normal leading-tight line-clamp-2">
                                            {category.name}
                                        </span>
                                    </div>
                                );
                            })}
                        </div>

                        {/* Popular Categories Section - 5 per row, 10 total */}
                        <div className="pt-3 border-t border-gray-100">
                            <h4 className="text-sm font-medium text-gray-900 mb-3">Popular Categories</h4>
                            <div className="grid grid-cols-5 gap-2">
                                {categoriesData.popularCategories.map((category, index) => (
                                    <div
                                        key={category._id || index}
                                        onClick={() => handleCategoryClick(category)}
                                        className="px-3 py-1.5 bg-orange-500 text-white hover:bg-orange-600 text-xs rounded-full cursor-pointer transition-colors duration-200 text-center"
                                    >
                                        {category.name}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </>
                )}
            </div>
        </div>
    );
};

export default CategoriesHoverModal;