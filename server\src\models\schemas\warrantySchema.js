const mongoose = require('mongoose');

const warrantySchema = new mongoose.Schema({
  hasWarranty: {
    type: Boolean,
    default: false
  },
  warrantyPeriod: {
    type: Number,
    min: 0,
    max: 120, // months
    required: function() { return this.hasWarranty; }
  },
  warrantyType: {
    type: String,
    enum: ['manufacturer', 'seller', 'extended'],
    required: function() { return this.hasWarranty; }
  },
  warrantyTerms: {
    type: String,
    trim: true,
    maxlength: [1000, 'Warranty terms cannot exceed 1000 characters'],
    required: function() { return this.hasWarranty; }
  },
  warrantyContact: {
    email: String,
    phone: String
  }
}, { _id: false });

module.exports = { warrantySchema };
