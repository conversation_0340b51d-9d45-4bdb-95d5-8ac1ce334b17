const User = require('../models/User');

/**
 * Middleware to add user's preferred currency to the request
 * This allows controllers to know which currency to display prices in
 */
const addUserCurrency = async (req, res, next) => {
  try {
    // Default currency
    req.userCurrency = 'INR';
    
    // If user is authenticated, get their preferred currency
    if (req.user && req.user.id) {
      const user = await User.findById(req.user.id).select('preferences.currency');
      if (user && user.preferences && user.preferences.currency) {
        req.userCurrency = user.preferences.currency;
      }
    }
    
    // Also check for currency in query params or headers (for API flexibility)
    if (req.query.currency) {
      req.userCurrency = req.query.currency.toUpperCase();
    } else if (req.headers['x-currency']) {
      req.userCurrency = req.headers['x-currency'].toUpperCase();
    }
    
    next();
  } catch (error) {
    // If error occurs, just use default currency and continue
    req.userCurrency = 'INR';
    next();
  }
};

module.exports = { addUserCurrency };
