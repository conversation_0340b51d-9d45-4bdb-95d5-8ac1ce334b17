const mongoose = require('mongoose');

const categorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Category name is required'],
    trim: true,
    maxlength: [100, 'Category name cannot exceed 100 characters']
  },
  slug: {
    type: String,
    unique: true,
    lowercase: true,
    trim: true
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  parent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    default: null
  },
  level: {
    type: Number,
    default: 0,
    min: 0,
    max: 5
  },
  path: {
    type: String,
    default: ''
  },
  image: {
    type: String,
    default: null
  },
  icon: {
    type: String,
    default: null
  },
  banner: {
    type: String,
    default: null
  },
  status: {
    type: String,
    enum: ['active', 'inactive'],
    default: 'active'
  },
  featured: {
    type: Boolean,
    default: false
  },
  sortOrder: {
    type: Number,
    default: 0
  },
  seo: {
    metaTitle: {
      type: String,
      trim: true,
      maxlength: [60, 'Meta title cannot exceed 60 characters']
    },
    metaDescription: {
      type: String,
      trim: true,
      maxlength: [160, 'Meta description cannot exceed 160 characters']
    },
    keywords: [{
      type: String,
      trim: true
    }]
  },
  attributes: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    type: {
      type: String,
      enum: ['text', 'number', 'select', 'multiselect', 'boolean', 'date'],
      default: 'text'
    },
    required: {
      type: Boolean,
      default: false
    },
    options: [{
      value: String,
      label: String
    }],
    unit: String,
    validation: {
      min: Number,
      max: Number,
      pattern: String
    }
  }],
  commission: {
    rate: {
      type: Number,
      min: 0,
      max: 100,
      default: 10
    },
    type: {
      type: String,
      enum: ['percentage', 'fixed'],
      default: 'percentage'
    }
  },
  statistics: {
    productCount: {
      type: Number,
      default: 0
    },
    vendorCount: {
      type: Number,
      default: 0
    },
    totalSales: {
      type: Number,
      default: 0
    },
    totalRevenue: {
      type: Number,
      default: 0
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes (slug index is automatically created by unique: true)
categorySchema.index({ parent: 1 });
categorySchema.index({ level: 1 });
categorySchema.index({ status: 1 });
categorySchema.index({ featured: 1 });
categorySchema.index({ sortOrder: 1 });
categorySchema.index({ path: 1 });

// Virtual for children categories
categorySchema.virtual('children', {
  ref: 'Category',
  localField: '_id',
  foreignField: 'parent'
});

// Virtual for products count
categorySchema.virtual('productsCount', {
  ref: 'Product',
  localField: '_id',
  foreignField: 'category',
  count: true
});

// Virtual for full path name
categorySchema.virtual('fullPath').get(function() {
  return this.path ? this.path.split(',').join(' > ') : this.name;
});

// Virtual for breadcrumb
categorySchema.virtual('breadcrumb').get(function() {
  if (!this.path) return [{ name: this.name, slug: this.slug }];
  
  const pathIds = this.path.split(',');
  // This would need to be populated with actual category data
  return pathIds.map(id => ({ id }));
});

// Pre-save middleware to generate slug and path
categorySchema.pre('save', async function(next) {
  // Generate slug if name is modified
  if (this.isModified('name') && !this.slug) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
    
    // Ensure slug uniqueness
    let counter = 1;
    let originalSlug = this.slug;
    while (await this.constructor.findOne({ slug: this.slug, _id: { $ne: this._id } })) {
      this.slug = `${originalSlug}-${counter}`;
      counter++;
    }
  }
  
  // Calculate level and path
  if (this.parent) {
    const parentCategory = await this.constructor.findById(this.parent);
    if (parentCategory) {
      this.level = parentCategory.level + 1;
      this.path = parentCategory.path ? `${parentCategory.path},${this.parent}` : this.parent.toString();
    }
  } else {
    this.level = 0;
    this.path = '';
  }
  
  next();
});

// Post-save middleware to update children paths
categorySchema.post('save', async function(doc) {
  if (doc.isModified('path') || doc.isModified('parent')) {
    await updateChildrenPaths(doc);
  }
});

// Helper function to update children paths recursively
async function updateChildrenPaths(category) {
  const children = await category.constructor.find({ parent: category._id });
  
  for (const child of children) {
    const newPath = category.path ? `${category.path},${category._id}` : category._id.toString();
    child.path = newPath;
    child.level = category.level + 1;
    await child.save();
  }
}

// Static method to get category tree
categorySchema.statics.getTree = async function() {
  const categories = await this.find({ status: 'active' })
    .sort({ sortOrder: 1, name: 1 })
    .lean();
  
  const categoryMap = {};
  const tree = [];
  
  // Create a map for quick lookup
  categories.forEach(cat => {
    categoryMap[cat._id] = { ...cat, children: [] };
  });
  
  // Build the tree
  categories.forEach(cat => {
    if (cat.parent) {
      if (categoryMap[cat.parent]) {
        categoryMap[cat.parent].children.push(categoryMap[cat._id]);
      }
    } else {
      tree.push(categoryMap[cat._id]);
    }
  });
  
  return tree;
};

// Static method to get category statistics
categorySchema.statics.getStatistics = async function() {
  const stats = await this.aggregate([
    {
      $group: {
        _id: null,
        totalCategories: { $sum: 1 },
        activeCategories: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        inactiveCategories: {
          $sum: { $cond: [{ $eq: ['$status', 'inactive'] }, 1, 0] }
        },
        featuredCategories: {
          $sum: { $cond: ['$featured', 1, 0] }
        },
        rootCategories: {
          $sum: { $cond: [{ $eq: ['$level', 0] }, 1, 0] }
        },
        totalProducts: { $sum: '$statistics.productCount' },
        totalRevenue: { $sum: '$statistics.totalRevenue' }
      }
    }
  ]);
  
  return stats[0] || {
    totalCategories: 0,
    activeCategories: 0,
    inactiveCategories: 0,
    featuredCategories: 0,
    rootCategories: 0,
    totalProducts: 0,
    totalRevenue: 0
  };
};

// Static method to get popular categories
categorySchema.statics.getPopular = function(limit = 10) {
  return this.find({ status: 'active' })
    .sort({ 'statistics.productCount': -1, 'statistics.totalSales': -1 })
    .limit(limit)
    .select('name slug image statistics');
};

// Static method to get category hierarchy
categorySchema.statics.getHierarchy = async function(categoryId) {
  const category = await this.findById(categoryId);
  if (!category) return [];
  
  const hierarchy = [category];
  
  if (category.path) {
    const pathIds = category.path.split(',');
    const parents = await this.find({ _id: { $in: pathIds } })
      .sort({ level: 1 });
    hierarchy.unshift(...parents);
  }
  
  return hierarchy;
};

// Instance method to get all descendants
categorySchema.methods.getDescendants = async function() {
  const descendants = await this.constructor.find({
    path: new RegExp(`(^|,)${this._id}(,|$)`)
  });
  
  return descendants;
};

// Instance method to update statistics
categorySchema.methods.updateStatistics = async function() {
  const Product = mongoose.model('Product');
  const Vendor = mongoose.model('Vendor');
  
  // Get all descendant categories
  const descendants = await this.getDescendants();
  const categoryIds = [this._id, ...descendants.map(d => d._id)];
  
  // Count products
  const productStats = await Product.aggregate([
    { $match: { category: { $in: categoryIds }, status: 'active' } },
    {
      $group: {
        _id: null,
        productCount: { $sum: 1 },
        totalSales: { $sum: '$sales.totalSold' },
        totalRevenue: { $sum: '$sales.totalRevenue' }
      }
    }
  ]);
  
  // Count unique vendors
  const vendorCount = await Product.distinct('vendor', {
    category: { $in: categoryIds },
    status: 'active'
  });
  
  const stats = productStats[0] || { productCount: 0, totalSales: 0, totalRevenue: 0 };
  
  this.statistics = {
    productCount: stats.productCount,
    vendorCount: vendorCount.length,
    totalSales: stats.totalSales,
    totalRevenue: stats.totalRevenue
  };
  
  return this.save();
};

// Instance method to move category
categorySchema.methods.moveTo = async function(newParentId) {
  const oldPath = this.path;
  const oldLevel = this.level;
  
  this.parent = newParentId || null;
  await this.save(); // This will trigger pre-save to update path and level
  
  // Update all descendants
  const descendants = await this.constructor.find({
    path: new RegExp(`(^|,)${this._id}(,|$)`)
  });
  
  for (const descendant of descendants) {
    const relativePath = descendant.path.replace(oldPath, this.path);
    const levelDiff = this.level - oldLevel;
    
    descendant.path = relativePath;
    descendant.level += levelDiff;
    await descendant.save();
  }
  
  return this;
};

module.exports = mongoose.model('Category', categorySchema);