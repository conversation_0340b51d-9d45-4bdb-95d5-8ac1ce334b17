require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./src/models/User');
const Vendor = require('./src/models/Vendor');

async function findVendorUsers() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('📊 Connected to database');
    
    console.log('\n=== Finding Vendor Users ===');
    
    // Find users with vendor type
    const vendorUsers = await User.find({ userType: 'vendor' })
      .select('email firstName lastName userType status isEmailVerified')
      .limit(10);
    
    console.log(`Found ${vendorUsers.length} vendor users:`);
    
    if (vendorUsers.length === 0) {
      console.log('No vendor users found. Let\'s create a test vendor user...');
      
      // Create a test vendor user
      const testVendor = new User({
        firstName: 'Test',
        lastName: 'Vendor', 
        email: '<EMAIL>',
        password: 'password123', // This will be hashed by the model
        userType: 'vendor',
        status: 'active',
        isEmailVerified: true
      });
      
      await testVendor.save();
      console.log('✅ Created test vendor user:');
      console.log('Email: <EMAIL>');
      console.log('Password: password123');
      
    } else {
      vendorUsers.forEach((user, index) => {
        console.log(`${index + 1}. Email: ${user.email}`);
        console.log(`   Name: ${user.firstName} ${user.lastName}`);
        console.log(`   Status: ${user.status}`);
        console.log(`   Email Verified: ${user.isEmailVerified}`);
        console.log('');
      });
      
      // Check if these users have vendor records
      console.log('\n=== Checking Vendor Records ===');
      
      for (const user of vendorUsers) {
        const vendorRecord = await Vendor.findOne({ user: user._id });
        if (vendorRecord) {
          console.log(`✅ ${user.email} has vendor record - Status: ${vendorRecord.status}, Verification: ${vendorRecord.verification.status}`);
        } else {
          console.log(`❌ ${user.email} has NO vendor record`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n📊 Disconnected from database');
  }
}

findVendorUsers();

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./src/models/User');
const Vendor = require('./src/models/Vendor');

async function findVendorUsers() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('📊 Connected to database');
    
    console.log('\n=== Finding Vendor Users ===');
    
    // Find users with vendor type
    const vendorUsers = await User.find({ userType: 'vendor' })
      .select('email firstName lastName userType status isEmailVerified')
      .limit(10);
    
    console.log(`Found ${vendorUsers.length} vendor users:`);
    
    if (vendorUsers.length === 0) {
      console.log('No vendor users found. Let\'s create a test vendor user...');
      
      // Create a test vendor user
      const testVendor = new User({
        firstName: 'Test',
        lastName: 'Vendor',
        email: '<EMAIL>',
        password: 'password123', // This will be hashed by the model
        userType: 'vendor',
        status: 'active',
        isEmailVerified: true
      });
      
      await testVendor.save();
      console.log('✅ Created test vendor user:');
      console.log('Email: <EMAIL>');
      console.log('Password: password123');
      
      // Also create a basic vendor record
      const vendorRecord = new Vendor({
        user: testVendor._id,
        businessName: 'Test Vendor Store',
        businessDescription: 'A test vendor store',
        businessType: 'individual',
        status: 'active',
        verification: {
          status: 'verified'
        },
        businessAddress: {
          street: 'Test Street',
          city: 'Test City',
          state: 'Test State',
          zipCode: '12345',
          country: 'Test Country'
        },
        contactInfo: {
          businessPhone: '**********',
          businessEmail: '<EMAIL>'
        },
        bankDetails: {
          accountHolderName: 'Test Vendor',
          bankName: 'Test Bank',
          accountNumber: '*********',
          routingNumber: '*********',
          accountType: 'checking'
        }
      });
      
      await vendorRecord.save();
      console.log('✅ Created vendor record for test user');
      
    } else {
      vendorUsers.forEach((user, index) => {
        console.log(`${index + 1}. Email: ${user.email}`);
        console.log(`   Name: ${user.firstName} ${user.lastName}`);
        console.log(`   Status: ${user.status}`);
        console.log(`   Email Verified: ${user.isEmailVerified}`);
        console.log('');
      });
      
      // Check if these users have vendor records
      console.log('\n=== Checking Vendor Records ===');
      
      for (const user of vendorUsers) {
        const vendorRecord = await Vendor.findOne({ user: user._id });
        if (vendorRecord) {
          console.log(`✅ ${user.email} has vendor record - Status: ${vendorRecord.status}, Verification: ${vendorRecord.verification.status}`);
        } else {
          console.log(`❌ ${user.email} has NO vendor record`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n📊 Disconnected from database');
  }
}

findVendorUsers();
