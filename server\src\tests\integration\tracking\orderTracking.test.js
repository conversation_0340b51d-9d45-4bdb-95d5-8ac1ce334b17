const mongoose = require('mongoose');
const request = require('supertest');
const app = require('../../../app');
const Order = require('../../../models/Order');
const OrderTracking = require('../../../models/OrderTracking');
const User = require('../../../models/User');
const Vendor = require('../../../models/Vendor');
const Product = require('../../../models/Product');
const Cart = require('../../../models/Cart');

// Test data
let testUser, testVendor, testProduct, testOrder, userToken, vendorToken;

const mockUser = {
  _id: new mongoose.Types.ObjectId(),
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  password: '$2a$10$test.hash.password',
  role: 'customer',
  isEmailVerified: true
};

const mockVendor = {
  _id: new mongoose.Types.ObjectId(),
  firstName: 'Jane',
  lastName: 'Smith',
  email: '<EMAIL>',
  password: '$2a$10$test.hash.password',
  role: 'vendor',
  isEmailVerified: true,
  businessName: 'Test Vendor Store',
  businessDescription: 'A test vendor store'
};

const mockCategory = {
  _id: new mongoose.Types.ObjectId(),
  name: 'Electronics',
  slug: 'electronics',
  status: 'active'
};

const mockProduct = {
  _id: new mongoose.Types.ObjectId(),
  name: 'Test Product',
  description: 'A test product for tracking',
  sku: 'TEST001',
  vendor: null,
  category: null,
  status: 'active',
  pricing: {
    regular: 29.99,
    sale: null,
    cost: 20.00
  },
  inventory: {
    stock: 100,
    reserved: 0,
    sold: 0
  },
  images: []
};

// Helper function to generate JWT token
const generateToken = (user) => {
  const jwt = require('jsonwebtoken');
  return jwt.sign(
    { 
      userId: user._id, 
      email: user.email, 
      role: user.role 
    },
    process.env.JWT_SECRET || 'test-secret',
    { expiresIn: '1h' }
  );
};

// Helper function for authenticated requests
const authenticatedRequest = (token) => {
  return request(app).set('Authorization', `Bearer ${token}`);
};

// Database cleanup
const cleanupDatabase = async () => {
  const collections = ['users', 'vendors', 'products', 'orders', 'ordertrackings', 'carts'];
  for (const collection of collections) {
    try {
      await mongoose.connection.db.collection(collection).deleteMany({});
    } catch (error) {
      // Collection might not exist yet
    }
  }
};

describe('Order Tracking System Tests', () => {
  beforeAll(async () => {
    // Connect to test database
    const mongoURI = process.env.MONGODB_TEST_URI || 'mongodb://127.0.0.1/alicartify-test';
    
    if (mongoose.connection.readyState === 0) {
      await mongoose.connect(mongoURI);
    }

    // Clean up database
    await cleanupDatabase();

    // Set vendor reference in product
    mockProduct.vendor = mockVendor._id;

    // Create test data
    testUser = await new User(mockUser).save();
    testVendor = await new User(mockVendor).save();
    testProduct = await new Product(mockProduct).save();

    // Generate tokens
    userToken = generateToken(mockUser);
    vendorToken = generateToken(mockVendor);
  });

  afterAll(async () => {
    await cleanupDatabase();
    if (mongoose.connection.readyState !== 0) {
      await mongoose.connection.close();
    }
  });

  beforeEach(async () => {
    // Clean tracking data before each test
    await OrderTracking.deleteMany({});
    await Order.deleteMany({});
  });

  describe('Order Creation with Tracking', () => {
    test('should create tracking when order is placed', async () => {
      // First add item to cart
      await authenticatedRequest(userToken)
        .post('/api/customer/cart/add')
        .send({
          productId: testProduct._id,
          quantity: 2
        });

      // Place order
      const orderData = {
        items: [{
          product: testProduct._id,
          quantity: 2,
          price: testProduct.pricing.regular,
          vendor: testVendor._id
        }],
        billing: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '1234567890',
          address: {
            street: '123 Test St',
            city: 'Test City',
            state: 'Test State',
            zipCode: '12345',
            country: 'Test Country'
          }
        },
        shipping: {
          firstName: 'John',
          lastName: 'Doe',
          address: {
            street: '123 Test St',
            city: 'Test City',
            state: 'Test State',
            zipCode: '12345',
            country: 'Test Country'
          }
        },
        paymentMethod: 'credit_card',
        totalAmount: testProduct.price * 2
      };

      // Create order directly in database (simulating successful order placement)
      const order = new Order({
        ...orderData,
        customer: testUser._id,
        orderNumber: `ORD${Date.now()}`,
        status: 'pending'
      });
      await order.save();

      // Create tracking using the createTracking method
      const trackingData = {
        orderId: order._id,
        vendorId: testVendor._id,
        deliveryAddress: orderData.shipping.address,
        recipient: {
          name: `${orderData.shipping.firstName} ${orderData.shipping.lastName}`,
          phone: orderData.billing.phone,
          email: orderData.billing.email
        }
      };

      const tracking = await OrderTracking.createTracking(trackingData);

      expect(tracking).toBeDefined();
      expect(tracking.trackingNumber).toMatch(/^TRK\d+[A-Z]\d+$/);
      expect(tracking.order.toString()).toBe(order._id.toString());
      expect(tracking.vendor.toString()).toBe(testVendor._id.toString());
      expect(tracking.currentStatus).toBe('order_confirmed');
      expect(tracking.progressPercentage).toBe(0);
    });
  });

  describe('Tracking API Endpoints', () => {
    let testTracking;

    beforeEach(async () => {
      // Create a test order and tracking
      const order = new Order({
        customer: testUser._id,
        orderNumber: `ORD${Date.now()}`,
        items: [{
          product: testProduct._id,
          quantity: 1,
          price: testProduct.price,
          vendor: testVendor._id
        }],
        totalAmount: testProduct.price,
        status: 'pending',
        billing: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '1234567890'
        },
        shipping: {
          firstName: 'John',
          lastName: 'Doe',
          address: {
            street: '123 Test St',
            city: 'Test City',
            state: 'Test State',
            zipCode: '12345',
            country: 'Test Country'
          }
        }
      });
      await order.save();

      testTracking = await OrderTracking.createTracking({
        orderId: order._id,
        vendorId: testVendor._id,
        deliveryAddress: order.shipping.address,
        recipient: {
          name: `${order.shipping.firstName} ${order.shipping.lastName}`,
          phone: order.billing.phone,
          email: order.billing.email
        }
      });

      testOrder = order;
    });

    test('should get tracking by tracking number (public)', async () => {
      const response = await request(app)
        .get(`/api/order-tracking/${testTracking.trackingNumber}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.trackingNumber).toBe(testTracking.trackingNumber);
      expect(response.body.data.currentStatus).toBe('order_confirmed');
    });

    test('should get customer tracking list', async () => {
      const response = await authenticatedRequest(userToken)
        .get('/api/order-tracking/customer/my-trackings');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0].trackingNumber).toBe(testTracking.trackingNumber);
    });

    test('should get tracking by order ID', async () => {
      const response = await authenticatedRequest(userToken)
        .get(`/api/order-tracking/order/${testOrder._id}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data[0].trackingNumber).toBe(testTracking.trackingNumber);
    });

    test('should return 404 for non-existent tracking number', async () => {
      const response = await request(app)
        .get('/api/order-tracking/INVALID123');

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });

    test('should require authentication for customer tracking list', async () => {
      const response = await request(app)
        .get('/api/order-tracking/customer/my-trackings');

      expect(response.status).toBe(401);
    });
  });

  describe('Order Status Integration', () => {
    let testTracking, testOrder;

    beforeEach(async () => {
      // Create test order and tracking
      const order = new Order({
        customer: testUser._id,
        orderNumber: `ORD${Date.now()}`,
        items: [{
          product: testProduct._id,
          quantity: 1,
          price: testProduct.price,
          vendor: testVendor._id
        }],
        totalAmount: testProduct.price,
        status: 'pending',
        billing: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '1234567890'
        },
        shipping: {
          firstName: 'John',
          lastName: 'Doe',
          address: {
            street: '123 Test St',
            city: 'Test City',
            state: 'Test State',
            zipCode: '12345',
            country: 'Test Country'
          }
        }
      });
      await order.save();

      testTracking = await OrderTracking.createTracking({
        orderId: order._id,
        vendorId: testVendor._id,
        deliveryAddress: order.shipping.address,
        recipient: {
          name: `${order.shipping.firstName} ${order.shipping.lastName}`,
          phone: order.billing.phone,
          email: order.billing.email
        }
      });

      testOrder = order;
    });

    test('should retrieve orders with tracking information', async () => {
      const response = await authenticatedRequest(userToken)
        .get('/api/customer/orders');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      
      const orderWithTracking = response.body.data.orders.find(
        order => order._id === testOrder._id.toString()
      );
      
      expect(orderWithTracking).toBeDefined();
      expect(orderWithTracking.tracking).toBeDefined();
      
      // For single vendor orders, tracking should be an object
      if (!Array.isArray(orderWithTracking.tracking)) {
        expect(orderWithTracking.tracking.trackingNumber).toBe(testTracking.trackingNumber);
      } else {
        // For multi-vendor orders, tracking should be an array
        expect(orderWithTracking.tracking[0].trackingNumber).toBe(testTracking.trackingNumber);
      }
    });

    test('should handle order cancellation with tracking update', async () => {
      // Cancel the order
      const cancelResponse = await authenticatedRequest(userToken)
        .patch(`/api/customer/orders/${testOrder._id}/cancel`);

      expect(cancelResponse.status).toBe(200);

      // Check if tracking status was updated
      const updatedTracking = await OrderTracking.findById(testTracking._id);
      expect(updatedTracking.currentStatus).toBe('cancelled');
      expect(updatedTracking.statusHistory).toContainEqual(
        expect.objectContaining({
          status: 'cancelled',
          reason: 'Order cancelled by customer'
        })
      );
    });
  });

  describe('Multi-Vendor Tracking', () => {
    let vendor2, product2;

    beforeEach(async () => {
      // Create second vendor and product
      const mockVendor2 = {
        _id: new mongoose.Types.ObjectId(),
        firstName: 'Bob',
        lastName: 'Wilson',
        email: '<EMAIL>',
        password: '$2a$10$test.hash.password',
        role: 'vendor',
        isEmailVerified: true,
        businessName: 'Second Vendor Store',
        businessDescription: 'Another test vendor store'
      };

      vendor2 = await new User(mockVendor2).save();

      const mockProduct2 = {
        _id: new mongoose.Types.ObjectId(),
        name: 'Second Test Product',
        description: 'Another test product',
        price: 39.99,
        stock: 50,
        vendor: vendor2._id,
        category: 'Home',
        status: 'active',
        images: []
      };

      product2 = await new Product(mockProduct2).save();
    });

    test('should create separate tracking for multi-vendor order', async () => {
      // Create multi-vendor order
      const order = new Order({
        customer: testUser._id,
        orderNumber: `ORD${Date.now()}`,
        items: [
          {
            product: testProduct._id,
            quantity: 1,
            price: testProduct.price,
            vendor: testVendor._id
          },
          {
            product: product2._id,
            quantity: 2,
            price: product2.price,
            vendor: vendor2._id
          }
        ],
        totalAmount: testProduct.price + (product2.price * 2),
        status: 'pending',
        billing: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '1234567890'
        },
        shipping: {
          firstName: 'John',
          lastName: 'Doe',
          address: {
            street: '123 Test St',
            city: 'Test City',
            state: 'Test State',
            zipCode: '12345',
            country: 'Test Country'
          }
        }
      });
      await order.save();

      // Create tracking for each vendor
      const vendors = [...new Set(order.items.map(item => item.vendor.toString()))];
      
      const trackingPromises = vendors.map(vendorId => 
        OrderTracking.createTracking({
          orderId: order._id,
          vendorId: vendorId,
          deliveryAddress: order.shipping.address,
          recipient: {
            name: `${order.shipping.firstName} ${order.shipping.lastName}`,
            phone: order.billing.phone,
            email: order.billing.email
          }
        })
      );

      const trackings = await Promise.all(trackingPromises);

      expect(trackings).toHaveLength(2);
      expect(trackings[0].trackingNumber).not.toBe(trackings[1].trackingNumber);
      expect(trackings[0].vendor.toString()).not.toBe(trackings[1].vendor.toString());

      // Verify both vendors are represented
      const vendorIds = trackings.map(t => t.vendor.toString()).sort();
      const expectedVendorIds = [testVendor._id.toString(), vendor2._id.toString()].sort();
      expect(vendorIds).toEqual(expectedVendorIds);
    });

    test('should retrieve multi-vendor tracking correctly', async () => {
      // Create multi-vendor order with tracking
      const order = new Order({
        customer: testUser._id,
        orderNumber: `ORD${Date.now()}`,
        items: [
          {
            product: testProduct._id,
            quantity: 1,
            price: testProduct.price,
            vendor: testVendor._id
          },
          {
            product: product2._id,
            quantity: 1,
            price: product2.price,
            vendor: vendor2._id
          }
        ],
        totalAmount: testProduct.price + product2.price,
        status: 'pending',
        billing: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '1234567890'
        },
        shipping: {
          firstName: 'John',
          lastName: 'Doe',
          address: {
            street: '123 Test St',
            city: 'Test City',
            state: 'Test State',
            zipCode: '12345',
            country: 'Test Country'
          }
        }
      });
      await order.save();

      // Create tracking for both vendors
      await OrderTracking.createTracking({
        orderId: order._id,
        vendorId: testVendor._id,
        deliveryAddress: order.shipping.address,
        recipient: {
          name: `${order.shipping.firstName} ${order.shipping.lastName}`,
          phone: order.billing.phone,
          email: order.billing.email
        }
      });

      await OrderTracking.createTracking({
        orderId: order._id,
        vendorId: vendor2._id,
        deliveryAddress: order.shipping.address,
        recipient: {
          name: `${order.shipping.firstName} ${order.shipping.lastName}`,
          phone: order.billing.phone,
          email: order.billing.email
        }
      });

      // Get tracking by order ID
      const response = await authenticatedRequest(userToken)
        .get(`/api/order-tracking/order/${order._id}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data).toHaveLength(2);

      // Verify both tracking records have different vendors
      const trackingVendors = response.body.data.map(t => t.vendor._id).sort();
      const expectedVendors = [testVendor._id.toString(), vendor2._id.toString()].sort();
      expect(trackingVendors).toEqual(expectedVendors);
    });
  });

  describe('Tracking Number Generation', () => {
    test('should generate unique tracking numbers', async () => {
      const trackingNumbers = new Set();

      // Generate multiple tracking records
      for (let i = 0; i < 10; i++) {
        const order = new Order({
          customer: testUser._id,
          orderNumber: `ORD${Date.now()}${i}`,
          items: [{
            product: testProduct._id,
            quantity: 1,
            price: testProduct.price,
            vendor: testVendor._id
          }],
          totalAmount: testProduct.price,
          status: 'pending',
          billing: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '1234567890'
          },
          shipping: {
            firstName: 'John',
            lastName: 'Doe',
            address: {
              street: '123 Test St',
              city: 'Test City',
              state: 'Test State',
              zipCode: '12345',
              country: 'Test Country'
            }
          }
        });
        await order.save();

        const tracking = await OrderTracking.createTracking({
          orderId: order._id,
          vendorId: testVendor._id,
          deliveryAddress: order.shipping.address,
          recipient: {
            name: `${order.shipping.firstName} ${order.shipping.lastName}`,
            phone: order.billing.phone,
            email: order.billing.email
          }
        });

        expect(tracking.trackingNumber).toMatch(/^TRK\d+[A-Z]\d+$/);
        expect(trackingNumbers.has(tracking.trackingNumber)).toBe(false);
        trackingNumbers.add(tracking.trackingNumber);
      }

      expect(trackingNumbers.size).toBe(10);
    });
  });

  describe('Tracking Status Updates', () => {
    let testTracking, testOrder;

    beforeEach(async () => {
      const order = new Order({
        customer: testUser._id,
        orderNumber: `ORD${Date.now()}`,
        items: [{
          product: testProduct._id,
          quantity: 1,
          price: testProduct.price,
          vendor: testVendor._id
        }],
        totalAmount: testProduct.price,
        status: 'pending',
        billing: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '1234567890'
        },
        shipping: {
          firstName: 'John',
          lastName: 'Doe',
          address: {
            street: '123 Test St',
            city: 'Test City',
            state: 'Test State',
            zipCode: '12345',
            country: 'Test Country'
          }
        }
      });
      await order.save();

      testTracking = await OrderTracking.createTracking({
        orderId: order._id,
        vendorId: testVendor._id,
        deliveryAddress: order.shipping.address,
        recipient: {
          name: `${order.shipping.firstName} ${order.shipping.lastName}`,
          phone: order.billing.phone,
          email: order.billing.email
        }
      });

      testOrder = order;
    });

    test('should update tracking status correctly', async () => {
      // Update tracking status
      await testTracking.updateStatus('shipped', 'Package shipped from warehouse');

      const updatedTracking = await OrderTracking.findById(testTracking._id);
      expect(updatedTracking.currentStatus).toBe('shipped');
      expect(updatedTracking.progressPercentage).toBe(50);
      expect(updatedTracking.statusHistory).toHaveLength(2); // order_confirmed + shipped
      
      const latestStatus = updatedTracking.statusHistory[updatedTracking.statusHistory.length - 1];
      expect(latestStatus.status).toBe('shipped');
      expect(latestStatus.reason).toBe('Package shipped from warehouse');
    });

    test('should handle delivery completion', async () => {
      // Update to delivered status
      await testTracking.updateStatus('delivered', 'Package delivered successfully');

      const updatedTracking = await OrderTracking.findById(testTracking._id);
      expect(updatedTracking.currentStatus).toBe('delivered');
      expect(updatedTracking.progressPercentage).toBe(100);
      expect(updatedTracking.deliveredAt).toBeDefined();
    });
  });
});
