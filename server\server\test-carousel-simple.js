const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

async function testCarouselUpload() {
  console.log('Starting simple carousel upload test...');
  
  const BASE_URL = 'http://localhost:5000';
  const token = 'your-admin-token-here'; // We'll need to get this manually
  
  // Login first
  try {
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    console.log('Login successful:', loginResponse.data);
    const authToken = loginResponse.data.data.token;
    
    // Create a real test image file
    const testImagePath = path.join(__dirname, 'simple-test.jpg');
    
    // Create a minimal valid JPEG
    const validJpeg = Buffer.from([
      0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
      0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
      0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
      0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
      0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
      0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
      0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
      0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xC0, 0x00, 0x11, 0x08, 0x00, 0x01,
      0x00, 0x01, 0x01, 0x01, 0x11, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01,
      0xFF, 0xC4, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xC4,
      0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xDA, 0x00, 0x0C,
      0x03, 0x01, 0x00, 0x02, 0x11, 0x03, 0x11, 0x00, 0x3F, 0x00, 0x80, 0xFF, 0xD9
    ]);
    
    fs.writeFileSync(testImagePath, validJpeg);
    console.log('Created test image file');
    
    // Create FormData carefully
    const form = new FormData();
    form.append('image', fs.createReadStream(testImagePath));
    form.append('title', 'Test Carousel Image');
    form.append('description', 'Test description for debugging');
    form.append('linkUrl', 'https://example.com');
    
    console.log('Sending request...');
    console.log('FormData headers:', form.getHeaders());
    
    const response = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, form, {
      headers: {
        ...form.getHeaders(),
        'Authorization': `Bearer ${authToken}`
      },
      timeout: 30000
    });
    
    console.log('✅ Success!', response.data);
    
    // Cleanup
    fs.unlinkSync(testImagePath);
    
  } catch (error) {
    console.log('❌ Failed:', error.response?.status, error.response?.data || error.message);
  }
}

testCarouselUpload();
