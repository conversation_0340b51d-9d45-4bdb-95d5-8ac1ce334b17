import React from 'react';
import { Card, Row, Col, Select, Spin } from 'antd';
import { Line, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const { Option } = Select;

const ChartsSection = ({ 
  analyticsData, 
  dashboardData, 
  selectedPeriod, 
  onPeriodChange,
  loading = false 
}) => {
  // Revenue chart configuration
  const revenueChartData = {
    labels: analyticsData?.analytics?.map(item => item._id) || [],
    datasets: [
      {
        label: 'Revenue ($)',
        data: analyticsData?.analytics?.map(item => item.revenue) || [],
        borderColor: '#40a9ff',
        backgroundColor: 'rgba(64, 169, 255, 0.1)',
        fill: true,
        tension: 0.4,
        pointBackgroundColor: '#40a9ff',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4
      },
      {
        label: 'Orders',
        data: analyticsData?.analytics?.map(item => item.orders) || [],
        borderColor: '#ff7875',
        backgroundColor: 'rgba(255, 120, 117, 0.1)',
        fill: false,
        tension: 0.4,
        pointBackgroundColor: '#ff7875',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
        yAxisID: 'y1'
      }
    ]
  };

  const revenueChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          color: '#666'
        }
      },
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        grid: {
          color: 'rgba(0,0,0,0.05)'
        },
        ticks: {
          color: '#666',
          callback: function(value) {
            return '$' + value.toLocaleString();
          }
        }
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        grid: {
          drawOnChartArea: false,
        },
        ticks: {
          color: '#666'
        }
      },
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          usePointStyle: true,
          padding: 20,
          color: '#666'
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0,0,0,0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#ddd',
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          label: function(context) {
            if (context.datasetIndex === 0) {
              return 'Revenue: $' + context.parsed.y.toLocaleString();
            }
            return 'Orders: ' + context.parsed.y;
          }
        }
      }
    }
  };

  // Order status chart configuration
  const orderStatusData = {
    labels: ['Pending', 'Confirmed', 'Processing', 'Shipped', 'Delivered', 'Cancelled'],
    datasets: [{
      data: dashboardData ? [
        dashboardData.overview?.orders?.pendingOrders || 0,
        dashboardData.overview?.orders?.confirmedOrders || 0,
        dashboardData.overview?.orders?.processingOrders || 0,
        dashboardData.overview?.orders?.shippedOrders || 0,
        dashboardData.overview?.orders?.deliveredOrders || 0,
        dashboardData.overview?.orders?.cancelledOrders || 0
      ] : [],
      backgroundColor: [
        '#faad14', // Pending - yellow
        '#52c41a', // Confirmed - green
        '#1890ff', // Processing - blue
        '#722ed1', // Shipped - purple
        '#13c2c2', // Delivered - cyan
        '#f5222d'  // Cancelled - red
      ],
      borderWidth: 0,
      cutout: '60%'
    }]
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          padding: 15,
          color: '#666'
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0,0,0,0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        cornerRadius: 8,
        callbacks: {
          label: function(context) {
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : '0.0';
            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
          }
        }
      }
    }
  };

  return (
    <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
      <Col xs={24} lg={16}>
        <Card 
          title={
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'space-between',
              flexWrap: 'wrap',
              gap: '8px'
            }}>
              <span>Revenue & Orders Trend</span>
              <Select
                value={selectedPeriod}
                onChange={onPeriodChange}
                style={{ width: 140 }}
                size="small"
              >
                <Option value="7d">Last 7 days</Option>
                <Option value="30d">Last 30 days</Option>
                <Option value="90d">Last 90 days</Option>
                <Option value="1y">Last year</Option>
              </Select>
            </div>
          }
          style={{ 
            borderRadius: '12px', 
            border: 'none',
            boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
          }}
        >
          <div style={{ height: '300px' }}>
            {loading ? (
              <div style={{ 
                display: 'flex', 
                justifyContent: 'center', 
                alignItems: 'center', 
                height: '100%' 
              }}>
                <Spin tip="Loading chart data..." />
              </div>
            ) : (
              <Line data={revenueChartData} options={revenueChartOptions} />
            )}
          </div>
        </Card>
      </Col>

      <Col xs={24} lg={8}>
        <Card 
          title="Order Status Distribution"
          style={{ 
            borderRadius: '12px', 
            border: 'none',
            boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
          }}
        >
          <div style={{ height: '300px' }}>
            {loading ? (
              <div style={{ 
                display: 'flex', 
                justifyContent: 'center', 
                alignItems: 'center', 
                height: '100%' 
              }}>
                <Spin tip="Loading chart data..." />
              </div>
            ) : (
              <Doughnut data={orderStatusData} options={doughnutOptions} />
            )}
          </div>
        </Card>
      </Col>
    </Row>
  );
};

export default ChartsSection;
