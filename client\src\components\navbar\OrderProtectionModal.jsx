import React from 'react';
import { SafetyCertificateOutlined, DollarOutlined, TruckOutlined, ToolOutlined } from '@ant-design/icons';

const OrderProtectionModal = ({ showModal, onMouseEnter, onMouseLeave }) => {
    if (!showModal) return null;

    return (
        <div
            className="absolute left-1/2 sm:left-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-xl z-50 navbar-hover-modal w-[95vw] sm:w-[520px] max-w-[520px] transform -translate-x-1/2 sm:translate-x-0"
            onMouseEnter={onMouseEnter}
            onMouseLeave={onMouseLeave}
        >
            <div className="p-4">
                {/* Header with Trade Assurance badge */}
                <div className="mb-4">
                    <div className="flex items-center mb-2">
                        <div className="w-6 h-6 mr-2">
                            <SafetyCertificateOutlined className="text-xl text-blue-600" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900">Trade Assurance</h3>
                    </div>
                    <p className="text-sm text-gray-700 mb-3">
                        Enjoy protection from payment to delivery.
                    </p>
                    <button className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded text-sm font-medium transition-colors duration-200">
                        Learn more
                    </button>
                </div>

                {/* Protection features grid - 2x2 layout */}
                <div className="grid grid-cols-2 gap-4">
                    {/* Safe & easy payments */}
                    <div className="group cursor-pointer">
                        <div className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                            <div className="w-8 h-8 mr-3 flex-shrink-0">
                                <SafetyCertificateOutlined className="text-2xl text-blue-600" />
                            </div>
                            <div className="flex-1">
                                <h4 className="text-sm font-medium text-gray-900 mb-1">Safe & easy payments</h4>
                                <div className="flex items-center text-gray-600">
                                    <span className="text-xs">→</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Money-back policy */}
                    <div className="group cursor-pointer">
                        <div className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                            <div className="w-8 h-8 mr-3 flex-shrink-0">
                                <DollarOutlined className="text-2xl text-green-600" />
                            </div>
                            <div className="flex-1">
                                <h4 className="text-sm font-medium text-gray-900 mb-1">Money-back policy</h4>
                                <div className="flex items-center text-gray-600">
                                    <span className="text-xs">→</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Shipping & logistics services */}
                    <div className="group cursor-pointer">
                        <div className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                            <div className="w-8 h-8 mr-3 flex-shrink-0">
                                <TruckOutlined className="text-2xl text-orange-600" />
                            </div>
                            <div className="flex-1">
                                <h4 className="text-sm font-medium text-gray-900 mb-1">Shipping & logistics services</h4>
                                <div className="flex items-center text-gray-600">
                                    <span className="text-xs">→</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* After-sales protections */}
                    <div className="group cursor-pointer">
                        <div className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                            <div className="w-8 h-8 mr-3 flex-shrink-0">
                                <ToolOutlined className="text-2xl text-purple-600" />
                            </div>
                            <div className="flex-1">
                                <h4 className="text-sm font-medium text-gray-900 mb-1">After-sales protections</h4>
                                <div className="flex items-center text-gray-600">
                                    <span className="text-xs">→</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Bottom section with additional info */}
                <div className="mt-4 pt-3 border-t border-gray-100">
                    <div className="grid grid-cols-3 gap-4 text-center">
                        <div className="flex flex-col items-center">
                            <div className="text-xl font-bold text-gray-900 mb-1">Millions</div>
                            <div className="text-xs text-gray-600">of business customers</div>
                        </div>
                        <div className="flex flex-col items-center">
                            <div className="text-xl font-bold text-gray-900 mb-1">Assured</div>
                            <div className="text-xs text-gray-600">quality and delivery</div>
                        </div>
                        <div className="flex flex-col items-center">
                            <div className="text-xl font-bold text-gray-900 mb-1">One-stop</div>
                            <div className="text-xs text-gray-600">trading platform</div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Triangle pointer */}
            <div className="absolute -top-2 left-1/2 sm:left-8 w-4 h-4 bg-white border-l border-t border-gray-200 transform rotate-45 -translate-x-1/2 sm:translate-x-0"></div>
        </div>
    );
};

export default OrderProtectionModal;
