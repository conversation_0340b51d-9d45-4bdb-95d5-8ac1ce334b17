const mongoose = require('mongoose');

const shippingSchema = new mongoose.Schema({
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    required: true
  },
  vendor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vendor',
    required: true
  },
  trackingNumber: {
    type: String,
    required: true,
    unique: true
  },
  carrier: {
    name: {
      type: String,
      required: true
    },
    code: {
      type: String,
      required: true
    },
    trackingUrl: String,
    logo: String,
    contactInfo: {
      phone: String,
      email: String,
      website: String
    }
  },
  service: {
    type: {
      type: String,
      enum: ['standard', 'express', 'overnight', 'same_day', 'economy'],
      default: 'standard'
    },
    name: String,
    description: String,
    estimatedDays: {
      min: Number,
      max: Number
    }
  },
  status: {
    type: String,
    enum: ['label_created', 'in_transit', 'out_for_delivery', 'delivered', 'exception', 'returned', 'cancelled'],
    default: 'label_created'
  },
  addresses: {
    origin: {
      name: String,
      company: String,
      street1: String,
      street2: String,
      city: String,
      state: String,
      zipCode: String,
      country: String,
      phone: String
    },
    destination: {
      name: {
        type: String,
        required: true
      },
      company: String,
      street1: {
        type: String,
        required: true
      },
      street2: String,
      city: {
        type: String,
        required: true
      },
      state: {
        type: String,
        required: true
      },
      zipCode: {
        type: String,
        required: true
      },
      country: {
        type: String,
        required: true
      },
      phone: String,
      instructions: String
    }
  },
  package: {
    weight: {
      value: Number,
      unit: {
        type: String,
        enum: ['lb', 'kg', 'oz', 'g'],
        default: 'lb'
      }
    },
    dimensions: {
      length: Number,
      width: Number,
      height: Number,
      unit: {
        type: String,
        enum: ['in', 'cm'],
        default: 'in'
      }
    },
    insuranceValue: {
      type: Number,
      default: 0
    },
    signature: {
      type: String,
      enum: ['none', 'required', 'adult'],
      default: 'none'
    }
  },
  costs: {
    shipping: {
      type: Number,
      required: true,
      min: 0
    },
    insurance: {
      type: Number,
      default: 0
    },
    tax: {
      type: Number,
      default: 0
    },
    total: {
      type: Number,
      required: true
    },
    currency: {
      type: String,
      default: 'USD'
    }
  },
  dates: {
    labelCreated: Date,
    shipped: Date,
    estimatedDelivery: Date,
    actualDelivery: Date
  },
  events: [{
    status: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: true
    },
    location: {
      city: String,
      state: String,
      country: String,
      coordinates: {
        lat: Number,
        lng: Number
      }
    },
    timestamp: {
      type: Date,
      required: true
    },
    source: {
      type: String,
      enum: ['carrier', 'manual', 'webhook'],
      default: 'manual'
    }
  }],
  delivery: {
    attempts: [{
      timestamp: Date,
      status: {
        type: String,
        enum: ['attempted', 'delivered', 'failed']
      },
      reason: String,
      location: String,
      signature: String
    }],
    finalStatus: {
      type: String,
      enum: ['delivered', 'returned', 'lost', 'damaged']
    },
    signedBy: String,
    leftAt: String, // "Front door", "Mailbox", etc.
    photos: [String] // URLs to delivery photos
  },
  notifications: {
    customer: {
      shipped: { sent: Boolean, timestamp: Date },
      outForDelivery: { sent: Boolean, timestamp: Date },
      delivered: { sent: Boolean, timestamp: Date },
      exception: { sent: Boolean, timestamp: Date }
    },
    vendor: {
      delivered: { sent: Boolean, timestamp: Date },
      exception: { sent: Boolean, timestamp: Date }
    }
  },
  metadata: {
    labelUrl: String,
    manifestUrl: String,
    invoiceUrl: String,
    carrierData: mongoose.Schema.Types.Mixed, // Raw carrier response
    customFields: mongoose.Schema.Types.Mixed
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes (trackingNumber index is automatically created by unique: true)
shippingSchema.index({ order: 1, vendor: 1 });
shippingSchema.index({ status: 1 });
shippingSchema.index({ 'dates.estimatedDelivery': 1 });
shippingSchema.index({ 'carrier.code': 1 });

// Virtual for tracking URL
shippingSchema.virtual('trackingUrl').get(function() {
  if (this.carrier.trackingUrl && this.trackingNumber) {
    return this.carrier.trackingUrl.replace('{trackingNumber}', this.trackingNumber);
  }
  return null;
});

// Virtual for delivery status
shippingSchema.virtual('deliveryStatus').get(function() {
  if (this.status === 'delivered') return 'delivered';
  if (this.status === 'exception') return 'exception';
  if (this.status === 'returned') return 'returned';
  
  const now = new Date();
  const estimated = this.dates.estimatedDelivery;
  
  if (!estimated) return 'unknown';
  
  if (now > estimated) return 'overdue';
  
  const timeDiff = estimated.getTime() - now.getTime();
  const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
  
  if (daysDiff <= 1) return 'due_soon';
  return 'on_time';
});

// Virtual for current location
shippingSchema.virtual('currentLocation').get(function() {
  if (this.events.length === 0) return null;
  return this.events[this.events.length - 1].location;
});

// Virtual for latest event
shippingSchema.virtual('latestEvent').get(function() {
  if (this.events.length === 0) return null;
  return this.events.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))[0];
});

// Static method to create shipping label
shippingSchema.statics.createShipping = async function(shippingData) {
  const trackingNumber = `SHP${Date.now()}${Math.random().toString(36).substr(2, 6).toUpperCase()}`;
  
  const shipping = new this({
    ...shippingData,
    trackingNumber,
    status: 'label_created',
    dates: {
      labelCreated: new Date(),
      estimatedDelivery: shippingData.estimatedDelivery || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
    },
    events: [{
      status: 'label_created',
      description: 'Shipping label created',
      timestamp: new Date(),
      source: 'manual'
    }]
  });
  
  return shipping.save();
};

// Instance method to add tracking event
shippingSchema.methods.addEvent = function(eventData) {
  this.events.push({
    status: eventData.status,
    description: eventData.description,
    location: eventData.location,
    timestamp: eventData.timestamp || new Date(),
    source: eventData.source || 'manual'
  });
  
  // Update main status
  this.status = eventData.status;
  
  // Update specific dates
  if (eventData.status === 'in_transit' && !this.dates.shipped) {
    this.dates.shipped = eventData.timestamp || new Date();
  } else if (eventData.status === 'delivered') {
    this.dates.actualDelivery = eventData.timestamp || new Date();
  }
  
  return this.save();
};

// Instance method to update delivery attempt
shippingSchema.methods.addDeliveryAttempt = function(attemptData) {
  this.delivery.attempts.push({
    timestamp: attemptData.timestamp || new Date(),
    status: attemptData.status,
    reason: attemptData.reason,
    location: attemptData.location,
    signature: attemptData.signature
  });
  
  if (attemptData.status === 'delivered') {
    this.delivery.finalStatus = 'delivered';
    this.delivery.signedBy = attemptData.signature;
    this.delivery.leftAt = attemptData.location;
    this.status = 'delivered';
    this.dates.actualDelivery = attemptData.timestamp || new Date();
  }
  
  return this.save();
};

// Static method to get shipping analytics
shippingSchema.statics.getAnalytics = function(vendorId, dateRange) {
  const matchStage = { vendor: vendorId };
  
  if (dateRange) {
    matchStage.createdAt = {
      $gte: new Date(dateRange.start),
      $lte: new Date(dateRange.end)
    };
  }
  
  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalShipments: { $sum: 1 },
        totalCost: { $sum: '$costs.total' },
        averageCost: { $avg: '$costs.total' },
        onTimeDeliveries: {
          $sum: {
            $cond: [
              { $lte: ['$dates.actualDelivery', '$dates.estimatedDelivery'] },
              1, 0
            ]
          }
        },
        lateDeliveries: {
          $sum: {
            $cond: [
              { $gt: ['$dates.actualDelivery', '$dates.estimatedDelivery'] },
              1, 0
            ]
          }
        },
        statusBreakdown: {
          delivered: {
            $sum: { $cond: [{ $eq: ['$status', 'delivered'] }, 1, 0] }
          },
          inTransit: {
            $sum: { $cond: [{ $eq: ['$status', 'in_transit'] }, 1, 0] }
          },
          outForDelivery: {
            $sum: { $cond: [{ $eq: ['$status', 'out_for_delivery'] }, 1, 0] }
          },
          exception: {
            $sum: { $cond: [{ $eq: ['$status', 'exception'] }, 1, 0] }
          }
        }
      }
    }
  ]);
};

module.exports = mongoose.model('Shipping', shippingSchema);
