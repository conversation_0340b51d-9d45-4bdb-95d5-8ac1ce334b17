/**
 * Currency utilities for consistent formatting across the application
 */

/**
 * Format price with currency symbol
 * @param {number} price - The price to format
 * @param {string} currencySymbol - The currency symbol to use
 * @param {number} decimals - Number of decimal places (default: 2)
 * @returns {string} Formatted price string
 */
export const formatPrice = (price, currencySymbol, decimals = 2) => {
  if (typeof price !== 'number' || isNaN(price)) {
    return `${currencySymbol}0.${'0'.repeat(decimals)}`;
  }
  
  return `${currencySymbol}${price.toFixed(decimals)}`;
};

/**
 * Format price with currency symbol (no decimals for whole numbers)
 * @param {number} price - The price to format
 * @param {string} currencySymbol - The currency symbol to use
 * @returns {string} Formatted price string
 */
export const formatPriceWhole = (price, currencySymbol) => {
  if (typeof price !== 'number' || isNaN(price)) {
    return `${currencySymbol}0`;
  }
  
  return `${currencySymbol}${Math.round(price)}`;
};

/**
 * Get currency display based on user's current selection
 * @param {Function} getCurrencySymbol - Function from useCurrency hook
 * @param {number} price - The price to format
 * @param {boolean} showDecimals - Whether to show decimal places
 * @returns {string} Formatted price with dynamic currency
 */
export const formatDynamicPrice = (getCurrencySymbol, price, showDecimals = true) => {
  const symbol = getCurrencySymbol();
  
  if (showDecimals) {
    return formatPrice(price, symbol);
  } else {
    return formatPriceWhole(price, symbol);
  }
};

/**
 * Common currency configurations
 */
export const CURRENCY_CONFIG = {
  USD: { symbol: '$', name: 'US Dollar', code: 'USD' },
  EUR: { symbol: '€', name: 'Euro', code: 'EUR' },
  GBP: { symbol: '£', name: 'British Pound', code: 'GBP' },
  INR: { symbol: '₹', name: 'Indian Rupee', code: 'INR' },
  JPY: { symbol: '¥', name: 'Japanese Yen', code: 'JPY' },
  CNY: { symbol: '¥', name: 'Chinese Yuan', code: 'CNY' },
  CAD: { symbol: 'C$', name: 'Canadian Dollar', code: 'CAD' },
  AUD: { symbol: 'A$', name: 'Australian Dollar', code: 'AUD' },
  SGD: { symbol: 'S$', name: 'Singapore Dollar', code: 'SGD' },
  AED: { symbol: 'د.إ', name: 'UAE Dirham', code: 'AED' }
};
