import React from 'react';
import { <PERSON>, Row, Col, Input, Button, Space } from 'antd';
import { SearchOutlined, StarFilled, ReloadOutlined } from '@ant-design/icons';

const ReviewsFilters = ({
  selectedRating,
  searchText,
  onRatingFilter,
  onSearch,
  onRefresh,
  loading = false
}) => {
  return (
    <Card className="mb-6 rounded-xl shadow-sm border border-gray-200">
      <Row gutter={16} align="middle">
        <Col xs={24} sm={12} md={8} className="mb-4 md:mb-0">
          <Input.Search
            placeholder="Search reviews..."
            allowClear
            enterButton={<SearchOutlined />}
            size="middle"
            onSearch={onSearch}
            className="rounded-lg"
            defaultValue={searchText}
          />
        </Col>
        <Col xs={24} sm={12} md={12} className="mb-4 md:mb-0">
          <Space wrap className="w-full flex-wrap gap-2">
            <Button 
              type={selectedRating === 'all' ? 'primary' : 'default'}
              onClick={() => onRatingFilter('all')}
              className="rounded-lg"
            >
              All
            </Button>
            {[5, 4, 3, 2, 1].map(rating => (
              <Button
                key={rating}
                type={selectedRating === rating ? 'primary' : 'default'}
                onClick={() => onRatingFilter(rating)}
                icon={<StarFilled />}
                className="rounded-lg"
              >
                {rating}
              </Button>
            ))}
          </Space>
        </Col>
        <Col xs={24} sm={24} md={4}>
          <Button
            icon={<ReloadOutlined />}
            onClick={onRefresh}
            loading={loading}
            className="w-full md:w-auto rounded-lg"
          >
            Refresh
          </Button>
        </Col>
      </Row>
    </Card>
  );
};

export default ReviewsFilters;
