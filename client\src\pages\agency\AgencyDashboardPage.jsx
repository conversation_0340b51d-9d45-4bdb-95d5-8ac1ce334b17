import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Spin } from 'antd';
import { useAuth } from '../../hooks/useAuth';
import AgencyPanel from '../../components/agency/AgencyPanel';

const AgencyDashboardPage = () => {
  // const navigate = useNavigate();
  // const { isAuthenticated, userType, isLoading } = useAuth();

  // useEffect(() => {
  //   if (!isLoading) {
  //     if (!isAuthenticated) {
  //       // Not authenticated at all, redirect to agency auth
  //       navigate('/agency/auth');
  //     } else if (userType !== 'agency') {
  //       // Authenticated but not as agency, redirect appropriately
  //       if (userType === 'admin') {
  //         navigate('/admin/dashboard');
  //       } else if (userType === 'vendor') {
  //         navigate('/vendor/dashboard');
  //       } else {
  //         navigate('/');
  //       }
  //     }
  //   }
  // }, [isAuthenticated, userType, isLoading, navigate]);

  // // Show loading while checking authentication
  // if (isLoading) {
  //   return (
  //     <div className="min-h-screen bg-gray-50 flex items-center justify-center">
  //       <Spin size="large" />
  //     </div>
  //   );
  // }

  // // Only render agency panel if authenticated as agency
  // if (isAuthenticated && userType === 'agency') {
  //   return <AgencyPanel />;
  // }

  // // Return null while redirecting
  // return null;
  
  // For now, directly render the AgencyPanel without authentication
  return <AgencyPanel />;
};

export default AgencyDashboardPage;
