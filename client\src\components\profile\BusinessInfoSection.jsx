import React from 'react';
import { ShopOutlined, UserOutlined, BriefcaseOutlined } from '@ant-design/icons';
import ProfileF<PERSON><PERSON>ield from './ProfileFormField';

const BusinessInfoSection = ({ user, userType, editData, isEditing, onInputChange }) => {
    const businessTypeOptions = [
        { value: 'retail', label: 'Retail' },
        { value: 'wholesale', label: 'Wholesale' },
        { value: 'manufacturer', label: 'Manufacturer' },
        { value: 'dropshipping', label: 'Dropshipping' },
        { value: 'services', label: 'Services' },
        { value: 'other', label: 'Other' }
    ];

    if (userType !== 'vendor') {
        return null; // Only show for vendors
    }

    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900 flex items-center space-x-2">
                    <ShopOutlined className="text-orange-500" />
                    <span>Business Information</span>
                </h2>
            </div>
            <div className="px-6 py-4 space-y-4">
                <ProfileFormField
                    label="Business Name"
                    value={editData.businessName}
                    displayValue={user.businessName}
                    isEditing={isEditing}
                    onChange={(value) => onInputChange('businessName', value)}
                    icon={ShopOutlined}
                    required
                />
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <ProfileFormField
                        label="Contact Person"
                        value={editData.contactPerson}
                        displayValue={user.contactPerson}
                        isEditing={isEditing}
                        onChange={(value) => onInputChange('contactPerson', value)}
                        icon={UserOutlined}
                        required
                    />
                    <ProfileFormField
                        label="Business Type"
                        value={editData.businessType}
                        displayValue={user.businessType ? user.businessType.charAt(0).toUpperCase() + user.businessType.slice(1) : 'Not provided'}
                        isEditing={isEditing}
                        onChange={(value) => onInputChange('businessType', value)}
                        type="select"
                        options={businessTypeOptions}
                        icon={BriefcaseOutlined}
                        required
                    />
                </div>
            </div>
        </div>
    );
};

export default BusinessInfoSection;
