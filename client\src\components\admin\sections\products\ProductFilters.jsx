import React from 'react';
import { Space, Select, Input, Button } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';

const { Option } = Select;

const ProductFilters = ({
  filters,
  onFiltersChange,
  onRefresh,
  loading
}) => {
  const handleFilterChange = (key, value) => {
    onFiltersChange(prev => ({ ...prev, [key]: value }));
  };

  return (
    <div style={{ 
      marginBottom: '16px', 
      display: 'flex', 
      justifyContent: 'space-between', 
      alignItems: 'center',
      flexWrap: 'wrap',
      gap: '12px'
    }}>
      <Space wrap>
        <Select
          placeholder="Filter by status"
          style={{ width: 150 }}
          value={filters.status}
          onChange={(value) => handleFilterChange('status', value)}
          allowClear
        >
          <Option value="draft">Draft</Option>
          <Option value="pending_approval">Pending Approval</Option>
          <Option value="active">Active</Option>
          <Option value="inactive">Inactive</Option>
          <Option value="rejected">Rejected</Option>
          <Option value="archived">Archived</Option>
        </Select>
        
        <Input.Search
          placeholder="Search products..."
          style={{ width: 250 }}
          value={filters.search}
          onChange={(e) => handleFilterChange('search', e.target.value)}
          onSearch={onRefresh}
        />
      </Space>
      
      <Button 
        icon={<ReloadOutlined />} 
        onClick={onRefresh}
        loading={loading}
      >
        Refresh
      </Button>
    </div>
  );
};

export default ProductFilters;
