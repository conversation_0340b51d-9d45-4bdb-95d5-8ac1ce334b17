import React from 'react';

const ProfileFormField = ({ 
  label, 
  value, 
  displayValue, 
  isEditing, 
  onChange, 
  type = 'text', 
  placeholder, 
  options = [], 
  required = false,
  className = '',
  icon: Icon
}) => {
  const renderDisplayValue = () => {
    if (displayValue !== undefined) {
      return displayValue;
    }
    return value || 'Not provided';
  };

  const renderInput = () => {
    if (type === 'select') {
      return (
        <select
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200 ${className}`}
          required={required}
        >
          <option value="">{placeholder || `Select ${label}`}</option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      );
    }

    return (
      <input
        type={type}
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
        className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200 ${className}`}
        placeholder={placeholder || `Enter ${label.toLowerCase()}`}
        required={required}
      />
    );
  };

  return (
    <div className="space-y-1">
      <label className="block text-sm font-medium text-gray-700">
        <div className="flex items-center space-x-2">
          {Icon && <Icon className="text-gray-500" />}
          <span>{label}</span>
          {required && <span className="text-red-500">*</span>}
        </div>
      </label>
      {isEditing ? (
        renderInput()
      ) : (
        <div className="flex items-center space-x-2 min-h-[42px] px-3 py-2 bg-gray-50 rounded-md">
          <p className="text-gray-900">{renderDisplayValue()}</p>
        </div>
      )}
    </div>
  );
};

export default ProfileFormField;
