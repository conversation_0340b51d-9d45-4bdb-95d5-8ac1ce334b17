import React from 'react';
import { Card, Row, Col, Statistic } from 'antd';
import {
  DollarOutlined,
  ShoppingCartOutlined,
  ProductOutlined,
  RiseOutlined
} from '@ant-design/icons';
import useResponsive from '../../../hooks/useResponsive';

const StatCard = ({ title, value, prefix, color, formatter, growth, loading = false }) => {
  const { isMobile } = useResponsive();
  
  return (
    <Card size={isMobile ? 'small' : 'default'} loading={loading}>
      <Statistic
        title={title}
        value={value}
        formatter={formatter}
        prefix={prefix}
        valueStyle={{ 
          color, 
          fontSize: isMobile ? '16px' : '24px' 
        }}
      />
      {growth && (
        <div style={{ 
          marginTop: '8px',
          fontSize: isMobile ? '11px' : '12px',
          color: growth > 0 ? '#52c41a' : '#f5222d'
        }}>
          <RiseOutlined style={{ marginRight: '4px' }} />
          {growth > 0 ? '+' : ''}{growth}% from last period
        </div>
      )}
    </Card>
  );
};

const StatisticsCards = ({ dashboardData, loading, formatCurrency }) => {
  const statisticsData = [
    {
      title: "Total Revenue",
      value: dashboardData?.orders?.totalRevenue || 0,
      formatter: formatCurrency,
      prefix: <DollarOutlined style={{ color: '#52c41a' }} />,
      color: "#52c41a",
      growth: dashboardData?.growth?.revenueGrowth || 0
    },
    {
      title: "Total Orders",
      value: dashboardData?.orders?.totalOrders || 0,
      prefix: <ShoppingCartOutlined style={{ color: '#1890ff' }} />,
      color: "#1890ff",
      growth: dashboardData?.growth?.ordersGrowth || 0
    },
    {
      title: "Total Products",
      value: dashboardData?.products?.totalProducts || 0,
      prefix: <ProductOutlined style={{ color: '#faad14' }} />,
      color: "#faad14",
      growth: dashboardData?.growth?.productsGrowth || 0
    },
    {
      title: "Avg Order Value",
      value: dashboardData?.orders?.averageOrderValue || 0,
      formatter: formatCurrency,
      prefix: <RiseOutlined style={{ color: '#722ed1' }} />,
      color: "#722ed1",
      growth: dashboardData?.growth?.avgOrderValueGrowth || 0
    }
  ];

  return (
    <Row gutter={[8, 8]} style={{ marginBottom: 16 }}>
      {statisticsData.map((stat, index) => (
        <Col xs={12} sm={12} md={6} lg={6} key={index}>
          <StatCard
            title={stat.title}
            value={stat.value}
            formatter={stat.formatter}
            prefix={stat.prefix}
            color={stat.color}
            growth={stat.growth}
            loading={loading}
          />
        </Col>
      ))}
    </Row>
  );
};

export default StatisticsCards;
