const express = require('express');
const router = express.Router();

// Import middleware
const { optionalAuth } = require('../../middleware/auth/authMiddleware');

// Import controller
const {
  searchProducts,
  searchCategories,
  searchVendors,
  globalSearch,
  getSuggestions
} = require('../../controllers/public/searchController');

/**
 * @route   GET /api/public/search/suggestions
 * @desc    Get search suggestions
 * @access  Public
 */
router.get('/suggestions', optionalAuth, getSuggestions);

/**
 * @route   GET /api/public/search/products
 * @desc    Search products
 * @access  Public
 */
router.get('/products', optionalAuth, searchProducts);

/**
 * @route   GET /api/public/search/categories
 * @desc    Search categories
 * @access  Public
 */
router.get('/categories', optionalAuth, searchCategories);

/**
 * @route   GET /api/public/search/vendors
 * @desc    Search vendors
 * @access  Public
 */
router.get('/vendors', optionalAuth, searchVendors);

/**
 * @route   GET /api/public/search
 * @desc    Global search across products, categories, and vendors
 * @access  Public
 */
router.get('/', optionalAuth, globalSearch);

module.exports = router;