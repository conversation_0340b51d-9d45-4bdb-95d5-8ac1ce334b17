const asyncHandler = require('express-async-handler');
const OrderTracking = require('../models/OrderTracking');
const Order = require('../models/Order');
const { validationResult } = require('express-validator');

// @desc    Create order tracking
// @route   POST /api/order-tracking
// @access  Private (Vendor/Admin)
const createOrderTracking = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation errors',
      errors: errors.array()
    });
  }

  const {
    orderId,
    carrier,
    estimatedDelivery,
    deliveryAddress,
    recipient
  } = req.body;

  // Check if order exists
  const order = await Order.findById(orderId);
  if (!order) {
    return res.status(404).json({
      success: false,
      message: 'Order not found'
    });
  }

  // Check if tracking already exists for this order
  const existingTracking = await OrderTracking.findOne({ order: orderId });
  if (existingTracking) {
    return res.status(400).json({
      success: false,
      message: 'Tracking already exists for this order'
    });
  }

  // Create tracking
  const tracking = await OrderTracking.createTracking({
    orderId,
    carrier,
    estimatedDelivery,
    deliveryAddress,
    recipient,
    vendorId: req.user.vendor || req.body.vendorId // From authenticated vendor or admin input
  });

  // Populate the response
  const populatedTracking = await OrderTracking.findById(tracking._id)
    .populate('order', 'orderNumber customer items pricing')
    .populate('vendor', 'businessName contactInfo');

  return res.status(201).json({
    success: true,
    message: 'Order tracking created successfully',
    data: populatedTracking
  });
});

// @desc    Get order tracking by tracking number
// @route   GET /api/order-tracking/:trackingNumber
// @access  Public
const getTrackingByNumber = asyncHandler(async (req, res) => {
  const { trackingNumber } = req.params;

  const tracking = await OrderTracking.getByTrackingNumber(trackingNumber);
  if (!tracking) {
    return res.status(404).json({
      success: false,
      message: 'Tracking information not found'
    });
  }

  // Filter out private notes for public access
  const publicTracking = {
    ...tracking.toObject(),
    notes: tracking.notes.filter(note => note.isPublic)
  };

  return res.status(200).json({
    success: true,
    data: publicTracking
  });
});

// @desc    Get order tracking by order ID
// @route   GET /api/order-tracking/order/:orderId
// @access  Private
const getTrackingByOrderId = asyncHandler(async (req, res) => {
  const { orderId } = req.params;

  const tracking = await OrderTracking.getByOrderId(orderId);
  if (!tracking) {
    return res.status(404).json({
      success: false,
      message: 'Tracking information not found'
    });
  }

  return res.status(200).json({
    success: true,
    data: tracking
  });
});

// @desc    Update tracking status - ENHANCED FOR MULTI-VENDOR
// @route   PUT /api/order-tracking/:trackingId/status
// @access  Private (Vendor/Admin)
const updateTrackingStatus = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation errors',
      errors: errors.array()
    });
  }

  const { trackingId } = req.params;
  const {
    status,
    title,
    description,
    location,
    metadata,
    timestamp
  } = req.body;

  console.log('Updating tracking status:', { trackingId, status, title });

  const tracking = await OrderTracking.findById(trackingId)
    .populate('order')
    .populate('vendor');
    
  if (!tracking) {
    return res.status(404).json({
      success: false,
      message: 'Tracking not found'
    });
  }

  // Enhanced permission check for multi-vendor support
  let hasPermission = false;
  if (req.user.role === 'admin') {
    hasPermission = true;
  } else if (req.user.role === 'vendor') {
    // Get vendor ID from user
    const { Vendor } = require('../models');
    const vendor = await Vendor.findOne({ user: req.user.userId });
    if (vendor && vendor._id.toString() === tracking.vendor.toString()) {
      hasPermission = true;
    }
  }

  if (!hasPermission) {
    return res.status(403).json({
      success: false,
      message: 'Not authorized to update this tracking'
    });
  }

  const updateData = {
    title,
    description,
    location,
    metadata,
    timestamp,
    updatedBy: {
      type: req.user.role === 'admin' ? 'admin' : 'vendor',
      [req.user.role === 'admin' ? 'user' : 'vendor']: req.user._id
    }
  };

  await tracking.updateStatus(status, updateData);

  // Update the main order status intelligently for multi-vendor orders
  const order = await Order.findById(tracking.order._id);
  if (order) {
    // For multi-vendor orders, we need to consider all vendor items
    const vendorItems = order.items.filter(item => 
      item.vendor.toString() === tracking.vendor.toString()
    );
    
    // Update vendor's items status based on tracking status
    const statusMapping = {
      'order_confirmed': 'confirmed',
      'processing': 'processing',
      'shipped': 'shipped',
      'out_for_delivery': 'shipped',
      'delivered': 'delivered',
      'cancelled': 'cancelled',
      'returned': 'returned'
    };
    
    const newItemStatus = statusMapping[status] || status;
    
    // Update items for this vendor
    order.items.forEach((item, index) => {
      if (item.vendor.toString() === tracking.vendor.toString()) {
        order.items[index].status = newItemStatus;
      }
    });
    
    // Update overall order status based on all items
    const allItemStatuses = order.items.map(item => item.status);
    const uniqueStatuses = [...new Set(allItemStatuses)];
    
    if (uniqueStatuses.length === 1) {
      order.status = uniqueStatuses[0];
    } else if (allItemStatuses.every(s => ['delivered', 'cancelled', 'returned'].includes(s))) {
      order.status = 'delivered';
    } else if (allItemStatuses.some(s => s === 'shipped')) {
      order.status = 'shipped';
    } else if (allItemStatuses.some(s => s === 'processing')) {
      order.status = 'processing';
    } else {
      order.status = 'confirmed';
    }
    
    // Add to timeline
    order.timeline.push({
      status: newItemStatus,
      timestamp: new Date(),
      note: description || `${tracking.vendor.businessName} updated tracking to ${status}`,
      updatedBy: req.user._id
    });
    
    await order.save();
    
    console.log('Order updated:', {
      orderNumber: order.orderNumber,
      newOverallStatus: order.status,
      vendorItemsUpdated: vendorItems.length
    });
  }

  const updatedTracking = await OrderTracking.getByOrderId(tracking.order._id);

  return res.status(200).json({
    success: true,
    message: 'Tracking status updated successfully',
    data: updatedTracking
  });
});

// @desc    Add tracking note
// @route   POST /api/order-tracking/:trackingId/notes
// @access  Private (Vendor/Admin/Customer)
const addTrackingNote = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation errors',
      errors: errors.array()
    });
  }

  const { trackingId } = req.params;
  const { message, isPublic = true } = req.body;

  const tracking = await OrderTracking.findById(trackingId);
  if (!tracking) {
    return res.status(404).json({
      success: false,
      message: 'Tracking not found'
    });
  }

  // Check permissions
  if (req.user.role === 'customer') {
    // Customer can only add public notes to their own orders
    const order = await Order.findById(tracking.order);
    if (!order || order.customer.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to add notes to this tracking'
      });
    }
  } else if (req.user.role === 'vendor') {
    // Vendor can only add notes to their own orders
    if (req.user.vendor?.toString() !== tracking.vendor.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to add notes to this tracking'
      });
    }
  }

  const addedBy = {
    type: req.user.role,
    [req.user.role === 'vendor' ? 'vendor' : 'user']: req.user._id
  };

  await tracking.addNote(message, addedBy, isPublic);

  const updatedTracking = await OrderTracking.getByOrderId(tracking.order);

  return res.status(200).json({
    success: true,
    message: 'Note added successfully',
    data: updatedTracking
  });
});

// @desc    Get vendor's order trackings
// @route   GET /api/order-tracking/vendor/my-trackings
// @access  Private (Vendor)
const getVendorTrackings = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const status = req.query.status;
  const search = req.query.search;

  let query = { vendor: req.user.vendor };

  if (status) {
    query.currentStatus = status;
  }

  if (search) {
    query.$or = [
      { trackingNumber: { $regex: search, $options: 'i' } },
      { 'carrier.name': { $regex: search, $options: 'i' } }
    ];
  }

  const trackings = await OrderTracking.find(query)
    .populate('order', 'orderNumber customer items pricing createdAt')
    .populate('order.customer', 'firstName lastName email')
    .sort({ createdAt: -1 })
    .limit(limit)
    .skip((page - 1) * limit);

  const total = await OrderTracking.countDocuments(query);

  return res.status(200).json({
    success: true,
    data: {
      trackings,
      currentPage: page,
      totalPages: Math.ceil(total / limit),
      totalItems: total,
      hasNextPage: page < Math.ceil(total / limit),
      hasPrevPage: page > 1
    }
  });
});

// @desc    Get customer's order trackings
// @route   GET /api/order-tracking/customer/my-trackings
// @access  Private (Customer)
const getCustomerTrackings = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const status = req.query.status;

  // First get customer's orders
  const customerOrders = await Order.find({ customer: req.user._id }).select('_id');
  const orderIds = customerOrders.map(order => order._id);

  let query = { order: { $in: orderIds } };

  if (status) {
    query.currentStatus = status;
  }

  const trackings = await OrderTracking.find(query)
    .populate('order', 'orderNumber items pricing createdAt')
    .populate('vendor', 'businessName contactInfo')
    .sort({ createdAt: -1 })
    .limit(limit)
    .skip((page - 1) * limit);

  // Filter out private notes
  const publicTrackings = trackings.map(tracking => ({
    ...tracking.toObject(),
    notes: tracking.notes.filter(note => note.isPublic)
  }));

  const total = await OrderTracking.countDocuments(query);

  return res.status(200).json({
    success: true,
    data: {
      trackings: publicTrackings,
      currentPage: page,
      totalPages: Math.ceil(total / limit),
      totalItems: total,
      hasNextPage: page < Math.ceil(total / limit),
      hasPrevPage: page > 1
    }
  });
});

// @desc    Get tracking statistics
// @route   GET /api/order-tracking/stats
// @access  Private (Admin)
const getTrackingStats = asyncHandler(async (req, res) => {
  const { startDate, endDate, vendorId } = req.query;

  let matchQuery = {};

  if (startDate && endDate) {
    matchQuery.createdAt = {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };
  }

  if (vendorId) {
    matchQuery.vendor = vendorId;
  }

  const stats = await OrderTracking.aggregate([
    { $match: matchQuery },
    {
      $group: {
        _id: null,
        totalTrackings: { $sum: 1 },
        orderConfirmed: {
          $sum: { $cond: [{ $eq: ['$currentStatus', 'order_confirmed'] }, 1, 0] }
        },
        processing: {
          $sum: { $cond: [{ $eq: ['$currentStatus', 'processing'] }, 1, 0] }
        },
        shipped: {
          $sum: { $cond: [{ $eq: ['$currentStatus', 'shipped'] }, 1, 0] }
        },
        outForDelivery: {
          $sum: { $cond: [{ $eq: ['$currentStatus', 'out_for_delivery'] }, 1, 0] }
        },
        delivered: {
          $sum: { $cond: [{ $eq: ['$currentStatus', 'delivered'] }, 1, 0] }
        },
        cancelled: {
          $sum: { $cond: [{ $eq: ['$currentStatus', 'cancelled'] }, 1, 0] }
        },
        returned: {
          $sum: { $cond: [{ $eq: ['$currentStatus', 'returned'] }, 1, 0] }
        },
        averageDeliveryTime: {
          $avg: {
            $cond: [
              { $ne: ['$actualDelivery', null] },
              {
                $divide: [
                  { $subtract: ['$actualDelivery', '$createdAt'] },
                  1000 * 60 * 60 * 24 // Convert to days
                ]
              },
              null
            ]
          }
        }
      }
    }
  ]);

  return res.status(200).json({
    success: true,
    data: stats[0] || {
      totalTrackings: 0,
      orderConfirmed: 0,
      processing: 0,
      shipped: 0,
      outForDelivery: 0,
      delivered: 0,
      cancelled: 0,
      returned: 0,
      averageDeliveryTime: 0
    }
  });
});

module.exports = {
  createOrderTracking,
  getTrackingByNumber,
  getTrackingByOrderId,
  updateTrackingStatus,
  addTrackingNote,
  getVendorTrackings,
  getCustomerTrackings,
  getTrackingStats
};
