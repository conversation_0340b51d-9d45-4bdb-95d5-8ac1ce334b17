const path = require('path');
const multer = require('multer');
const crypto = require('crypto');

// Define storage locations
const UPLOAD_FOLDER = path.resolve(__dirname, '../../uploads');
const IMAGE_FOLDER = path.join(UPLOAD_FOLDER, 'images');
const DOCUMENT_FOLDER = path.join(UPLOAD_FOLDER, 'documents');

// Define file size limits
const FILE_SIZE_LIMITS = {
  image: 2 * 1024 * 1024, // 2MB
  document: 5 * 1024 * 1024, // 5MB
  default: 1 * 1024 * 1024 // 1MB
};

// Define allowed file types
const ALLOWED_FILE_TYPES = {
  image: ['image/jpeg', 'image/png', 'image/gif'],
  document: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  default: []
};

// Configure storage for different file types
const storage = {
  // Storage for images
  image: multer.diskStorage({
    destination: (req, file, cb) => {
      // Determine subfolder based on request
      let subfolder = 'users';
      if (req.path.includes('product')) {
        subfolder = 'products';
      } else if (req.path.includes('vendor')) {
        subfolder = 'vendors';
      }
      
      const destination = path.join(IMAGE_FOLDER, subfolder);
      cb(null, destination);
    },
    filename: (req, file, cb) => {
      // Generate unique filename
      const uniqueSuffix = crypto.randomBytes(16).toString('hex');
      const extension = path.extname(file.originalname);
      cb(null, `${Date.now()}-${uniqueSuffix}${extension}`);
    }
  }),
  
  // Storage for documents
  document: multer.diskStorage({
    destination: (req, file, cb) => {
      // Determine subfolder based on request
      let subfolder = 'legal';
      if (req.path.includes('vendor')) {
        subfolder = 'vendor-docs';
      }
      
      const destination = path.join(DOCUMENT_FOLDER, subfolder);
      cb(null, destination);
    },
    filename: (req, file, cb) => {
      // Generate unique filename
      const uniqueSuffix = crypto.randomBytes(16).toString('hex');
      const extension = path.extname(file.originalname);
      cb(null, `${Date.now()}-${uniqueSuffix}${extension}`);
    }
  })
};

// File filter function
const fileFilter = (fileType) => (req, file, cb) => {
  const allowedTypes = ALLOWED_FILE_TYPES[fileType] || ALLOWED_FILE_TYPES.default;
  
  if (allowedTypes.length === 0 || allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`Only ${allowedTypes.join(', ')} files are allowed!`), false);
  }
};

// Create multer upload instances
const createUploader = (fileType) => {
  return multer({
    storage: storage[fileType] || multer.memoryStorage(),
    limits: {
      fileSize: FILE_SIZE_LIMITS[fileType] || FILE_SIZE_LIMITS.default
    },
    fileFilter: fileFilter(fileType)
  });
};

// Export configuration
module.exports = {
  UPLOAD_FOLDER,
  IMAGE_FOLDER,
  DOCUMENT_FOLDER,
  FILE_SIZE_LIMITS,
  ALLOWED_FILE_TYPES,
  createUploader
};