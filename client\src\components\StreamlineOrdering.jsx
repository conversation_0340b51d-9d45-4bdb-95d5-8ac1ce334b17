import React, { useState } from 'react';

const StreamlineOrdering = () => {
  const [activeImage, setActiveImage] = useState(0);

  const menuItems = [
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
          <circle cx="11" cy="11" r="8"/>
          <path d="m21 21-4.35-4.35"/>
        </svg>
      ),
      title: 'Search for matches',
      description: 'Search and filter from millions of product and supplier offerings to find the matching ones for your business.',
      image: 'https://s.alicdn.com/@img/imgextra/i1/O1CN01KrWFW11fg52xUQzdc_!!6000000004035-0-tps-1380-1060.jpg_q60.jpg'
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="2.5" viewBox="0 0 24 24">
          <path d="M20 6L9 17l-5-5"/>
        </svg>
      ),
      title: 'Identify the right one',
      description: 'Evaluate product quality and supplier capabilities easily and efficiently through verified inspections and digital sourcing tools.',
      image: 'https://s.alicdn.com/@img/imgextra/i2/O1CN0168f1F61TkrjFojnmE_!!6000000002421-2-tps-1380-1060.png_q60.jpg'
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
          <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
          <line x1="8" y1="21" x2="16" y2="21"/>
          <line x1="12" y1="17" x2="12" y2="21"/>
        </svg>
      ),
      title: 'Pay with confidence',
      description: 'Pay for your order in all currencies via secure payment methods, including flexible payment terms.',
      image: 'https://s.alicdn.com/@img/imgextra/i1/O1CN01XW2muo1PFU87b4zQ5_!!6000000001811-2-tps-1380-1060.png_q60.jpg'
    },
     {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
          <circle cx="12" cy="7" r="4"/>
        </svg>
      ),
      title: 'Manage with ease',
      description: 'Check order status, manage suppliers, track payments and shipments, and contact after-sales support all via My Alicartify',
      image: 'https://s.alicdn.com/@img/imgextra/i1/O1CN01XW2muo1PFU87b4zQ5_!!6000000001811-2-tps-1380-1060.png_q60.jpg'
    },
  ];

  return (
    <section className="w-full bg-[#FAF5F5]">
      <div className="max-w-7xl mx-auto px-10 py-16 font-sans">
        <div className="w-full">
        <h1 className="text-4xl font-bold text-gray-800 leading-tight mb-16 max-w-xl">
          Streamline ordering from search to fulfillment, all in one place
        </h1>
        
        <div className="flex gap-20 items-start">
          <div className="flex-1 max-w-lg">
            <div className="flex flex-col relative">
              {/* Vertical connecting line */}
              <div className="absolute left-12 top-8 bottom-8 w-px bg-gray-300 z-0"></div>
              
              {menuItems.map((item, index) => (
                <div
                  key={index}
                  className={`flex items-center gap-4 p-4 cursor-pointer relative z-10 group transition-transform duration-300`}
                  onMouseEnter={() => setActiveImage(index)}
                >
                  <div className={`w-16 h-16 rounded-full flex items-center justify-center border shadow-sm transition-all duration-300 transform group-hover:bg-orange-100 group-hover:text-orange-500 group-hover:border-orange-200 ${
                    activeImage === index
                      ? 'bg-orange-100 text-orange-500 border-orange-200 scale-105'
                      : 'bg-white text-gray-400 border-gray-200 group-hover:scale-105'
                  }`}>
                    {item.icon}
                  </div>
                  <div className="flex-1">
                    <h3 className={`text-lg font-medium text-gray-800 mb-1 transition-all duration-300 transform origin-left ${
                      activeImage === index ? 'text-gray-900 scale-105' : 'group-hover:scale-105'
                    }`}>{item.title}</h3>
                    <div className={`overflow-hidden transition-all duration-300 ${
                      activeImage === index ? 'max-h-20 opacity-100' : 'max-h-0 opacity-0 group-hover:max-h-20 group-hover:opacity-100'
                    }`}>
                      <p className="text-sm text-gray-600 leading-relaxed pt-1">{item.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="flex-1 max-w-xl">
            <div className="rounded-xl overflow-hidden">
              <img
                src={menuItems[activeImage].image}
                alt={menuItems[activeImage].title}
                className="w-full h-auto"
              />
            </div>
          </div>
        </div>
        </div>
      </div>
    </section>
  );
};

export default StreamlineOrdering;
