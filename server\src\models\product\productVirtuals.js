// Product virtual properties
function addProductVirtuals(productSchema) {
  // Virtual for current price (sale price if available, otherwise base price)
  productSchema.virtual('currentPrice').get(function() {
    if (!this.pricing) return null;
    return this.pricing.salePrice || this.pricing.basePrice;
  });

  // Virtual for discount percentage
  productSchema.virtual('discountPercentage').get(function() {
    if (!this.pricing) return 0;
    if (this.pricing.salePrice && this.pricing.salePrice < this.pricing.basePrice) {
      return Math.round(((this.pricing.basePrice - this.pricing.salePrice) / this.pricing.basePrice) * 100);
    }
    return 0;
  });

  // Virtual for stock status
  productSchema.virtual('isInStock').get(function() {
    if (!this.inventory) return true;
    if (!this.inventory.trackQuantity) return true;
    return this.inventory.quantity > 0 || this.inventory.allowBackorders;
  });

  // Virtual for low stock status
  productSchema.virtual('isLowStock').get(function() {
    if (!this.inventory) return false;
    if (!this.inventory.trackQuantity) return false;
    return this.inventory.quantity <= this.inventory.lowStockThreshold && this.inventory.quantity > 0;
  });

  // Virtual for primary image
  productSchema.virtual('primaryImage').get(function() {
    if (!this.images || !Array.isArray(this.images) || this.images.length === 0) {
      return null;
    }
    const primary = this.images.find(img => img.isPrimary);
    return primary || this.images[0] || null;
  });

  // Virtual for multi-currency current price
  productSchema.virtual('multiCurrencyCurrentPrice').get(function() {
    // Check if methods exist (they might not when product is partially populated)
    if (typeof this.getAvailableCurrencies !== 'function' || 
        typeof this.getCurrentPriceInCurrency !== 'function') {
      return {};
    }
    
    try {
      const currencies = this.getAvailableCurrencies();
      const prices = {};
      
      currencies.forEach(currency => {
        const currentPrice = this.getCurrentPriceInCurrency(currency);
        if (currentPrice !== null) {
          prices[currency] = currentPrice;
        }
      });
      
      return prices;
    } catch (error) {
      // Return empty object if there's an error
      return {};
    }
  });
}

module.exports = { addProductVirtuals };
