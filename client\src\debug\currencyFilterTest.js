// Test case to debug currency filtering issue
// Run this in browser console or as a separate test

const testProduct = {
  _id: "test123",
  name: "<PERSON><PERSON>",
  description: "This is demo",
  brand: "Samsung",
  sku: "SMSNG",
  pricing: {
    basePrice: 45000,
    salePrice: 4400,
    currency: "INR"
  },
  multiCurrency: {
    INR: {
      basePrice: 45000,
      salePrice: 4400
    },
    USD: {
      basePrice: 5400,
      salePrice: 4144
    }
  },
  inventory: {
    quantity: 10
  }
};

const testProducts = [testProduct];

// Test currency filtering functions
function testFilterProductsByCurrency(products, currency) {
  console.log(`\n=== Testing filterProductsByCurrency with ${currency} ===`);
  console.log('Input products:', products);
  
  const filteredProducts = products.filter(product => {
    console.log(`\nTesting product: ${product.name}`);
    
    // Check if product has multi-currency pricing (array format)
    if (product.multiCurrencyPricing && Array.isArray(product.multiCurrencyPricing)) {
      console.log('Has multiCurrencyPricing array:', product.multiCurrencyPricing);
      const hasArrayCurrency = product.multiCurrencyPricing.some(pricing => pricing.currency === currency);
      console.log(`Array format has ${currency}:`, hasArrayCurrency);
      return hasArrayCurrency;
    }
    
    // Check if product has multi-currency pricing (object format)
    if (product.multiCurrency && typeof product.multiCurrency === 'object') {
      console.log('Has multiCurrency object:', product.multiCurrency);
      const hasObjectCurrency = product.multiCurrency.hasOwnProperty(currency);
      console.log(`Object format has ${currency}:`, hasObjectCurrency);
      if (hasObjectCurrency) return true;
    }
    
    // Fallback: if product has basic pricing, assume it's available in default currency (INR)
    if (product.pricing && (product.pricing.basePrice || product.pricing.salePrice)) {
      console.log('Has basic pricing:', product.pricing);
      const isINR = currency === 'INR';
      console.log(`Currency is INR (${currency}):`, isINR);
      if (isINR) return true;
    }
    
    console.log('Product filtered out');
    return false;
  });
  
  console.log(`Filtered results for ${currency}:`, filteredProducts);
  return filteredProducts;
}

function testGetProductPriceInCurrency(product, currency) {
  console.log(`\n=== Testing getProductPriceInCurrency with ${currency} ===`);
  console.log('Input product:', product);
  
  // Check if product has multi-currency pricing (array format)
  if (product.multiCurrencyPricing && Array.isArray(product.multiCurrencyPricing)) {
    console.log('Checking multiCurrencyPricing array:', product.multiCurrencyPricing);
    const currencyPricing = product.multiCurrencyPricing.find(pricing => pricing.currency === currency);
    if (currencyPricing) {
      const result = {
        basePrice: currencyPricing.basePrice,
        salePrice: currencyPricing.salePrice,
        currency: currencyPricing.currency
      };
      console.log('Array format result:', result);
      return result;
    }
  }
  
  // Check if product has multi-currency pricing (object format)
  if (product.multiCurrency && typeof product.multiCurrency === 'object' && product.multiCurrency[currency]) {
    console.log('Checking multiCurrency object:', product.multiCurrency);
    const currencyPricing = product.multiCurrency[currency];
    console.log(`Found ${currency} pricing:`, currencyPricing);
    const result = {
      basePrice: currencyPricing.basePrice,
      salePrice: currencyPricing.salePrice,
      currency: currency
    };
    console.log('Object format result:', result);
    return result;
  }
  
  // Fallback to default pricing (assuming INR)
  if (currency === 'INR' && product.pricing) {
    console.log('Using fallback INR pricing:', product.pricing);
    const result = {
      basePrice: product.pricing.basePrice,
      salePrice: product.pricing.salePrice,
      currency: 'INR'
    };
    console.log('Fallback result:', result);
    return result;
  }
  
  console.log('No pricing found for currency:', currency);
  return null;
}

// Run tests
console.log('=== CURRENCY FILTER DEBUG TEST ===');
console.log('Test product:', testProduct);

// Test filtering with INR
const inrFiltered = testFilterProductsByCurrency(testProducts, 'INR');
console.log('\nINR filtered count:', inrFiltered.length);

// Test filtering with USD
const usdFiltered = testFilterProductsByCurrency(testProducts, 'USD');
console.log('\nUSD filtered count:', usdFiltered.length);

// Test price retrieval
const inrPrice = testGetProductPriceInCurrency(testProduct, 'INR');
console.log('\nINR price:', inrPrice);

const usdPrice = testGetProductPriceInCurrency(testProduct, 'USD');
console.log('\nUSD price:', usdPrice);

// Export for use in React components
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testProduct,
    testProducts,
    testFilterProductsByCurrency,
    testGetProductPriceInCurrency
  };
}

// For browser console usage
if (typeof window !== 'undefined') {
  window.currencyDebug = {
    testProduct,
    testProducts,
    testFilterProductsByCurrency,
    testGetProductPriceInCurrency
  };
  console.log('Debug functions available as window.currencyDebug');
}
