const twilio = require('twilio');
const { parsePhoneNumber, getCountryCallingCode } = require('libphonenumber-js');

class TwilioService {
  constructor() {
    this.client = null;
    this.from = null;
    this.initialize();
  }

  initialize() {
    try {
      const accountSid = process.env.TWILIO_ACCOUNT_SID;
      const authToken = process.env.TWILIO_AUTH_TOKEN;
      this.from = process.env.TWILIO_PHONE_NUMBER;

      // Check if credentials are placeholder values or missing
      if (!accountSid || !authToken || !this.from || 
          accountSid === 'your-twilio-account-sid' ||
          authToken === 'your-twilio-auth-token' ||
          this.from === 'your-twilio-phone-number' ||
          !accountSid.startsWith('AC')) {
        console.warn('🔔 Twilio credentials not configured or invalid. OTP functionality will be disabled.');
        return;
      }

      this.client = twilio(accountSid, authToken);
      console.log('✅ Twilio service initialized successfully');
    } catch (error) {
      console.error('❌ Error initializing Twilio service:', error);
    }
  }

  isConfigured() {
    return this.client !== null && this.from !== null;
  }

  async sendOTP(phone, otp, countryCode = 'US') {
    try {
      if (!this.isConfigured()) {
        console.warn('🔔 Twilio not configured. Skipping OTP send.');
        // In development, log the OTP for testing
        if (process.env.NODE_ENV === 'development') {
          console.log(`📱 [DEV MODE] OTP for ${phone} (${countryCode}): ${otp}`);
        }
        return { success: false, message: 'SMS service not configured' };
      }

      // Format phone number using libphonenumber-js
      const formattedPhone = this.formatPhoneNumber(phone, countryCode);
      
      if (!formattedPhone) {
        throw new Error('Invalid phone number format');
      }
      
      const message = await this.client.messages.create({
        body: `Your verification code is: ${otp}. This code will expire in 5 minutes. Do not share this code with anyone.`,
        from: this.from,
        to: formattedPhone
      });

      console.log(`✅ OTP sent successfully to ${formattedPhone}, SID: ${message.sid}`);
      
      return {
        success: true,
        messageSid: message.sid,
        to: formattedPhone
      };

    } catch (error) {
      console.error('❌ Error sending OTP via Twilio:', error);
      
      // In development, still log the OTP for testing even if Twilio fails
      if (process.env.NODE_ENV === 'development') {
        console.log(`📱 [DEV MODE FALLBACK] OTP for ${phone} (${countryCode}): ${otp}`);
      }
      
      return {
        success: false,
        error: error.message,
        code: error.code
      };
    }
  }

  formatPhoneNumber(phone, countryCode = 'US') {
    try {
      // If phone already starts with +, try to parse it directly
      if (phone.startsWith('+')) {
        const phoneNumber = parsePhoneNumber(phone);
        return phoneNumber.isValid() ? phoneNumber.format('E.164') : null;
      }

      // Clean the phone number (remove all non-digits)
      const cleanPhone = phone.replace(/\D/g, '');
      
      // Try parsing with country code first
      try {
        const phoneNumber = parsePhoneNumber(cleanPhone, countryCode);
        if (phoneNumber && phoneNumber.isValid()) {
          return phoneNumber.format('E.164');
        }
      } catch (parseError) {
        // If parsing fails, try manual formatting
        console.log('Direct parsing failed, trying manual formatting...');
      }
      
      // Fallback: Try adding the country calling code manually
      try {
        const callingCode = getCountryCallingCode(countryCode);
        const fullNumber = `+${callingCode}${cleanPhone}`;
        const parsedFull = parsePhoneNumber(fullNumber);
        return parsedFull && parsedFull.isValid() ? parsedFull.format('E.164') : null;
      } catch (fallbackError) {
        console.error('❌ Fallback formatting also failed:', fallbackError);
        return null;
      }
      
    } catch (error) {
      console.error('❌ Error formatting phone number:', error);
      return null;
    }
  }

  async sendLoginAlert(phone, countryCode, userInfo) {
    try {
      if (!this.isConfigured()) {
        console.warn('🔔 Twilio not configured. Skipping login alert.');
        return { success: false, message: 'SMS service not configured' };
      }

      const formattedPhone = this.formatPhoneNumber(phone, countryCode);
      
      if (!formattedPhone) {
        throw new Error('Invalid phone number format for login alert');
      }
      
      const timestamp = new Date().toLocaleString();
      
      const message = await this.client.messages.create({
        body: `Security Alert: Login detected on your ${userInfo.userType} account at ${timestamp}. If this wasn't you, please secure your account immediately.`,
        from: this.from,
        to: formattedPhone
      });

      console.log(`✅ Login alert sent successfully to ${formattedPhone}, SID: ${message.sid}`);
      
      return {
        success: true,
        messageSid: message.sid,
        to: formattedPhone
      };

    } catch (error) {
      console.error('❌ Error sending login alert via Twilio:', error);
      
      return {
        success: false,
        error: error.message,
        code: error.code
      };
    }
  }

  async validatePhoneNumber(phone, countryCode = 'US') {
    try {
      // Clean the phone number
      const cleanPhone = phone.replace(/\D/g, '');
      
      // Try to format the phone number first
      const formattedPhone = this.formatPhoneNumber(phone, countryCode);
      if (!formattedPhone) {
        return {
          success: false,
          valid: false,
          error: 'Invalid phone number format'
        };
      }
      
      // Parse the formatted phone number
      const phoneNumber = parsePhoneNumber(formattedPhone);
      
      if (!phoneNumber || !phoneNumber.isValid()) {
        return {
          success: false,
          valid: false,
          error: 'Invalid phone number format'
        };
      }

      const finalFormattedPhone = phoneNumber.format('E.164');

      // If Twilio is configured, also validate with Twilio Lookup API
      if (this.isConfigured()) {
        try {
          const twilioLookup = await this.client.lookups.v1
            .phoneNumbers(finalFormattedPhone)
            .fetch();

          return {
            success: true,
            valid: true,
            phoneNumber: twilioLookup.phoneNumber,
            nationalFormat: twilioLookup.nationalFormat,
            countryCode: twilioLookup.countryCode,
            carrier: twilioLookup.carrier
          };
        } catch (twilioError) {
          // If Twilio lookup fails, still return libphonenumber validation
          console.warn('Twilio lookup failed, using libphonenumber validation only:', twilioError.message);
        }
      }

      return {
        success: true,
        valid: true,
        phoneNumber: finalFormattedPhone,
        nationalFormat: phoneNumber.formatNational(),
        countryCode: phoneNumber.country,
        internationalFormat: phoneNumber.formatInternational()
      };

    } catch (error) {
      console.error('❌ Error validating phone number:', error);
      
      return {
        success: false,
        valid: false,
        error: error.message
      };
    }
  }

  // Helper method to get country calling code
  getCountryCallingCode(countryCode) {
    try {
      return getCountryCallingCode(countryCode);
    } catch (error) {
      console.error(`❌ Error getting calling code for ${countryCode}:`, error);
      return null;
    }
  }

  // Helper method to format phone for display
  formatPhoneForDisplay(phone, countryCode = 'US') {
    try {
      const phoneNumber = parsePhoneNumber(phone, countryCode);
      return phoneNumber && phoneNumber.isValid() ? phoneNumber.formatInternational() : phone;
    } catch (error) {
      return phone;
    }
  }

  // Send welcome SMS for new registrations
  async sendWelcomeSMS(phone, countryCode, userName, userType = 'customer') {
    try {
      if (!this.isConfigured()) {
        console.warn('🔔 Twilio not configured. Skipping welcome SMS.');
        return { success: false, message: 'SMS service not configured' };
      }

      const formattedPhone = this.formatPhoneNumber(phone, countryCode);
      
      if (!formattedPhone) {
        throw new Error('Invalid phone number format for welcome SMS');
      }

      let welcomeMessage = `Welcome to our platform, ${userName}! `;
      
      if (userType === 'vendor') {
        welcomeMessage += 'Your vendor account has been created successfully. You can now start listing your products.';
      } else {
        welcomeMessage += 'Your account has been created successfully. Start exploring and shopping with us!';
      }
      
      const message = await this.client.messages.create({
        body: welcomeMessage,
        from: this.from,
        to: formattedPhone
      });

      console.log(`✅ Welcome SMS sent successfully to ${formattedPhone}, SID: ${message.sid}`);
      
      return {
        success: true,
        messageSid: message.sid,
        to: formattedPhone
      };

    } catch (error) {
      console.error('❌ Error sending welcome SMS:', error);
      
      return {
        success: false,
        error: error.message,
        code: error.code
      };
    }
  }
}

module.exports = new TwilioService();
