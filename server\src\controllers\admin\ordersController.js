const { Order, User, Vendor, Product, OrderTracking } = require('../../models');

// Get all orders with pagination and filtering
const getOrders = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      paymentStatus = '',
      dateFrom = '',
      dateTo = '',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build filter object
    const filter = {};
    
    if (search) {
      filter.$or = [
        { orderNumber: { $regex: search, $options: 'i' } },
        { 'billing.email': { $regex: search, $options: 'i' } },
        { 'billing.firstName': { $regex: search, $options: 'i' } },
        { 'billing.lastName': { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      filter.status = status;
    }
    
    if (paymentStatus) {
      filter['payment.status'] = paymentStatus;
    }

    if (dateFrom || dateTo) {
      filter.createdAt = {};
      if (dateFrom) filter.createdAt.$gte = new Date(dateFrom);
      if (dateTo) filter.createdAt.$lte = new Date(dateTo);
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get orders with pagination
    const [orders, totalOrders] = await Promise.all([
      Order.find(filter)
        .populate('customer', 'firstName lastName email')
        .populate('items.vendor', 'businessName')
        .sort(sort)
        .skip(skip)
        .limit(parseInt(limit))
        .lean(),
      Order.countDocuments(filter)
    ]);

    const totalPages = Math.ceil(totalOrders / parseInt(limit));

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalOrders,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    console.error('Get orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch orders',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get order by ID
const getOrderById = async (req, res) => {
  try {
    const { id } = req.params;

    const order = await Order.findById(id)
      .populate('customer', 'firstName lastName email phone')
      .populate('items.vendor', 'businessName contactInfo.businessEmail contactInfo.businessPhone')
      .populate('items.product', 'name images')
      .populate('timeline.updatedBy', 'firstName lastName email')
      .populate('notes.author', 'firstName lastName email');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    res.json({
      success: true,
      data: { order }
    });

  } catch (error) {
    console.error('Get order by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch order details',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update order status
const updateOrderStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, note } = req.body;
    const adminId = req.user.id; // Assuming admin is authenticated

    const validStatuses = [
      'pending', 'confirmed', 'processing', 'shipped', 
      'delivered', 'cancelled', 'returned', 'refunded'
    ];

    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid order status'
      });
    }

    const order = await Order.findById(id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Update order status using the instance method
    await order.updateStatus(status, note, adminId);

    // Update payment status based on order status
    if (status === 'delivered') {
      order.payment.status = 'completed';
      order.payment.paidAt = new Date();
    } else if (status === 'cancelled' || status === 'refunded') {
      order.payment.status = status === 'refunded' ? 'refunded' : 'cancelled';
    }

    await order.save();

    // Update order tracking if it exists
    try {
      const tracking = await OrderTracking.findOne({ order: id });
      if (tracking) {
        // Map order status to tracking status
        const statusMapping = {
          'pending': 'order_confirmed',
          'confirmed': 'order_confirmed',
          'processing': 'processing',
          'shipped': 'shipped',
          'delivered': 'delivered',
          'cancelled': 'cancelled',
          'returned': 'returned'
        };

        const trackingStatus = statusMapping[status] || status;
        await tracking.updateStatus(trackingStatus, {
          title: `Order ${status.charAt(0).toUpperCase() + status.slice(1)}`,
          description: note || `Order status updated to ${status}`,
          updatedBy: { type: 'admin', id: adminId }
        });
      }
    } catch (trackingError) {
      console.error('Error updating order tracking:', trackingError);
      // Don't fail the main operation if tracking update fails
    }

    // Populate the updated order
    const updatedOrder = await Order.findById(id)
      .populate('customer', 'firstName lastName email')
      .populate('items.vendor', 'businessName')
      .populate('timeline.updatedBy', 'firstName lastName email');

    res.json({
      success: true,
      message: 'Order status updated successfully',
      data: { order: updatedOrder }
    });

  } catch (error) {
    console.error('Update order status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update order status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update payment status
const updatePaymentStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { paymentStatus, transactionId, refundAmount } = req.body;

    const validPaymentStatuses = [
      'pending', 'processing', 'completed', 'failed', 
      'cancelled', 'refunded', 'partially_refunded'
    ];

    if (!validPaymentStatuses.includes(paymentStatus)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid payment status'
      });
    }

    const updateData = {
      'payment.status': paymentStatus
    };

    if (transactionId) {
      updateData['payment.transactionId'] = transactionId;
    }

    if (paymentStatus === 'completed') {
      updateData['payment.paidAt'] = new Date();
    } else if (paymentStatus === 'refunded' || paymentStatus === 'partially_refunded') {
      updateData['payment.refundedAt'] = new Date();
      if (refundAmount) {
        updateData['payment.refundAmount'] = refundAmount;
      }
    }

    const order = await Order.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    ).populate('customer', 'firstName lastName email')
     .populate('items.vendor', 'businessName');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    res.json({
      success: true,
      message: 'Payment status updated successfully',
      data: { order }
    });

  } catch (error) {
    console.error('Update payment status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update payment status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Add order note
const addOrderNote = async (req, res) => {
  try {
    const { id } = req.params;
    const { message, isPrivate = false } = req.body;
    const adminId = req.user.id;

    const order = await Order.findById(id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    await order.addNote('admin', message, adminId, isPrivate);

    const updatedOrder = await Order.findById(id)
      .populate('notes.author', 'firstName lastName email');

    res.json({
      success: true,
      message: 'Note added successfully',
      data: { order: updatedOrder }
    });

  } catch (error) {
    console.error('Add order note error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add note',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update shipping information
const updateShippingInfo = async (req, res) => {
  try {
    const { id } = req.params;
    const { trackingNumber, carrier, estimatedDelivery } = req.body;

    const updateData = {};
    if (trackingNumber) updateData['shipping.trackingNumber'] = trackingNumber;
    if (carrier) updateData['shipping.carrier'] = carrier;
    if (estimatedDelivery) updateData['shipping.estimatedDelivery'] = new Date(estimatedDelivery);

    const order = await Order.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    ).populate('customer', 'firstName lastName email')
     .populate('items.vendor', 'businessName');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    res.json({
      success: true,
      message: 'Shipping information updated successfully',
      data: { order }
    });

  } catch (error) {
    console.error('Update shipping info error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update shipping information',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get order statistics
const getOrderStatistics = async (req, res) => {
  try {
    const stats = await Order.getStatistics();
    
    // Get recent orders trends (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const orderTrends = await Order.getOrdersByDateRange(thirtyDaysAgo, new Date());
    
    // Get top customers
    const topCustomers = await Order.getTopCustomers(10);

    // Get order status distribution over time
    const statusTrends = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            date: {
              $dateToString: {
                format: '%Y-%m-%d',
                date: '$createdAt'
              }
            },
            status: '$status'
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.date': 1 } }
    ]);

    // Get payment method distribution
    const paymentMethods = await Order.aggregate([
      {
        $group: {
          _id: '$payment.method',
          count: { $sum: 1 },
          totalAmount: { $sum: '$pricing.total' }
        }
      }
    ]);

    // Get average order processing time
    const processingTimes = await Order.aggregate([
      {
        $match: {
          status: 'delivered',
          'timeline.status': { $in: ['confirmed', 'delivered'] }
        }
      },
      {
        $addFields: {
          confirmedTime: {
            $arrayElemAt: [
              {
                $filter: {
                  input: '$timeline',
                  cond: { $eq: ['$$this.status', 'confirmed'] }
                }
              },
              0
            ]
          },
          deliveredTime: {
            $arrayElemAt: [
              {
                $filter: {
                  input: '$timeline',
                  cond: { $eq: ['$$this.status', 'delivered'] }
                }
              },
              -1
            ]
          }
        }
      },
      {
        $addFields: {
          processingTime: {
            $divide: [
              { $subtract: ['$deliveredTime.timestamp', '$confirmedTime.timestamp'] },
              1000 * 60 * 60 * 24 // Convert to days
            ]
          }
        }
      },
      {
        $group: {
          _id: null,
          averageProcessingTime: { $avg: '$processingTime' },
          minProcessingTime: { $min: '$processingTime' },
          maxProcessingTime: { $max: '$processingTime' }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        overview: stats,
        trends: orderTrends,
        statusTrends,
        topCustomers,
        paymentMethods,
        processingTimes: processingTimes[0] || {
          averageProcessingTime: 0,
          minProcessingTime: 0,
          maxProcessingTime: 0
        }
      }
    });

  } catch (error) {
    console.error('Get order statistics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch order statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Bulk update orders
const bulkUpdateOrders = async (req, res) => {
  try {
    const { orderIds, updates } = req.body;
    const adminId = req.user.id;

    if (!Array.isArray(orderIds) || orderIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Order IDs array is required'
      });
    }

    let modifiedCount = 0;

    // If updating status, use the updateStatus method for each order
    if (updates.status) {
      for (const orderId of orderIds) {
        const order = await Order.findById(orderId);
        if (order) {
          await order.updateStatus(updates.status, updates.note || 'Bulk update', adminId);
          modifiedCount++;
        }
      }
    } else {
      // For other updates, use updateMany
      const result = await Order.updateMany(
        { _id: { $in: orderIds } },
        updates,
        { runValidators: true }
      );
      modifiedCount = result.modifiedCount;
    }

    res.json({
      success: true,
      message: `${modifiedCount} orders updated successfully`,
      data: {
        modifiedCount
      }
    });

  } catch (error) {
    console.error('Bulk update orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update orders',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Export orders data
const exportOrders = async (req, res) => {
  try {
    const {
      format = 'json',
      status = '',
      dateFrom = '',
      dateTo = '',
      limit = 1000
    } = req.query;

    // Build filter
    const filter = {};
    if (status) filter.status = status;
    if (dateFrom || dateTo) {
      filter.createdAt = {};
      if (dateFrom) filter.createdAt.$gte = new Date(dateFrom);
      if (dateTo) filter.createdAt.$lte = new Date(dateTo);
    }

    const orders = await Order.find(filter)
      .populate('customer', 'firstName lastName email')
      .populate('items.vendor', 'businessName')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .lean();

    if (format === 'csv') {
      // Convert to CSV format
      const csvHeaders = [
        'Order Number', 'Customer Name', 'Customer Email', 'Status',
        'Payment Status', 'Total Amount', 'Created Date', 'Items Count'
      ];

      const csvData = orders.map(order => [
        order.orderNumber,
        `${order.customer?.firstName || ''} ${order.customer?.lastName || ''}`.trim(),
        order.customer?.email || '',
        order.status,
        order.payment.status,
        order.pricing.total,
        order.createdAt.toISOString(),
        order.items.length
      ]);

      const csvContent = [csvHeaders, ...csvData]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n');

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=orders.csv');
      res.send(csvContent);
    } else {
      // Return JSON format
      res.json({
        success: true,
        data: {
          orders,
          exportedAt: new Date(),
          totalExported: orders.length
        }
      });
    }

  } catch (error) {
    console.error('Export orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export orders',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getOrders,
  getOrderById,
  updateOrderStatus,
  updatePaymentStatus,
  addOrderNote,
  updateShippingInfo,
  getOrderStatistics,
  bulkUpdateOrders,
  exportOrders
};