import React from 'react';
import { 
  EnvironmentOutlined, 
  HomeOutlined, 
  BankOutlined, 
  GlobalOutlined 
} from '@ant-design/icons';
import Profile<PERSON>orm<PERSON>ield from './ProfileFormField';

const AddressInfoSection = ({ user, editData, isEditing, onInputChange }) => {
  const countryOptions = [
    { value: 'United States', label: 'United States' },
    { value: 'Canada', label: 'Canada' },
    { value: 'United Kingdom', label: 'United Kingdom' },
    { value: 'Australia', label: 'Australia' },
    { value: 'Germany', label: 'Germany' },
    { value: 'France', label: 'France' },
    { value: 'Japan', label: 'Japan' },
    { value: 'India', label: 'India' },
    { value: 'Brazil', label: 'Brazil' },
    { value: 'Mexico', label: 'Mexico' },
    { value: 'Spain', label: 'Spain' },
    { value: 'Italy', label: 'Italy' },
    { value: 'Netherlands', label: 'Netherlands' },
    { value: 'Sweden', label: 'Sweden' },
    { value: 'Norway', label: 'Norway' }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-900 flex items-center space-x-2">
          <EnvironmentOutlined className="text-orange-500" />
          <span>Address Information</span>
        </h2>
      </div>
      <div className="px-6 py-4 space-y-4">
        <ProfileFormField
          label="Street Address"
          value={editData.address}
          displayValue={user.address}
          isEditing={isEditing}
          onChange={(value) => onInputChange('address', value)}
          placeholder="Enter your street address"
          icon={HomeOutlined}
        />
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <ProfileFormField
            label="City"
            value={editData.city}
            displayValue={user.city}
            isEditing={isEditing}
            onChange={(value) => onInputChange('city', value)}
            placeholder="City"
            icon={BankOutlined}
          />
          <ProfileFormField
            label="State/Province"
            value={editData.state}
            displayValue={user.state}
            isEditing={isEditing}
            onChange={(value) => onInputChange('state', value)}
            placeholder="State/Province"
            icon={EnvironmentOutlined}
          />
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <ProfileFormField
            label="ZIP/Postal Code"
            value={editData.zipCode}
            displayValue={user.zipCode}
            isEditing={isEditing}
            onChange={(value) => onInputChange('zipCode', value)}
            placeholder="ZIP/Postal Code"
            icon={EnvironmentOutlined}
          />
          <ProfileFormField
            label="Country"
            value={editData.country}
            displayValue={user.country}
            isEditing={isEditing}
            onChange={(value) => onInputChange('country', value)}
            type="select"
            options={countryOptions}
            icon={GlobalOutlined}
          />
        </div>
      </div>
    </div>
  );
};

export default AddressInfoSection;
