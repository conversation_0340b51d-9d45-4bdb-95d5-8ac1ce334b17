const express = require('express');
const router = express.Router();

// Import middleware
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');
const imageUpload = require('../../middleware/upload/imageUpload');

// Import controller
const {
  getCategories,
  getCategoryTree,
  getCategory,
  createCategory,
  updateCategory,
  deleteCategory,
  getCategoryStats
} = require('../../controllers/admin/categoryController');

// Apply authentication middleware to all routes
router.use(verifyToken);
router.use(requireUserType(['admin']));

/**
 * @route   GET /api/admin/categories/stats
 * @desc    Get category statistics
 * @access  Private (Admin)
 */
router.get('/stats', getCategoryStats);

/**
 * @route   GET /api/admin/categories/tree
 * @desc    Get category tree structure
 * @access  Private (Admin)
 */
router.get('/tree', getCategoryTree);

/**
 * @route   GET /api/admin/categories
 * @desc    Get all categories with pagination and filters
 * @access  Private (Admin)
 */
router.get('/', getCategories);

/**
 * @route   GET /api/admin/categories/:id
 * @desc    Get single category by ID
 * @access  Private (Admin)
 */
router.get('/:id', getCategory);

/**
 * @route   POST /api/admin/categories
 * @desc    Create new category
 * @access  Private (Admin)
 */
router.post(
  '/',
  imageUpload.single('categoryImage'),
  createCategory
);

/**
 * @route   PUT /api/admin/categories/:id
 * @desc    Update category
 * @access  Private (Admin)
 */
router.put(
  '/:id',
  imageUpload.single('categoryImage'),
  updateCategory
);

/**
 * @route   DELETE /api/admin/categories/:id
 * @desc    Delete category
 * @access  Private (Admin)
 */
router.delete('/:id', deleteCategory);

module.exports = router;
