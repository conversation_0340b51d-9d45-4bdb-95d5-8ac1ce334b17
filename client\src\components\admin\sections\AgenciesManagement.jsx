import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Input,
  Typography,
  Table,
  Space,
  Tag,
  Avatar,
  message
} from 'antd';
import {
  SearchOutlined,
  ShopOutlined,
  EyeOutlined
} from '@ant-design/icons';

// Import sub-components
import AgencyVendorsModal from './agencies/AgencyVendorsModal';
import { useAgencyActions } from './agencies/useAgencyActions';

const { Title } = Typography;

const AgenciesManagement = () => {
  const [agencies, setAgencies] = useState([]);
  const [vendorsModalVisible, setVendorsModalVisible] = useState(false);
  const [selectedAgency, setSelectedAgency] = useState(null);
  const [agencyVendors, setAgencyVendors] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // Use custom hook for agency actions
  const {
    loading,
    fetchAgencies: apiAgencies,
    fetchAgencyVendors
  } = useAgencyActions();

  useEffect(() => {
    loadData();
  }, [pagination.current, pagination.pageSize]);

  const loadData = async () => {
    // Fetch agencies
    const agenciesResult = await apiAgencies(pagination);
    setAgencies(agenciesResult.agencies || agenciesResult || []);
    if (agenciesResult.pagination) {
      setPagination(agenciesResult.pagination);
    }
  };

  const handleTableChange = (paginationData, tableFilters, sorter) => {
    setPagination(prev => ({
      ...prev,
      current: paginationData.current,
      pageSize: paginationData.pageSize
    }));
  };

  const handleViewAgencyVendors = async (agency) => {
    setSelectedAgency(agency);
    const vendorsResult = await fetchAgencyVendors(agency._id || agency.id);
    setAgencyVendors(vendorsResult || []);
    setVendorsModalVisible(true);
  };

  const handleVendorsModalCancel = () => {
    setVendorsModalVisible(false);
    setSelectedAgency(null);
    setAgencyVendors([]);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'pending_approval':
        return 'orange';
      case 'suspended':
        return 'red';
      case 'inactive':
        return 'default';
      default:
        return 'default';
    }
  };

  const getVerificationStatusColor = (status) => {
    switch (status) {
      case 'verified':
        return 'green';
      case 'pending':
        return 'orange';
      case 'rejected':
        return 'red';
      case 'suspended':
        return 'red';
      default:
        return 'default';
    }
  };

  // Filter agencies based on search text
  const filteredAgencies = agencies.filter(agency =>
    (agency.businessName || '').toLowerCase().includes(searchText.toLowerCase()) ||
    (agency.contactPerson || '').toLowerCase().includes(searchText.toLowerCase()) ||
    (agency.email || '').toLowerCase().includes(searchText.toLowerCase())
  );

  const columns = [
    {
      title: 'Agency',
      key: 'agency',
      render: (record) => (
        <Space>
          <Avatar icon={<ShopOutlined />} style={{ backgroundColor: '#1890ff' }} />
          <div>
            <div style={{ fontWeight: 500 }}>{record.businessName}</div>
            <div style={{ color: '#666', fontSize: '12px' }}>
              {record.user?.firstName} {record.user?.lastName}
            </div>
            <div style={{ color: '#666', fontSize: '12px' }}>
              {record.user?.email || record.contactInfo?.email}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: 'Status',
      key: 'status',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Tag color={getStatusColor(record.status)}>
            {record.status?.replace('_', ' ').toUpperCase()}
          </Tag>
          <Tag color={getVerificationStatusColor(record.verification?.status)} size="small">
            {record.verification?.status?.toUpperCase()}
          </Tag>
        </Space>
      ),
    },
    {
      title: 'Total Vendors',
      key: 'vendors',
      align: 'center',
      render: (_, record) => record.totalVendors || 0,
    },
    {
      title: 'Total Revenue',
      key: 'revenue',
      render: (_, record) => `$${record.totalRevenue || 0}`,
    },
    {
      title: 'Commission Rate',
      key: 'commission',
      render: (_, record) => `${record.commission?.rate || 0}%`,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Button
          type="primary"
          size="small"
          icon={<EyeOutlined />}
          onClick={() => handleViewAgencyVendors(record)}
        >
          View Vendors
        </Button>
      ),
    },
  ];

  return (
    <div style={{ 
      padding: '0 16px',
      maxWidth: '100%',
      overflow: 'hidden'
    }}>
      <Title level={2} style={{ 
        marginBottom: '16px',
        fontSize: 'clamp(1.5rem, 4vw, 2rem)'
      }}>Agencies Management</Title>

      <Card>
        <div style={{ 
          marginBottom: 16, 
          display: 'flex', 
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'center',
          gap: '12px',
          flexWrap: 'wrap'
        }}>
          <Input
            placeholder="Search agencies..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ 
              flex: '1',
              minWidth: '200px',
              maxWidth: '400px'
            }}
          />
        </div>

        <Table
          columns={columns}
          dataSource={filteredAgencies}
          rowKey={(record) => record._id || record.id}
          loading={loading}
          scroll={{ x: 800 }}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} agencies`,
            responsive: true,
          }}
          onChange={handleTableChange}
        />
      </Card>

      <AgencyVendorsModal
        visible={vendorsModalVisible}
        agency={selectedAgency}
        vendors={agencyVendors}
        onCancel={handleVendorsModalCancel}
      />
    </div>
  );
};

export default AgenciesManagement;
