const mongoose = require('mongoose');

const payoutSchema = new mongoose.Schema({
  vendor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vendor',
    required: true
  },
  requestedAmount: {
    type: Number,
    required: true,
    min: [1, 'Payout amount must be greater than 0']
  },
  availableAmount: {
    type: Number,
    required: true
  },
  method: {
    type: String,
    enum: ['bank_transfer', 'paypal', 'stripe', 'check'],
    required: true
  },
  bankDetails: {
    accountHolderName: String,
    bankName: String,
    accountNumber: String,
    routingNumber: String,
    accountType: {
      type: String,
      enum: ['checking', 'savings']
    }
  },
  paypalEmail: String,
  status: {
    type: String,
    enum: ['pending', 'approved', 'processing', 'completed', 'rejected', 'failed'],
    default: 'pending'
  },
  requestDate: {
    type: Date,
    default: Date.now
  },
  processedDate: Date,
  completedDate: Date,
  transactionId: String,
  adminNotes: String,
  rejectionReason: String,
  processingFee: {
    type: Number,
    default: 0
  },
  netAmount: {
    type: Number,
    required: true
  },
  processedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  metadata: {
    vendorBusinessName: String,
    vendorEmail: String,
    requestIpAddress: String,
    userAgent: String
  },
  documents: [{
    type: String,
    url: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
payoutSchema.index({ vendor: 1 });
payoutSchema.index({ status: 1 });
payoutSchema.index({ requestDate: -1 });
payoutSchema.index({ 'vendor': 1, 'status': 1 });

// Virtual for processing time
payoutSchema.virtual('processingTime').get(function() {
  if (!this.processedDate || !this.requestDate) return null;
  return Math.ceil((this.processedDate - this.requestDate) / (1000 * 60 * 60 * 24)); // days
});

// Virtual for formatted amount
payoutSchema.virtual('formattedAmount').get(function() {
  return `$${this.requestedAmount.toFixed(2)}`;
});

// Static method to get pending payouts for admin
payoutSchema.statics.getPendingPayouts = function(limit = 20, page = 1) {
  const skip = (page - 1) * limit;
  return this.find({ status: 'pending' })
    .populate('vendor', 'businessName contactInfo.businessEmail user')
    .populate('vendor.user', 'firstName lastName email')
    .sort({ requestDate: 1 })
    .skip(skip)
    .limit(limit);
};

// Static method to get payout statistics
payoutSchema.statics.getPayoutStats = async function() {
  const stats = await this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalAmount: { $sum: '$requestedAmount' }
      }
    }
  ]);

  const result = {
    pending: { count: 0, amount: 0 },
    approved: { count: 0, amount: 0 },
    processing: { count: 0, amount: 0 },
    completed: { count: 0, amount: 0 },
    rejected: { count: 0, amount: 0 },
    failed: { count: 0, amount: 0 }
  };

  stats.forEach(stat => {
    if (result[stat._id]) {
      result[stat._id] = {
        count: stat.count,
        amount: stat.totalAmount
      };
    }
  });

  return result;
};

// Instance method to approve payout
payoutSchema.methods.approve = function(adminId, notes = '') {
  this.status = 'approved';
  this.processedDate = new Date();
  this.processedBy = adminId;
  this.adminNotes = notes;
  return this.save();
};

// Instance method to reject payout
payoutSchema.methods.reject = function(adminId, reason, notes = '') {
  this.status = 'rejected';
  this.processedDate = new Date();
  this.processedBy = adminId;
  this.rejectionReason = reason;
  this.adminNotes = notes;
  return this.save();
};

// Instance method to mark as processing
payoutSchema.methods.startProcessing = function(transactionId = null) {
  this.status = 'processing';
  if (transactionId) {
    this.transactionId = transactionId;
  }
  return this.save();
};

// Instance method to complete payout
payoutSchema.methods.complete = function(transactionId) {
  this.status = 'completed';
  this.completedDate = new Date();
  this.transactionId = transactionId;
  return this.save();
};

// Instance method to mark as failed
payoutSchema.methods.markAsFailed = function(reason) {
  this.status = 'failed';
  this.rejectionReason = reason;
  return this.save();
};

// Pre-save middleware to calculate net amount
payoutSchema.pre('save', function(next) {
  if (this.isModified('requestedAmount') || this.isModified('processingFee')) {
    this.netAmount = this.requestedAmount - (this.processingFee || 0);
  }
  next();
});

module.exports = mongoose.model('Payout', payoutSchema);
