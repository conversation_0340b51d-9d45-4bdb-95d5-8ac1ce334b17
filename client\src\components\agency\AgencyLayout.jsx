import React, { useState, useEffect } from 'react';
import {
  ShopOutlined,
  ShoppingCartOutlined,
  ShoppingOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined
} from '@ant-design/icons';
import {
  Layout,
  Menu,
  theme,
  Button,
  Avatar,
  Dropdown,
  Space,
  Typography,
  Drawer
} from 'antd';
import { useAuth } from '../../hooks/useAuth';

const { Header, Content, Footer, Sider } = Layout;
const { Text } = Typography;

// Responsive breakpoints
const BREAKPOINTS = {
  xs: 480,   // Extra small devices (phones)
  sm: 576,   // Small devices (landscape phones)
  md: 768,   // Medium devices (tablets)
  lg: 992,   // Large devices (desktops)
  xl: 1200,  // Extra large devices (large desktops)
  xxl: 1600  // Extra extra large devices
};

// Menu items configuration for agency
const menuItems = [
  { key: 'vendors', icon: <ShopOutlined />, label: 'Vendors' },
  { key: 'orders', icon: <ShoppingCartOutlined />, label: 'Orders' },
  { key: 'products', icon: <ShoppingOutlined />, label: 'Products' },
];

const AgencyLayout = ({ children, activeKey = 'vendors', onMenuSelect }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerVisible, setMobileDrawerVisible] = useState(false);
  const [screenSize, setScreenSize] = useState('lg');
  const { user, logout } = useAuth();
  const { token: { colorBgContainer, borderRadiusLG } } = theme.useToken();

  // Determine screen size and device type
  const getScreenSize = (width) => {
    if (width < BREAKPOINTS.xs) return 'xs';
    if (width < BREAKPOINTS.sm) return 'sm';
    if (width < BREAKPOINTS.md) return 'md';
    if (width < BREAKPOINTS.lg) return 'lg';
    if (width < BREAKPOINTS.xl) return 'xl';
    return 'xxl';
  };

  const isMobile = ['xs', 'sm'].includes(screenSize);
  const isTablet = screenSize === 'md';
  const isSmallScreen = ['xs', 'sm', 'md'].includes(screenSize);

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const newScreenSize = getScreenSize(width);
      setScreenSize(newScreenSize);
      
      // Auto-collapse sidebar on smaller screens
      if (newScreenSize === 'xs' || newScreenSize === 'sm') {
        setCollapsed(true);
        setMobileDrawerVisible(false);
      } else if (newScreenSize === 'md') {
        setCollapsed(true);
      } else if (newScreenSize === 'lg' && collapsed) {
        setCollapsed(false);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [collapsed]);

  const handleLogout = () => {
    logout();
    window.location.href = '/auth';
  };

  const handleMenuClick = ({ key }) => {
    if (onMenuSelect) onMenuSelect(key);
    if (isMobile) setMobileDrawerVisible(false);
  };

  const userMenuItems = [
    { key: 'profile', icon: <UserOutlined />, label: 'Profile' },
    { type: 'divider' },
    { key: 'logout', icon: <LogoutOutlined />, label: 'Logout', onClick: handleLogout },
  ];

  const renderMenu = () => (
    <Menu
      theme="dark"
      selectedKeys={[activeKey]}
      mode="inline"
      items={menuItems}
      onClick={handleMenuClick}
      style={{ border: 'none' }}
    />
  );

  const renderLogo = () => (
    <div className={`
      ${isSmallScreen ? 'h-14' : 'h-16'} 
      ${isSmallScreen ? 'm-3' : 'm-4'} 
      bg-white bg-opacity-10 
      rounded-md 
      flex items-center justify-center 
      transition-all duration-200
      overflow-hidden
    `}>
      {collapsed && !isSmallScreen ? (
        <img 
          src="/logo.png" 
          alt="Alicartify" 
          className="h-8 w-8 object-contain"
        />
      ) : (
        <img 
          src="/logo.png" 
          alt="Alicartify" 
          className={`${screenSize === 'xs' ? 'h-8' : 'h-10'} object-contain max-w-full`}
        />
      )}
    </div>
  );

  // Get responsive dimensions
  const getSiderWidth = () => {
    if (collapsed) return 80;
    if (screenSize === 'md') return 180;
    return 200;
  };

  const getDrawerWidth = () => {
    if (screenSize === 'xs') return Math.min(280, window.innerWidth - 40);
    return 250;
  };

  return (
    <Layout className="min-h-screen">
      {/* Desktop/Tablet Sidebar */}
      {!isMobile && (
        <Sider
          collapsible
          collapsed={collapsed}
          onCollapse={!isTablet ? setCollapsed : undefined}
          trigger={isTablet ? null : undefined}
          breakpoint="lg"
          collapsedWidth={isTablet ? 0 : 80}
          width={getSiderWidth()}
          style={{
            overflow: 'auto',
            height: '100vh',
            position: 'fixed',
            left: 0,
            top: 0,
            bottom: 0,
            zIndex: 100,
            boxShadow: isTablet && !collapsed ? '2px 0 8px rgba(0,0,0,0.15)' : 'none'
          }}
        >
          {renderLogo()}
          {renderMenu()}
        </Sider>
      )}

      {/* Mobile Drawer */}
      {isMobile && (
        <Drawer
          title={
            <div className={`font-bold ${
              screenSize === 'xs' ? 'text-base' : 'text-lg'
            } text-white`}>
              Agency Panel
            </div>
          }
          placement="left"
          onClose={() => setMobileDrawerVisible(false)}
          open={mobileDrawerVisible}
          bodyStyle={{ padding: 0, backgroundColor: '#001529' }}
          headerStyle={{ backgroundColor: '#001529', borderBottom: '1px solid #303030' }}
          width={getDrawerWidth()}
          className="[&_.ant-drawer-content]:bg-gray-900"
        >
          <div className="bg-gray-900 min-h-full">
            {renderMenu()}
          </div>
        </Drawer>
      )}
      
      <Layout style={{
        marginLeft: isMobile ? 0 : (isTablet && collapsed ? 0 : getSiderWidth()),
        transition: 'margin-left 0.2s ease-in-out'
      }}>
        <Header
          style={{
            padding: screenSize === 'xs' ? '0 12px' : (isSmallScreen ? '0 16px' : '0 24px'),
            background: colorBgContainer,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            boxShadow: '0 1px 4px rgba(0,21,41,.08)',
            position: 'sticky',
            top: 0,
            zIndex: 99,
            height: isSmallScreen ? 56 : 64
          }}
        >
          <Button
            type="text"
            icon={isMobile ? <MenuFoldOutlined /> : (collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />)}
            onClick={() => isMobile ? setMobileDrawerVisible(true) : setCollapsed(!collapsed)}
            style={{
              fontSize: screenSize === 'xs' ? '14px' : '16px',
              width: screenSize === 'xs' ? 48 : 64,
              height: screenSize === 'xs' ? 48 : 64,
            }}
          />
          
          <Space size={screenSize === 'xs' ? "small" : (isSmallScreen ? "middle" : "large")}>
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
              trigger={['click']}
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar 
                  icon={<UserOutlined />} 
                  size={screenSize === 'xs' ? 32 : (isMobile ? "small" : "default")} 
                />
                {!isSmallScreen && (
                  <Text strong style={{
                    fontSize: screenSize === 'md' ? '14px' : '16px',
                    maxWidth: screenSize === 'md' ? '120px' : 'none',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}>
                    {user?.name || user?.fullName ||
                     (user?.firstName && user?.lastName ? `${user.firstName} ${user.lastName}` : '') ||
                     'Agency'}
                  </Text>
                )}
              </Space>
            </Dropdown>
          </Space>
        </Header>
        
        <Content style={{
          margin: screenSize === 'xs' ? '12px' : (isSmallScreen ? '16px' : '24px'),
          overflow: 'initial',
          minHeight: 'calc(100vh - 120px)'
        }}>
          <div
            style={{
              padding: screenSize === 'xs' ? 12 : (isSmallScreen ? 16 : 24),
              minHeight: 360,
              background: colorBgContainer,
              borderRadius: borderRadiusLG,
            }}
          >
            {children}
          </div>
        </Content>
        
        <Footer style={{
          textAlign: 'center',
          background: colorBgContainer,
          padding: screenSize === 'xs' ? '8px 12px' : (isSmallScreen ? '12px' : '24px'),
          fontSize: screenSize === 'xs' ? '12px' : '14px'
        }}>
          {screenSize === 'xs' ? '©2025 Agency' : (isSmallScreen ? '©2025 Agency Panel' : 'Alicartify ©2025 Agency Panel')}
        </Footer>
      </Layout>
    </Layout>
  );
};

export default AgencyLayout;
