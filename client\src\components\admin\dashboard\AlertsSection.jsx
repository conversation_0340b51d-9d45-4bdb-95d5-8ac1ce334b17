import React from 'react';
import { <PERSON>ert, Row, Col, Space, Button } from 'antd';

const AlertsSection = ({ alerts = [] }) => {
  if (!alerts || alerts.length === 0) {
    return null;
  }

  return (
    <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
      <Col span={24}>
        <Space direction="vertical" style={{ width: '100%' }}>
          {alerts.map((alert, index) => (
            <Alert
              key={index}
              message={alert.message}
              type={alert.type}
              action={
                alert.action && (
                  <Button 
                    size="small" 
                    type="link"
                    onClick={() => {
                      // Handle alert action based on type
                      if (alert.action === 'View Products') {
                        window.location.href = '/admin/products';
                      } else if (alert.action === 'Review Vendors') {
                        window.location.href = '/admin/vendors';
                      } else if (alert.action === 'View Orders') {
                        window.location.href = '/admin/orders';
                      }
                    }}
                  >
                    {alert.action}
                  </Button>
                )
              }
              closable
              style={{
                borderRadius: '8px',
                border: 'none',
                boxShadow: '0 1px 4px rgba(0,0,0,0.05)'
              }}
            />
          ))}
        </Space>
      </Col>
    </Row>
  );
};

export default AlertsSection;
