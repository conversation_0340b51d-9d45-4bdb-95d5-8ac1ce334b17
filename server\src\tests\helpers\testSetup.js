const mongoose = require('mongoose');
const request = require('supertest');
const app = require('../../../index'); // Main app

// Mock data for testing
const mockUser = {
  _id: new mongoose.Types.ObjectId(),
  email: '<EMAIL>',
  userType: 'customer',
  isEmailVerified: true,
  status: 'active'
};

const mockVendor = {
  _id: new mongoose.Types.ObjectId(),
  email: '<EMAIL>',
  userType: 'vendor',
  businessName: 'Test Vendor',
  isEmailVerified: true,
  status: 'active'
};

const mockProduct = {
  _id: new mongoose.Types.ObjectId(),
  name: 'Test Product',
  vendor: mockVendor._id,
  pricing: {
    basePrice: 100,
    salePrice: 80,
    currency: 'USD'
  },
  inventory: {
    quantity: 50,
    trackQuantity: true,
    lowStockThreshold: 5
  },
  status: 'active'
};

// Generate JWT token for testing
const jwt = require('jsonwebtoken');
const generateToken = (user) => {
  return jwt.sign(
    { userId: user._id, userType: user.userType },
    process.env.JWT_SECRET || 'test-secret',
    { expiresIn: '1h' }
  );
};

// Helper function to make authenticated requests
const authenticatedRequest = (token) => {
  return request(app)
    .set('Authorization', `Bearer ${token}`);
};

// Clean up database after tests
const cleanupDatabase = async () => {
  if (mongoose.connection.readyState === 1) {
    const collections = await mongoose.connection.db.collections();
    for (let collection of collections) {
      await collection.deleteMany({});
    }
  }
};

module.exports = {
  mockUser,
  mockVendor,
  mockProduct,
  generateToken,
  authenticatedRequest,
  cleanupDatabase,
  request
};
