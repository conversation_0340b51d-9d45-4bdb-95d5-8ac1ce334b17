const express = require('express');
const router = express.Router();
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');
const imageUpload = require('../../middleware/upload/imageUpload');
const {
  getHomepageSettings,
  updateGeneralSettings,
  addCarouselImage,
  updateCarouselImage,
  deleteCarouselImage,
  addPromotionImage,
  updatePromotionImage,
  deletePromotionImage,
  addFeaturedCategory,
  updateFeaturedCategory,
  deleteFeaturedCategory,
  // All Categories Modal
  getAllCategoriesModal,
  updateAllCategoriesModal,
  addMainCategory,
  addPopularCategory,
  updateMainCategory,
  updatePopularCategory,
  deleteMainCategory,
  deletePopularCategory,
  initializeDefaultCategories
} = require('../../controllers/admin/homepageSettingsController');

// Apply authentication middleware to all routes
router.use(verifyToken);
router.use(requireUserType(['admin']));

// General settings routes
router.get('/', getHomepageSettings);
router.put('/settings', updateGeneralSettings);

// Carousel image routes
router.post('/carousel', addCarouselImage);
router.put('/carousel/:imageId', updateCarouselImage);
router.delete('/carousel/:imageId', deleteCarouselImage);

// Promotion image routes
router.post('/promotions', addPromotionImage);
router.put('/promotions/:imageId', updatePromotionImage);
router.delete('/promotions/:imageId', deletePromotionImage);

// Featured category routes
router.post('/featured-categories', imageUpload.categoryImage(), addFeaturedCategory);
router.put('/featured-categories/:categoryId', imageUpload.categoryImage(), updateFeaturedCategory);
router.delete('/featured-categories/:categoryId', deleteFeaturedCategory);

// All Categories Modal routes
router.get('/all-categories-modal', getAllCategoriesModal);
router.put('/all-categories-modal', updateAllCategoriesModal);
router.post('/all-categories-modal/initialize', initializeDefaultCategories);

// Main categories routes
router.post('/all-categories-modal/main-categories', addMainCategory);
router.put('/all-categories-modal/main-categories/:categoryId', updateMainCategory);
router.delete('/all-categories-modal/main-categories/:categoryId', deleteMainCategory);

// Popular categories routes
router.post('/all-categories-modal/popular-categories', addPopularCategory);
router.put('/all-categories-modal/popular-categories/:categoryId', updatePopularCategory);
router.delete('/all-categories-modal/popular-categories/:categoryId', deletePopularCategory);

module.exports = router;
