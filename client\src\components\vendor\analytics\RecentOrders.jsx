import React from 'react';
import { Card, Space, Avatar, Empty } from 'antd';
import useResponsive from '../../../hooks/useResponsive';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

// Extend dayjs with relativeTime plugin
dayjs.extend(relativeTime);

const RecentOrders = ({ dashboardData, loading, formatCurrency }) => {
  const { isMobile } = useResponsive();
  
  const recentOrders = dashboardData?.recentOrders || [];
  const colors = ['#52c41a', '#1890ff', '#faad14', '#f5222d', '#722ed1'];
  
  const renderOrderItem = (order, index) => {
    const customer = order.customer;
    const customerName = customer 
      ? `${customer.firstName} ${customer.lastName}` 
      : 'Unknown Customer';
    
    const initials = customer 
      ? `${customer.firstName?.charAt(0) || ''}${customer.lastName?.charAt(0) || ''}`.toUpperCase()
      : 'UK';
    
    const bgColor = colors[index % colors.length];
    
    return (
      <div 
        key={order._id} 
        style={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: isMobile ? '8px' : '12px' 
        }}
      >
        <Avatar 
          style={{ backgroundColor: bgColor }} 
          size={isMobile ? 'small' : 'default'}
        >
          {initials}
        </Avatar>
        
        <div style={{ flex: 1 }}>
          <div style={{ 
            fontWeight: 500, 
            fontSize: isMobile ? '12px' : '14px' 
          }}>
            {order.orderNumber}
          </div>
          <div style={{ 
            color: '#666', 
            fontSize: isMobile ? '10px' : '12px' 
          }}>
            {customerName}
          </div>
        </div>
        
        <div style={{ textAlign: 'right' }}>
          <div style={{ 
            fontWeight: 500, 
            color: '#52c41a', 
            fontSize: isMobile ? '12px' : '14px' 
          }}>
            {formatCurrency(order.pricing?.total || 0)}
          </div>
          <div style={{ 
            color: '#666', 
            fontSize: isMobile ? '10px' : '12px' 
          }}>
            {dayjs(order.createdAt).fromNow()}
          </div>
        </div>
      </div>
    );
  };
  
  return (
    <Card 
      title="Recent Orders" 
      size={isMobile ? 'small' : 'default'}
      loading={loading}
    >
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        {recentOrders && recentOrders.length > 0 ? (
          recentOrders.slice(0, 5).map(renderOrderItem)
        ) : (
          <Empty 
            description="No recent orders found"
            style={{ 
              fontSize: isMobile ? '12px' : '14px',
              color: '#999',
              padding: '20px 0'
            }}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
      </Space>
    </Card>
  );
};

export default RecentOrders;
