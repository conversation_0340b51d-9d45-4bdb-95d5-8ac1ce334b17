import React from 'react';
import { Typography, <PERSON>, Button, Select } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import useResponsive from '../../../hooks/useResponsive';
import dayjs from 'dayjs';

const { Title: AntTitle } = Typography;

const AnalyticsHeader = ({
  selectedPeriod,
  onPeriodChange,
  onRefresh,
  refreshing,
  lastUpdated
}) => {
  const { isMobile } = useResponsive();

  return (
    <div style={{ 
      marginBottom: 24, 
      display: 'flex', 
      justifyContent: 'space-between', 
      alignItems: 'flex-start',
      flexWrap: 'wrap',
      gap: 16
    }}>
      <div style={{ flex: 1, minWidth: 200 }}>
        <AntTitle level={isMobile ? 3 : 2} style={{ margin: 0, color: '#52c41a' }}>
          Analytics Dashboard
        </AntTitle>
        <p style={{ 
          color: '#666', 
          marginTop: '8px', 
          fontSize: isMobile ? '12px' : '14px',
          margin: '4px 0'
        }}>
          Real-time insights and performance metrics for your vendor store
        </p>
        {lastUpdated && (
          <div style={{ fontSize: '11px', color: '#999', marginTop: 4 }}>
            Last updated: {dayjs(lastUpdated).format('MMM DD, YYYY HH:mm:ss')}
          </div>
        )}
      </div>
      
      <Space>
        <Select
          value={selectedPeriod}
          onChange={onPeriodChange}
          size={isMobile ? 'small' : 'default'}
          style={{ minWidth: 100 }}
        >
          <Select.Option value="7d">7 Days</Select.Option>
          <Select.Option value="30d">30 Days</Select.Option>
          <Select.Option value="90d">90 Days</Select.Option>
          <Select.Option value="1y">1 Year</Select.Option>
        </Select>
        
        <Button 
          icon={<ReloadOutlined />} 
          onClick={onRefresh}
          loading={refreshing}
          size={isMobile ? 'small' : 'default'}
        >
          {!isMobile && 'Refresh'}
        </Button>
      </Space>
    </div>
  );
};

export default AnalyticsHeader;
