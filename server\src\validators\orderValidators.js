const { body } = require('express-validator');

const orderValidator = [
  body('items')
    .isArray({ min: 1 })
    .withMessage('Order must contain at least one item'),
  
  body('items.*.product')
    .notEmpty()
    .withMessage('Product ID is required')
    .isMongoId()
    .withMessage('Invalid product ID'),
  
  body('items.*.vendor')
    .notEmpty()
    .withMessage('Vendor ID is required')
    .isMongoId()
    .withMessage('Invalid vendor ID'),
  
  body('items.*.quantity')
    .isInt({ min: 1 })
    .withMessage('Quantity must be at least 1'),
  
  body('items.*.unitPrice')
    .isFloat({ min: 0 })
    .withMessage('Unit price must be a positive number'),
  
  body('billing.firstName')
    .notEmpty()
    .withMessage('Billing first name is required')
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters'),
  
  body('billing.lastName')
    .notEmpty()
    .withMessage('Billing last name is required')
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters'),
  
  body('billing.email')
    .isEmail()
    .withMessage('Valid email is required'),
  
  body('billing.phone')
    .notEmpty()
    .withMessage('Phone number is required')
    .isLength({ min: 10, max: 15 })
    .withMessage('Phone number must be between 10 and 15 digits')
    .matches(/^[\\+]?[0-9\\s\\-\\(\\)]+$/)
    .withMessage('Invalid phone number format. Use only numbers, spaces, dashes, plus sign, and parentheses.'),
  
  body('billing.address.street')
    .notEmpty()
    .withMessage('Billing street address is required')
    .isLength({ min: 1, max: 200 })
    .withMessage('Street address must be between 1 and 200 characters'),
  
  body('billing.address.city')
    .notEmpty()
    .withMessage('Billing city is required')
    .isLength({ min: 1, max: 100 })
    .withMessage('City must be between 1 and 100 characters'),
  
  body('billing.address.state')
    .notEmpty()
    .withMessage('Billing state is required')
    .isLength({ min: 1, max: 100 })
    .withMessage('State must be between 1 and 100 characters'),
  
  body('billing.address.zipCode')
    .notEmpty()
    .withMessage('Billing zip code is required')
    .isLength({ min: 1, max: 20 })
    .withMessage('Zip code must be between 1 and 20 characters'),
  
  body('billing.address.country')
    .notEmpty()
    .withMessage('Billing country is required')
    .isLength({ min: 1, max: 100 })
    .withMessage('Country must be between 1 and 100 characters'),
  
  body('shipping.firstName')
    .notEmpty()
    .withMessage('Shipping first name is required')
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters'),
  
  body('shipping.lastName')
    .notEmpty()
    .withMessage('Shipping last name is required')
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters'),
  
  body('shipping.address.street')
    .notEmpty()
    .withMessage('Shipping street address is required')
    .isLength({ min: 1, max: 200 })
    .withMessage('Street address must be between 1 and 200 characters'),
  
  body('shipping.address.city')
    .notEmpty()
    .withMessage('Shipping city is required')
    .isLength({ min: 1, max: 100 })
    .withMessage('City must be between 1 and 100 characters'),
  
  body('shipping.address.state')
    .notEmpty()
    .withMessage('Shipping state is required')
    .isLength({ min: 1, max: 100 })
    .withMessage('State must be between 1 and 100 characters'),
  
  body('shipping.address.zipCode')
    .notEmpty()
    .withMessage('Shipping zip code is required')
    .isLength({ min: 1, max: 20 })
    .withMessage('Zip code must be between 1 and 20 characters'),
  
  body('shipping.address.country')
    .notEmpty()
    .withMessage('Shipping country is required')
    .isLength({ min: 1, max: 100 })
    .withMessage('Country must be between 1 and 100 characters'),
  
  body('payment.method')
    .isIn(['credit_card', 'debit_card', 'paypal', 'stripe', 'bank_transfer', 'cash_on_delivery', 'cod'])
    .withMessage('Invalid payment method'),
  
  body('pricing.subtotal')
    .isFloat({ min: 0 })
    .withMessage('Subtotal must be a positive number'),
  
  body('pricing.total')
    .isFloat({ min: 0 })
    .withMessage('Total must be a positive number'),
  
  body('customerNotes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Customer notes must not exceed 500 characters')
];

const cancelOrderValidator = [
  body('reason')
    .optional()
    .isString()
    .withMessage('Reason must be a string')
    .isLength({ max: 500 })
    .withMessage('Reason must not exceed 500 characters')
];

const returnOrderValidator = [
  body('reason')
    .notEmpty()
    .withMessage('Return reason is required')
    .isString()
    .withMessage('Reason must be a string')
    .isLength({ min: 10, max: 500 })
    .withMessage('Reason must be between 10 and 500 characters'),
  
  body('items')
    .optional()
    .isArray()
    .withMessage('Items must be an array'),
  
  body('items.*.product')
    .optional()
    .isMongoId()
    .withMessage('Invalid product ID'),
  
  body('items.*.quantity')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Quantity must be at least 1')
];

module.exports = {
  orderValidator,
  cancelOrderValidator,
  returnOrderValidator
};
