/**
 * Utility functions for price validation
 */

export const validatePrice = (currency, basePrice, salePrice, required = false) => {
  const errors = [];
  
  if (required && (!basePrice || basePrice <= 0)) {
    errors.push(`${currency} base price is required`);
  }
  
  if (salePrice && basePrice && salePrice >= basePrice) {
    errors.push(`${currency} sale price must be less than base price`);
  }
  
  return errors;
};

export const validateMultiCurrencyPrices = (prices, required = false) => {
  const allErrors = {};
  
  Object.keys(prices).forEach(currency => {
    const currencyPrices = prices[currency] || {};
    const errors = validatePrice(
      currency, 
      currencyPrices.basePrice, 
      currencyPrices.salePrice, 
      required && currency === 'INR' // Only INR is required by default
    );
    
    if (errors.length > 0) {
      allErrors[currency] = errors;
    }
  });
  
  return allErrors;
};

export const formatCurrency = (amount, currency = 'INR') => {
  const symbols = {
    'INR': '₹',
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'CAD': 'C$',
    'AUD': 'A$'
  };
  
  const symbol = symbols[currency] || currency;
  return `${symbol}${parseFloat(amount || 0).toFixed(2)}`;
};
