// Public API utilities (no authentication required)
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

class PublicAPI {
  constructor() {
    this.baseURL = `${API_BASE_URL}/public`;
  }

  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;

    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      // Check if response is JSON
      const contentType = response.headers.get('content-type');
      let data;

      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        const text = await response.text();
        data = { message: text || 'Server error' };
      }

      if (!response.ok) {
        const error = new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
        error.response = { data, status: response.status, statusText: response.statusText };
        throw error;
      }

      return data;
    } catch (error) {
      // Handle network connection errors
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        const networkError = new Error('Unable to connect to server. Please check your internet connection and try again.');
        networkError.isNetworkError = true;
        throw networkError;
      }

      console.error('Public API Error:', error);
      throw error;
    }
  }

  // Homepage API methods
  async getHomepageData() {
    return this.makeRequest('/homepage');
  }

  async getFeaturedCategories() {
    return this.makeRequest('/homepage/featured-categories');
  }

  async getAllCategoriesModal() {
    try {
      return await this.makeRequest('/homepage/all-categories-modal');
    } catch (error) {
      console.error('Failed to fetch categories from API, using fallback data:', error);
      
      // Return fallback data if API fails
      return {
        success: true,
        data: {
          mainCategories: [
            { _id: '1', name: 'Apparel & Accessories', icon: 'ShopOutlined', linkUrl: '' },
            { _id: '2', name: 'Consumer Electronics', icon: 'MobileOutlined', linkUrl: '' },
            { _id: '3', name: 'Sports & Entertainment', icon: 'CrownOutlined', linkUrl: '' },
            { _id: '4', name: 'Jewelry, Eyewear & Watches', icon: 'HeartOutlined', linkUrl: '' },
            { _id: '5', name: 'Shoes & Accessories', icon: 'CarOutlined', linkUrl: '' },
            { _id: '6', name: 'Home & Garden', icon: 'HomeOutlined', linkUrl: '' },
            { _id: '7', name: 'Beauty', icon: 'SkinOutlined', linkUrl: '' },
            { _id: '8', name: 'Luggage, Bags & Cases', icon: 'ShoppingOutlined', linkUrl: '' },
            { _id: '9', name: 'Packaging & Printing', icon: 'PrinterOutlined', linkUrl: '' },
            { _id: '10', name: 'Parents, Kids & Toys', icon: 'BugOutlined', linkUrl: '' },
            { _id: '11', name: 'Personal Care & Home Care', icon: 'SmileOutlined', linkUrl: '' },
            { _id: '12', name: 'Health & Medical', icon: 'MedicineBoxOutlined', linkUrl: '' },
            { _id: '13', name: 'Gifts & Crafts', icon: 'GiftOutlined', linkUrl: '' },
            { _id: '14', name: 'Furniture', icon: 'TableOutlined', linkUrl: '' },
            { _id: '15', name: 'Lights & Lighting', icon: 'BulbOutlined', linkUrl: '' },
            { _id: '16', name: 'Home Appliances', icon: 'SettingOutlined', linkUrl: '' },
            { _id: '17', name: 'Safety & Security', icon: 'SafetyOutlined', linkUrl: '' },
            { _id: '18', name: 'View All', icon: 'AppstoreOutlined', linkUrl: '' }
          ],
          popularCategories: [
            { _id: '1', name: 'Consumer Electronics', linkUrl: '' },
            { _id: '2', name: 'Apparel & Accessories', linkUrl: '' },
            { _id: '3', name: 'Home & Garden', linkUrl: '' },
            { _id: '4', name: 'Sports & Entertainment', linkUrl: '' },
            { _id: '5', name: 'Beauty', linkUrl: '' },
            { _id: '6', name: 'Jewelry, Eyewear & Watches', linkUrl: '' },
            { _id: '7', name: 'Shoes & Accessories', linkUrl: '' },
            { _id: '8', name: 'Health & Medical', linkUrl: '' },
            { _id: '9', name: 'Furniture', linkUrl: '' },
            { _id: '10', name: 'Home Appliances', linkUrl: '' }
          ]
        }
      };
    }
  }

  // Product API methods
  async getProducts(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.makeRequest(`/products${queryString ? `?${queryString}` : ''}`);
  }

  async getProduct(productId) {
    return this.makeRequest(`/products/${productId}`);
  }

  async getFeaturedProducts(limit = 8) {
    const params = { limit };
    const queryString = new URLSearchParams(params).toString();
    return this.makeRequest(`/products/featured?${queryString}`);
  }

  async getNewArrivals(limit = 8) {
    const params = { limit };
    const queryString = new URLSearchParams(params).toString();
    return this.makeRequest(`/products/new-arrivals?${queryString}`);
  }

  async getBudgetFriendlyProducts(limit = 8) {
    const params = { limit };
    const queryString = new URLSearchParams(params).toString();
    return this.makeRequest(`/products/budget-friendly?${queryString}`);
  }

  async searchProducts(query, filters = {}) {
    const params = { q: query, ...filters };
    const queryString = new URLSearchParams(params).toString();
    return this.makeRequest(`/search?${queryString}`);
  }

  async getSuggestions(query) {
    try {
      const params = { q: query, limit: 10 };
      const queryString = new URLSearchParams(params).toString();
      return this.makeRequest(`/search/suggestions?${queryString}`);
    } catch (error) {
      console.error('Failed to fetch search suggestions:', error);
      // Return empty suggestions if API fails
      return {
        success: true,
        data: []
      };
    }
  }

  // Category API methods
  async getCategories() {
    return this.makeRequest('/categories');
  }

  async getCategory(categoryId) {
    return this.makeRequest(`/categories/${categoryId}`);
  }

  async getCategoryProducts(categoryId, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.makeRequest(`/categories/${categoryId}/products${queryString ? `?${queryString}` : ''}`);
  }

  // Health check
  async healthCheck() {
    return this.makeRequest('/health');
  }
}

// Create and export a singleton instance
const publicAPI = new PublicAPI();

// Export the instance and specific methods
export { publicAPI };

// Export specific API groups for convenience
export const homepageApi = {
  getHomepageData: () => publicAPI.getHomepageData(),
  getFeaturedCategories: () => publicAPI.getFeaturedCategories(),
  getAllCategoriesModal: () => publicAPI.getAllCategoriesModal(),
};

export const productsApi = {
  getProducts: (params) => publicAPI.getProducts(params),
  getProduct: (productId) => publicAPI.getProduct(productId),
  getFeaturedProducts: (limit) => publicAPI.getFeaturedProducts(limit),
  getNewArrivals: (limit) => publicAPI.getNewArrivals(limit),
  getBudgetFriendlyProducts: (limit) => publicAPI.getBudgetFriendlyProducts(limit),
  searchProducts: (query, filters) => publicAPI.searchProducts(query, filters),
};

export const categoriesApi = {
  getCategories: () => publicAPI.getCategories(),
  getCategory: (categoryId) => publicAPI.getCategory(categoryId),
  getCategoryProducts: (categoryId, params) => publicAPI.getCategoryProducts(categoryId, params),
};

export const searchApi = {
  searchProducts: (query, filters) => publicAPI.searchProducts(query, filters),
  getSuggestions: (query) => publicAPI.getSuggestions(query),
};

export default publicAPI;