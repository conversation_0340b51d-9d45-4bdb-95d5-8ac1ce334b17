module.exports = {
  bcrypt: {
    saltRounds: 12
  },
  session: {
    secret: process.env.SESSION_SECRET || 'your-session-secret',
    timeout: parseInt(process.env.SESSION_TIMEOUT) || 30 * 60 * 1000 // 30 minutes
  },
  password: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false
  },
  login: {
    maxAttempts: 5,
    lockoutTime: 15 * 60 * 1000 // 15 minutes
  }
};