const mongoose = require('mongoose');

// Import sub-schemas
const { pricingSchema } = require('./pricingSchema');
const { inventorySchema } = require('./inventorySchema');
const { imageSchema } = require('./imageSchema');
const { variantSchema } = require('./variantSchema');
const { attributeSchema } = require('./attributeSchema');
const { colorSchema } = require('./colorSchema');
const { sizeSchema } = require('./sizeSchema');
const { deliveryChargesSchema } = require('./deliveryChargesSchema');
const { shippingOptionsSchema } = require('./shippingOptionsSchema');
const { returnPolicySchema } = require('./returnPolicySchema');
const { warrantySchema } = require('./warrantySchema');
const { specificationsSchema } = require('./specificationsSchema');
const { shippingSchema } = require('./shippingSchema');
const { seoSchema } = require('./seoSchema');
const { approvalSchema } = require('./approvalSchema');
const { reviewsSchema } = require('./reviewsSchema');
const { salesSchema } = require('./salesSchema');
const { analyticsSchema } = require('./analyticsSchema');

const productSchema = new mongoose.Schema({
  vendor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vendor',
    required: true
  },
  name: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    maxlength: [200, 'Product name cannot exceed 200 characters']
  },
  slug: {
    type: String,
    unique: true,
    lowercase: true,
    trim: true
  },
  description: {
    type: String,
    required: [true, 'Product description is required'],
    trim: true,
    maxlength: [2000, 'Product description cannot exceed 2000 characters']
  },
  shortDescription: {
    type: String,
    trim: true,
    maxlength: [500, 'Short description cannot exceed 500 characters']
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: false
  },
  subcategory: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category'
  },
  brand: {
    type: String,
    trim: true,
    maxlength: [100, 'Brand name cannot exceed 100 characters']
  },
  sku: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    uppercase: true
  },
  barcode: {
    type: String,
    trim: true,
    sparse: true
  },
  images: [imageSchema],
  pricing: pricingSchema,
  inventory: inventorySchema,
  variants: [variantSchema],
  attributes: [attributeSchema],
  colors: [colorSchema],
  sizes: [sizeSchema],
  highlights: [{
    type: String,
    trim: true,
    maxlength: [200, 'Highlight cannot exceed 200 characters']
  }],
  deliveryCharges: deliveryChargesSchema,
  shippingOptions: shippingOptionsSchema,
  returnPolicy: returnPolicySchema,
  warranty: warrantySchema,
  specifications: specificationsSchema,
  shipping: shippingSchema,
  seo: seoSchema,
  status: {
    type: String,
    enum: ['draft', 'pending_approval', 'active', 'inactive', 'archived', 'rejected'],
    default: 'draft'
  },
  approval: approvalSchema,
  visibility: {
    type: String,
    enum: ['public', 'private', 'password_protected'],
    default: 'public'
  },
  featured: {
    type: Boolean,
    default: false
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  reviews: reviewsSchema,
  sales: salesSchema,
  analytics: analyticsSchema,
  publishedAt: Date,
  lastModified: {
    type: Date,
    default: Date.now
  },
  modifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

module.exports = { productSchema };
