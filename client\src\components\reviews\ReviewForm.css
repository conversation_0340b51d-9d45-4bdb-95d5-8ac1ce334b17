.review-form-card {
  margin: 20px 0;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
}

.review-form-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

.review-icon {
  color: #faad14;
  font-size: 18px;
}

.review-form {
  margin-top: 20px;
}

.rating-section {
  margin-bottom: 24px;
}

.rating-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.rating-stars {
  font-size: 24px;
}

.rating-stars .ant-rate-star {
  margin-right: 8px;
}

.rating-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  padding: 4px 8px;
  background: #f0f0f0;
  border-radius: 4px;
}

.review-textarea {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.review-textarea:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.review-guidelines {
  background: #f9f9f9;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  border-left: 4px solid #1890ff;
}

.review-guidelines h4 {
  margin: 0 0 12px 0;
  color: #1890ff;
  font-size: 14px;
  font-weight: 600;
}

.review-guidelines ul {
  margin: 0;
  padding-left: 20px;
}

.review-guidelines li {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
  line-height: 1.4;
}

.submit-section {
  margin-bottom: 0;
  text-align: center;
}

.submit-btn {
  min-width: 140px;
  height: 40px;
  border-radius: 8px;
  font-weight: 600;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border: none;
  transition: all 0.3s ease;
}

.submit-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.submit-btn:disabled {
  background: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .review-form-card {
    margin: 15px 0;
    border-radius: 8px;
  }
  
  .review-form-header {
    font-size: 14px;
  }
  
  .rating-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .rating-stars {
    font-size: 20px;
  }
  
  .review-guidelines {
    padding: 12px;
  }
  
  .review-guidelines h4 {
    font-size: 13px;
  }
  
  .review-guidelines li {
    font-size: 12px;
  }
  
  .submit-btn {
    width: 100%;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .review-guidelines {
    background: #1f1f1f;
    border-left-color: #40a9ff;
  }
  
  .review-guidelines h4 {
    color: #40a9ff;
  }
  
  .review-guidelines li {
    color: #bfbfbf;
  }
  
  .rating-text {
    background: #262626;
    color: #bfbfbf;
  }
}
