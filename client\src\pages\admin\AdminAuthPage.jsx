import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { message, Spin, notification } from 'antd';
import { 
  UserOutlined, 
  LockOutlined, 
  EyeOutlined,
  EyeInvisibleOutlined,
  SecurityScanOutlined
} from '@ant-design/icons';
import { useAuth } from '../../hooks/useAuth';

const AdminAuthPage = () => {
  const navigate = useNavigate();
  const { login, isAuthenticated, userType, isLoading } = useAuth();
  
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Redirect if already authenticated as admin
  useEffect(() => {
    if (!isLoading && isAuthenticated && userType === 'admin') {
      navigate('/admin/dashboard');
    } else if (!isLoading && isAuthenticated && userType !== 'admin') {
      // If authenticated but not admin, redirect to appropriate dashboard
      if (userType === 'vendor') {
        navigate('/vendor/dashboard');
      } else {
        navigate('/');
      }
    }
  }, [isAuthenticated, userType, isLoading, navigate]);

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.email || !formData.password) {
      message.error('Please fill in all fields');
      return;
    }

    setLoading(true);
    
    try {
      console.log('🔄 Attempting admin login...');
      
      // Show a notification about the login attempt
      notification.info({
        message: 'Authenticating',
        description: 'Verifying admin credentials...',
        duration: 2,
        placement: 'topRight'
      });
      
      const result = await login({
        email: formData.email,
        password: formData.password,
        userType: 'admin'
      });

      console.log('📋 Login result:', result);
      
      // Show detailed debug info in notification
      notification.info({
        message: 'Debug Info',
        description: `Login result: ${result.success ? 'Success' : 'Failed'} | Error: ${result.error || 'None'}`,
        duration: 5,
        placement: 'topRight'
      });

      if (result.success) {
        // Check if the user data contains admin userType
        const userData = result.data?.user;
        const actualUserType = userData?.userType;
        
        console.log('✅ Login successful, user type:', actualUserType);
        
        if (actualUserType === 'admin') {
          message.success('Admin login successful!');
          navigate('/admin/dashboard');
        } else {
          message.error({
            content: `Access denied. This account is registered as a ${actualUserType} account, not an admin account.`,
            duration: 6
          });
          // Clear form
          setFormData({ email: '', password: '' });
        }
      } else {
        console.log('❌ Login failed:', result.error);
        
        // Handle different types of errors with specific messages
        let errorMessage = result.error || 'Login failed';
        
        if (errorMessage.includes('Invalid email or password')) {
          errorMessage = 'Invalid email or password. Please check your credentials and try again.';
        } else if (errorMessage.includes('registered as a')) {
          // Account type mismatch - show the specific error
          errorMessage = result.error;
        } else if (errorMessage.includes('locked')) {
          errorMessage = 'Account is temporarily locked due to multiple failed login attempts. Please try again later.';
        } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
          errorMessage = 'Unable to connect to server. Please check your internet connection and try again.';
        }
        
        message.error({
          content: errorMessage,
          duration: 6
        });
        
        // Clear password field on error
        setFormData(prev => ({ ...prev, password: '' }));
      }
    } catch (error) {
      console.error('❌ Admin login error:', error);
      
      // Handle network and other errors
      let errorMessage = 'Login failed. Please try again.';
      
      if (error.message?.includes('fetch') || error.message?.includes('network')) {
        errorMessage = 'Unable to connect to server. Please check your internet connection and try again.';
      } else if (error.message?.includes('timeout')) {
        errorMessage = 'Request timed out. Please try again.';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      message.error({
        content: errorMessage,
        duration: 6
      });
      
      // Clear password field on error
      setFormData(prev => ({ ...prev, password: '' }));
    } finally {
      setLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="mx-auto h-16 w-16 bg-red-600 rounded-full flex items-center justify-center mb-6">
            <SecurityScanOutlined className="h-8 w-8 text-white" />
          </div>
          <h2 className="text-3xl font-bold text-white mb-2">
            Admin Access
          </h2>
          <p className="text-gray-400">
            Secure administrator login portal
          </p>
        </div>

        {/* Login Form */}
        <div className="bg-white rounded-xl shadow-2xl p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email Field */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Administrator Email
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <UserOutlined className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleChange('email', e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
                  placeholder="Enter admin email"
                  required
                  autoComplete="email"
                />
              </div>
            </div>

            {/* Password Field */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <LockOutlined className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={(e) => handleChange('password', e.target.value)}
                  className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
                  placeholder="Enter password"
                  required
                  autoComplete="current-password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? (
                    <EyeInvisibleOutlined className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  ) : (
                    <EyeOutlined className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  )}
                </button>
              </div>
            </div>

            {/* Login Button */}
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-gradient-to-r from-red-600 to-red-700 text-white py-3 px-4 rounded-lg font-medium hover:from-red-700 hover:to-red-800 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {loading ? (
                <>
                  <Spin size="small" className="mr-2" />
                  Authenticating...
                </>
              ) : (
                <>
                  <SecurityScanOutlined className="mr-2" />
                  Access Admin Panel
                </>
              )}
            </button>
          </form>

          {/* Security Notice */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-start">
              <SecurityScanOutlined className="h-5 w-5 text-gray-400 mr-2 mt-0.5" />
              <div>
                <p className="text-sm text-gray-600 font-medium">
                  Secure Access Only
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  This portal is restricted to authorized administrators. All access attempts are logged and monitored.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Back to Site Link */}
        <div className="text-center">
          <button
            onClick={() => navigate('/')}
            className="text-gray-400 hover:text-white transition-colors duration-200 text-sm"
          >
            ← Back to main site
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdminAuthPage;
