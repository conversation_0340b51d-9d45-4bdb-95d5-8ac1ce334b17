const express = require('express');
const router = express.Router();

// Import vendor route modules
const authRoutes = require('./auth');
const dashboardRoutes = require('./dashboard');
const productRoutes = require('./products');
const orderRoutes = require('./orders');
const storeRoutes = require('./store');
const analyticsRoutes = require('./analytics');
const categoryRoutes = require('./categories');

// Mount routes
router.use('/auth', authRoutes);
router.use('/dashboard', dashboardRoutes);
router.use('/products', productRoutes);
router.use('/orders', orderRoutes);
router.use('/store', storeRoutes);
router.use('/analytics', analyticsRoutes);
router.use('/categories', categoryRoutes);

module.exports = router;