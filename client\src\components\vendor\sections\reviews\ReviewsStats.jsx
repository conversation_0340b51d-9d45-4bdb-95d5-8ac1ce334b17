import React from 'react';
import { <PERSON>, Row, Col, Statistic, Progress, Space, Typography } from 'antd';
import { StarFilled, MessageOutlined } from '@ant-design/icons';
import useResponsive from '../../../../hooks/useResponsive';

const { Text } = Typography;

const ReviewsStats = ({ stats }) => {
  const { isMobile, isTablet, isSmallScreen } = useResponsive();
  const {
    totalReviews = 0,
    averageRating = 0,
    repliedReviews = 0,
    pendingReplies = 0,
    ratingDistribution = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
  } = stats;

  // Format rating display - show 4.0 instead of 0.0 when no reviews
  const displayRating = averageRating > 0 ? averageRating.toFixed(1) : '4.0';

  return (
    <>
      {/* Main Statistics Cards */}
      <Row gutter={[8, 8]} className={isMobile ? "mb-4" : "mb-6"}>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card className="rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300 hover:-translate-y-1" size={isMobile ? 'small' : 'default'}>
            <Statistic
              title={<span style={{ fontSize: isMobile ? '12px' : '14px' }}>Total Reviews</span>}
              value={totalReviews}
              prefix={<StarFilled className="text-yellow-500" style={{ fontSize: isMobile ? '16px' : '20px' }} />}
              valueStyle={{ fontSize: isMobile ? '18px' : '24px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card className="rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300 hover:-translate-y-1" size={isMobile ? 'small' : 'default'}>
            <Statistic
              title={<span style={{ fontSize: isMobile ? '12px' : '14px' }}>Average Rating</span>}
              value={displayRating}
              suffix={<span style={{ fontSize: isMobile ? '12px' : '14px' }}>/ 5</span>}
              prefix={<StarFilled className="text-yellow-500" style={{ fontSize: isMobile ? '16px' : '20px' }} />}
              valueStyle={{ fontSize: isMobile ? '18px' : '24px', color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card className="rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300 hover:-translate-y-1" size={isMobile ? 'small' : 'default'}>
            <Statistic
              title={<span style={{ fontSize: isMobile ? '12px' : '14px' }}>Replied Reviews</span>}
              value={repliedReviews}
              prefix={<MessageOutlined className="text-green-500" style={{ fontSize: isMobile ? '16px' : '20px' }} />}
              valueStyle={{ color: '#10b981', fontSize: isMobile ? '18px' : '24px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card className="rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300 hover:-translate-y-1" size={isMobile ? 'small' : 'default'}>
            <Statistic
              title={<span style={{ fontSize: isMobile ? '12px' : '14px' }}>Pending Replies</span>}
              value={pendingReplies}
              prefix={<MessageOutlined className="text-red-500" style={{ fontSize: isMobile ? '16px' : '20px' }} />}
              valueStyle={{ color: '#ef4444', fontSize: isMobile ? '18px' : '24px' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Rating Distribution */}
      <Card 
        className={isMobile ? "mb-4 rounded-xl shadow-sm border border-gray-200" : "mb-6 rounded-xl shadow-sm border border-gray-200"}
        title={<span className={isMobile ? "text-base font-semibold text-gray-800" : "text-lg font-semibold text-gray-800"}>Rating Distribution</span>}
        size={isMobile ? 'small' : 'default'}
      >
        <Row gutter={[8, 8]}>
          {[5, 4, 3, 2, 1].map(rating => {
            const count = ratingDistribution[rating] || 0;
            const percentage = totalReviews > 0 ? (count / totalReviews) * 100 : 0;
            
            return (
              <Col key={rating} xs={24} sm={12} md={8} lg={4} xl={4} className={isMobile ? "mb-2" : "mb-4"}>
                <div className={`flex flex-col gap-2 ${isMobile ? 'p-2' : 'p-3'} bg-gray-50 rounded-lg`}>
                  <Space className="justify-between">
                    <div className="flex items-center gap-1">
                      <Text className={`font-medium ${isMobile ? 'text-sm' : ''}`}>{rating}</Text>
                      <StarFilled className="text-yellow-500" style={{ fontSize: isMobile ? '12px' : '14px' }} />
                    </div>
                  </Space>
                  <Progress
                    percent={percentage}
                    size={isMobile ? "small" : "default"}
                    strokeColor={rating >= 4 ? '#10b981' : rating >= 3 ? '#f59e0b' : '#ef4444'}
                    showInfo={false}
                    className="mb-1"
                  />
                  <Text type="secondary" className={isMobile ? "text-xs" : "text-sm"}>
                    {count} ({percentage.toFixed(0)}%)
                  </Text>
                </div>
              </Col>
            );
          })}
        </Row>
      </Card>
    </>
  );
};

export default ReviewsStats;
