const mongoose = require('mongoose');
const HomepageSettings = require('../src/models/HomepageSettings');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

// Initialize categories in database
const initializeCategories = async () => {
  try {
    console.log('🚀 Starting category initialization...');

    // Get or create homepage settings
    let settings = await HomepageSettings.getSettings();
    
    // Check if categories already exist
    if (settings.allCategoriesModal && 
        settings.allCategoriesModal.mainCategories && 
        settings.allCategoriesModal.mainCategories.length > 0) {
      console.log('⚠️  Categories already exist in database');
      console.log(`📊 Found ${settings.allCategoriesModal.mainCategories.length} main categories`);
      console.log(`📊 Found ${settings.allCategoriesModal.popularCategories?.length || 0} popular categories`);
      
      // Ask if user wants to overwrite
      const readline = require('readline');
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      return new Promise((resolve) => {
        rl.question('Do you want to overwrite existing categories? (y/N): ', (answer) => {
          rl.close();
          if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
            console.log('🔄 Overwriting existing categories...');
            initializeDefaultCategories(settings).then(resolve);
          } else {
            console.log('✅ Keeping existing categories');
            resolve();
          }
        });
      });
    } else {
      // No categories exist, initialize them
      await initializeDefaultCategories(settings);
    }
  } catch (error) {
    console.error('❌ Error initializing categories:', error);
    throw error;
  }
};

// Initialize default categories
const initializeDefaultCategories = async (settings) => {
  try {
    console.log('📝 Initializing default categories...');

    // Initialize with hardcoded categories
    await settings.initializeDefaultCategories();
    
    console.log('✅ Categories initialized successfully!');
    console.log('📊 Created 18 main categories');
    console.log('📊 Created 10 popular categories');
    
    // Display the categories
    console.log('\n📋 Main Categories:');
    settings.allCategoriesModal.mainCategories.forEach((cat, index) => {
      console.log(`  ${index + 1}. ${cat.name} (${cat.icon})`);
    });
    
    console.log('\n📋 Popular Categories:');
    settings.allCategoriesModal.popularCategories.forEach((cat, index) => {
      console.log(`  ${index + 1}. ${cat.name}`);
    });
    
    console.log('\n🎉 All categories have been saved to the database!');
    console.log('🔗 You can now manage them through the admin panel at: Admin Panel → Homepage Settings → All Categories');
    
  } catch (error) {
    console.error('❌ Error initializing default categories:', error);
    throw error;
  }
};

// Main execution
const main = async () => {
  try {
    await connectDB();
    await initializeCategories();
    console.log('\n✅ Script completed successfully!');
  } catch (error) {
    console.error('\n❌ Script failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
};

// Run the script
if (require.main === module) {
  main();
}

module.exports = { initializeCategories, connectDB };