import React, { useState } from "react";
import { DownOutlined, InfoCircleOutlined, SafetyOutlined, GlobalOutlined, CustomerServiceOutlined, ShopOutlined } from "@ant-design/icons";
import { useCurrency } from '../contexts/CurrencyContext';

const Header = () => {
    const { currentCurrency, supportedCurrencies, changeCurrency, getCurrencySymbol, loading } = useCurrency();
    
    // State for currency dropdown only
    const [isCurrencyOpen, setIsCurrencyOpen] = useState(false);

    const closeCurrencyDropdown = () => {
        setIsCurrencyOpen(false);
    };

    return (
        <>
            {/* Main Navigation Menu */}
            <div className="bg-white border-b border-gray-200 w-full">
                <div className="container mx-auto px-2 sm:px-4">
                    <div className="flex items-center justify-between h-10 sm:h-12 text-xs sm:text-sm">
                        {/* Desktop Navigation Links */}
                        <div className="hidden lg:flex flex-1 space-x-4 xl:space-x-8 overflow-x-auto hide-scrollbar">
                            <a href="/ready-to-ship" className="whitespace-nowrap text-gray-700 hover:text-orange-500 flex items-center">
                                <SafetyOutlined className="mr-1" />
                                <span className="hidden xl:inline">Ready to Ship</span>
                                <span className="xl:hidden">Ship</span>
                            </a>
                            <a href="/trade-shows" className="whitespace-nowrap text-gray-700 hover:text-orange-500 flex items-center">
                                <GlobalOutlined className="mr-1" />
                                <span className="hidden xl:inline">Trade Shows</span>
                                <span className="xl:hidden">Trade</span>
                            </a>
                            <a href="/ppe" className="whitespace-nowrap text-gray-700 hover:text-orange-500 hidden xl:block">Personal Protective Equipment</a>
                            
                            <a href="/source" className="whitespace-nowrap text-gray-700 hover:text-orange-500 flex items-center">
                                <span className="hidden xl:inline">Source on Alicartify</span>
                                <span className="xl:hidden">Source</span>
                            </a>
                            
                            <a href="/sell" className="whitespace-nowrap text-gray-700 hover:text-orange-500 flex items-center">
                                <ShopOutlined className="mr-1" />
                                <span className="hidden xl:inline">Sell on Alicartify</span>
                                <span className="xl:hidden">Sell</span>
                            </a>
                            
                            <a href="/help" className="whitespace-nowrap text-gray-700 hover:text-orange-500 flex items-center">
                                <CustomerServiceOutlined className="mr-1" />
                                <span>Help</span>
                            </a>
                        </div>

                        {/* Desktop Right Side Links */}
                        <div className="hidden md:flex items-center space-x-3 xl:space-x-6 ml-auto lg:ml-0">
                            {/* Currency Dropdown */}
                            <div className="relative">
                                <button 
                                    className="flex items-center text-gray-700 hover:text-orange-500"
                                    onClick={() => setIsCurrencyOpen(!isCurrencyOpen)}
                                >
                                    <GlobalOutlined className="mr-1" />
                                    <span className="hidden sm:inline">{getCurrencySymbol()} {currentCurrency}</span>
                                    <span className="sm:hidden">{currentCurrency}</span>
                                    <DownOutlined className="ml-1 text-xs" />
                                </button>
                                
                                {/* Currency Dropdown Menu */}
                                {isCurrencyOpen && (
                                    <div className="absolute top-full right-0 mt-1 w-40 sm:w-48 bg-white border border-gray-300 rounded-md shadow-lg z-50 max-h-64 overflow-y-auto">
                                        <div className="py-2">
                                            {supportedCurrencies.map((currency) => (
                                                <button
                                                    key={currency.code}
                                                    className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 hover:text-orange-500 ${
                                                        currentCurrency === currency.code ? 'bg-orange-50 text-orange-500' : 'text-gray-700'
                                                    }`}
                                                    onClick={() => {
                                                        changeCurrency(currency.code);
                                                        setIsCurrencyOpen(false);
                                                    }}
                                                    disabled={loading}
                                                >
                                                    <span className="mr-2">{currency.symbol}</span>
                                                    {currency.code} - {currency.name}
                                                </button>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Mobile Currency Dropdown */}
                        <div className="md:hidden ml-auto mr-2">
                            <div className="relative">
                                <button 
                                    className="flex items-center text-gray-700 hover:text-orange-500 p-2 rounded-md active:bg-gray-100 transition-colors touch-manipulation"
                                    onClick={() => setIsCurrencyOpen(!isCurrencyOpen)}
                                    style={{ minHeight: '44px', minWidth: '44px' }}
                                >
                                    <GlobalOutlined className="mr-1" />
                                    <span>{currentCurrency}</span>
                                    <DownOutlined className="ml-1 text-xs" />
                                </button>
                                
                                {/* Currency Dropdown Menu */}
                                {isCurrencyOpen && (
                                    <div className="absolute top-full right-0 mt-1 w-40 bg-white border border-gray-300 rounded-md shadow-lg z-50 max-h-64 overflow-y-auto">
                                        <div className="py-2">
                                            {supportedCurrencies.map((currency) => (
                                                <button
                                                    key={currency.code}
                                                    className={`block w-full text-left px-4 py-3 text-sm hover:bg-gray-100 hover:text-orange-500 active:bg-gray-200 transition-colors touch-manipulation ${
                                                        currentCurrency === currency.code ? 'bg-orange-50 text-orange-500' : 'text-gray-700'
                                                    }`}
                                                    onClick={() => {
                                                        changeCurrency(currency.code);
                                                        setIsCurrencyOpen(false);
                                                    }}
                                                    disabled={loading}
                                                    style={{ minHeight: '44px' }}
                                                >
                                                    <span className="mr-2">{currency.symbol}</span>
                                                    {currency.code}
                                                </button>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Notification Bar */}
            <div className="bg-orange-50 border-b border-orange-200 w-full">
                <div className="container mx-auto px-2 sm:px-4">
                    <div className="flex items-center h-6 sm:h-8 text-xs text-gray-600">
                        <div className="flex items-center">
                            <InfoCircleOutlined className="mr-1 sm:mr-2 text-orange-500 flex-shrink-0" />
                            <span className="truncate">
                                <span className="hidden sm:inline">Welcome to Alicartify - Your trusted global marketplace for quality products</span>
                                <span className="sm:hidden">Welcome to Alicartify marketplace</span>
                            </span>
                            <a href="/about" className="ml-1 sm:ml-2 text-orange-500 hover:underline whitespace-nowrap">learn more</a>
                            <span className="ml-1">›</span>
                        </div>
                    </div>
                </div>
            </div>
            
            {/* Overlay to close currency dropdown when clicking outside */}
            {isCurrencyOpen && (
                <div 
                    className="fixed inset-0 z-40" 
                    onClick={closeCurrencyDropdown}
                ></div>
            )}
        </>
    );
}

export default Header;