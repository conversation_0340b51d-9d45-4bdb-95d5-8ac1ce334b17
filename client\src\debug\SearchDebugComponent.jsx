import React, { useEffect } from 'react';
import { useSearch } from '../contexts/SearchContext';
import { useCurrency } from '../contexts/CurrencyContext';

const SearchDebugComponent = () => {
  const { searchTerm, searchResults, loading } = useSearch();
  const { currentCurrency, filterProductsByCurrency } = useCurrency();

  useEffect(() => {
    console.log('🐛 Search Debug Component - State Update:');
    console.log('  - Search Term:', searchTerm);
    console.log('  - Current Currency:', currentCurrency);
    console.log('  - Search Results Count:', searchResults.length);
    console.log('  - Loading:', loading);
    console.log('  - Search Results:', searchResults);
  }, [searchTerm, searchResults, loading, currentCurrency]);

  if (!searchTerm) {
    return (
      <div className="fixed bottom-4 right-4 bg-blue-100 border border-blue-300 rounded-lg p-4 max-w-sm z-50">
        <h4 className="font-bold text-blue-800">Search Debug</h4>
        <p className="text-sm text-blue-700">No search term active</p>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-blue-100 border border-blue-300 rounded-lg p-4 max-w-sm z-50">
      <h4 className="font-bold text-blue-800">Search Debug</h4>
      <div className="text-sm text-blue-700 space-y-1">
        <p><strong>Term:</strong> "{searchTerm}"</p>
        <p><strong>Currency:</strong> {currentCurrency}</p>
        <p><strong>Results:</strong> {searchResults.length}</p>
        <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
        {searchResults.length > 0 && (
          <div>
            <p><strong>First Result:</strong></p>
            <p className="text-xs">
              {searchResults[0].name} - {searchResults[0].pricing?.currency || 'No currency'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchDebugComponent;