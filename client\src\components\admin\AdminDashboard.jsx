import React, { useState, useEffect } from 'react';
import { Layout, Card, Row, Col, Statistic, Typography, Space, Avatar, Badge, message, Spin, Alert } from 'antd';
import {
  UserOutlined,
  ShopOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  RiseOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import { dashboardApi, analyticsApi } from '../../services/adminApi';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

// Add dayjs plugins
dayjs.extend(relativeTime);

const { Content } = Layout;
const { Title: AntTitle, Text } = Typography;

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const AdminDashboard = () => {
  // State management
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [realtimeData, setRealtimeData] = useState(null);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  // Chart data states
  const [revenueData, setRevenueData] = useState(null);
  const [orderStatusData, setOrderStatusData] = useState(null);
  const [categoryData, setCategoryData] = useState(null);
  const [userGrowthData, setUserGrowthData] = useState(null);

  useEffect(() => {
    fetchDashboardData();
    fetchRealtimeData();
    fetchAnalyticsData();

    // Set up real-time updates
    const interval = setInterval(() => {
      fetchRealtimeData();
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const response = await dashboardApi.getStats();
      setDashboardData(response.data.data);
      setError(null);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError('Failed to load dashboard data');
      message.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const fetchRealtimeData = async () => {
    try {
      const response = await dashboardApi.getRealTimeMetrics();
      setRealtimeData(response.data.data);
    } catch (error) {
      console.error('Error fetching realtime data:', error);
    }
  };

  const fetchAnalyticsData = async () => {
    try {
      // Fetch revenue analytics
      const revenueResponse = await dashboardApi.getAnalytics({ period: '30d', type: 'revenue' });
      const revenueAnalytics = revenueResponse.data.data.analytics;
      
      // Process revenue data for chart
      if (revenueAnalytics && revenueAnalytics.length > 0) {
        setRevenueData({
          labels: revenueAnalytics.map(item => dayjs(item._id).format('MMM DD')),
          datasets: [
            {
              label: 'Revenue ($)',
              data: revenueAnalytics.map(item => item.revenue || 0),
              borderColor: '#1890ff',
              backgroundColor: 'rgba(24, 144, 255, 0.1)',
              fill: true,
              tension: 0.4,
            },
          ],
        });
      }

      // Fetch user growth analytics
      const userResponse = await dashboardApi.getAnalytics({ period: '30d', type: 'users' });
      const userAnalytics = userResponse.data.data.analytics;
      
      if (userAnalytics && userAnalytics.length > 0) {
        setUserGrowthData({
          labels: userAnalytics.map(item => dayjs(item._id).format('MMM DD')),
          datasets: [
            {
              label: 'New Users',
              data: userAnalytics.map(item => item.total || 0),
              borderColor: '#52c41a',
              backgroundColor: 'rgba(82, 196, 26, 0.2)',
              fill: true,
              tension: 0.4,
            },
          ],
        });
      }
    } catch (error) {
      console.error('Error fetching analytics data:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      fetchDashboardData(),
      fetchRealtimeData(),
      fetchAnalyticsData()
    ]);
    setRefreshing(false);
    message.success('Dashboard refreshed');
  };

  // Process order status data when dashboard data is available
  useEffect(() => {
    if (dashboardData?.distributions?.orderStatus) {
      const orderStatusDistribution = dashboardData.distributions.orderStatus;
      setOrderStatusData({
        labels: orderStatusDistribution.map(item => item._id?.charAt(0).toUpperCase() + item._id?.slice(1) || 'Unknown'),
        datasets: [
          {
            data: orderStatusDistribution.map(item => item.count || 0),
            backgroundColor: [
              '#faad14', // pending
              '#1890ff', // processing
              '#52c41a', // shipped
              '#13c2c2', // delivered
              '#f5222d', // cancelled
            ],
          },
        ],
      });
    }

    // Process category data
    if (dashboardData?.topPerformers?.categories) {
      const categories = dashboardData.topPerformers.categories;
      setCategoryData({
        labels: categories.map(cat => cat.name),
        datasets: [
          {
            label: 'Revenue ($)',
            data: categories.map(cat => cat.statistics?.totalRevenue || 0),
            backgroundColor: [
              '#1890ff',
              '#52c41a',
              '#faad14',
              '#f5222d',
              '#722ed1',
              '#13c2c2',
            ],
          },
        ],
      });
    }
  }, [dashboardData]);

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
      },
    },
  };

  // Helper function to get growth indicator
  const getGrowthIndicator = (current, previous) => {
    if (!previous || previous === 0) return { icon: <RiseOutlined />, color: '#52c41a' };
    const growth = ((current - previous) / previous) * 100;
    return growth >= 0 
      ? { icon: <ArrowUpOutlined />, color: '#52c41a' }
      : { icon: <ArrowDownOutlined />, color: '#f5222d' };
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
        <Spin size="large" tip="Loading dashboard data..." />
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="Error"
          description={error}
          type="error"
          action={
            <Space>
              <ReloadOutlined onClick={handleRefresh} style={{ cursor: 'pointer' }} />
            </Space>
          }
        />
      </div>
    );
  }

  return (
    <Layout style={{ minHeight: '100vh', background: '#f0f2f5' }}>
      <Content style={{ padding: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
          <div>
            <AntTitle level={2} style={{ margin: 0, color: '#1890ff' }}>
              Admin Dashboard
            </AntTitle>
            <p style={{ color: '#666', marginTop: '8px' }}>
              Welcome back! Here's what's happening with your platform today.
            </p>
          </div>
          <Space>
            <Text type="secondary">
              Last updated: {realtimeData?.timestamp ? dayjs(realtimeData.timestamp).format('HH:mm:ss') : 'Never'}
            </Text>
            <ReloadOutlined 
              onClick={handleRefresh} 
              spin={refreshing}
              style={{ cursor: 'pointer', color: '#1890ff' }}
            />
          </Space>
        </div>

        {/* Statistics Cards */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Users"
                value={dashboardData?.overview?.users?.totalUsers || 0}
                prefix={<UserOutlined style={{ color: '#1890ff' }} />}
                suffix={
                  dashboardData?.growth?.users && (
                    <Space>
                      <Text type={dashboardData.growth.users.growth >= 0 ? 'success' : 'danger'}>
                        {Math.abs(dashboardData.growth.users.growth)}%
                      </Text>
                      {getGrowthIndicator(dashboardData.growth.users.current, dashboardData.growth.users.previous).icon}
                    </Space>
                  )
                }
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Active Vendors"
                value={dashboardData?.overview?.vendors?.activeVendors || 0}
                prefix={<ShopOutlined style={{ color: '#52c41a' }} />}
                suffix={
                  <Badge count={<RiseOutlined style={{ color: '#52c41a' }} />} />
                }
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Orders"
                value={dashboardData?.overview?.orders?.totalOrders || 0}
                prefix={<ShoppingCartOutlined style={{ color: '#faad14' }} />}
                suffix={
                  dashboardData?.growth?.orders && (
                    <Space>
                      <Text type={dashboardData.growth.orders.growth >= 0 ? 'success' : 'danger'}>
                        {Math.abs(dashboardData.growth.orders.growth)}%
                      </Text>
                      {getGrowthIndicator(dashboardData.growth.orders.current, dashboardData.growth.orders.previous).icon}
                    </Space>
                  )
                }
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Revenue"
                value={dashboardData?.overview?.orders?.totalRevenue || 0}
                prefix={<DollarOutlined style={{ color: '#f5222d' }} />}
                precision={2}
                suffix={
                  dashboardData?.growth?.revenue && (
                    <Space>
                      <Text type={dashboardData.growth.revenue.growth >= 0 ? 'success' : 'danger'}>
                        {Math.abs(dashboardData.growth.revenue.growth)}%
                      </Text>
                      {getGrowthIndicator(dashboardData.growth.revenue.current, dashboardData.growth.revenue.previous).icon}
                    </Space>
                  )
                }
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>

        {/* Charts Row 1 */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} lg={12}>
            <Card 
              title="Revenue Trends" 
              extra={<Badge status="processing" text="Live Data" />}
              style={{ height: '400px' }}
            >
              <div style={{ height: '300px' }}>
                {revenueData ? (
                  <Line data={revenueData} options={chartOptions} />
                ) : (
                  <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                    <Spin tip="Loading revenue data..." />
                  </div>
                )}
              </div>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card 
              title="Sales by Category" 
              extra={<Badge status="success" text="Updated" />}
              style={{ height: '400px' }}
            >
              <div style={{ height: '300px' }}>
                {categoryData ? (
                  <Bar data={categoryData} options={chartOptions} />
                ) : (
                  <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                    <Spin tip="Loading category data..." />
                  </div>
                )}
              </div>
            </Card>
          </Col>
        </Row>

        {/* Charts Row 2 */}
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card 
              title="Order Status Distribution" 
              extra={<Badge status="default" text="Real-time" />}
              style={{ height: '400px' }}
            >
              <div style={{ height: '300px' }}>
                {orderStatusData ? (
                  <Doughnut data={orderStatusData} options={doughnutOptions} />
                ) : (
                  <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                    <Spin tip="Loading order status data..." />
                  </div>
                )}
              </div>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card 
              title="User Growth Metrics" 
              extra={<Badge status="processing" text="Monthly" />}
              style={{ height: '400px' }}
            >
              <div style={{ height: '300px' }}>
                {userGrowthData ? (
                  <Line data={userGrowthData} options={chartOptions} />
                ) : (
                  <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                    <Spin tip="Loading user growth data..." />
                  </div>
                )}
              </div>
            </Card>
          </Col>
        </Row>

        {/* Recent Activity */}
        <Row style={{ marginTop: '24px' }}>
          <Col span={24}>
            <Card title="Recent Activity" extra={<a href="#">View All</a>}>
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                {dashboardData?.recentActivity ? (
                  <>
                    {/* Recent Users */}
                    {dashboardData.recentActivity.users?.slice(0, 2).map((user, index) => (
                      <div key={`user-${index}`} style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                        <Avatar icon={<UserOutlined />} style={{ backgroundColor: '#1890ff' }} />
                        <div>
                          <div style={{ fontWeight: 500 }}>New user registration</div>
                          <div style={{ color: '#666', fontSize: '12px' }}>
                            {user.firstName} {user.lastName} joined the platform
                          </div>
                        </div>
                        <div style={{ marginLeft: 'auto', color: '#666', fontSize: '12px' }}>
                          {dayjs(user.createdAt).fromNow()}
                        </div>
                      </div>
                    ))}
                    
                    {/* Recent Vendors */}
                    {dashboardData.recentActivity.vendors?.slice(0, 1).map((vendor, index) => (
                      <div key={`vendor-${index}`} style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                        <Avatar icon={<ShopOutlined />} style={{ backgroundColor: '#52c41a' }} />
                        <div>
                          <div style={{ fontWeight: 500 }}>New vendor registration</div>
                          <div style={{ color: '#666', fontSize: '12px' }}>
                            {vendor.businessName} has registered
                          </div>
                        </div>
                        <div style={{ marginLeft: 'auto', color: '#666', fontSize: '12px' }}>
                          {dayjs(vendor.createdAt).fromNow()}
                        </div>
                      </div>
                    ))}
                    
                    {/* Recent Orders */}
                    {dashboardData.recentActivity.orders?.slice(0, 2).map((order, index) => (
                      <div key={`order-${index}`} style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                        <Avatar icon={<ShoppingCartOutlined />} style={{ backgroundColor: '#faad14' }} />
                        <div>
                          <div style={{ fontWeight: 500 }}>New order placed</div>
                          <div style={{ color: '#666', fontSize: '12px' }}>
                            Order {order.orderNumber} worth ${order.pricing?.total?.toFixed(2) || '0.00'}
                          </div>
                        </div>
                        <div style={{ marginLeft: 'auto', color: '#666', fontSize: '12px' }}>
                          {dayjs(order.createdAt).fromNow()}
                        </div>
                      </div>
                    ))}
                  </>
                ) : (
                  <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
                    <Spin tip="Loading recent activity..." />
                  </div>
                )}
              </Space>
            </Card>
          </Col>
        </Row>
      </Content>
    </Layout>
  );
};

export default AdminDashboard;