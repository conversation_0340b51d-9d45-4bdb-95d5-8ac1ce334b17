const mongoose = require('mongoose');
const Cart = require('../../../models/Cart');
const Product = require('../../../models/Product');
const { mockUser, mockVendor, mockProduct, cleanupDatabase } = require('../../helpers/testSetup');

beforeAll(async () => {
  const mongoURI = 'mongodb://127.0.0.1/alicartify-unit-test';
  await mongoose.connect(mongoURI, { useNewUrlParser: true, useUnifiedTopology: true });
  await cleanupDatabase();
});

afterAll(async () => {
  await cleanupDatabase();
  await mongoose.connection.close();
});

beforeEach(async () => {
  await Cart.deleteMany({});
});

describe('Cart Model Tests', () => {
  describe('Cart Instance Methods', () => {
    it('should add item to cart', async () => {
      const cart = new Cart({ customer: mockUser._id });
      await cart.save();

      await cart.addItem(mockProduct._id, mockVendor._id, 2, 100);
      
      expect(cart.items.length).toBe(1);
      expect(cart.items[0].quantity).toBe(2);
      expect(cart.items[0].priceAtAdd).toBe(100);
    });

    it('should update item quantity', async () => {
      const cart = new Cart({ customer: mockUser._id });
      await cart.addItem(mockProduct._id, mockVendor._id, 2, 100);

      await cart.updateItemQuantity(mockProduct._id, 5);
      
      expect(cart.items[0].quantity).toBe(5);
    });

    it('should remove item when quantity is 0', async () => {
      const cart = new Cart({ customer: mockUser._id });
      await cart.addItem(mockProduct._id, mockVendor._id, 2, 100);

      await cart.updateItemQuantity(mockProduct._id, 0);
      
      expect(cart.items.length).toBe(0);
    });

    it('should remove item from cart', async () => {
      const cart = new Cart({ customer: mockUser._id });
      await cart.addItem(mockProduct._id, mockVendor._id, 2, 100);

      await cart.removeItem(mockProduct._id);
      
      expect(cart.items.length).toBe(0);
    });

    it('should clear entire cart', async () => {
      const cart = new Cart({ customer: mockUser._id });
      await cart.addItem(mockProduct._id, mockVendor._id, 2, 100);

      await cart.clearCart();
      
      expect(cart.items.length).toBe(0);
    });

    it('should bulk add items', async () => {
      const cart = new Cart({ customer: mockUser._id });
      
      const itemsToAdd = [
        { productId: mockProduct._id, vendorId: mockVendor._id, quantity: 2, price: 100 },
        { productId: new mongoose.Types.ObjectId(), vendorId: mockVendor._id, quantity: 1, price: 50 }
      ];

      await cart.bulkAddItems(itemsToAdd);
      
      expect(cart.items.length).toBe(2);
      expect(cart.totalItems).toBe(3);
    });

    it('should bulk update items', async () => {
      const cart = new Cart({ customer: mockUser._id });
      await cart.addItem(mockProduct._id, mockVendor._id, 2, 100);

      const itemsToUpdate = [
        { productId: mockProduct._id, quantity: 5 }
      ];

      await cart.bulkUpdateItems(itemsToUpdate);
      
      expect(cart.items[0].quantity).toBe(5);
    });
  });

  describe('Cart Validation', () => {
    it('should validate maximum items in cart', async () => {
      const cart = new Cart({ customer: mockUser._id });
      
      // Add 51 different items (exceeds limit of 50)
      const items = [];
      for (let i = 0; i < 51; i++) {
        items.push({
          product: new mongoose.Types.ObjectId(),
          vendor: mockVendor._id,
          quantity: 1,
          priceAtAdd: 10
        });
      }
      
      cart.items = items;
      
      await expect(cart.save()).rejects.toThrow('Cart cannot contain more than 50 different items');
    });

    it('should validate maximum total items in cart', async () => {
      const cart = new Cart({ 
        customer: mockUser._id,
        totalItems: 101 // Exceeds limit of 100
      });
      
      await expect(cart.save()).rejects.toThrow('Cart cannot contain more than 100 total items');
    });
  });

  describe('Cart Pre-save Middleware', () => {
    it('should update totals before saving', async () => {
      const cart = new Cart({ customer: mockUser._id });
      await cart.addItem(mockProduct._id, mockVendor._id, 2, 100);
      await cart.addItem(new mongoose.Types.ObjectId(), mockVendor._id, 3, 50);

      expect(cart.totalItems).toBe(5);
      expect(cart.totalAmount).toBe(350); // (2 * 100) + (3 * 50)
    });

    it('should update lastUpdated timestamp', async () => {
      const cart = new Cart({ customer: mockUser._id });
      const beforeSave = new Date();
      
      await cart.addItem(mockProduct._id, mockVendor._id, 1, 100);
      
      expect(cart.lastUpdated.getTime()).toBeGreaterThanOrEqual(beforeSave.getTime());
    });
  });

  describe('Cart Virtual Properties', () => {
    it('should calculate vendor count correctly', async () => {
      const cart = new Cart({ customer: mockUser._id });
      const vendor2Id = new mongoose.Types.ObjectId();
      
      await cart.addItem(mockProduct._id, mockVendor._id, 2, 100);
      await cart.addItem(new mongoose.Types.ObjectId(), vendor2Id, 1, 50);
      
      expect(cart.vendorCount).toBe(2);
    });
  });

  describe('Cart Static Methods', () => {
    it('should find cart by customer with populated fields', async () => {
      // Note: This would require actual Product and User documents
      // For now, just test the method exists
      expect(typeof Cart.findByCustomer).toBe('function');
    });
  });
});
