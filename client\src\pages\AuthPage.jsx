import React, { useState, useEffect } from 'react';
import { message } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import AuthSegment from '../components/ui/AuthSegment';
import LoginForm from '../components/auth/LoginForm';
import SignUpForm from '../components/auth/SignUpForm';
import ForgotPasswordForm from '../components/auth/ForgotPasswordForm';
import { useAuth } from '../hooks/useAuth';
import { authAPI } from '../utils/authApi';

const AuthPage = () => {
  const [userType, setUserType] = useState('customer');
  const [activeTab, setActiveTab] = useState('login');
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  
  const { login, register, isAuthenticated, error, clearError } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      const from = location.state?.from?.pathname || '/home';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  // Show error messages
  useEffect(() => {
    if (error) {
      message.error(error);
      clearError();
    }
  }, [error, clearError]);

  const handleForgotPassword = () => {
    setShowForgotPassword(true);
  };

  const handleBackToLogin = () => {
    setShowForgotPassword(false);
    setActiveTab('login');
  };

  const handleSendOTP = async (otpData) => {
    try {
      const result = await authAPI.sendOTP(otpData);
      return result;
    } catch (error) {
      console.error('Send OTP error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to send OTP'
      };
    }
  };

  const handleVerifyOTP = async (otpData) => {
    try {
      const result = await authAPI.verifyOTP(otpData);
      return result;
    } catch (error) {
      console.error('Verify OTP error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'OTP verification failed'
      };
    }
  };

  const handleLogin = async (credentials) => {
    const result = await login(credentials, userType);
    if (result.success) {
      message.success('Login successful!');
    }
    return result;
  };

  const handleRegister = async (userData) => {
    const result = await register(userData, userType);
    if (result.success) {
      message.success('Account created successfully!');
    }
    return result;
  };

  const renderAuthContent = () => {
    if (showForgotPassword) {
      return <ForgotPasswordForm onBackToLogin={handleBackToLogin} />;
    }

    return (
      <>
        {/* Tab Navigation */}
        <div className="flex bg-gray-100 rounded-lg p-1 mb-8">
          <button
            onClick={() => setActiveTab('login')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200 ${
              activeTab === 'login'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Sign In
          </button>
          <button
            onClick={() => setActiveTab('signup')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200 ${
              activeTab === 'signup'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Sign Up
          </button>
        </div>

        {/* Auth Forms */}
        {activeTab === 'login' ? (
          <LoginForm
            userType={userType}
            onForgotPassword={handleForgotPassword}
            onLogin={handleLogin}
            onSendOTP={handleSendOTP}
            onVerifyOTP={handleVerifyOTP}
          />
        ) : (
          <SignUpForm
            userType={userType}
            onRegister={handleRegister}
            onSendOTP={handleSendOTP}
            onVerifyOTP={handleVerifyOTP}
          />
        )}
      </>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-50 flex items-center justify-center p-4">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ff6b35' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="relative w-full max-w-6xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Left Side - Branding */}
          <div className="hidden lg:block text-center lg:text-left">
            <div className="mb-8">
              <img 
                src="https://i.ibb.co/9mQR1Z1H/Alicartify-Logo.png" 
                alt="Alicartify Logo"
                className="h-16 w-auto mx-auto lg:mx-0 mb-6"
              />
              <h1 className="text-4xl xl:text-5xl font-bold text-gray-800 mb-4">
                Welcome to{' '}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-500 to-orange-600">
                  Alicartify
                </span>
              </h1>
              <p className="text-xl text-gray-600 mb-8">
                Your ultimate multi-vendor eCommerce platform. Connect, sell, and grow your business with us.
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div className="bg-white/50 backdrop-blur-sm rounded-xl p-6 border border-orange-100">
                <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mb-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-800 mb-2">For Customers</h3>
                <p className="text-gray-600 text-sm">
                  Discover amazing products from trusted vendors worldwide
                </p>
              </div>

              <div className="bg-white/50 backdrop-blur-sm rounded-xl p-6 border border-orange-100">
                <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mb-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-800 mb-2">For Vendors</h3>
                <p className="text-gray-600 text-sm">
                  Start selling and reach millions of customers globally
                </p>
              </div>
            </div>
          </div>

          {/* Right Side - Auth Form */}
          <div className="w-full">
            <div className="w-full max-w-lg mx-auto bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200 p-8">
              {/* Mobile Logo */}
              <div className="lg:hidden text-center mb-6">
                <img 
                  src="https://i.ibb.co/9mQR1Z1H/Alicartify-Logo.png" 
                  alt="Alicartify Logo"
                  className="h-12 w-auto mx-auto mb-4"
                />
                <h1 className="text-2xl font-bold text-gray-800">
                  Welcome to{' '}
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-500 to-orange-600">
                    Alicartify
                  </span>
                </h1>
              </div>

              {/* User Type Selector */}
              {!showForgotPassword && (
                <div className="mb-8">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">
                    Join as:
                  </h3>
                  <div className="flex justify-center">
                    <AuthSegment
                      value={userType}
                      onChange={setUserType}
                    />
                  </div>
                </div>
              )}

              {/* Auth Content */}
              {renderAuthContent()}

              {/* Footer */}
              {!showForgotPassword && (
                <div className="mt-8 text-center">
                  <p className="text-gray-500 text-sm">
                    {activeTab === 'login' ? "Don't have an account? " : "Already have an account? "}
                    <button
                      onClick={() => setActiveTab(activeTab === 'login' ? 'signup' : 'login')}
                      className="text-orange-600 hover:text-orange-700 font-medium transition-colors"
                    >
                      {activeTab === 'login' ? 'Sign up here' : 'Sign in here'}
                    </button>
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthPage;