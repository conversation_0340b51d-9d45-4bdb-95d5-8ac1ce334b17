const mongoose = require('mongoose');

const orderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    unique: true,
    required: true
  },
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  items: [{
    product: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product',
      required: true
    },
    vendor: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Vendor',
      required: true
    },
    name: {
      type: String,
      required: true
    },
    sku: {
      type: String,
      required: true
    },
    image: String,
    quantity: {
      type: Number,
      required: true,
      min: 1
    },
    unitPrice: {
      type: Number,
      required: true,
      min: 0
    },
    totalPrice: {
      type: Number,
      required: true,
      min: 0
    },
    variant: {
      name: String,
      options: [{
        name: String,
        value: String
      }]
    },
    status: {
      type: String,
      enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned', 'refunded'],
      default: 'pending'
    },
    vendorCommission: {
      type: Number,
      default: 0
    },
    platformCommission: {
      type: Number,
      default: 0
    }
  }],
  billing: {
    firstName: {
      type: String,
      required: true
    },
    lastName: {
      type: String,
      required: true
    },
    email: {
      type: String,
      required: true
    },
    phone: {
      type: String,
      required: true
    },
    address: {
      street: {
        type: String,
        required: true
      },
      city: {
        type: String,
        required: true
      },
      state: {
        type: String,
        required: true
      },
      zipCode: {
        type: String,
        required: true
      },
      country: {
        type: String,
        required: true
      }
    }
  },
  shipping: {
    firstName: {
      type: String,
      required: true
    },
    lastName: {
      type: String,
      required: true
    },
    address: {
      street: {
        type: String,
        required: true
      },
      city: {
        type: String,
        required: true
      },
      state: {
        type: String,
        required: true
      },
      zipCode: {
        type: String,
        required: true
      },
      country: {
        type: String,
        required: true
      }
    },
    method: {
      type: String,
      enum: ['standard', 'express', 'overnight', 'pickup'],
      default: 'standard'
    },
    cost: {
      type: Number,
      default: 0,
      min: 0
    },
    estimatedDelivery: Date,
    trackingNumber: String,
    carrier: String
  },
  payment: {
    method: {
      type: String,
      enum: ['credit_card', 'debit_card', 'paypal', 'stripe', 'razorpay', 'bank_transfer', 'cash_on_delivery', 'cod'],
      required: true
    },
    status: {
      type: String,
      enum: ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded', 'partially_refunded'],
      default: 'pending'
    },
    transactionId: String,
    paymentIntentId: String,
    paidAt: Date,
    refundedAt: Date,
    refundAmount: {
      type: Number,
      default: 0
    },
    currency: {
      type: String,
      default: 'INR'
    }
  },
  pricing: {
    subtotal: {
      type: Number,
      required: true,
      min: 0
    },
    tax: {
      type: Number,
      default: 0,
      min: 0
    },
    shipping: {
      type: Number,
      default: 0,
      min: 0
    },
    discount: {
      type: Number,
      default: 0,
      min: 0
    },
    total: {
      type: Number,
      required: true,
      min: 0
    }
  },
  discounts: [{
    code: String,
    type: {
      type: String,
      enum: ['percentage', 'fixed', 'free_shipping']
    },
    value: Number,
    amount: Number
  }],
  orderStatus: {
    type: String,
    enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned'],
    default: 'pending'
  },
  timeline: [{
    status: {
      type: String,
      required: true
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    note: String,
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  notes: [{
    type: {
      type: String,
      enum: ['customer', 'vendor', 'admin', 'system'],
      required: true
    },
    message: {
      type: String,
      required: true
    },
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    isPrivate: {
      type: Boolean,
      default: false
    }
  }],
  customerNotes: String,
  vendorNotes: String,
  adminNotes: String,
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  source: {
    type: String,
    enum: ['web', 'mobile', 'api', 'admin'],
    default: 'web'
  },
  tags: [{
    type: String,
    trim: true
  }],
  metadata: {
    userAgent: String,
    ipAddress: String,
    referrer: String,
    utm: {
      source: String,
      medium: String,
      campaign: String,
      term: String,
      content: String
    }
  },
  // Shiprocket integration data for multi-vendor orders
  shiprocketOrders: [{
    vendorId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Vendor'
    },
    vendorName: String,
    shiprocketOrderId: String,
    awbCode: String,
    courierName: String,
    pickupLocation: String,
    items: [{
      name: String,
      quantity: Number,
      sku: String
    }],
    error: String,
    createdAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes (orderNumber index is automatically created by unique: true)
orderSchema.index({ customer: 1 });
orderSchema.index({ 'items.vendor': 1 });
orderSchema.index({ status: 1 });
orderSchema.index({ 'payment.status': 1 });
orderSchema.index({ createdAt: -1 });
orderSchema.index({ 'pricing.total': -1 });
orderSchema.index({ 'shipping.estimatedDelivery': 1 });

// Virtual for order total items count
orderSchema.virtual('totalItems').get(function() {
  if (!this.items || !Array.isArray(this.items)) {
    return 0;
  }
  return this.items.reduce((total, item) => total + item.quantity, 0);
});

// Virtual for unique vendors count
orderSchema.virtual('vendorCount').get(function() {
  if (!this.items || !Array.isArray(this.items)) {
    return 0;
  }
  const vendors = new Set(this.items.map(item => item.vendor.toString()));
  return vendors.size;
});

// Virtual for order age in days
orderSchema.virtual('ageInDays').get(function() {
  return Math.floor((Date.now() - this.createdAt) / (1000 * 60 * 60 * 24));
});

// Virtual for estimated delivery status
orderSchema.virtual('deliveryStatus').get(function() {
  if (!this.shipping.estimatedDelivery) return 'unknown';
  
  const now = new Date();
  const estimated = new Date(this.shipping.estimatedDelivery);
  
  if (this.status === 'delivered') return 'delivered';
  if (now > estimated) return 'overdue';
  if (now.getTime() + (24 * 60 * 60 * 1000) > estimated.getTime()) return 'due_soon';
  return 'on_time';
});

// Pre-save middleware to generate order number
orderSchema.pre('save', async function(next) {
  try {
    if (this.isNew && !this.orderNumber) {
      const date = new Date();
      const year = date.getFullYear().toString().slice(-2);
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      
      // Generate a simple order number with timestamp
      const timestamp = Date.now().toString().slice(-6);
      this.orderNumber = `ORD${year}${month}${day}${timestamp}`;
    }
    
    // Add status change to timeline
    if (this.isModified('status') && !this.isNew) {
      this.timeline.push({
        status: this.status,
        timestamp: new Date()
      });
    }
    
    next();
  } catch (error) {
    next(error);
  }
});

// Static method to get order statistics
orderSchema.statics.getStatistics = async function() {
  const stats = await this.aggregate([
    {
      $group: {
        _id: null,
        totalOrders: { $sum: 1 },
        pendingOrders: {
          $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
        },
        confirmedOrders: {
          $sum: { $cond: [{ $eq: ['$status', 'confirmed'] }, 1, 0] }
        },
        processingOrders: {
          $sum: { $cond: [{ $eq: ['$status', 'processing'] }, 1, 0] }
        },
        shippedOrders: {
          $sum: { $cond: [{ $eq: ['$status', 'shipped'] }, 1, 0] }
        },
        deliveredOrders: {
          $sum: { $cond: [{ $eq: ['$status', 'delivered'] }, 1, 0] }
        },
        cancelledOrders: {
          $sum: { $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0] }
        },
        returnedOrders: {
          $sum: { $cond: [{ $eq: ['$status', 'returned'] }, 1, 0] }
        },
        totalRevenue: { $sum: '$pricing.total' },
        averageOrderValue: { $avg: '$pricing.total' },
        totalItems: { 
          $sum: { 
            $reduce: {
              input: '$items',
              initialValue: 0,
              in: { $add: ['$$value', '$$this.quantity'] }
            }
          }
        }
      }
    }
  ]);
  
  return stats[0] || {
    totalOrders: 0,
    pendingOrders: 0,
    confirmedOrders: 0,
    processingOrders: 0,
    shippedOrders: 0,
    deliveredOrders: 0,
    cancelledOrders: 0,
    returnedOrders: 0,
    totalRevenue: 0,
    averageOrderValue: 0,
    totalItems: 0
  };
};

// Static method to get recent orders
orderSchema.statics.getRecent = function(limit = 10) {
  return this.find()
    .populate('customer', 'firstName lastName email')
    .populate('items.vendor', 'businessName')
    .sort({ createdAt: -1 })
    .limit(limit)
    .select('orderNumber customer status pricing.total createdAt items');
};

// Static method to get orders by date range
orderSchema.statics.getOrdersByDateRange = function(startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        createdAt: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      }
    },
    {
      $group: {
        _id: {
          $dateToString: {
            format: '%Y-%m-%d',
            date: '$createdAt'
          }
        },
        count: { $sum: 1 },
        revenue: { $sum: '$pricing.total' }
      }
    },
    {
      $sort: { _id: 1 }
    }
  ]);
};

// Static method to get top customers
orderSchema.statics.getTopCustomers = function(limit = 10) {
  return this.aggregate([
    {
      $group: {
        _id: '$customer',
        totalOrders: { $sum: 1 },
        totalSpent: { $sum: '$pricing.total' },
        lastOrderDate: { $max: '$createdAt' }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'customer'
      }
    },
    {
      $unwind: '$customer'
    },
    {
      $sort: { totalSpent: -1 }
    },
    {
      $limit: limit
    },
    {
      $project: {
        customer: {
          _id: '$customer._id',
          firstName: '$customer.firstName',
          lastName: '$customer.lastName',
          email: '$customer.email'
        },
        totalOrders: 1,
        totalSpent: 1,
        lastOrderDate: 1
      }
    }
  ]);
};

// Instance method to update status
orderSchema.methods.updateStatus = function(newStatus, note, updatedBy) {
  this.status = newStatus;
  this.timeline.push({
    status: newStatus,
    timestamp: new Date(),
    note: note,
    updatedBy: updatedBy
  });
  
  return this.save();
};

// Instance method to add note
orderSchema.methods.addNote = function(type, message, author, isPrivate = false) {
  this.notes.push({
    type: type,
    message: message,
    author: author,
    timestamp: new Date(),
    isPrivate: isPrivate
  });
  
  return this.save();
};

// Instance method to calculate commission
orderSchema.methods.calculateCommissions = function() {
  this.items.forEach(item => {
    // Platform commission (default 5%)
    item.platformCommission = item.totalPrice * 0.05;
    // Vendor gets the rest
    item.vendorCommission = item.totalPrice - item.platformCommission;
  });
  
  return this.save();
};

module.exports = mongoose.model('Order', orderSchema);