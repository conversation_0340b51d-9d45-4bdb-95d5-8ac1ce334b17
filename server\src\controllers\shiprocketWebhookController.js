const { Order, Shipping } = require('../models');

const handleShiprocketWebhook = async (req, res) => {
  try {
    const webhookData = req.body;
    
    console.log('Received Shiprocket webhook:', webhookData);
    
    // Extract order information from webhook
    const { order_id, awb, current_status, shipment_status, shipped_date, delivered_date } = webhookData;
    
    if (!order_id) {
      return res.status(400).json({ error: 'Missing order_id in webhook data' });
    }
    
    // Find the order by order number
    const order = await Order.findOne({ orderNumber: order_id });
    
    if (!order) {
      console.log(`Order not found for order_id: ${order_id}`);
      return res.status(404).json({ error: 'Order not found' });
    }
    
    // Update order tracking information
    if (awb) {
      order.shipping.trackingNumber = awb;
    }
    
    // Map Shiprocket status to our order status
    let newStatus = order.orderStatus;
    
    switch (current_status?.toLowerCase()) {
      case 'shipped':
      case 'in_transit':
        newStatus = 'shipped';
        if (shipped_date) {
          order.shipping.estimatedDelivery = new Date(shipped_date);
        }
        break;
      case 'out_for_delivery':
        newStatus = 'shipped'; // We don't have out_for_delivery status
        break;
      case 'delivered':
        newStatus = 'delivered';
        if (delivered_date) {
          order.shipping.estimatedDelivery = new Date(delivered_date);
        }
        break;
      case 'returned':
        newStatus = 'returned';
        break;
      case 'cancelled':
        newStatus = 'cancelled';
        break;
    }
    
    // Update order status if it changed
    if (newStatus !== order.orderStatus) {
      order.orderStatus = newStatus;
      
      // Add to timeline
      order.timeline.push({
        status: newStatus,
        timestamp: new Date(),
        note: `Updated via Shiprocket webhook: ${current_status}`
      });
    }
    
    await order.save();
    
    console.log(`Order ${order_id} updated with status: ${newStatus}`);
    
    res.status(200).json({ 
      success: true, 
      message: 'Webhook processed successfully',
      order_id: order_id,
      status: newStatus
    });
    
  } catch (error) {
    console.error('Error processing Shiprocket webhook:', error);
    res.status(500).json({ 
      error: 'Internal server error',
      message: error.message 
    });
  }
};

module.exports = {
  handleShiprocketWebhook
};
