/**
 * Simple currency utility for formatting and symbols
 */

const currencySymbols = {
  INR: '₹',
  USD: '$',
  EUR: '€',
  GBP: '£',
  JPY: '¥',
  CNY: '¥',
  CAD: 'C$',
  AUD: 'A$',
  SGD: 'S$',
  AED: 'د.إ'
};

const currencyNames = {
  INR: 'Indian Rupee',
  USD: 'US Dollar',
  EUR: 'Euro',
  GBP: 'British Pound',
  JPY: 'Japanese Yen',
  CNY: 'Chinese Yuan',
  CAD: 'Canadian Dollar',
  AUD: 'Australian Dollar',
  SGD: 'Singapore Dollar',
  AED: 'UAE Dirham'
};

/**
 * Format currency amount with symbol
 */
function formatCurrency(amount, currency = 'INR', showSymbol = true) {
  const formattedAmount = new Intl.NumberFormat('en-IN', {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);

  if (showSymbol) {
    const symbol = currencySymbols[currency] || currency;
    return `${symbol} ${formattedAmount}`;
  }
  
  return formattedAmount;
}

/**
 * Get currency symbol
 */
function getCurrencySymbol(currency) {
  return currencySymbols[currency] || currency;
}

/**
 * Get currency name
 */
function getCurrencyName(currency) {
  return currencyNames[currency] || currency;
}

/**
 * Get all supported currencies
 */
function getSupportedCurrencies() {
  return Object.keys(currencySymbols).map(code => ({
    code,
    name: currencyNames[code],
    symbol: currencySymbols[code]
  }));
}

/**
 * Validate currency code
 */
function isValidCurrency(currency) {
  return currencySymbols.hasOwnProperty(currency);
}

module.exports = {
  formatCurrency,
  getCurrencySymbol,
  getCurrencyName,
  getSupportedCurrencies,
  isValidCurrency,
  currencySymbols,
  currencyNames
};
