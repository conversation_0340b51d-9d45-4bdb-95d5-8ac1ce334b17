const express = require('express');
const router = express.Router();

// Import controllers
const PayoutRequestController = require('../../controllers/admin/payoutRequestController');
const PayoutProcessingController = require('../../controllers/admin/payoutProcessingController');
const PayoutValidator = require('../../validators/payoutValidator');
const { authenticate } = require('../../middleware/auth');
const { requireAdmin } = require('../../middleware/adminAuth');

// Apply authentication and admin middleware to all routes
router.use(authenticate);
router.use(requireAdmin);

// ====== PAYOUT REQUEST MANAGEMENT ROUTES ======

/**
 * @route   GET /api/admin/payouts/dashboard
 * @desc    Get admin payout dashboard with statistics
 * @access  Private (Admin only)
 */
router.get('/dashboard', PayoutRequestController.getDashboard);

/**
 * @route   GET /api/admin/payouts
 * @desc    Get all payout requests with filtering and pagination
 * @access  Private (Admin only)
 */
router.get('/', PayoutValidator.validatePayoutQuery(), PayoutRequestController.getAllPayouts);

/**
 * @route   GET /api/admin/payouts/pending
 * @desc    Get pending payout requests for processing
 * @access  Private (Admin only)
 */
router.get('/pending', PayoutValidator.validatePayoutQuery(), PayoutRequestController.getPendingPayouts);

/**
 * @route   GET /api/admin/payouts/export
 * @desc    Export payout data in various formats
 * @access  Private (Admin only)
 */
router.get('/export', PayoutValidator.validateExportQuery(), PayoutRequestController.exportPayouts);

/**
 * @route   GET /api/admin/payouts/:payoutId
 * @desc    Get detailed information about a specific payout
 * @access  Private (Admin only)
 */
router.get('/:payoutId', PayoutValidator.validatePayoutId(), PayoutRequestController.getPayoutDetails);

// ====== PAYOUT PROCESSING ROUTES ======

/**
 * @route   PUT /api/admin/payouts/:payoutId/approve
 * @desc    Approve a payout request
 * @access  Private (Admin only)
 */
router.put('/:payoutId/approve', PayoutValidator.validatePayoutApproval(), PayoutProcessingController.approvePayout);

/**
 * @route   PUT /api/admin/payouts/:payoutId/reject
 * @desc    Reject a payout request
 * @access  Private (Admin only)
 */
router.put('/:payoutId/reject', PayoutValidator.validatePayoutRejection(), PayoutProcessingController.rejectPayout);

/**
 * @route   PUT /api/admin/payouts/:payoutId/processing
 * @desc    Mark payout as processing
 * @access  Private (Admin only)
 */
router.put('/:payoutId/processing', PayoutValidator.validatePayoutId(), PayoutProcessingController.markAsProcessing);

/**
 * @route   PUT /api/admin/payouts/:payoutId/complete
 * @desc    Complete a payout and mark as done
 * @access  Private (Admin only)
 */
router.put('/:payoutId/complete', PayoutValidator.validatePayoutCompletion(), PayoutProcessingController.completePayout);

/**
 * @route   PUT /api/admin/payouts/:payoutId/failed
 * @desc    Mark a payout as failed
 * @access  Private (Admin only)
 */
router.put('/:payoutId/failed', PayoutValidator.validateMarkAsFailed(), PayoutProcessingController.markAsFailed);

/**
 * @route   POST /api/admin/payouts/bulk-approve
 * @desc    Bulk approve multiple payout requests
 * @access  Private (Admin only)
 */
router.post('/bulk-approve', PayoutValidator.validateBulkPayoutOperation(), PayoutProcessingController.bulkApprovePayout);

module.exports = router;
