const { body, param, query } = require('express-validator');

/**
 * Validation rules for payout operations
 */
class PayoutValidator {

  /**
   * Validation for vendor payout request
   */
  static validatePayoutRequest() {
    return [
      body('amount')
        .isNumeric()
        .withMessage('Amount must be a number')
        .isFloat({ min: 50 })
        .withMessage('Minimum payout amount is $50')
        .isFloat({ max: 50000 })
        .withMessage('Maximum payout amount is $50,000'),

      body('method')
        .isIn(['bank_transfer', 'paypal', 'stripe', 'check'])
        .withMessage('Invalid payout method'),

      body('bankDetails.accountHolderName')
        .if(body('method').equals('bank_transfer'))
        .notEmpty()
        .withMessage('Account holder name is required for bank transfer')
        .isLength({ min: 2, max: 100 })
        .withMessage('Account holder name must be between 2-100 characters'),

      body('bankDetails.bankName')
        .if(body('method').equals('bank_transfer'))
        .notEmpty()
        .withMessage('Bank name is required for bank transfer')
        .isLength({ min: 2, max: 100 })
        .withMessage('Bank name must be between 2-100 characters'),

      body('bankDetails.accountNumber')
        .if(body('method').equals('bank_transfer'))
        .notEmpty()
        .withMessage('Account number is required for bank transfer')
        .isLength({ min: 5, max: 20 })
        .withMessage('Account number must be between 5-20 characters'),

      body('bankDetails.routingNumber')
        .if(body('method').equals('bank_transfer'))
        .notEmpty()
        .withMessage('Routing number is required for bank transfer')
        .isLength({ min: 9, max: 9 })
        .withMessage('Routing number must be exactly 9 digits'),

      body('bankDetails.accountType')
        .if(body('method').equals('bank_transfer'))
        .isIn(['checking', 'savings'])
        .withMessage('Account type must be checking or savings'),

      body('paypalEmail')
        .if(body('method').equals('paypal'))
        .isEmail()
        .withMessage('Valid PayPal email is required for PayPal payout')
        .normalizeEmail()
    ];
  }

  /**
   * Validation for admin payout approval
   */
  static validatePayoutApproval() {
    return [
      param('payoutId')
        .isMongoId()
        .withMessage('Invalid payout ID'),

      body('notes')
        .optional()
        .isLength({ max: 500 })
        .withMessage('Notes cannot exceed 500 characters'),

      body('processingFee')
        .optional()
        .isNumeric()
        .withMessage('Processing fee must be a number')
        .isFloat({ min: 0 })
        .withMessage('Processing fee cannot be negative')
    ];
  }

  /**
   * Validation for admin payout rejection
   */
  static validatePayoutRejection() {
    return [
      param('payoutId')
        .isMongoId()
        .withMessage('Invalid payout ID'),

      body('reason')
        .notEmpty()
        .withMessage('Rejection reason is required')
        .isLength({ min: 10, max: 500 })
        .withMessage('Rejection reason must be between 10-500 characters'),

      body('notes')
        .optional()
        .isLength({ max: 500 })
        .withMessage('Notes cannot exceed 500 characters')
    ];
  }

  /**
   * Validation for completing payout
   */
  static validatePayoutCompletion() {
    return [
      param('payoutId')
        .isMongoId()
        .withMessage('Invalid payout ID'),

      body('transactionId')
        .notEmpty()
        .withMessage('Transaction ID is required')
        .isLength({ min: 5, max: 100 })
        .withMessage('Transaction ID must be between 5-100 characters')
    ];
  }

  /**
   * Validation for bulk payout operations
   */
  static validateBulkPayoutOperation() {
    return [
      body('payoutIds')
        .isArray({ min: 1 })
        .withMessage('At least one payout ID is required')
        .custom((value) => {
          if (!Array.isArray(value)) return false;
          return value.every(id => typeof id === 'string' && id.match(/^[0-9a-fA-F]{24}$/));
        })
        .withMessage('All payout IDs must be valid MongoDB ObjectIds'),

      body('notes')
        .optional()
        .isLength({ max: 500 })
        .withMessage('Notes cannot exceed 500 characters')
    ];
  }

  /**
   * Validation for payout queries
   */
  static validatePayoutQuery() {
    return [
      query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),

      query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1-100'),

      query('status')
        .optional()
        .isIn(['pending', 'approved', 'processing', 'completed', 'rejected', 'failed'])
        .withMessage('Invalid status filter'),

      query('sortBy')
        .optional()
        .isIn(['requestDate', 'processedDate', 'completedDate', 'requestedAmount', 'status'])
        .withMessage('Invalid sort field'),

      query('sortOrder')
        .optional()
        .isIn(['asc', 'desc'])
        .withMessage('Sort order must be asc or desc')
    ];
  }

  /**
   * Validation for export queries
   */
  static validateExportQuery() {
    return [
      query('status')
        .optional()
        .isIn(['pending', 'approved', 'processing', 'completed', 'rejected', 'failed'])
        .withMessage('Invalid status filter'),

      query('startDate')
        .optional()
        .isISO8601()
        .withMessage('Start date must be a valid ISO 8601 date'),

      query('endDate')
        .optional()
        .isISO8601()
        .withMessage('End date must be a valid ISO 8601 date')
        .custom((value, { req }) => {
          if (req.query.startDate && value) {
            const start = new Date(req.query.startDate);
            const end = new Date(value);
            if (end <= start) {
              throw new Error('End date must be after start date');
            }
          }
          return true;
        }),

      query('format')
        .optional()
        .isIn(['json', 'csv'])
        .withMessage('Format must be json or csv')
    ];
  }

  /**
   * Validation for marking payout as failed
   */
  static validateMarkAsFailed() {
    return [
      param('payoutId')
        .isMongoId()
        .withMessage('Invalid payout ID'),

      body('reason')
        .notEmpty()
        .withMessage('Failure reason is required')
        .isLength({ min: 10, max: 500 })
        .withMessage('Failure reason must be between 10-500 characters')
    ];
  }

  /**
   * Validation for payout ID parameter
   */
  static validatePayoutId() {
    return [
      param('payoutId')
        .isMongoId()
        .withMessage('Invalid payout ID')
    ];
  }
}

module.exports = PayoutValidator;
