import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Select,
  Typography,
  Row,
  Col,
  Divider,
  message,
  Upload,
  Avatar,
  Space,
  Tabs,
  InputNumber,
  Spin
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  ShopOutlined,
  UserOutlined,
  SecurityScanOutlined,
  CameraOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { storeApi } from '../../../services/vendorApi';
import vendorApi from '../../../services/vendorApi';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const SettingsFixed = () => {
  const [businessForm] = Form.useForm();
  const [profileForm] = Form.useForm();
  const [securityForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [profileLoading, setProfileLoading] = useState(true);
  const [vendorProfile, setVendorProfile] = useState(null);
  const [uploadingLogo, setUploadingLogo] = useState(false);
  const [uploadingBanner, setUploadingBanner] = useState(false);

  useEffect(() => {
    fetchVendorProfile();
  }, []);

  const fetchVendorProfile = async () => {
    try {
      setProfileLoading(true);
      const response = await storeApi.getProfile();
      if (response.data.success) {
        const profile = response.data.data;
        setVendorProfile(profile);

        // Set business form values
        businessForm.setFieldsValue({
          businessName: profile.businessName || '',
          businessDescription: profile.businessDescription || '',
          businessType: profile.businessType || 'individual',
          contactPerson: profile.user?.firstName + ' ' + profile.user?.lastName || '',
          businessEmail: profile.contactInfo?.businessEmail || profile.user?.email || '',
          businessPhone: profile.contactInfo?.businessPhone || profile.user?.phone || '',
          website: profile.contactInfo?.website || '',
          street: profile.businessAddress?.street || '',
          city: profile.businessAddress?.city || '',
          state: profile.businessAddress?.state || '',
          zipCode: profile.businessAddress?.zipCode || '',
          country: profile.businessAddress?.country || '',
          returnPolicy: profile.settings?.returnPolicy || '',
          shippingPolicy: profile.settings?.shippingPolicy || '',
          minimumOrderAmount: profile.settings?.minimumOrderAmount || 0,
          processingTime: profile.settings?.processingTime || 2,
          taxId: profile.taxId || '',
          businessLicense: profile.businessRegistrationNumber || ''
        });

        // Set profile form values
        profileForm.setFieldsValue({
          firstName: profile.user?.firstName || '',
          lastName: profile.user?.lastName || '',
          email: profile.user?.email || '',
          phone: profile.user?.phone || '',
          address: profile.user?.address || '',
          language: profile.user?.preferences?.language || 'en',
          currency: profile.user?.preferences?.currency || 'INR'
        });
      }
    } catch (error) {
      console.error('Error fetching vendor profile:', error);
      message.error('Failed to load profile data');
    } finally {
      setProfileLoading(false);
    }
  };

  const handleImageUpload = async (file, type) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('You can only upload image files!');
      return false;
    }

    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('Image must be smaller than 2MB!');
      return false;
    }

    try {
      if (type === 'logo') setUploadingLogo(true);
      if (type === 'banner') setUploadingBanner(true);

      const formData = new FormData();
      formData.append(type, file);

      const response = await vendorApi.post(`/store/${type}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.success) {
        message.success(`${type.charAt(0).toUpperCase() + type.slice(1)} uploaded successfully`);
        await fetchVendorProfile(); // Refresh profile to show new image
        return true;
      }
    } catch (error) {
      console.error(`Error uploading ${type}:`, error);
      const errorMessage = error.response?.data?.message || `Failed to upload ${type}`;
      message.error(errorMessage);
    } finally {
      if (type === 'logo') setUploadingLogo(false);
      if (type === 'banner') setUploadingBanner(false);
    }
    return false;
  };

  const handleSaveSettings = async (formType, values) => {
    setLoading(true);
    try {
      if (formType === 'Business') {
        // Format business data for backend
        const businessData = {
          businessName: values.businessName,
          businessDescription: values.businessDescription,
          businessType: values.businessType,
          contactInfo: {
            businessEmail: values.businessEmail,
            businessPhone: values.businessPhone,
            website: values.website
          },
          businessAddress: {
            street: values.street,
            city: values.city,
            state: values.state,
            zipCode: values.zipCode,
            country: values.country
          },
          settings: {
            returnPolicy: values.returnPolicy,
            shippingPolicy: values.shippingPolicy,
            minimumOrderAmount: values.minimumOrderAmount,
            processingTime: values.processingTime
          },
          taxId: values.taxId,
          businessRegistrationNumber: values.businessLicense
        };
        await storeApi.updateProfile(businessData);
        
      } else if (formType === 'Profile') {
        // Format user profile data for backend
        const profileData = {
          firstName: values.firstName,
          lastName: values.lastName,
          email: values.email,
          phone: values.phone,
          address: values.address,
          preferences: {
            language: values.language,
            currency: values.currency
          }
        };
        await storeApi.updateProfile(profileData);
        
      } else if (formType === 'Security') {
        // Handle password reset
        if (values.newPassword && values.currentPassword) {
          if (values.newPassword !== values.confirmPassword) {
            message.error('New passwords do not match');
            return;
          }
          
          const securityData = {
            security: {
              currentPassword: values.currentPassword,
              newPassword: values.newPassword
            }
          };
          await storeApi.updateSettings(securityData);
          
          // Clear form after successful password update
          securityForm.resetFields();
        }
      }

      message.success(`${formType} settings saved successfully`);

      // Refresh profile data after business or profile updates
      if (formType === 'Business' || formType === 'Profile') {
        await fetchVendorProfile();
      }
      
    } catch (error) {
      console.error('Error saving settings:', error);
      const errorMessage = error.response?.data?.message || 'Failed to save settings';
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const businessSettings = (
    <Card>
      {profileLoading ? (
        <div className="text-center py-8">
          <Spin size="large" />
          <p className="mt-4 text-gray-500">Loading profile data...</p>
        </div>
      ) : (
        <Form
          form={businessForm}
          layout="vertical"
          onFinish={(values) => handleSaveSettings('Business', values)}
        >
          <Row gutter={16}>
            <Col span={24} style={{ textAlign: 'center', marginBottom: 24 }}>
              <Avatar
                size={100}
                src={vendorProfile?.logo}
                icon={<ShopOutlined />}
                style={{ backgroundColor: '#52c41a' }}
              />
              <div style={{ marginTop: 8 }}>
                <Upload
                  showUploadList={false}
                  beforeUpload={(file) => {
                    handleImageUpload(file, 'logo');
                    return false;
                  }}
                  accept="image/*"
                >
                  <Button 
                    icon={<CameraOutlined />} 
                    type="link"
                    loading={uploadingLogo}
                  >
                    {uploadingLogo ? 'Uploading...' : 'Change Logo'}
                  </Button>
                </Upload>
              </div>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24} style={{ textAlign: 'center', marginBottom: 24 }}>
              <div style={{ 
                width: '100%', 
                height: '200px', 
                backgroundColor: '#f5f5f5',
                backgroundImage: vendorProfile?.banner ? `url(${vendorProfile.banner})` : 'none',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: '2px dashed #d9d9d9'
              }}>
                {!vendorProfile?.banner && (
                  <div style={{ textAlign: 'center', color: '#999' }}>
                    <ShopOutlined style={{ fontSize: '48px', marginBottom: '8px' }} />
                    <div>Store Banner</div>
                  </div>
                )}
              </div>
              <div style={{ marginTop: 8 }}>
                <Upload
                  showUploadList={false}
                  beforeUpload={(file) => {
                    handleImageUpload(file, 'banner');
                    return false;
                  }}
                  accept="image/*"
                >
                  <Button 
                    icon={<UploadOutlined />} 
                    type="link"
                    loading={uploadingBanner}
                  >
                    {uploadingBanner ? 'Uploading...' : 'Change Banner'}
                  </Button>
                </Upload>
              </div>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="businessName"
                label="Business Name"
                rules={[{ required: true, message: 'Please enter business name' }]}
              >
                <Input placeholder="Enter business name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="businessType"
                label="Business Type"
                rules={[{ required: true, message: 'Please select business type' }]}
              >
                <Select placeholder="Select business type">
                  <Option value="individual">Individual</Option>
                  <Option value="company">Company</Option>
                  <Option value="partnership">Partnership</Option>
                  <Option value="corporation">Corporation</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="businessDescription"
            label="Business Description"
            rules={[{ required: true, message: 'Please enter business description' }]}
          >
            <TextArea rows={3} placeholder="Describe your business" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="businessEmail"
                label="Business Email"
                rules={[
                  { required: true, message: 'Please enter business email' },
                  { type: 'email', message: 'Please enter valid email' }
                ]}
              >
                <Input placeholder="Enter business email" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="businessPhone"
                label="Business Phone"
                rules={[{ required: true, message: 'Please enter business phone' }]}
              >
                <Input placeholder="Enter business phone" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="website"
            label="Website"
          >
            <Input placeholder="Enter website URL" />
          </Form.Item>

          <Divider>Business Address</Divider>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="street"
                label="Street Address"
                rules={[{ required: true, message: 'Please enter street address' }]}
              >
                <Input placeholder="Enter street address" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="city"
                label="City"
                rules={[{ required: true, message: 'Please enter city' }]}
              >
                <Input placeholder="Enter city" />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="state"
                label="State/Province"
                rules={[{ required: true, message: 'Please enter state/province' }]}
              >
                <Input placeholder="Enter state/province" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="zipCode"
                label="Zip/Postal Code"
                rules={[{ required: true, message: 'Please enter zip/postal code' }]}
              >
                <Input placeholder="Enter zip/postal code" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="country"
                label="Country"
                rules={[{ required: true, message: 'Please enter country' }]}
              >
                <Input placeholder="Enter country" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="taxId"
                label="Tax ID"
              >
                <Input placeholder="Enter tax identification number" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="businessLicense"
                label="Business License"
              >
                <Input placeholder="Enter business license number" />
              </Form.Item>
            </Col>
          </Row>

          <Divider>Business Policies</Divider>

          <Form.Item
            name="returnPolicy"
            label="Return Policy"
          >
            <TextArea rows={2} placeholder="Describe your return policy" />
          </Form.Item>

          <Form.Item
            name="shippingPolicy"
            label="Shipping Policy"
          >
            <TextArea rows={2} placeholder="Describe your shipping policy" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="minimumOrderAmount"
                label="Minimum Order Amount ($)"
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="Enter minimum order amount"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="processingTime"
                label="Processing Time (days)"
              >
                <InputNumber
                  min={1}
                  max={30}
                  style={{ width: '100%' }}
                  placeholder="Enter processing time"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
              Save Business Settings
            </Button>
          </Form.Item>
        </Form>
      )}
    </Card>
  );

  const profileSettings = (
    <Card>
      {profileLoading ? (
        <div className="text-center py-8">
          <Spin size="large" />
          <p className="mt-4 text-gray-500">Loading profile data...</p>
        </div>
      ) : (
        <Form
          form={profileForm}
          layout="vertical"
          onFinish={(values) => handleSaveSettings('Profile', values)}
        >
          <Row gutter={16}>
            <Col span={24} style={{ textAlign: 'center', marginBottom: 24 }}>
              <Avatar
                size={100}
                src={vendorProfile?.user?.avatar}
                icon={<UserOutlined />}
                style={{ backgroundColor: '#1890ff' }}
              />
              <div style={{ marginTop: 8 }}>
                <Upload
                  showUploadList={false}
                  beforeUpload={() => false}
                >
                  <Button icon={<CameraOutlined />} type="link">
                    Change Photo
                  </Button>
                </Upload>
              </div>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="firstName"
                label="First Name"
                rules={[{ required: true, message: 'Please enter first name' }]}
              >
                <Input placeholder="Enter first name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="lastName"
                label="Last Name"
                rules={[{ required: true, message: 'Please enter last name' }]}
              >
                <Input placeholder="Enter last name" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="email"
                label="Email"
                rules={[
                  { required: true, message: 'Please enter email' },
                  { type: 'email', message: 'Please enter valid email' }
                ]}
              >
                <Input placeholder="Enter email address" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="Phone"
                rules={[{ required: true, message: 'Please enter phone number' }]}
              >
                <Input placeholder="Enter phone number" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="address"
            label="Address"
          >
            <TextArea rows={2} placeholder="Enter your address" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="language"
                label="Language"
              >
                <Select placeholder="Select language">
                  <Option value="en">English</Option>
                  <Option value="es">Spanish</Option>
                  <Option value="fr">French</Option>
                  <Option value="de">German</Option>
                  <Option value="hi">Hindi</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="currency"
                label="Preferred Currency"
              >
                <Select placeholder="Select currency">
                  <Option value="INR">INR - Indian Rupee</Option>
                  <Option value="USD">USD - US Dollar</Option>
                  <Option value="EUR">EUR - Euro</Option>
                  <Option value="GBP">GBP - British Pound</Option>
                  <Option value="CAD">CAD - Canadian Dollar</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
              Save Profile Settings
            </Button>
          </Form.Item>
        </Form>
      )}
    </Card>
  );

  const securitySettings = (
    <Card>
      <Form
        form={securityForm}
        layout="vertical"
        onFinish={(values) => handleSaveSettings('Security', values)}
      >
        <Divider>Password Security</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="currentPassword"
              label="Current Password"
            >
              <Input.Password placeholder="Enter current password" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="newPassword"
              label="New Password"
            >
              <Input.Password placeholder="Enter new password" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="confirmPassword"
              label="Confirm New Password"
            >
              <Input.Password placeholder="Confirm new password" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
            Save Security Settings
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );

  const tabItems = [
    {
      key: 'business',
      label: (
        <span>
          <ShopOutlined />
          Business Info
        </span>
      ),
      children: businessSettings,
    },
    {
      key: 'profile',
      label: (
        <span>
          <UserOutlined />
          Profile
        </span>
      ),
      children: profileSettings,
    },
    {
      key: 'security',
      label: (
        <span>
          <SecurityScanOutlined />
          Security
        </span>
      ),
      children: securitySettings,
    },
  ];

  return (
    <div>
      <Title level={2}>
        <SettingOutlined /> Vendor Settings
      </Title>
      <Tabs defaultActiveKey="business" items={tabItems} />
    </div>
  );
};

export default SettingsFixed;