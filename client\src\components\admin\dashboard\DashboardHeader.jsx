import React from 'react';
import { Typography, Space, Button } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

const DashboardHeader = ({ 
  onRefresh, 
  loading = false, 
  lastUpdated = null,
  userName = "Admin"
}) => {
  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'space-between', 
      alignItems: 'flex-start',
      marginBottom: '24px',
      flexWrap: 'wrap',
      gap: '16px'
    }}>
      <div style={{ flex: 1, minWidth: '280px' }}>
        <Title level={2} style={{ 
          margin: 0, 
          color: '#262626', 
          fontSize: 'clamp(20px, 4vw, 28px)' 
        }}>
          Dashboard
        </Title>
        <Text type="secondary" style={{ 
          fontSize: '14px', 
          display: 'block', 
          marginTop: '4px' 
        }}>
          Welcome back, {userName}! Here's what's happening with your platform.
        </Text>
      </div>
      
      <Space wrap>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          Last updated: {lastUpdated ? dayjs(lastUpdated).format('HH:mm:ss') : 'Never'}
        </Text>
        <Button 
          icon={<ReloadOutlined />} 
          onClick={onRefresh}
          loading={loading}
          type="primary"
          size="small"
        >
          Refresh
        </Button>
      </Space>
    </div>
  );
};

export default DashboardHeader;
