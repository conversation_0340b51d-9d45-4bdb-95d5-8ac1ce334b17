const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('authToken'); // Use correct token key
  const user = JSON.parse(localStorage.getItem('authUser') || '{}');
  
  // Debug logging
  console.log('Cart API Auth Debug:', {
    hasToken: !!token,
    userType: user.userType,
    userId: user._id || user.id
  });
  
  return {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` })
  };
};

// Helper function to handle API responses
const handleResponse = async (response) => {
  let data;
  
  try {
    data = await response.json();
  } catch (jsonError) {
    throw new Error(`Failed to parse response: ${response.status} ${response.statusText}`);
  }
  
  if (!response.ok) {
    // Provide more specific error messages based on status code
    let errorMessage = data.message || `HTTP error! status: ${response.status}`;
    
    switch (response.status) {
      case 401:
        errorMessage = 'Please log in to continue';
        break;
      case 403:
        errorMessage = 'Cart functionality is only available for customers. Please log in with a customer account.';
        break;
      case 404:
        errorMessage = 'Product not found or no longer available';
        break;
      case 400:
        errorMessage = data.message || 'Invalid request. Please check your input.';
        break;
      case 500:
        errorMessage = 'Server error. Please try again later.';
        break;
    }
    
    throw new Error(errorMessage);
  }
  
  return data;
};

// Cart API functions
export const cartApi = {
  // Get cart with all product details
  getCart: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/customer/cart`, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching cart:', error);
      throw error;
    }
  },

  // Get cart summary (count, total)
  getCartSummary: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/customer/cart/summary`, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error fetching cart summary:', error);
      throw error;
    }
  },

  // Add item to cart
  addToCart: async (productId, quantity = 1, variantSku = null) => {
    try {
      const response = await fetch(`${API_BASE_URL}/customer/cart/add`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({
          productId,
          quantity,
          variantSku
        })
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error adding to cart:', error);
      throw error;
    }
  },

  // Update cart item quantity
  updateCartItem: async (productId, quantity, variantSku = null) => {
    try {
      const response = await fetch(`${API_BASE_URL}/customer/cart/update`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify({
          productId,
          quantity,
          variantSku
        })
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error updating cart item:', error);
      throw error;
    }
  },

  // Remove item from cart
  removeFromCart: async (productId, variantSku = null) => {
    try {
      const url = new URL(`${API_BASE_URL}/customer/cart/remove/${productId}`);
      if (variantSku) {
        url.searchParams.append('variantSku', variantSku);
      }
      
      const response = await fetch(url.toString(), {
        method: 'DELETE',
        headers: getAuthHeaders()
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error removing from cart:', error);
      throw error;
    }
  },

  // Clear entire cart
  clearCart: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/customer/cart/clear`, {
        method: 'DELETE',
        headers: getAuthHeaders()
      });
      return await handleResponse(response);
    } catch (error) {
      console.error('Error clearing cart:', error);
      throw error;
    }
  }
};

export default cartApi;
