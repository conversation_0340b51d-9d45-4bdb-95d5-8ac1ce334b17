const express = require('express');
const router = express.Router();
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');
const imageUpload = require('../../middleware/upload/imageUpload');
const {
  getProfile,
  updateProfile,
  updateSettings,
  uploadLogo,
  uploadBanner,
  uploadAvatar
} = require('../../controllers/vendor/storeController');

// Apply authentication middleware to all routes
router.use(verifyToken);
router.use(requireUserType(['vendor']));
// Note: Not requiring vendor approval for store management

/**
 * @route   GET /api/vendor/store/profile
 * @desc    Get vendor store profile
 * @access  Private (Vendor)
 */
router.get('/profile', getProfile);

/**
 * @route   PUT /api/vendor/store/profile
 * @desc    Update vendor store profile
 * @access  Private (Vendor)
 */
router.put('/profile', updateProfile);

/**
 * @route   PUT /api/vendor/store/settings
 * @desc    Update vendor store settings
 * @access  Private (Vendor)
 */
router.put('/settings', updateSettings);

/**
 * @route   POST /api/vendor/store/logo
 * @desc    Upload store logo
 * @access  Private (Vendor)
 */
router.post('/logo', imageUpload.single('logo', 'vendorLogo'), uploadLogo);

/**
 * @route   POST /api/vendor/store/banner
 * @desc    Upload store banner
 * @access  Private (Vendor)
 */
router.post('/banner', imageUpload.single('banner', 'vendorLogo'), uploadBanner);

/**
 * @route   POST /api/vendor/store/avatar
 * @desc    Upload user avatar
 * @access  Private (Vendor)
 */
router.post('/avatar', imageUpload.single('avatar', 'userAvatar'), uploadAvatar);

module.exports = router;
