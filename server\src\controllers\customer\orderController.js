const { Order, User, Vendor, Product, OrderTracking, Shipping } = require('../../models');
const mongoose = require('mongoose');

/**
 * Get customer's orders with pagination and filters - MULTI-VENDOR SUPPORT
 */
const getOrders = async (req, res) => {
  try {
    const customerId = req.user.userId;
    const {
      page = 1,
      limit = 10,
      status,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    console.log('Getting orders for customer user ID:', customerId);

    // Build filter query
    const filter = { customer: customerId };

    if (status) {
      filter.status = status;
    }

    if (search) {
      filter.$or = [
        { orderNumber: { $regex: search, $options: 'i' } },
        { 'items.name': { $regex: search, $options: 'i' } },
        { 'items.sku': { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Get orders with vendor information
    const [orders, totalOrders] = await Promise.all([
      Order.find(filter)
        .populate('customer', 'firstName lastName email phone')
        .populate('items.product', 'name images pricing sku')
        .populate('items.vendor', 'businessName contactInfo')
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit))
        .lean(),
      Order.countDocuments(filter)
    ]);

    // Enhance orders with vendor grouping and tracking info
    const enhancedOrders = await Promise.all(
      orders.map(async (order) => {
        // Group items by vendor
        const vendorGroups = {};
        order.items.forEach(item => {
          const vendorId = item.vendor._id.toString();
          if (!vendorGroups[vendorId]) {
            vendorGroups[vendorId] = {
              vendor: item.vendor,
              items: [],
              total: 0,
              itemCount: 0
            };
          }
          vendorGroups[vendorId].items.push(item);
          vendorGroups[vendorId].total += item.totalPrice;
          vendorGroups[vendorId].itemCount += item.quantity;
        });

        // Get tracking information (support multi-vendor)
        const trackings = await OrderTracking.find({ order: order._id })
          .populate('vendor', 'businessName')
          .select('trackingNumber currentStatus progressPercentage vendor')
          .lean();
        
        // For backward compatibility, if only one tracking record, use it directly
        const tracking = trackings.length === 1 ? trackings[0] : trackings;

        return {
          ...order,
          vendorGroups: Object.values(vendorGroups),
          vendorCount: Object.keys(vendorGroups).length,
          tracking
        };
      })
    );

    const totalPages = Math.ceil(totalOrders / parseInt(limit));

    res.json({
      success: true,
      data: {
        orders: enhancedOrders,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalOrders,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    console.error('Get customer orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch orders',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get single order by ID with full tracking details
 */
const getOrder = async (req, res) => {
  try {
    const customerId = req.user.userId;
    const { id } = req.params;

    console.log('Getting order:', id, 'for customer:', customerId);

    // Find order
    const order = await Order.findOne({ 
      _id: id, 
      customer: customerId 
    })
      .populate('customer', 'firstName lastName email phone')
      .populate('items.product', 'name images pricing sku description')
      .populate('items.vendor', 'businessName contactInfo')
      .lean();

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found',
        code: 'ORDER_NOT_FOUND'
      });
    }

    // Group items by vendor with status tracking
    const vendorGroups = {};
    order.items.forEach(item => {
      const vendorId = item.vendor._id.toString();
      if (!vendorGroups[vendorId]) {
        vendorGroups[vendorId] = {
          vendor: item.vendor,
          items: [],
          total: 0,
          itemCount: 0,
          statuses: new Set()
        };
      }
      vendorGroups[vendorId].items.push(item);
      vendorGroups[vendorId].total += item.totalPrice;
      vendorGroups[vendorId].itemCount += item.quantity;
      vendorGroups[vendorId].statuses.add(item.status);
    });

    // Convert status sets to arrays and determine primary status
    Object.values(vendorGroups).forEach(group => {
      const statusArray = Array.from(group.statuses);
      group.statuses = statusArray;
      // Determine primary status (most advanced)
      const statusOrder = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned'];
      group.primaryStatus = statusArray.sort((a, b) => 
        statusOrder.indexOf(b) - statusOrder.indexOf(a)
      )[0];
    });

    // Get detailed tracking information (support multi-vendor)
    const trackings = await OrderTracking.find({ order: order._id })
      .populate('vendor', 'businessName')
      .populate('trackingSteps.updatedBy.user', 'firstName lastName')
      .populate('trackingSteps.updatedBy.vendor', 'businessName')
      .lean();
    
    // For backward compatibility, if only one tracking record, use it directly
    const tracking = trackings.length === 1 ? trackings[0] : trackings;

    const enhancedOrder = {
      ...order,
      vendorGroups: Object.values(vendorGroups),
      vendorCount: Object.keys(vendorGroups).length,
      isMultiVendor: Object.keys(vendorGroups).length > 1,
      tracking
    };

    console.log('Order found:', {
      orderNumber: order.orderNumber,
      vendorCount: enhancedOrder.vendorCount,
      isMultiVendor: enhancedOrder.isMultiVendor,
      hasTracking: !!tracking
    });

    res.json({
      success: true,
      data: enhancedOrder
    });

  } catch (error) {
    console.error('Get customer order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Track order by order ID or tracking number
 */
const trackOrder = async (req, res) => {
  try {
    const customerId = req.user.userId;
    const { identifier } = req.params; // Can be order ID or tracking number

    console.log('Tracking order:', identifier, 'for customer:', customerId);

    let tracking;
    let order;

    // First try to find by tracking number
    if (identifier.startsWith('TRK')) {
      tracking = await OrderTracking.getByTrackingNumber(identifier);
      if (tracking) {
        order = await Order.findOne({ 
          _id: tracking.order._id,
          customer: customerId 
        });
      }
    } else {
      // Try to find by order ID
      order = await Order.findOne({ 
        _id: identifier,
        customer: customerId 
      });
      if (order) {
        tracking = await OrderTracking.getByOrderId(order._id);
      }
    }

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found or access denied',
        code: 'ORDER_NOT_FOUND'
      });
    }

    // Get order with vendor details
    const fullOrder = await Order.findById(order._id)
      .populate('items.vendor', 'businessName contactInfo')
      .populate('items.product', 'name images')
      .lean();

    // Group by vendor for tracking display
    const vendorGroups = {};
    fullOrder.items.forEach(item => {
      const vendorId = item.vendor._id.toString();
      if (!vendorGroups[vendorId]) {
        vendorGroups[vendorId] = {
          vendor: item.vendor,
          items: [],
          statuses: new Set()
        };
      }
      vendorGroups[vendorId].items.push(item);
      vendorGroups[vendorId].statuses.add(item.status);
    });

    // Convert sets to arrays
    Object.values(vendorGroups).forEach(group => {
      group.statuses = Array.from(group.statuses);
    });

    const trackingInfo = {
      order: {
        _id: fullOrder._id,
        orderNumber: fullOrder.orderNumber,
        status: fullOrder.status,
        createdAt: fullOrder.createdAt,
        pricing: fullOrder.pricing,
        vendorGroups: Object.values(vendorGroups)
      },
      tracking: tracking ? {
        trackingNumber: tracking.trackingNumber,
        currentStatus: tracking.currentStatus,
        progressPercentage: tracking.progressPercentage,
        estimatedDelivery: tracking.estimatedDelivery,
        actualDelivery: tracking.actualDelivery,
        carrier: tracking.carrier,
        trackingSteps: tracking.trackingSteps,
        notes: tracking.notes.filter(note => note.isPublic) // Only public notes
      } : null
    };

    res.json({
      success: true,
      data: trackingInfo
    });

  } catch (error) {
    console.error('Track order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to track order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Cancel order or specific items
 */
const cancelOrder = async (req, res) => {
  try {
    const customerId = req.user.userId;
    const { id } = req.params;
    const { reason, itemIds } = req.body;

    console.log('Cancelling order:', id, 'items:', itemIds, 'reason:', reason);

    // Find order
    const order = await Order.findOne({ 
      _id: id, 
      customer: customerId 
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found',
        code: 'ORDER_NOT_FOUND'
      });
    }

    // Check if order can be cancelled
    const nonCancellableStatuses = ['shipped', 'delivered', 'cancelled', 'returned'];
    if (nonCancellableStatuses.includes(order.status)) {
      return res.status(400).json({
        success: false,
        message: `Cannot cancel order with status: ${order.status}`,
        code: 'ORDER_NOT_CANCELLABLE'
      });
    }

    let cancelledItemsCount = 0;

    if (itemIds && itemIds.length > 0) {
      // Cancel specific items
      order.items.forEach((item, index) => {
        if (itemIds.includes(item._id.toString()) && 
            !nonCancellableStatuses.includes(item.status)) {
          order.items[index].status = 'cancelled';
          cancelledItemsCount++;
        }
      });
    } else {
      // Cancel entire order
      order.items.forEach((item, index) => {
        if (!nonCancellableStatuses.includes(item.status)) {
          order.items[index].status = 'cancelled';
          cancelledItemsCount++;
        }
      });
      order.status = 'cancelled';
    }

    if (cancelledItemsCount === 0) {
      return res.status(400).json({
        success: false,
        message: 'No items could be cancelled',
        code: 'NO_ITEMS_CANCELLED'
      });
    }

    // Add to timeline
    order.timeline.push({
      status: 'cancelled',
      timestamp: new Date(),
      note: reason || 'Order cancelled by customer',
      updatedBy: customerId
    });

    // Update overall order status if all items are cancelled
    const allItemsCancelled = order.items.every(item => 
      ['cancelled', 'delivered', 'returned'].includes(item.status)
    );
    if (allItemsCancelled) {
      order.status = 'cancelled';
    }

    await order.save();

    console.log('Order cancellation processed:', {
      orderNumber: order.orderNumber,
      cancelledItemsCount,
      newStatus: order.status
    });

    res.json({
      success: true,
      message: `Successfully cancelled ${cancelledItemsCount} item(s)`,
      data: {
        order: {
          _id: order._id,
          orderNumber: order.orderNumber,
          status: order.status,
          cancelledItemsCount
        }
      }
    });

  } catch (error) {
    console.error('Cancel order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get order statistics for customer
 */
const getOrderStats = async (req, res) => {
  try {
    const customerId = req.user.userId;

    console.log('Getting order statistics for customer:', customerId);

    const stats = await Order.aggregate([
      { $match: { customer: new mongoose.Types.ObjectId(customerId) } },
      {
        $group: {
          _id: null,
          totalOrders: { $sum: 1 },
          totalSpent: { $sum: '$pricing.total' },
          pendingOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          },
          confirmedOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'confirmed'] }, 1, 0] }
          },
          processingOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'processing'] }, 1, 0] }
          },
          shippedOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'shipped'] }, 1, 0] }
          },
          deliveredOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'delivered'] }, 1, 0] }
          },
          cancelledOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0] }
          },
          totalItems: {
            $sum: {
              $reduce: {
                input: '$items',
                initialValue: 0,
                in: { $add: ['$$value', '$$this.quantity'] }
              }
            }
          },
          averageOrderValue: { $avg: '$pricing.total' }
        }
      }
    ]);

    const customerStats = stats[0] || {
      totalOrders: 0,
      totalSpent: 0,
      pendingOrders: 0,
      confirmedOrders: 0,
      processingOrders: 0,
      shippedOrders: 0,
      deliveredOrders: 0,
      cancelledOrders: 0,
      totalItems: 0,
      averageOrderValue: 0
    };

    // Get recent orders
    const recentOrders = await Order.find({ customer: customerId })
      .populate('items.vendor', 'businessName')
      .sort({ createdAt: -1 })
      .limit(5)
      .select('orderNumber status pricing.total createdAt items')
      .lean();

    res.json({
      success: true,
      data: {
        stats: customerStats,
        recentOrders
      }
    });

  } catch (error) {
    console.error('Get order stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch order statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getOrders,
  getOrder,
  trackOrder,
  cancelOrder,
  getOrderStats
};
