/**
 * Utility functions for parsing FormData and nested objects from frontend
 */

/**
 * Parse nested FormData fields like field[level1][level2][level3]
 * @param {Object} body - Request body
 * @param {string} fieldName - Base field name to parse
 * @returns {Object|null} - Parsed nested object or null if not found
 */
const parseNestedField = (body, fieldName) => {
  const result = {};
  const regex = new RegExp(`${fieldName}\\[([^\\]]+)\\](?:\\[([^\\]]+)\\])?(?:\\[([^\\]]+)\\])?`);
  
  Object.keys(body).forEach(key => {
    if (key.startsWith(`${fieldName}[`) && key.endsWith(']')) {
      const match = key.match(regex);
      if (match) {
        const [, level1, level2, level3] = match;
        if (level3) {
          // Three levels deep: field[level1][level2][level3]
          if (!result[level1]) result[level1] = {};
          if (!result[level1][level2]) result[level1][level2] = {};
          result[level1][level2][level3] = body[key];
        } else if (level2) {
          // Two levels deep: field[level1][level2]
          if (!result[level1]) result[level1] = {};
          result[level1][level2] = body[key];
        } else {
          // One level deep: field[level1]
          result[level1] = body[key];
        }
      }
    }
  });
  
  return Object.keys(result).length > 0 ? result : null;
};

/**
 * Parse colors data from various formats
 * @param {Object} body - Request body
 * @returns {Array} - Array of color objects
 */
const parseColorsData = (body) => {
  let colorsData = [];
  
  if (body.colors) {
    if (Array.isArray(body.colors)) {
      colorsData = body.colors;
    } else if (typeof body.colors === 'string') {
      try {
        colorsData = JSON.parse(body.colors);
      } catch (e) {
        console.log('Failed to parse colors JSON:', e.message);
      }
    }
  } else {
    // Check for FormData format: colors[0][name], colors[0][hexCode], etc.
    const colorsFromFormData = parseNestedField(body, 'colors');
    if (colorsFromFormData) {
      colorsData = Object.keys(colorsFromFormData)
        .sort((a, b) => parseInt(a) - parseInt(b))
        .map(index => colorsFromFormData[index])
        .filter(color => color && color.name); // Only include colors with names
    }
  }
  
  // Validate and clean colors data
  return colorsData.map(color => ({
    name: color.name || '',
    hexCode: color.hexCode || '#000000',
    available: color.available === true || color.available === 'true',
    stock: parseInt(color.stock) || 0,
    image: color.image || ''
  })).filter(color => color.name.trim() !== '');
};

/**
 * Parse shipping options data
 * @param {Object} body - Request body
 * @returns {Object} - Shipping options object
 */
const parseShippingOptions = (body) => {
  let shippingOptionsData = body.shippingOptions;
  if (!shippingOptionsData) {
    shippingOptionsData = parseNestedField(body, 'shippingOptions');
  }
  
  if (shippingOptionsData) {
    return {
      processingDays: parseInt(shippingOptionsData.processingDays) || 1,
      shippingDays: parseInt(shippingOptionsData.shippingDays) || 3,
      expeditedShipping: {
        available: shippingOptionsData.expeditedShipping?.available === true || 
                  shippingOptionsData.expeditedShipping?.available === 'true',
        days: parseInt(shippingOptionsData.expeditedShipping?.days) || 2,
        additionalCost: parseFloat(shippingOptionsData.expeditedShipping?.additionalCost) || 0
      },
      freeShippingThreshold: parseFloat(shippingOptionsData.freeShippingThreshold) || 0,
      shippingRegions: Array.isArray(shippingOptionsData.shippingRegions) 
        ? shippingOptionsData.shippingRegions 
        : (shippingOptionsData.shippingRegions ? [shippingOptionsData.shippingRegions] : ['domestic'])
    };
  }
  
  // Default shipping options
  return {
    processingDays: 1,
    shippingDays: 3,
    expeditedShipping: { available: false, days: 2, additionalCost: 0 },
    freeShippingThreshold: 0,
    shippingRegions: ['domestic']
  };
};

/**
 * Parse return policy data
 * @param {Object} body - Request body
 * @returns {Object} - Return policy object
 */
const parseReturnPolicy = (body) => {
  let returnPolicyData = body.returnPolicy;
  if (!returnPolicyData) {
    returnPolicyData = parseNestedField(body, 'returnPolicy');
  }
  
  if (returnPolicyData) {
    return {
      returnsAccepted: returnPolicyData.returnsAccepted !== false && 
                      returnPolicyData.returnsAccepted !== 'false',
      returnWindow: parseInt(returnPolicyData.returnWindow) || 30,
      returnConditions: Array.isArray(returnPolicyData.returnConditions) 
        ? returnPolicyData.returnConditions 
        : (returnPolicyData.returnConditions ? [returnPolicyData.returnConditions] : ['unused', 'original_packaging']),
      restockingFee: parseFloat(returnPolicyData.restockingFee) || 0,
      returnShippingCost: returnPolicyData.returnShippingCost || 'buyer_pays',
      customReturnPolicy: returnPolicyData.customReturnPolicy || ''
    };
  }
  
  // Default return policy
  return {
    returnsAccepted: true,
    returnWindow: 30,
    returnConditions: ['unused', 'original_packaging'],
    restockingFee: 0,
    returnShippingCost: 'buyer_pays'
  };
};

/**
 * Parse warranty data
 * @param {Object} body - Request body
 * @returns {Object} - Warranty object
 */
const parseWarranty = (body) => {
  let warrantyData = body.warranty;
  if (!warrantyData) {
    warrantyData = parseNestedField(body, 'warranty');
  }
  
  if (warrantyData) {
    const hasWarranty = warrantyData.hasWarranty === true || warrantyData.hasWarranty === 'true';
    return {
      hasWarranty: hasWarranty,
      warrantyPeriod: hasWarranty ? parseInt(warrantyData.warrantyPeriod) || 12 : undefined,
      warrantyType: hasWarranty ? warrantyData.warrantyType || 'seller' : undefined,
      warrantyTerms: hasWarranty ? warrantyData.warrantyTerms || '' : undefined,
      warrantyContact: {
        email: warrantyData.warrantyContact?.email || '',
        phone: warrantyData.warrantyContact?.phone || ''
      }
    };
  }
  
  // Default warranty (no warranty)
  return {
    hasWarranty: false
  };
};

/**
 * Parse specifications data
 * @param {Object} body - Request body
 * @returns {Object|null} - Specifications object or null
 */
const parseSpecifications = (body) => {
  let specificationsData = body.specifications;
  if (!specificationsData) {
    specificationsData = parseNestedField(body, 'specifications');
  }
  
  if (specificationsData) {
    const specs = {};
    
    // Handle basic specifications
    if (specificationsData.weight) {
      specs.weight = {
        value: parseFloat(specificationsData.weight.value) || 0,
        unit: specificationsData.weight.unit || 'kg'
      };
    }
    
    if (specificationsData.dimensions) {
      specs.dimensions = {
        length: parseFloat(specificationsData.dimensions.length) || 0,
        width: parseFloat(specificationsData.dimensions.width) || 0,
        height: parseFloat(specificationsData.dimensions.height) || 0,
        unit: specificationsData.dimensions.unit || 'cm'
      };
    }
    
    if (specificationsData.material) specs.material = specificationsData.material;
    if (specificationsData.color) specs.color = specificationsData.color;
    if (specificationsData.size) specs.size = specificationsData.size;
    
    // Handle features array
    if (specificationsData.features) {
      if (Array.isArray(specificationsData.features)) {
        specs.features = specificationsData.features.filter(feature => 
          feature && feature.name && feature.value
        );
      }
    }
    
    // Handle technical specs
    if (specificationsData.technicalSpecs) {
      if (Array.isArray(specificationsData.technicalSpecs)) {
        specs.technicalSpecs = specificationsData.technicalSpecs.filter(spec => 
          spec && spec.category
        );
      }
    }
    
    return Object.keys(specs).length > 0 ? specs : null;
  }
  
  return null;
};

/**
 * Parse pricing data with multi-currency support
 * @param {Object} body - Request body
 * @returns {Object} - Pricing object
 */
const parsePricing = (body) => {
  let pricing = {
    basePrice: 0,
    salePrice: null,
    currency: 'INR',
    taxClass: 'standard',
    taxRate: 0
  };

  // Handle different pricing structures from frontend
  if (body.pricing) {
    // Structured pricing object from frontend
    pricing.basePrice = parseFloat(body.pricing.basePrice) || 0;
    pricing.salePrice = body.pricing.salePrice ? parseFloat(body.pricing.salePrice) : null;
    pricing.currency = body.pricing.currency || 'INR';
    pricing.taxClass = body.pricing.taxClass || 'standard';
    pricing.taxRate = parseFloat(body.pricing.taxRate) || 0;
    
    // Handle multi-currency pricing
    if (body.pricing.multiCurrency) {
      pricing.multiCurrency = {};
      Object.keys(body.pricing.multiCurrency).forEach(currency => {
        const currencyData = body.pricing.multiCurrency[currency];
        if (currencyData && currencyData.basePrice) {
          pricing.multiCurrency[currency.toUpperCase()] = {
            basePrice: parseFloat(currencyData.basePrice),
            salePrice: currencyData.salePrice ? parseFloat(currencyData.salePrice) : null
          };
        }
      });
    }
  } else {
    // Handle direct pricing fields (backward compatibility)
    pricing.basePrice = parseFloat(body.basePrice) || parseFloat(body.price) || 0;
    pricing.salePrice = body.salePrice ? parseFloat(body.salePrice) : null;
    pricing.currency = body.currency || 'INR';
    pricing.taxClass = body.taxClass || 'standard';
    pricing.taxRate = parseFloat(body.taxRate) || 0;
    
    // Handle legacy multiCurrencyPricing field
    if (body.multiCurrencyPricing) {
      pricing.multiCurrency = {};
      Object.keys(body.multiCurrencyPricing).forEach(currency => {
        const currencyData = body.multiCurrencyPricing[currency];
        if (currencyData && currencyData.basePrice) {
          pricing.multiCurrency[currency.toUpperCase()] = {
            basePrice: parseFloat(currencyData.basePrice),
            salePrice: currencyData.salePrice ? parseFloat(currencyData.salePrice) : null
          };
        }
      });
    }
  }

  return pricing;
};

/**
 * Parse inventory data
 * @param {Object} body - Request body
 * @returns {Object} - Inventory object
 */
const parseInventory = (body) => {
  let inventory = {
    trackQuantity: true, // Default to true
    quantity: 0,
    lowStockThreshold: 5,
    allowBackorders: false
  };

  // Handle different inventory structures from frontend
  if (body.inventory) {
    // Structured inventory object from frontend
    inventory.trackQuantity = body.inventory.trackQuantity !== false && 
                             body.inventory.trackQuantity !== 'false';
    inventory.quantity = parseInt(body.inventory.quantity) || 0;
    inventory.lowStockThreshold = parseInt(body.inventory.lowStockThreshold) || 5;
    inventory.allowBackorders = body.inventory.allowBackorders === true || 
                               body.inventory.allowBackorders === 'true';
  } else {
    // Handle direct inventory fields (backward compatibility)
    inventory.trackQuantity = body.trackQuantity !== false && body.trackQuantity !== 'false';
    inventory.quantity = parseInt(body.quantity) || 0;
    inventory.lowStockThreshold = parseInt(body.lowStockThreshold) || 5;
    inventory.allowBackorders = body.allowBackorders === true || body.allowBackorders === 'true';
  }

  return inventory;
};

/**
 * Convert string boolean values to actual booleans
 * @param {any} value - Value to convert
 * @returns {boolean} - Boolean value
 */
const parseBoolean = (value) => {
  if (typeof value === 'boolean') return value;
  if (typeof value === 'string') {
    return value.toLowerCase() === 'true' || value === '1';
  }
  return Boolean(value);
};

/**
 * Parse array from string or return array as-is
 * @param {any} value - Value to parse
 * @returns {Array} - Array value
 */
const parseArray = (value) => {
  if (Array.isArray(value)) return value;
  if (typeof value === 'string') {
    try {
      const parsed = JSON.parse(value);
      return Array.isArray(parsed) ? parsed : [value];
    } catch (e) {
      return [value];
    }
  }
  return value ? [value] : [];
};

/**
 * Parse sizes data from various formats
 * @param {Object} body - Request body
 * @returns {Array} - Array of size objects
 */
const parseSizesData = (body) => {
  let sizesData = [];
  
  if (body.sizes) {
    if (Array.isArray(body.sizes)) {
      sizesData = body.sizes;
    } else if (typeof body.sizes === 'string') {
      try {
        sizesData = JSON.parse(body.sizes);
      } catch (e) {
        console.log('Failed to parse sizes JSON:', e.message);
      }
    }
  }
  
  // Validate and clean sizes data
  return sizesData.map(size => ({
    name: size.name || '',
    value: size.value || '',
    available: size.available === true || size.available === 'true',
    stock: parseInt(size.stock) || 0,
    priceModifier: parseFloat(size.priceModifier) || 0
  })).filter(size => size.name.trim() !== '' && size.value.trim() !== '');
};

/**
 * Parse highlights data from various formats
 * @param {Object} body - Request body
 * @returns {Array} - Array of highlight strings
 */
const parseHighlightsData = (body) => {
  let highlightsData = [];
  
  if (body.highlights) {
    if (Array.isArray(body.highlights)) {
      highlightsData = body.highlights;
    } else if (typeof body.highlights === 'string') {
      try {
        highlightsData = JSON.parse(body.highlights);
      } catch (e) {
        // If JSON parse fails, treat as comma-separated string
        highlightsData = body.highlights.split(',').map(h => h.trim());
      }
    }
  }
  
  // Filter out empty highlights and limit to 200 chars each
  return highlightsData
    .filter(highlight => highlight && highlight.trim() !== '')
    .map(highlight => highlight.trim().substring(0, 200));
};

/**
 * Parse delivery charges data
 * @param {Object} body - Request body
 * @returns {Object} - Delivery charges object
 */
const parseDeliveryCharges = (body) => {
  let deliveryChargesData = body.deliveryCharges;
  if (!deliveryChargesData) {
    deliveryChargesData = parseNestedField(body, 'deliveryCharges');
  }
  
  if (deliveryChargesData) {
    return {
      standard: {
        charge: parseFloat(deliveryChargesData.standard?.charge) || 0,
        freeAbove: parseFloat(deliveryChargesData.standard?.freeAbove) || 0,
        estimatedDays: parseInt(deliveryChargesData.standard?.estimatedDays) || 5
      },
      express: {
        available: deliveryChargesData.express?.available === true || 
                  deliveryChargesData.express?.available === 'true',
        charge: parseFloat(deliveryChargesData.express?.charge) || 0,
        estimatedDays: parseInt(deliveryChargesData.express?.estimatedDays) || 2
      },
      sameDay: {
        available: deliveryChargesData.sameDay?.available === true || 
                  deliveryChargesData.sameDay?.available === 'true',
        charge: parseFloat(deliveryChargesData.sameDay?.charge) || 0,
        cutoffTime: deliveryChargesData.sameDay?.cutoffTime || '12:00'
      }
    };
  }
  
  // Default delivery charges
  return {
    standard: { charge: 0, freeAbove: 0, estimatedDays: 5 },
    express: { available: false, charge: 0, estimatedDays: 2 },
    sameDay: { available: false, charge: 0, cutoffTime: '12:00' }
  };
};

module.exports = {
  parseNestedField,
  parseColorsData,
  parseSizesData,
  parseHighlightsData,
  parseDeliveryCharges,
  parseShippingOptions,
  parseReturnPolicy,
  parseWarranty,
  parseSpecifications,
  parsePricing,
  parseInventory,
  parseBoolean,
  parseArray
};
