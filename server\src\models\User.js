const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');

const userSchema = new mongoose.Schema({
  firstName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },
  lastName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6,
    select: false
  },
  
  // Profile Information - only necessary fields
  phone: {
    type: String,
    trim: true
  },
  countryCode: {
    type: String,
    trim: true,
    uppercase: true,
    maxlength: 3
  },
  avatar: {
    type: String,
    default: null
  },
  
  userType: {
    type: String,
    enum: ['customer', 'vendor', 'admin'],
    default: 'customer'
  },
  
  // Vendor fields
  businessName: String,
  businessType: String,
  contactPerson: String,
  isVendorApproved: {
    type: Boolean,
    default: false
  },
  
  status: {
    type: String,
    enum: ['active', 'inactive', 'pending'],
    default: 'active'
  },
  
  // Email verification
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: String,
  emailVerificationExpires: Date,
  
  // Password reset
  resetPasswordToken: String,
  resetPasswordExpires: Date,
  
  // OTP fields
  otpCode: {
    type: String,
    select: false
  },
  otpExpires: {
    type: Date,
    select: false
  },
  isPhoneVerified: {
    type: Boolean,
    default: false
  },
  otpAttempts: {
    type: Number,
    default: 0,
    select: false
  },
  lastOtpRequest: {
    type: Date,
    select: false
  },
  
  // Security
  loginAttempts: {
    type: Number,
    default: 0
  },
  lockUntil: Date,
  lastLogin: Date,
  
  // Enhanced addresses with type support
  addresses: [{
    type: {
      type: String,
      enum: ['home', 'work', 'other'],
      default: 'home'
    },
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: String,
    isDefault: {
      type: Boolean,
      default: false
    }
  }],
  
  // Single address fields for checkout (main address fields)
  address: String,
  city: String,
  state: String,
  zipCode: String,
  country: {
    type: String,
    default: 'United States'
  },
  
  // User Preferences - only necessary fields
  preferences: {
    language: {
      type: String,
      default: 'en'
    },
    timezone: {
      type: String,
      default: 'UTC'
    },
    currency: {
      type: String,
      default: 'INR'
    }
  }
}, {
  timestamps: true
});

// Indexes (email index is automatically created by unique: true)
userSchema.index({ userType: 1 });
userSchema.index({ status: 1 });

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  this.password = await bcrypt.hash(this.password, 12);
  next();
});

// Compare password
userSchema.methods.comparePassword = async function(password) {
  return await bcrypt.compare(password, this.password);
};

// Check if locked
userSchema.methods.isLocked = function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
};

// Generate email verification token
userSchema.methods.generateEmailVerificationToken = function() {
  const resetToken = crypto.randomBytes(32).toString('hex');
  this.emailVerificationToken = crypto.createHash('sha256').update(resetToken).digest('hex');
  this.emailVerificationExpires = Date.now() + 24 * 60 * 60 * 1000; // 24 hours
  return resetToken;
};

// Generate password reset token
userSchema.methods.generatePasswordResetToken = function() {
  const resetToken = crypto.randomBytes(32).toString('hex');
  this.resetPasswordToken = crypto.createHash('sha256').update(resetToken).digest('hex');
  this.resetPasswordExpires = Date.now() + 10 * 60 * 1000; // 10 minutes
  return resetToken;
};

// Generate OTP code
userSchema.methods.generateOTP = function() {
  const otp = Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit OTP
  this.otpCode = crypto.createHash('sha256').update(otp).digest('hex');
  this.otpExpires = Date.now() + 5 * 60 * 1000; // 5 minutes
  this.lastOtpRequest = new Date();
  return otp;
};

// Verify OTP code
userSchema.methods.verifyOTP = function(otp) {
  if (!otp || !this.otpCode || !this.otpExpires) {
    return false;
  }
  
  if (Date.now() > this.otpExpires) {
    return false;
  }
  
  const hashedOTP = crypto.createHash('sha256').update(otp).digest('hex');
  return hashedOTP === this.otpCode;
};

// Clear OTP
userSchema.methods.clearOTP = function() {
  this.otpCode = undefined;
  this.otpExpires = undefined;
  this.otpAttempts = 0;
};

// Check if OTP rate limited
userSchema.methods.isOTPRateLimited = function() {
  const now = new Date();
  const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
  
  return this.lastOtpRequest && this.lastOtpRequest > fiveMinutesAgo && this.otpAttempts >= 3;
};

// Static method to find by email
userSchema.statics.findByEmail = function(email) {
  return this.findOne({ email: email.toLowerCase() });
};

// Static method to get user statistics
userSchema.statics.getStatistics = async function() {
  const now = new Date();
  const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  const [
    totalUsers,
    activeUsers,
    todayUsers,
    weekUsers,
    monthUsers,
    adminUsers,
    vendorUsers,
    customerUsers
  ] = await Promise.all([
    this.countDocuments(),
    this.countDocuments({ status: 'active' }),
    this.countDocuments({ createdAt: { $gte: startOfToday } }),
    this.countDocuments({ createdAt: { $gte: startOfWeek } }),
    this.countDocuments({ createdAt: { $gte: startOfMonth } }),
    this.countDocuments({ userType: 'admin' }),
    this.countDocuments({ userType: 'vendor' }),
    this.countDocuments({ userType: 'customer' })
  ]);

  return {
    totalUsers: totalUsers,
    activeUsers: activeUsers,
    newThisMonth: monthUsers,
    todayUsers: todayUsers,
    weekUsers: weekUsers,
    monthUsers: monthUsers,
    // Legacy fields for compatibility
    total: totalUsers,
    active: activeUsers,
    today: todayUsers,
    week: weekUsers,
    month: monthUsers,
    byType: {
      admin: adminUsers,
      vendor: vendorUsers,
      customer: customerUsers
    }
  };
};

// Static method to get recent user registrations
// Fixed version of getRecentRegistrations
userSchema.statics.getRecentRegistrationsFixed = function(days = 7) {
  console.log('🔍 getRecentRegistrationsFixed called with days:', days);
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return this.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          $dateToString: {
            format: '%Y-%m-%d',
            date: '$createdAt'
          }
        },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { _id: 1 }
    }
  ]);
};

// Keep the old method for compatibility
userSchema.statics.getRecentRegistrations = userSchema.statics.getRecentRegistrationsFixed;

module.exports = mongoose.model('User', userSchema);
