const express = require('express');
const { body } = require('express-validator');
const authController = require('../controllers/authController');
const { verifyToken, authRateLimit } = require('../middleware/auth/authMiddleware');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Enhanced validation with better error messages
const registerValidation = [
    body('email')
        .isEmail()
        .withMessage('Please provide a valid email address')
        .normalizeEmail(),
    body('password')
        .isLength({ min: 6 })
        .withMessage('Password must be at least 6 characters long'),
    body('firstName')
        .notEmpty()
        .withMessage('First name is required')
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('First name must be between 1 and 50 characters'),
    body('lastName')
        .notEmpty()
        .withMessage('Last name is required')
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Last name must be between 1 and 50 characters'),
    body('userType')
        .optional()
        .isIn(['customer', 'vendor', 'admin'])
        .withMessage('User type must be customer, vendor, or admin'),
    body('businessName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Business name must be between 2 and 100 characters'),
    body('businessType')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Business type must be between 2 and 50 characters'),
    body('phone')
        .optional()
        .isMobilePhone()
        .withMessage('Please provide a valid phone number'),
    body('countryCode')
        .optional()
        .isLength({ min: 2, max: 3 })
        .withMessage('Country code must be 2-3 characters')
];

const loginValidation = [
    body('email').isEmail().withMessage('Valid email required'),
    body('password').notEmpty().withMessage('Password required')
];

// Routes
router.post('/register', 
    registerValidation,
    validateRequest,
    authController.register
);

router.post('/login',
    loginValidation,
    validateRequest,
    authController.login
);

router.get('/profile',
    verifyToken,
    authController.getProfile
);

router.put('/profile',
    verifyToken,
    authController.updateProfile
);

router.get('/verify-email',
    authController.verifyEmail
);

router.post('/resend-verification',
    body('email').isEmail().withMessage('Valid email required'),
    validateRequest,
    authController.resendVerificationEmail
);

router.post('/request-reset-password',
    body('email').isEmail().withMessage('Valid email required'),
    validateRequest,
    authController.requestPasswordReset
);

// Alias for forgot-password (frontend compatibility)
router.post('/forgot-password',
    body('email').isEmail().withMessage('Valid email required'),
    validateRequest,
    authController.requestPasswordReset
);

router.post('/reset-password',
    [
        body('email').isEmail().withMessage('Valid email required'),
        body('token').notEmpty().withMessage('Token is required'),
        body('newPassword').isLength({ min: 6 }).withMessage('Password must be at least 6 characters long')
    ],
    validateRequest,
    authController.resetPassword
);

// OTP Routes
router.post('/send-otp',
    [
        body('phone')
            .notEmpty()
            .withMessage('Phone number is required'),
        body('countryCode')
            .optional()
            .isLength({ min: 2, max: 3 })
            .withMessage('Country code must be 2-3 characters'),
        body('purpose')
            .optional()
            .isIn(['verification', 'login', 'signup'])
            .withMessage('Purpose must be verification, login, or signup')
    ],
    validateRequest,
    authController.sendOTP
);

router.post('/verify-otp',
    [
        body('phone')
            .notEmpty()
            .withMessage('Phone number is required'),
        body('otp')
            .isLength({ min: 6, max: 6 })
            .withMessage('OTP must be exactly 6 digits')
            .isNumeric()
            .withMessage('OTP must contain only numbers'),
        body('countryCode')
            .optional()
            .isLength({ min: 2, max: 3 })
            .withMessage('Country code must be 2-3 characters'),
        body('purpose')
            .optional()
            .isIn(['verification', 'login', 'signup'])
            .withMessage('Purpose must be verification, login, or signup'),
        body('tempUserId')
            .optional()
            .isMongoId()
            .withMessage('Invalid user ID format')
    ],
    validateRequest,
    authController.verifyOTP
);

router.post('/logout',
    verifyToken,
    authController.logout
);

router.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'Auth service is running',
        timestamp: new Date().toISOString()
    });
});

module.exports = router;