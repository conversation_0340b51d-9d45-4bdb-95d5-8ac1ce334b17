const { verifyToken } = require('./auth/authMiddleware');

// Protect vendor routes - check if user is vendor type
const protectVendorRoute = async (req, res, next) => {
    try {
        // First verify the token
        await new Promise((resolve, reject) => {
            verifyToken(req, res, (error) => {
                if (error) reject(error);
                else resolve();
            });
        });

        // Check if user type is vendor
        if (req.user && req.user.userType === 'vendor') {
            return next();
        } else {
            // If not vendor, redirect to home page
            return res.redirect('/');
        }
    } catch (error) {
        // If authentication fails, redirect to home page
        return res.redirect('/');
    }
};

// Protect admin routes - redirect to admin auth if not authenticated as admin
const protectAdminRoute = async (req, res, next) => {
    try {
        // First verify the token
        await new Promise((resolve, reject) => {
            verifyToken(req, res, (error) => {
                if (error) reject(error);
                else resolve();
            });
        });

        // Check if user type is admin
        if (req.user && req.user.userType === 'admin') {
            return next();
        } else {
            // If not admin, redirect to admin auth page
            return res.redirect('/admin/auth');
        }
    } catch (error) {
        // If authentication fails, redirect to admin auth page
        return res.redirect('/admin/auth');
    }
};

module.exports = {
    protectVendorRoute,
    protectAdminRoute
};
