import React, { useState, useEffect } from 'react';
import { Card, Typography, Button, Input, message } from 'antd';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import AgencyVendorModal from './AgencyVendorModal';
import VendorTable from '../../admin/sections/vendors/VendorTable';
import { mockVendorService } from './mockVendorsData';

const VendorsSection = () => {
  const [vendors, setVendors] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingVendor, setEditingVendor] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadVendors();
  }, []);

  const loadVendors = async () => {
    setLoading(true);
    try {
      const result = await mockVendorService.getVendors();
      setVendors(result.vendors);
    } catch (error) {
      message.error('Failed to load vendors');
    } finally {
      setLoading(false);
    }
  };

  const handleAddVendor = () => {
    setEditingVendor(null);
    setModalVisible(true);
  };

  const handleEditVendor = (vendor) => {
    setEditingVendor(vendor);
    setModalVisible(true);
  };

  const handleModalOk = async (values) => {
    let result;
    if (editingVendor) {
      result = await mockVendorService.updateVendor(editingVendor._id, values);
    } else {
      result = await mockVendorService.createVendor(values);
    }
    if (result.success) {
      message.success(`Vendor ${editingVendor ? 'updated' : 'created'} successfully`);
      setModalVisible(false);
      loadVendors();
    } else {
      message.error('Action failed!');
    }
  };

  const handleModalCancel = () => {
    setModalVisible(false);
    setEditingVendor(null);
  };

  const handleApprove = async (vendorId) => {
    const result = await mockVendorService.approveVendor(vendorId);
    if (result.success) {
      message.success('Vendor approved successfully');
      loadVendors();
    } else {
      message.error('Failed to approve vendor');
    }
  };

  const handleSuspend = async (vendorId) => {
    const result = await mockVendorService.suspendVendor(vendorId);
    if (result.success) {
      message.success('Vendor suspended');
      loadVendors();
    } else {
      message.error('Failed to suspend vendor');
    }
  };

  const handleDelete = async (vendorId) => {
    const result = await mockVendorService.deleteVendor(vendorId);
    if (result.success) {
      message.success('Vendor deleted');
      loadVendors();
    } else {
      message.error('Failed to delete vendor');
    }
  };

  const filteredVendors = vendors.filter(vendor =>
    vendor.businessName.toLowerCase().includes(searchText.toLowerCase()) ||
    vendor.contactPerson.toLowerCase().includes(searchText.toLowerCase())
  );

  return (
    <div style={{ padding: '24px' }}>
      <Typography.Title level={2}>Vendors Management</Typography.Title>
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Input
            placeholder="Search vendors..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 200, marginRight: 16 }}
          />
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAddVendor}>
            Add Vendor
          </Button>
        </div>
        <VendorTable
          vendors={filteredVendors}
          loading={loading}
          onEdit={handleEditVendor}
          onApprove={handleApprove}
          onSuspend={handleSuspend}
          onDelete={handleDelete}
        />
      </Card>
      <AgencyVendorModal
        visible={modalVisible}
        editingVendor={editingVendor}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        loading={loading}
      />
    </div>
  );
};

export default VendorsSection;
