const express = require('express');
const router = express.Router();

const {
  getNotifications,
  getNotificationStats,
  sendSystemAnnouncement,
  markAsRead,
  archiveNotification,
  deleteNotification,
  bulkOperation
} = require('../../controllers/admin/notificationController');

const authenticate = require('../../middleware/auth/authenticate');
const { requireUserType } = require('../../middleware/auth/authMiddleware');

// Apply authentication and admin role requirement to all routes
router.use(authenticate);
router.use(requireUserType(['admin']));

/**
 * @route   GET /api/admin/notifications
 * @desc    Get all notifications
 * @access  Private (Admin)
 */
router.get('/', getNotifications);

/**
 * @route   GET /api/admin/notifications/stats
 * @desc    Get notification statistics
 * @access  Private (Admin)
 */
router.get('/stats', getNotificationStats);

/**
 * @route   POST /api/admin/notifications/announcement
 * @desc    Send system announcement
 * @access  Private (Admin)
 */
router.post('/announcement', sendSystemAnnouncement);

/**
 * @route   PATCH /api/admin/notifications/:id/read
 * @desc    Mark notification as read
 * @access  Private (Admin)
 */
router.patch('/:id/read', markAsRead);

/**
 * @route   PATCH /api/admin/notifications/:id/archive
 * @desc    Archive notification
 * @access  Private (Admin)
 */
router.patch('/:id/archive', archiveNotification);

/**
 * @route   DELETE /api/admin/notifications/:id
 * @desc    Delete notification
 * @access  Private (Admin)
 */
router.delete('/:id', deleteNotification);

/**
 * @route   POST /api/admin/notifications/bulk
 * @desc    Bulk operations on notifications
 * @access  Private (Admin)
 */
router.post('/bulk', bulkOperation);

module.exports = router;