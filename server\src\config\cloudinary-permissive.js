const cloudinary = require('cloudinary').v2;
const { CloudinaryStorage } = require('multer-storage-cloudinary');
const multer = require('multer');

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Track if warning has been shown
let warningShown = false;

// Validate Cloudinary configuration
const validateCloudinaryConfig = () => {
  const { cloud_name, api_key, api_secret } = cloudinary.config();

  if (!cloud_name || !api_key || !api_secret) {
    if (!warningShown) {
      console.warn('Cloudinary configuration is missing. Please set CLOUDINARY_CLOUD_NAME, CLOUDINARY_API_KEY, and CLOUDINARY_API_SECRET in your .env file');
      warningShown = true;
    }
    return false;
  }

  return true;
};

// Create Cloudinary storage for different image types
const createCloudinaryStorage = (folder, transformation = {}) => {
  return new CloudinaryStorage({
    cloudinary: cloudinary,
    params: {
      folder: folder,
      // Remove allowed_formats restriction to be more permissive
      resource_type: 'auto', // Let Cloudinary auto-detect
      transformation: {
        quality: 'auto',
        fetch_format: 'auto',
        ...transformation
      },
      public_id: (req, file) => {
        // Generate unique filename
        const timestamp = Date.now();
        const randomString = Math.random().toString(36).substring(2, 15);
        return `${timestamp}-${randomString}`;
      }
    }
  });
};

// Storage configurations for different image types
const storageConfigs = {
  // Product images storage
  productImages: createCloudinaryStorage('ecommerce/products', {
    width: 1200,
    height: 1200,
    crop: 'limit',
    quality: 'auto:good'
  }),

  // User avatars storage
  userAvatars: createCloudinaryStorage('ecommerce/users/avatars', {
    width: 300,
    height: 300,
    crop: 'fill',
    gravity: 'face',
    quality: 'auto:good'
  }),

  // Vendor logos storage
  vendorLogos: createCloudinaryStorage('ecommerce/vendors/logos', {
    width: 500,
    height: 500,
    crop: 'limit',
    quality: 'auto:good'
  }),

  // Category images storage
  categoryImages: createCloudinaryStorage('ecommerce/categories', {
    width: 800,
    height: 600,
    crop: 'limit',
    quality: 'auto:good'
  }),

  // Carousel images storage - Very permissive
  carouselImages: createCloudinaryStorage('ecommerce/homepage/carousel', {
    width: 1920,
    height: 800,
    crop: 'limit',
    quality: 'auto:good'
  }),

  // Promotion images storage
  promotionImages: createCloudinaryStorage('ecommerce/homepage/promotions', {
    width: 800,
    height: 600,
    crop: 'limit',
    quality: 'auto:good'
  })
};

// Very permissive file filter function
const fileFilter = (req, file, cb) => {
  console.log('🔍 File filter check:', {
    fieldname: file.fieldname,
    originalname: file.originalname,
    mimetype: file.mimetype,
    size: file.size
  });
  
  // Very permissive validation - allow almost any file that could be an image
  const isImageMimetype = file.mimetype && file.mimetype.startsWith('image/');
  const hasImageExtension = file.originalname && file.originalname.match(/\.(jpg|jpeg|png|gif|webp|bmp|tiff|tif|svg|ico|avif|heic|heif)$/i);
  const hasNoExtension = file.originalname && !file.originalname.includes('.');
  
  // Allow if:
  // 1. Has image mimetype
  // 2. Has image file extension
  // 3. Has no extension (could be raw image data)
  if (isImageMimetype || hasImageExtension || hasNoExtension) {
    console.log('✅ File filter passed: Accepting file as potential image');
    cb(null, true);
  } else {
    console.log('❌ File filter failed: File does not appear to be an image');
    console.log('   Mimetype:', file.mimetype);
    console.log('   Filename:', file.originalname);
    // Still be permissive - only reject obvious non-image files
    if (file.originalname && file.originalname.match(/\.(txt|doc|docx|pdf|zip|rar|exe|js|css|html)$/i)) {
      cb(new Error('Please upload an image file'), false);
    } else {
      console.log('⚠️ Allowing file anyway (permissive mode)');
      cb(null, true);
    }
  }
};

// Create multer instances for different image types
const createMulterInstance = (storageType, options = {}) => {
  const defaultOptions = {
    storage: storageConfigs[storageType],
    fileFilter: fileFilter,
    limits: {
      fileSize: 10 * 1024 * 1024, // Increased to 10MB limit
      files: 10 // Max 10 files
    }
  };

  return multer({
    ...defaultOptions,
    ...options
  });
};

// Multer instances for different use cases
const uploaders = {
  // Product images uploader (multiple files)
  productImages: createMulterInstance('productImages'),

  // User avatar uploader (single file)
  userAvatar: createMulterInstance('userAvatars', {
    limits: { fileSize: 5 * 1024 * 1024, files: 1 } // 5MB, single file
  }),

  // Vendor logo uploader (single file)
  vendorLogo: createMulterInstance('vendorLogos', {
    limits: { fileSize: 5 * 1024 * 1024, files: 1 } // 5MB, single file
  }),

  // Category image uploader (single file)
  categoryImage: createMulterInstance('categoryImages', {
    limits: { fileSize: 5 * 1024 * 1024, files: 1 } // 5MB, single file
  }),

  // Carousel image uploader (single file) - Very permissive
  carouselImage: createMulterInstance('carouselImages', {
    limits: { fileSize: 10 * 1024 * 1024, files: 1 } // 10MB, single file
  }),

  // Promotion image uploader (single file)
  promotionImage: createMulterInstance('promotionImages', {
    limits: { fileSize: 5 * 1024 * 1024, files: 1 } // 5MB, single file
  })
};

// Helper function to delete image from Cloudinary
const deleteImage = async (publicId) => {
  try {
    if (!publicId) return false;
    
    const result = await cloudinary.uploader.destroy(publicId);
    return result.result === 'ok';
  } catch (error) {
    console.error('Error deleting image from Cloudinary:', error);
    return false;
  }
};

// Helper function to extract public ID from Cloudinary URL
const extractPublicId = (url) => {
  try {
    if (!url || typeof url !== 'string') return null;
    
    // Extract public ID from Cloudinary URL
    const matches = url.match(/\/v\d+\/(.+)\.(jpg|jpeg|png|gif|webp|bmp|tiff|svg|ico|avif|heic|heif)$/i);
    return matches ? matches[1] : null;
  } catch (error) {
    console.error('Error extracting public ID from URL:', error);
    return null;
  }
};

// Helper function to get optimized image URL
const getOptimizedImageUrl = (publicId, options = {}) => {
  try {
    if (!publicId) return null;
    
    const defaultOptions = {
      quality: 'auto',
      fetch_format: 'auto'
    };
    
    return cloudinary.url(publicId, {
      ...defaultOptions,
      ...options
    });
  } catch (error) {
    console.error('Error generating optimized image URL:', error);
    return null;
  }
};

module.exports = {
  cloudinary,
  uploaders,
  storageConfigs,
  deleteImage,
  extractPublicId,
  getOptimizedImageUrl,
  validateCloudinaryConfig
};