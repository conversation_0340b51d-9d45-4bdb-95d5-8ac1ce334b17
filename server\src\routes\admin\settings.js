const express = require('express');
const router = express.Router();
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');
const {
  getSettings,
  getSettingsByCategory,
  updateSettings,
  updateSetting,
  deleteSetting,
  initializeDefaultSettings
} = require('../../controllers/admin/settingsController');

// Apply authentication middleware to all routes
router.use(verifyToken);
router.use(requireUserType(['admin']));

/**
 * @route   GET /api/admin/settings
 * @desc    Get all system settings
 * @access  Private (Admin)
 */
router.get('/', getSettings);

/**
 * @route   GET /api/admin/settings/category/:category
 * @desc    Get settings by category
 * @access  Private (Admin)
 */
router.get('/category/:category', getSettingsByCategory);

/**
 * @route   POST /api/admin/settings/initialize
 * @desc    Initialize default settings
 * @access  Private (Admin)
 */
router.post('/initialize', initializeDefaultSettings);

/**
 * @route   PUT /api/admin/settings
 * @desc    Update multiple settings
 * @access  Private (Admin)
 */
router.put('/', updateSettings);

/**
 * @route   PUT /api/admin/settings/:key
 * @desc    Update single setting
 * @access  Private (Admin)
 */
router.put('/:key', updateSetting);

/**
 * @route   DELETE /api/admin/settings/:key
 * @desc    Delete setting
 * @access  Private (Admin)
 */
router.delete('/:key', deleteSetting);

module.exports = router;