const Product = require('../../models/Product');
const Category = require('../../models/Category');
const Vendor = require('../../models/Vendor');

/**
 * Build product filters based on query parameters
 */
const buildProductFilter = (queryParams, userCurrency = null) => {
  const { 
    search, 
    category, 
    subcategory, 
    vendor, 
    minPrice, 
    maxPrice, 
    featured, 
    inStock 
  } = queryParams;

  const filter = { 
    status: 'active',
    visibility: 'public'
  };

  // Currency filter - only show products that have pricing for the selected currency
  if (userCurrency && userCurrency !== 'INR') {
    filter.$or = [
      // Products with default currency matching user's currency
      { 'pricing.currency': userCurrency },
      // Products with multi-currency pricing for user's currency
      { [`pricing.multiCurrency.${userCurrency}.basePrice`]: { $exists: true, $ne: null } }
    ];
  }

  // Category filter
  if (category) {
    filter.category = category;
  }

  // Subcategory filter
  if (subcategory) {
    filter.subcategory = subcategory;
  }

  // Vendor filter
  if (vendor) {
    filter.vendor = vendor;
  }

  // Featured filter
  if (featured === 'true') {
    filter.featured = true;
  }

  // Stock filter
  if (inStock === 'true') {
    const stockConditions = [
      { 'inventory.trackQuantity': false },
      { 
        'inventory.trackQuantity': true,
        'inventory.quantity': { $gt: 0 }
      },
      {
        'inventory.trackQuantity': true,
        'inventory.allowBackorders': true
      }
    ];
    
    // If currency filter exists, combine with stock conditions
    if (filter.$or && userCurrency && userCurrency !== 'INR') {
      filter.$and = filter.$and || [];
      filter.$and.push({ $or: filter.$or }); // Currency conditions
      filter.$and.push({ $or: stockConditions }); // Stock conditions
      delete filter.$or;
    } else {
      filter.$or = stockConditions;
    }
  }

  // Price range filter
  if (!isNaN(minPrice) || !isNaN(maxPrice)) {
    filter['pricing.basePrice'] = {};
    if (!isNaN(minPrice)) {
      filter['pricing.basePrice'].$gte = minPrice;
    }
    if (!isNaN(maxPrice)) {
      filter['pricing.basePrice'].$lte = maxPrice;
    }
  }

  // Search filter
  if (search) {
    const searchConditions = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
      { tags: { $in: [new RegExp(search, 'i')] } }
    ];
    
    // If currency filter exists, combine with search conditions
    if (filter.$or && userCurrency && userCurrency !== 'INR') {
      filter.$and = [
        { $or: filter.$or }, // Currency conditions
        { $or: searchConditions } // Search conditions
      ];
      delete filter.$or;
    } else {
      filter.$or = searchConditions;
    }
  }

  return filter;
};

/**
 * Build sort object based on sort parameters
 */
const buildSortObject = (sortBy = 'createdAt', sortOrder = 'desc') => {
  const order = sortOrder === 'asc' ? 1 : -1;
  const sort = {};

  switch (sortBy) {
    case 'price':
      sort['pricing.basePrice'] = order;
      break;
    case 'rating':
      sort['reviews.averageRating'] = order;
      break;
    case 'popularity':
      sort['sales.totalSold'] = order;
      break;
    case 'name':
      sort.name = order;
      break;
    default:
      sort[sortBy] = order;
  }

  return sort;
};

/**
 * Calculate pagination info
 */
const calculatePagination = (page, limit, totalItems) => {
  const totalPages = Math.ceil(totalItems / limit);
  return {
    currentPage: page,
    totalPages,
    totalItems,
    hasNextPage: page < totalPages,
    hasPrevPage: page > 1,
    limit
  };
};

/**
 * Common product population fields
 */
const getPopulationFields = () => [
  { path: 'vendor', select: 'businessName' },
  { path: 'category', select: 'name slug' },
  { path: 'subcategory', select: 'name slug' }
];

/**
 * Common product selection fields
 */
const getSelectionFields = () => '-__v -modifiedBy -lastModified';

/**
 * Get products with filters, sorting, and pagination
 */
const getProductsWithFilters = async (queryParams, userCurrency = null) => {
  const page = parseInt(queryParams.page) || 1;
  const limit = parseInt(queryParams.limit) || 12;
  const skip = (page - 1) * limit;
  
  const filter = buildProductFilter(queryParams, userCurrency);
  const sort = buildSortObject(queryParams.sortBy, queryParams.sortOrder);

  const [products, totalProducts] = await Promise.all([
    Product.find(filter)
      .populate(getPopulationFields())
      .select(getSelectionFields())
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean(),
    Product.countDocuments(filter)
  ]);

  const pagination = calculatePagination(page, limit, totalProducts);

  return {
    products,
    pagination
  };
};

/**
 * Get single product by ID or slug
 */
const getProductById = async (identifier) => {
  let product;

  // Try to find by ID first, then by slug
  if (identifier.match(/^[0-9a-fA-F]{24}$/)) {
    product = await Product.findOne({ 
      _id: identifier, 
      status: 'active',
      visibility: 'public'
    });
  } else {
    product = await Product.findOne({ 
      slug: identifier, 
      status: 'active',
      visibility: 'public'
    });
  }

  if (!product) {
    return null;
  }

  // Populate related data
  await product.populate([
    { 
      path: 'vendor', 
      select: 'businessName businessDescription logo contactInfo.website performance.rating performance.totalReviews'
    },
    { path: 'category', select: 'name slug' },
    { path: 'subcategory', select: 'name slug' }
  ]);

  return product;
};

/**
 * Get related products from the same category
 */
const getRelatedProducts = async (productId, categoryId, limit = 8) => {
  if (!categoryId) return [];

  return await Product.find({
    category: categoryId,
    _id: { $ne: productId },
    status: 'active',
    visibility: 'public'
  })
    .populate('vendor', 'businessName')
    .select('name slug pricing.basePrice pricing.salePrice images reviews.averageRating')
    .limit(limit)
    .lean();
};

module.exports = {
  buildProductFilter,
  buildSortObject,
  calculatePagination,
  getPopulationFields,
  getSelectionFields,
  getProductsWithFilters,
  getProductById,
  getRelatedProducts
};
