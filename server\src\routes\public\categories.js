const express = require('express');
const router = express.Router();

// Import controller
const {
  getCategories,
  getCategoryTree,
  getFeaturedCategories,
  getCategory
} = require('../../controllers/public/categoryController');

/**
 * @route   GET /api/public/categories
 * @desc    Get all active categories
 * @access  Public
 */
router.get('/', getCategories);

/**
 * @route   GET /api/public/categories/tree
 * @desc    Get category tree structure
 * @access  Public
 */
router.get('/tree', getCategoryTree);

/**
 * @route   GET /api/public/categories/featured
 * @desc    Get featured categories
 * @access  Public
 */
router.get('/featured', getFeaturedCategories);

/**
 * @route   GET /api/public/categories/:id
 * @desc    Get single category by ID or slug
 * @access  Public
 */
router.get('/:id', getCategory);

module.exports = router;