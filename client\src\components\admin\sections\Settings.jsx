import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Switch,
  Select,
  InputNumber,
  Typography,
  Row,
  Col,
  Divider,
  message,
  Space,
  Tabs
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  DollarOutlined,
  MailOutlined,
  UserOutlined,
  DatabaseOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone
} from '@ant-design/icons';
import { settingsApi } from '../../../services/adminApi';
import { authAPI } from '../../../utils/authApi';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const Settings = () => {
  const [paymentForm] = Form.useForm();
  const [emailForm] = Form.useForm();
  const [profileForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [configurationForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState({});
  const [loadingSettings, setLoadingSettings] = useState(true);

  useEffect(() => {
    fetchSettings();
    loadProfileData();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoadingSettings(true);
      const response = await settingsApi.getSettings();
      if (response.data.success) {
        setSettings(response.data.data);
        
        // Pre-populate forms with current settings
        const paymentSettings = {};
        const emailSettings = {};
        const configurationSettings = {};
        
        Object.keys(response.data.data).forEach(key => {
          const setting = response.data.data[key];
          if (setting.category === 'payment') {
            paymentSettings[key] = setting.value;
          } else if (setting.category === 'email') {
            emailSettings[key] = setting.value;
          } else if (setting.category === 'configuration') {
            configurationSettings[key] = setting.value;
          }
        });
        
        paymentForm.setFieldsValue(paymentSettings);
        emailForm.setFieldsValue(emailSettings);
        configurationForm.setFieldsValue(configurationSettings);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      message.error('Failed to load settings');
    } finally {
      setLoadingSettings(false);
    }
  };

  const loadProfileData = async () => {
    try {
      const token = localStorage.getItem('authToken') || localStorage.getItem('token');
      if (token) {
        const response = await authAPI.getProfile(token);
        if (response.success) {
          profileForm.setFieldsValue({
            email: response.data.email,
            firstName: response.data.firstName,
            lastName: response.data.lastName
          });
        }
      }
    } catch (error) {
      console.error('Error loading profile:', error);
    }
  };

  const handleSaveSettings = async (category, values) => {
    setLoading(true);
    try {
      // Convert form values to settings format
      const settingsUpdate = {};
      Object.keys(values).forEach(key => {
        settingsUpdate[key] = values[key];
      });
      
      const response = await settingsApi.updateSettings({ settings: settingsUpdate });
      if (response.data.success) {
        message.success(`${category} settings saved successfully`);
        await fetchSettings(); // Refresh settings
      } else {
        throw new Error(response.data.message || 'Failed to save settings');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      message.error(error.message || 'Failed to save settings');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateProfile = async (values) => {
    setLoading(true);
    try {
      const token = localStorage.getItem('authToken') || localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }
      
      const response = await authAPI.updateProfile(token, {
        email: values.email,
        firstName: values.firstName,
        lastName: values.lastName
      });
      
      if (response.success) {
        message.success('Profile updated successfully');
      } else {
        throw new Error(response.message || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      message.error(error.message || 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const handleChangePassword = async (values) => {
    setLoading(true);
    try {
      const token = localStorage.getItem('authToken') || localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }
      
      const response = await authAPI.changePassword(token, {
        currentPassword: values.currentPassword,
        newPassword: values.newPassword
      });
      
      if (response.success) {
        message.success('Password changed successfully');
        passwordForm.resetFields();
      } else {
        throw new Error(response.message || 'Failed to change password');
      }
    } catch (error) {
      console.error('Error changing password:', error);
      message.error(error.message || 'Failed to change password');
    } finally {
      setLoading(false);
    }
  };

  const profileSettings = (
    <Card>
      <Typography.Title level={4}>Profile Information</Typography.Title>
      <Form
        form={profileForm}
        layout="vertical"
        onFinish={handleUpdateProfile}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="firstName"
              label="First Name"
              rules={[{ required: true, message: 'Please enter first name' }]}
            >
              <Input placeholder="Enter first name" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="lastName"
              label="Last Name"
              rules={[{ required: true, message: 'Please enter last name' }]}
            >
              <Input placeholder="Enter last name" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="email"
          label="Email Address"
          rules={[
            { required: true, message: 'Please enter email address' },
            { type: 'email', message: 'Please enter a valid email address' }
          ]}
        >
          <Input placeholder="Enter email address" />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
            Update Profile
          </Button>
        </Form.Item>
      </Form>

      <Divider />

      <Typography.Title level={4}>Change Password</Typography.Title>
      <Form
        form={passwordForm}
        layout="vertical"
        onFinish={handleChangePassword}
      >
        <Form.Item
          name="currentPassword"
          label="Current Password"
          rules={[{ required: true, message: 'Please enter current password' }]}
        >
          <Input.Password placeholder="Enter current password" />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="newPassword"
              label="New Password"
              rules={[
                { required: true, message: 'Please enter new password' },
                { min: 8, message: 'Password must be at least 8 characters' }
              ]}
            >
              <Input.Password placeholder="Enter new password" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="confirmPassword"
              label="Confirm New Password"
              dependencies={['newPassword']}
              rules={[
                { required: true, message: 'Please confirm new password' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('newPassword') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('The two passwords do not match'));
                  },
                }),
              ]}
            >
              <Input.Password placeholder="Confirm new password" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>
            Change Password
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );

  const paymentSettings = (
    <Card>
      <Form
        form={paymentForm}
        layout="vertical"
        onFinish={(values) => handleSaveSettings('Payment', values)}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="vendor_commission_rate"
              label="Default Commission Rate (%)"
              rules={[{ required: true, message: 'Please enter commission rate' }]}
            >
              <InputNumber
                min={0}
                max={100}
                style={{ width: '100%' }}
                placeholder="Enter commission rate"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="minimum_payout"
              label="Minimum Payout Amount ($)"
              rules={[{ required: true, message: 'Please enter minimum payout' }]}
            >
              <InputNumber
                min={0}
                style={{ width: '100%' }}
                placeholder="Enter minimum payout amount"
              />
            </Form.Item>
          </Col>
        </Row>

        <Divider>Payment Gateway Configuration</Divider>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="stripe_publishable_key" label="Stripe Public Key">
              <Input placeholder="Enter Stripe public key" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="stripe_secret_key" label="Stripe Secret Key">
              <Input.Password placeholder="Enter Stripe secret key" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="paypal_client_id" label="PayPal Client ID">
              <Input placeholder="Enter PayPal client ID" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="paypal_client_secret" label="PayPal Client Secret">
              <Input.Password placeholder="Enter PayPal client secret" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
            Save Payment Settings
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );

  const emailSettings = (
    <Card>
      <Form
        form={emailForm}
        layout="vertical"
        onFinish={(values) => handleSaveSettings('Email', values)}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="smtp_host"
              label="SMTP Host"
              rules={[{ required: true, message: 'Please enter SMTP host' }]}
            >
              <Input placeholder="Enter SMTP host" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="smtp_port"
              label="SMTP Port"
              rules={[{ required: true, message: 'Please enter SMTP port' }]}
            >
              <InputNumber style={{ width: '100%' }} placeholder="Enter SMTP port" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="smtp_username"
              label="SMTP Username"
              rules={[{ required: true, message: 'Please enter SMTP username' }]}
            >
              <Input placeholder="Enter SMTP username" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="smtp_password"
              label="SMTP Password"
              rules={[{ required: true, message: 'Please enter SMTP password' }]}
            >
              <Input.Password placeholder="Enter SMTP password" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="smtp_security" label="Security">
              <Select placeholder="Select security type">
                <Option value="none">None</Option>
                <Option value="ssl">SSL</Option>
                <Option value="tls">TLS</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="from_name" label="From Name">
              <Input placeholder="Enter sender name" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item name="from_email" label="From Email">
          <Input placeholder="Enter from email address" type="email" />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
            Save Email Settings
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );

  const configurationSettings = (
    <Card>
      <Form
        form={configurationForm}
        layout="vertical"
        onFinish={(values) => handleSaveSettings('Configuration', values)}
      >
        <Typography.Title level={4}>Database & Server Configuration</Typography.Title>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="mongodb_uri" label="MongoDB URI">
              <Input.Password 
                placeholder="mongodb://localhost:27017/database" 
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="port" label="Server Port">
              <Input placeholder="8000" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="node_env" label="Node Environment">
              <Select placeholder="Select environment">
                <Option value="development">Development</Option>
                <Option value="production">Production</Option>
                <Option value="staging">Staging</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="frontend_url" label="Frontend URL">
              <Input placeholder="http://localhost:3000" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="app_name" label="Application Name">
              <Input placeholder="Alicartify" />
            </Form.Item>
          </Col>
        </Row>

        <Divider>JWT & Security Configuration</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="jwt_secret" label="JWT Secret">
              <Input.Password 
                placeholder="Enter JWT secret key" 
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="jwt_expires_in" label="JWT Expires In">
              <Input placeholder="7d" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="jwt_refresh_secret" label="JWT Refresh Secret">
              <Input.Password 
                placeholder="Enter JWT refresh secret" 
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="jwt_refresh_expires_in" label="JWT Refresh Expires In">
              <Input placeholder="30d" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="session_secret" label="Session Secret">
              <Input.Password 
                placeholder="Enter session secret" 
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>
          </Col>
        </Row>

        <Divider>Email Configuration</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="smtp_user" label="SMTP User Email">
              <Input placeholder="<EMAIL>" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="smtp_pass" label="SMTP Password">
              <Input.Password 
                placeholder="Enter SMTP password" 
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="smtp_from" label="SMTP From Email">
              <Input placeholder="<EMAIL>" />
            </Form.Item>
          </Col>
        </Row>

        <Divider>Payment Gateway Configuration</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="razorpay_key_id" label="Razorpay Key ID">
              <Input placeholder="rzp_test_xxxxxxxxxx" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="razorpay_key_secret" label="Razorpay Secret">
              <Input.Password 
                placeholder="Enter Razorpay secret" 
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="stripe_webhook_secret" label="Stripe Webhook Secret">
              <Input.Password 
                placeholder="Enter Stripe webhook secret" 
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="paypal_mode" label="PayPal Mode">
              <Select placeholder="Select PayPal mode">
                <Option value="sandbox">Sandbox</Option>
                <Option value="live">Live</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Divider>Shipping Configuration</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="shiprocket_email" label="Shiprocket Email">
              <Input placeholder="<EMAIL>" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="shiprocket_password" label="Shiprocket Password">
              <Input.Password 
                placeholder="Enter Shiprocket password" 
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>
          </Col>
        </Row>

        <Divider>Cloud Storage Configuration</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="cloudinary_cloud_name" label="Cloudinary Cloud Name">
              <Input placeholder="your-cloud-name" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="cloudinary_api_key" label="Cloudinary API Key">
              <Input placeholder="123456789012345" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="cloudinary_api_secret" label="Cloudinary API Secret">
              <Input.Password 
                placeholder="Enter Cloudinary API secret" 
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>
          </Col>
        </Row>

        <Divider>Social Authentication</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="google_client_id" label="Google Client ID">
              <Input placeholder="Enter Google OAuth client ID" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="google_client_secret" label="Google Client Secret">
              <Input.Password 
                placeholder="Enter Google OAuth secret" 
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="facebook_app_id" label="Facebook App ID">
              <Input placeholder="Enter Facebook app ID" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="facebook_app_secret" label="Facebook App Secret">
              <Input.Password 
                placeholder="Enter Facebook app secret" 
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="github_client_id" label="GitHub Client ID">
              <Input placeholder="Enter GitHub client ID" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="github_client_secret" label="GitHub Client Secret">
              <Input.Password 
                placeholder="Enter GitHub client secret" 
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>
          </Col>
        </Row>

        <Divider>AWS Configuration</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="aws_access_key_id" label="AWS Access Key ID">
              <Input placeholder="AKIAIOSFODNN7EXAMPLE" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="aws_secret_access_key" label="AWS Secret Access Key">
              <Input.Password 
                placeholder="Enter AWS secret access key" 
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="aws_region" label="AWS Region">
              <Input placeholder="us-east-1" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="aws_s3_bucket" label="AWS S3 Bucket">
              <Input placeholder="your-s3-bucket-name" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
            Save Configuration Settings
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );


  const tabItems = [
    {
      key: 'profile',
      label: (
        <span>
          <UserOutlined />
          Profile
        </span>
      ),
      children: profileSettings,
    },
    {
      key: 'payment',
      label: (
        <span>
          <DollarOutlined />
          Payment
        </span>
      ),
      children: paymentSettings,
    },
    {
      key: 'email',
      label: (
        <span>
          <MailOutlined />
          Email
        </span>
      ),
      children: emailSettings,
    },
    {
      key: 'configuration',
      label: (
        <span>
          <DatabaseOutlined />
          Configuration
        </span>
      ),
      children: configurationSettings,
    },
  ];

  if (loadingSettings) {
    return (
      <div style={{ padding: '50px', textAlign: 'center' }}>
        <Typography.Title level={4}>Loading Settings...</Typography.Title>
      </div>
    );
  }

  return (
    <div>
      <Typography.Title level={2}>
        <SettingOutlined /> System Settings
      </Typography.Title>
      <Tabs defaultActiveKey="profile" items={tabItems} />
    </div>
  );
};

export default Settings;