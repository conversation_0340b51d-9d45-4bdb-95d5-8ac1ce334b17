import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ProductInfo } from '../utils/Api';
import SuggestedProductCard from './ui/SuggestedProductCard';

const SuggestedProducts = ({ currentProductId, categoryName, maxProducts = 8 }) => {
  const [suggestedProducts, setSuggestedProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchSuggestedProducts = async () => {
      try {
        setLoading(true);
        const allProducts = await ProductInfo();
        
        // Filter out the current product and get products from same category or random products
        let filteredProducts = allProducts.filter(product => 
          product.id !== parseInt(currentProductId)
        );

        // If we have a category, prioritize products from the same category
        if (categoryName) {
          const sameCategoryProducts = filteredProducts.filter(product => 
            product.category?.name === categoryName
          );
          
          // If we have enough products from same category, use them
          if (sameCategoryProducts.length >= maxProducts) {
            filteredProducts = sameCategoryProducts;
          } else {
            // Mix same category products with others
            const otherProducts = filteredProducts.filter(product => 
              product.category?.name !== categoryName
            );
            filteredProducts = [...sameCategoryProducts, ...otherProducts];
          }
        }

        // Shuffle and limit the products
        const shuffled = filteredProducts.sort(() => 0.5 - Math.random());
        const suggested = shuffled.slice(0, maxProducts);

        setSuggestedProducts(suggested);
      } catch (err) {
        console.error('Error fetching suggested products:', err);
        setError('Failed to load suggested products');
      } finally {
        setLoading(false);
      }
    };

    if (currentProductId) {
      fetchSuggestedProducts();
    }
  }, [currentProductId, categoryName, maxProducts]);

  const handleCartClick = (productId) => {
    console.log('Added to cart:', productId);
    // Add cart functionality here
  };

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">You might also like</h2>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-4 xl:grid-cols-4 gap-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-gray-200 aspect-square rounded-lg mb-3"></div>
              <div className="bg-gray-200 h-4 rounded mb-2"></div>
              <div className="bg-gray-200 h-4 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error || !suggestedProducts.length) {
    return null; // Don't show anything if there's an error or no products
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">You might also like</h2>
        <button 
          onClick={() => navigate('/page')}
          className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors"
        >
          View all products →
        </button>
      </div>
      
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-4 xl:grid-cols-4 gap-4">
        {suggestedProducts.map(product => (
          <SuggestedProductCard
            key={product.id}
            product={product}
            onCartClick={handleCartClick}
          />
        ))}
      </div>

      {/* View More Button */}
      <div className="text-center mt-8">
        <button
          onClick={() => navigate('/page')}
          className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200"
        >
          View More Products
        </button>
      </div>
    </div>
  );
};

export default SuggestedProducts;