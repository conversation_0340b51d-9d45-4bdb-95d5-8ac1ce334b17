import { useAuth } from './useAuth';
import { notification } from 'antd';
import { UserOutlined, PhoneOutlined, HomeOutlined } from '@ant-design/icons';

export const useProfileValidation = () => {
  const { user } = useAuth();

  const validateProfileForOrder = () => {
    if (!user) {
      notification.error({
        message: 'Authentication Required',
        description: 'Please log in to place an order',
        icon: <UserOutlined style={{ color: '#ff4d4f' }} />,
        placement: 'topRight',
        duration: 5,
      });
      return false;
    }

    const missingFields = [];
    
    // Check required personal information
    if (!user.firstName || !user.lastName) {
      missingFields.push('Full Name');
    }
    
    if (!user.email) {
      missingFields.push('Email Address');
    }
    
    if (!user.phone) {
      missingFields.push('Phone Number');
    }
    
    // Check required address information
    if (!user.address || !user.city || !user.state || !user.zipCode) {
      missingFields.push('Complete Address');
    }

    if (missingFields.length > 0) {
      const missingFieldsList = missingFields.join(', ');
      
      notification.warning({
        message: 'Complete Your Profile',
        description: (
          <div>
            <p>Please complete your profile before placing an order.</p>
            <p style={{ marginTop: 8, fontWeight: 500 }}>
              Missing: {missingFieldsList}
            </p>
            <p style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              Go to Profile → Edit Profile to add missing information
            </p>
          </div>
        ),
        icon: <UserOutlined style={{ color: '#faad14' }} />,
        placement: 'topRight',
        duration: 8,
        style: {
          width: 400,
        },
      });
      return false;
    }

    // Validate phone number format
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,15}$/;
    if (!phoneRegex.test(user.phone)) {
      notification.error({
        message: 'Invalid Phone Number',
        description: 'Please update your phone number with a valid format (10-15 digits)',
        icon: <PhoneOutlined style={{ color: '#ff4d4f' }} />,
        placement: 'topRight',
        duration: 6,
      });
      return false;
    }

    // Validate address completeness
    if (user.address?.length < 10) {
      notification.warning({
        message: 'Incomplete Address',
        description: 'Please provide a more detailed delivery address',
        icon: <HomeOutlined style={{ color: '#faad14' }} />,
        placement: 'topRight',
        duration: 5,
      });
      return false;
    }

    return true;
  };

  const getProfileCompletionStatus = () => {
    if (!user) return { isComplete: false, completionPercentage: 0, missingFields: [] };

    const requiredFields = [
      { key: 'firstName', label: 'First Name', weight: 15 },
      { key: 'lastName', label: 'Last Name', weight: 15 },
      { key: 'email', label: 'Email', weight: 20 },
      { key: 'phone', label: 'Phone', weight: 20 },
      { key: 'address', label: 'Address', weight: 15 },
      { key: 'city', label: 'City', weight: 5 },
      { key: 'state', label: 'State', weight: 5 },
      { key: 'zipCode', label: 'ZIP Code', weight: 5 },
    ];

    let completedWeight = 0;
    const missingFields = [];

    requiredFields.forEach(field => {
      if (user[field.key] && user[field.key].toString().trim()) {
        completedWeight += field.weight;
      } else {
        missingFields.push(field.label);
      }
    });

    const isComplete = completedWeight === 100;
    
    return {
      isComplete,
      completionPercentage: completedWeight,
      missingFields,
    };
  };

  return {
    validateProfileForOrder,
    getProfileCompletionStatus,
  };
};