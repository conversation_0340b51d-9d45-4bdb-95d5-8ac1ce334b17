# Shiprocket Webhook Setup Guide

## 🎯 What are Shiprocket Webhooks?

Shiprocket webhooks notify your system when shipment status changes (like shipped, delivered, etc.). This keeps your order tracking updated automatically.

## 🚀 Quick Setup

### Step 1: Your Webhook URL

Your webhook endpoint is already set up at:
```
https://yourdomain.com/api/webhooks/shiprocket
```

For local development:
```
http://localhost:5000/api/webhooks/shiprocket
```

### Step 2: Configure in Shiprocket Dashboard

1. **Login to Shiprocket Dashboard**
   - Go to [https://app.shiprocket.in/](https://app.shiprocket.in/)
   - Login with your credentials

2. **Navigate to Webhooks**
   - Go to **Settings** → **API** → **Webhooks**
   - Click **"Add Webhook"**

3. **Configure Webhook**
   ```
   Webhook URL: https://yourdomain.com/api/webhooks/shiprocket
   Events: Select all shipping events
   ✅ Order Shipped
   ✅ Order Delivered  
   ✅ Order Returned
   ✅ Order Cancelled
   ✅ Order Exception
   ```

### Step 3: For Local Development (Using ngrok)

```bash
# Install ngrok
npm install -g ngrok

# Expose your local server
ngrok http 5000

# Use the ngrok URL in Shiprocket
# Example: https://abc123.ngrok.io/api/webhooks/shiprocket
```

## 📋 Webhook Events Handled

| Event | Description | Action |
|-------|-------------|---------|
| `SHIPPED` | Package shipped | Update order status to "shipped" |
| `DELIVERED` | Package delivered | Update order status to "delivered" |
| `RETURNED` | Package returned | Update order status to "returned" |
| `CANCELLED` | Shipment cancelled | Update order status to "cancelled" |
| `EXCEPTION` | Delivery exception | Log exception details |

## 🔍 Webhook Payload Example

```json
{
  "order_id": "ORD123456",
  "awb": "1234567890",
  "current_status": "DELIVERED",
  "shipment_status": "DELIVERED",
  "shipped_date": "2024-01-15",
  "delivered_date": "2024-01-17",
  "courier_name": "Delhivery"
}
```

## 🛠️ Code Implementation

The webhook handler is already implemented:

**File:** `server/src/controllers/shiprocketWebhookController.js`

```javascript
const handleShiprocketWebhook = async (req, res) => {
  try {
    const webhookData = req.body;
    console.log('Received Shiprocket webhook:', webhookData);
    
    const { order_id, current_status } = webhookData;
    
    if (!order_id) {
      return res.status(400).json({ error: 'Missing order_id' });
    }
    
    // Find and update order
    const order = await Order.findOne({ orderNumber: order_id });
    if (order) {
      // Update order status based on webhook
      // Add timeline entry
      order.timeline.push({
        status: current_status.toLowerCase(),
        timestamp: new Date(),
        note: `Updated via Shiprocket webhook: ${current_status}`
      });
      await order.save();
    }
    
    res.json({ success: true });
  } catch (error) {
    console.error('Error processing Shiprocket webhook:', error);
    res.status(500).json({ success: false });
  }
};
```

## ✅ Quick Verification

1. **Check if webhook endpoint works:**
   ```bash
   curl -X POST http://localhost:5000/api/webhooks/shiprocket \
     -H "Content-Type: application/json" \
     -d '{"order_id":"TEST123","current_status":"SHIPPED"}'
   ```

2. **Expected Response:**
   ```json
   {"success": true}
   ```

## 🚨 Troubleshooting

### Common Issues:

1. **Webhook not receiving events**
   - Ensure URL is publicly accessible
   - Check Shiprocket webhook configuration
   - Verify server is running

2. **Local development**
   - Use ngrok to expose local server
   - Update webhook URL in Shiprocket dashboard

### Debug Steps:

1. **Check server logs**
   ```bash
   # Your server should log:
   # "Received Shiprocket webhook: {...}"
   ```

2. **Test manually**
   ```bash
   node server/test-shiprocket-webhook.js
   ```

## 🎉 You're Done!

Once set up, your system will automatically:
- ✅ Receive shipping updates from Shiprocket
- ✅ Update order status in real-time
- ✅ Keep customers informed about delivery status

Your webhook endpoint: `https://yourdomain.com/api/webhooks/shiprocket`