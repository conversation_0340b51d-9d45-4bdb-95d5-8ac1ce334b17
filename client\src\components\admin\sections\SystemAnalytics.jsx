import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Select,
  DatePicker,
  Button,
  Space,
  Typography,
  Progress,
  Tag,
  Tabs,
  Alert,
  Spin,
  Tooltip,
  List,
  Avatar
} from 'antd';
import {
  RiseOutlined,
  FallOutlined,
  ShoppingOutlined,
  UserOutlined,
  ShopOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  BellOutlined,
  EyeOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { Line, Bar, Doughnut, Area } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  ArcElement,
  Filler
} from 'chart.js';
import { dashboardApi, productsApi, vendorsApi, ordersApi } from '../../../services/adminApi';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  ChartTooltip,
  Legend,
  ArcElement,
  Filler
);

const { Title: AntTitle, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

const SystemAnalytics = () => {
  const [loading, setLoading] = useState(true);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [systemHealth, setSystemHealth] = useState(null);
  const [performanceMetrics, setPerformanceMetrics] = useState(null);
  const [recentActivity, setRecentActivity] = useState([]);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [dateRange, setDateRange] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchAnalyticsData();
  }, [selectedPeriod, dateRange]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      
      const params = {
        period: selectedPeriod,
        ...(dateRange && {
          startDate: dateRange[0].format('YYYY-MM-DD'),
          endDate: dateRange[1].format('YYYY-MM-DD')
        })
      };

      const [
        dashboardResponse,
        productStatsResponse,
        vendorStatsResponse,
        orderStatsResponse
      ] = await Promise.all([
        dashboardApi.getStats(params),
        productsApi.getStats(params),
        vendorsApi.getStatistics(params),
        ordersApi.getStats(params)
      ]);

      // Combine all analytics data
      const combinedData = {
        dashboard: dashboardResponse.data.data,
        products: productStatsResponse.data.data,
        vendors: vendorStatsResponse.data.data,
        orders: orderStatsResponse.data.data
      };

      setAnalyticsData(combinedData);
      
      // Calculate system health metrics
      const health = calculateSystemHealth(combinedData);
      setSystemHealth(health);
      
      // Generate performance metrics
      const performance = generatePerformanceMetrics(combinedData);
      setPerformanceMetrics(performance);
      
      // Generate recent activity
      const activity = generateRecentActivity(combinedData);
      setRecentActivity(activity);

    } catch (error) {
      console.error('Failed to fetch analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateSystemHealth = (data) => {
    const health = {
      overall: 85,
      components: {
        database: 92,
        api: 88,
        storage: 95,
        notifications: 78
      },
      issues: [
        {
          type: 'warning',
          message: 'High notification delivery latency detected',
          component: 'notifications'
        }
      ]
    };
    
    return health;
  };

  const generatePerformanceMetrics = (data) => {
    return {
      responseTime: {
        avg: 245,
        p95: 580,
        p99: 1200
      },
      throughput: {
        requestsPerSecond: 125,
        ordersPerHour: 45,
        signupsPerDay: 23
      },
      errorRate: 0.02,
      uptime: 99.8
    };
  };

  const generateRecentActivity = (data) => {
    return [
      {
        id: 1,
        type: 'product_approved',
        message: 'Product "Wireless Headphones" approved',
        timestamp: new Date(Date.now() - 5 * 60 * 1000),
        user: 'Admin User',
        icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />
      },
      {
        id: 2,
        type: 'vendor_verified',
        message: 'Vendor "TechStore Inc" verified',
        timestamp: new Date(Date.now() - 15 * 60 * 1000),
        user: 'Admin User',
        icon: <ShopOutlined style={{ color: '#1890ff' }} />
      },
      {
        id: 3,
        type: 'order_placed',
        message: 'New order #12345 placed',
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
        user: 'Customer',
        icon: <ShoppingOutlined style={{ color: '#fa8c16' }} />
      }
    ];
  };

  const getHealthColor = (score) => {
    if (score >= 90) return '#52c41a';
    if (score >= 70) return '#faad14';
    return '#ff4d4f';
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const revenueChartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Revenue',
        data: [12000, 19000, 15000, 25000, 22000, 30000],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        fill: true,
      },
    ],
  };

  const userGrowthData = {
    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
    datasets: [
      {
        label: 'New Users',
        data: [65, 59, 80, 81],
        backgroundColor: 'rgba(54, 162, 235, 0.5)',
      },
      {
        label: 'New Vendors',
        data: [28, 48, 40, 19],
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
      },
    ],
  };

  const systemStatusData = {
    labels: ['Healthy', 'Warning', 'Critical'],
    datasets: [
      {
        data: [85, 12, 3],
        backgroundColor: [
          '#52c41a',
          '#faad14',
          '#ff4d4f',
        ],
      },
    ],
  };

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>
          <Text>Loading system analytics...</Text>
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <AntTitle level={2}>System Analytics</AntTitle>
          <Text type="secondary">
            Comprehensive system performance and business metrics
          </Text>
        </div>
        <Space>
          <Select
            value={selectedPeriod}
            onChange={setSelectedPeriod}
            style={{ width: 120 }}
          >
            <Option value="7d">Last 7 days</Option>
            <Option value="30d">Last 30 days</Option>
            <Option value="90d">Last 90 days</Option>
            <Option value="1y">Last year</Option>
          </Select>
          <RangePicker
            value={dateRange}
            onChange={setDateRange}
            style={{ width: 240 }}
          />
          <Button icon={<ReloadOutlined />} onClick={fetchAnalyticsData}>
            Refresh
          </Button>
        </Space>
      </div>

      {/* System Health Alert */}
      {systemHealth?.issues?.length > 0 && (
        <Alert
          message="System Health Issues Detected"
          description={
            <ul style={{ margin: 0, paddingLeft: '20px' }}>
              {systemHealth.issues.map((issue, index) => (
                <li key={index}>{issue.message}</li>
              ))}
            </ul>
          }
          type="warning"
          showIcon
          closable
          style={{ marginBottom: '24px' }}
        />
      )}

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="Overview" key="overview">
          {/* Key Metrics */}
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="System Health"
                  value={systemHealth?.overall || 0}
                  suffix="%"
                  valueStyle={{ color: getHealthColor(systemHealth?.overall || 0) }}
                  prefix={<RiseOutlined />}
                />
                <Progress
                  percent={systemHealth?.overall || 0}
                  strokeColor={getHealthColor(systemHealth?.overall || 0)}
                  showInfo={false}
                  size="small"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="Response Time"
                  value={performanceMetrics?.responseTime?.avg || 0}
                  suffix="ms"
                  valueStyle={{ color: '#1890ff' }}
                  prefix={<ClockCircleOutlined />}
                />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  P95: {performanceMetrics?.responseTime?.p95 || 0}ms
                </Text>
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="Uptime"
                  value={performanceMetrics?.uptime || 0}
                  suffix="%"
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<CheckCircleOutlined />}
                />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  Last 30 days
                </Text>
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="Error Rate"
                  value={performanceMetrics?.errorRate || 0}
                  suffix="%"
                  valueStyle={{ color: performanceMetrics?.errorRate > 1 ? '#ff4d4f' : '#52c41a' }}
                  prefix={<WarningOutlined />}
                />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  Target: &lt; 1%
                </Text>
              </Card>
            </Col>
          </Row>

          {/* Charts */}
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <Col xs={24} lg={12}>
              <Card title="Revenue Trend">
                <Line data={revenueChartData} options={chartOptions} />
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="User Growth">
                <Bar data={userGrowthData} options={chartOptions} />
              </Card>
            </Col>
          </Row>

          {/* System Status and Recent Activity */}
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={8}>
              <Card title="System Status">
                <Doughnut data={systemStatusData} />
              </Card>
            </Col>
            <Col xs={24} lg={16}>
              <Card 
                title="Recent Activity" 
                extra={<Button type="link" icon={<EyeOutlined />}>View All</Button>}
              >
                <List
                  dataSource={recentActivity}
                  renderItem={(item) => (
                    <List.Item>
                      <List.Item.Meta
                        avatar={<Avatar icon={item.icon} />}
                        title={item.message}
                        description={
                          <Space>
                            <Text type="secondary">{item.user}</Text>
                            <Text type="secondary">•</Text>
                            <Text type="secondary">
                              {item.timestamp.toLocaleTimeString()}
                            </Text>
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default SystemAnalytics;
