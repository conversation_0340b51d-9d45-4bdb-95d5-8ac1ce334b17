import dayjs from 'dayjs';

// Process sales performance data for line chart
export const prepareSalesPerformanceData = (analyticsData) => {
  if (!analyticsData?.analytics) {
    return {
      labels: [],
      datasets: [{
        label: 'Sales (₹)',
        data: [],
        borderColor: '#52c41a',
        backgroundColor: 'rgba(82, 196, 26, 0.1)',
        fill: true,
        tension: 0.4,
      }]
    };
  }
  
  const data = analyticsData.analytics;
  const labels = data.map(item => {
    if (item._id.day) {
      return dayjs(`${item._id.year}-${item._id.month}-${item._id.day}`).format('MMM DD');
    }
    return dayjs(`${item._id.year}-${item._id.month}-01`).format('MMM YYYY');
  });
  
  return {
    labels,
    datasets: [{
      label: 'Sales (₹)',
      data: data.map(item => item.revenue || 0),
      borderColor: '#52c41a',
      backgroundColor: 'rgba(82, 196, 26, 0.1)',
      fill: true,
      tension: 0.4,
    }]
  };
};

// Process order analytics data for bar chart
export const prepareOrderAnalyticsData = (analyticsData) => {
  if (!analyticsData?.analytics) {
    return {
      labels: [],
      datasets: [{
        label: 'Orders',
        data: [],
        backgroundColor: '#1890ff',
        borderColor: '#1890ff',
        borderWidth: 1,
      }]
    };
  }
  
  const data = analyticsData.analytics;
  const labels = data.map(item => {
    if (item._id.day) {
      return dayjs(`${item._id.year}-${item._id.month}-${item._id.day}`).format('MMM DD');
    }
    return dayjs(`${item._id.year}-${item._id.month}-01`).format('MMM YYYY');
  });
  
  return {
    labels,
    datasets: [{
      label: 'Orders',
      data: data.map(item => item.orders || 0),
      backgroundColor: '#1890ff',
      borderColor: '#1890ff',
      borderWidth: 1,
    }]
  };
};

// Process product performance data for doughnut chart
export const prepareProductPerformanceData = (dashboardData) => {
  const topProducts = dashboardData?.topProducts || [];
  if (!topProducts.length) {
    return {
      labels: ['No Data'],
      datasets: [{
        label: 'Revenue (₹)',
        data: [0],
        backgroundColor: ['#cccccc']
      }]
    };
  }
  
  return {
    labels: topProducts.map(product => 
      product.name?.slice(0, 20) + (product.name?.length > 20 ? '...' : '')
    ),
    datasets: [{
      label: 'Revenue (₹)',
      data: topProducts.map(product => product.sales?.totalRevenue || 0),
      backgroundColor: [
        '#1890ff',
        '#52c41a',
        '#faad14',
        '#f5222d',
        '#722ed1',
        '#13c2c2',
      ],
    }]
  };
};

// Process revenue breakdown data for pie chart
export const prepareRevenueBreakdownData = (dashboardData) => {
  const orders = dashboardData?.orders;
  if (!orders || !orders.totalRevenue) {
    return {
      labels: ['Product Sales', 'Shipping', 'Taxes', 'Other'],
      datasets: [{
        data: [0, 0, 0, 0],
        backgroundColor: ['#52c41a', '#1890ff', '#faad14', '#f5222d']
      }]
    };
  }
  
  const totalRevenue = orders.totalRevenue;
  const productSales = totalRevenue * 0.85;
  const shipping = totalRevenue * 0.10;
  const taxes = totalRevenue * 0.03;
  const other = totalRevenue * 0.02;
  
  return {
    labels: ['Product Sales', 'Shipping', 'Taxes', 'Other'],
    datasets: [{
      data: [productSales, shipping, taxes, other],
      backgroundColor: ['#52c41a', '#1890ff', '#faad14', '#f5222d']
    }]
  };
};

// Chart options configurations
export const getChartOptions = (isMobile) => ({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top',
      labels: {
        font: {
          size: isMobile ? 10 : 12
        }
      }
    },
  },
  scales: {
    y: {
      beginAtZero: true,
      ticks: {
        font: {
          size: isMobile ? 8 : 10
        }
      }
    },
    x: {
      ticks: {
        font: {
          size: isMobile ? 8 : 10
        }
      }
    }
  },
});

export const getDoughnutOptions = (isMobile) => ({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: isMobile ? 'bottom' : 'right',
      labels: {
        font: {
          size: isMobile ? 10 : 12
        }
      }
    },
  },
});
