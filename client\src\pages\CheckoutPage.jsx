import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../hooks/useAuth';
import { useProfileValidation } from '../hooks/useProfileValidation.jsx';
import { orderApi } from '../services/orderApi';
import Header from '../components/Header';
import Footer from '../components/Footer';
import AddressSelector from '../components/checkout/AddressSelector';
import OrderSummary from '../components/checkout/OrderSummary';
import PaymentOptions from '../components/checkout/PaymentOptions';
import OrderSuccess from '../components/checkout/OrderSuccess';
import { getPrimaryProductImage } from '../utils/imageUtils';
import { notification } from 'antd';

const CheckoutPage = () => {
  const navigate = useNavigate();
  const { cart, fetchCart, loading: cartLoading } = useCart();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { validateProfileForOrder } = useProfileValidation();
  
  const [selectedAddressId, setSelectedAddressId] = useState(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('cod');
  const [isPlacingOrder, setIsPlacingOrder] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [orderSuccess, setOrderSuccess] = useState(false);
  const [orderDetails, setOrderDetails] = useState(null);

  // Initialize checkout data
  useEffect(() => {
    const initializeCheckout = async () => {
      try {
        setLoading(true);
        
        // Check authentication
        if (!authLoading && !isAuthenticated) {
          notification.error({
            message: 'Authentication Required',
            description: 'Please login to continue with checkout'
          });
          navigate('/auth');
          return;
        }

        // Wait for auth to load
        if (authLoading) {
          return;
        }

        // Check if user has a complete address
        if (user) {
          const { address, city, state, zipCode } = user;
          if (address && city && state && zipCode) {
            setSelectedAddressId('profile'); // Use 'profile' to match AddressSelector
          }
        }

        // Fetch cart if not already loaded
        if (!cart) {
          await fetchCart();
        }
      } catch (err) {
        setError(err.message);
        notification.error({
          message: 'Error',
          description: 'Failed to load checkout data'
        });
      } finally {
        setLoading(false);
      }
    };

    initializeCheckout();
  }, [cart, fetchCart, navigate, user, isAuthenticated, authLoading]);

  const handleAddressSelect = (addressId) => {
    setSelectedAddressId(addressId);
  };

  const handlePaymentMethodSelect = (method) => {
    setSelectedPaymentMethod(method);
  };

  const calculateTotals = () => {
    if (!cart?.items) return { subtotal: 0, total: 0, savings: 0 };
    
    const subtotal = cart.totalAmount || 0;
    const protectPromiseFee = 28;
    const total = subtotal + protectPromiseFee;
    
    return {
      subtotal,
      protectPromiseFee,
      total,
      savings: 0
    };
  };

  const handlePlaceOrder = async () => {
    // Validate profile completeness first
    if (!validateProfileForOrder()) {
      return;
    }

    if (!selectedAddressId) {
      notification.error({
        message: 'Address Required',
        description: 'Please select a delivery address',
        placement: 'topRight',
        duration: 4,
      });
      return;
    }

    if (!cart?.items?.length) {
      notification.error({
        message: 'Empty Cart',
        description: 'Your cart is empty',
        placement: 'topRight',
        duration: 4,
      });
      return;
    }

    try {
      setIsPlacingOrder(true);
      
      const totals = calculateTotals();
      
      // Prepare order data with improved vendor ID extraction
      const orderData = {
        items: cart.items.map(item => {
          // FIXED: Enhanced vendor ID extraction with comprehensive fallback strategies
          let vendorId = null;
          let vendorExtractionStrategy = 'none';
          
          // Strategy 1: Check direct vendor reference from cart item (most reliable)
          if (item.vendor) {
            if (typeof item.vendor === 'object' && item.vendor._id) {
              vendorId = item.vendor._id.toString();
              vendorExtractionStrategy = 'item.vendor._id';
            } else if (typeof item.vendor === 'string' && item.vendor.length === 24) {
              // Valid ObjectId string
              vendorId = item.vendor;
              vendorExtractionStrategy = 'item.vendor (string)';
            }
          }
          
          // Strategy 2: Check vendor from populated product (fallback)
          if (!vendorId && item.product?.vendor) {
            if (typeof item.product.vendor === 'object' && item.product.vendor._id) {
              vendorId = item.product.vendor._id.toString();
              vendorExtractionStrategy = 'item.product.vendor._id';
            } else if (typeof item.product.vendor === 'string' && item.product.vendor.length === 24) {
              // Valid ObjectId string
              vendorId = item.product.vendor;
              vendorExtractionStrategy = 'item.product.vendor (string)';
            }
          }
          
          // Strategy 3: Log detailed debug info if still no vendor found
          if (!vendorId) {
            console.error('Detailed vendor extraction debug:', {
              productId: item.product?._id,
              productName: item.product?.name,
              strategy: vendorExtractionStrategy,
              itemVendor: {
                exists: !!item.vendor,
                type: typeof item.vendor,
                value: item.vendor,
                hasId: !!(item.vendor?._id),
                id: item.vendor?._id
              },
              productVendor: {
                exists: !!(item.product?.vendor),
                type: typeof item.product?.vendor,
                value: item.product?.vendor,
                hasId: !!(item.product?.vendor?._id),
                id: item.product?.vendor?._id
              },
              fullItemKeys: Object.keys(item),
              productKeys: Object.keys(item.product || {})
            });
            
            // Show user-friendly notification
            notification.error({
              message: 'Cart Data Issue',
              description: `There's an issue with the product "${item.product?.name || 'Unknown Product'}" in your cart. Please remove it and add it again.`,
              placement: 'topRight',
              duration: 6,
            });
            
            throw new Error(`Vendor information is missing for product: ${item.product?.name || 'Unknown Product'}. Please refresh your cart and try again.`);
          }
          
          console.log(`✅ Vendor ID extracted for ${item.product?.name}: ${vendorId} (via ${vendorExtractionStrategy})`);
          
          if (!vendorId) {
            throw new Error(`Vendor information is missing for product: ${item.product?.name || 'Unknown Product'}. Please refresh and try again.`);
          }
          
          return {
            product: item.product._id,
            vendor: vendorId,
            name: item.product.name,
            sku: item.product.sku || `SKU${item.product._id}`,
            image: item.product.images?.[0]?.url || item.product.images?.[0] || '',
            quantity: item.quantity,
            unitPrice: item.selectedVariant?.price || item.priceAtAdd || item.product.pricing?.basePrice || item.product.price,
            totalPrice: (item.selectedVariant?.price || item.priceAtAdd || item.product.pricing?.basePrice || item.product.price) * item.quantity,
            variant: item.selectedVariant ? {
              name: item.selectedVariant.name,
              options: item.selectedVariant.options || []
            } : undefined
          };
        }),
        billing: {
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          email: user.email || '',
          phone: user.phone || '',
          address: {
            street: user.address || '',
            city: user.city || '',
            state: user.state || '',
            zipCode: user.zipCode || '',
            country: user.country || 'India'
          }
        },
        shipping: {
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          address: {
            street: user.address || '',
            city: user.city || '',
            state: user.state || '',
            zipCode: user.zipCode || '',
            country: user.country || 'India'
          },
          method: 'standard',
          cost: 0
        },
        payment: {
          method: selectedPaymentMethod,
          status: 'pending'
        },
        pricing: {
          subtotal: totals.subtotal,
          tax: 0,
          shipping: totals.protectPromiseFee || 0,
          discount: 0,
          total: totals.total
        },
        customerNotes: '',
        estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
      };
      
      console.log('Order data being sent:', orderData);
      
      // Create the order
      const response = await orderApi.createOrder(orderData);
      
      if (response.success) {
        const orderInfo = {
          orderId: response.data.order.orderNumber,
          totalAmount: totals.total,
          paymentMethod: selectedPaymentMethod,
          deliveryAddress: selectedAddress,
          items: cart.items.map(item => ({
            name: item.product.name,
            quantity: item.quantity,
            price: item.selectedVariant?.price || item.priceAtAdd
          })),
          estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }),
          trackingNumbers: response.data.trackings.map(t => t.trackingNumber)
        };
        
        setOrderDetails(orderInfo);
        setOrderSuccess(true);
        
        notification.success({
          message: 'Order Placed Successfully!',
          description: 'Your order has been confirmed and is being processed.'
        });
      }
      
    } catch (err) {
      console.error('Order placement error:', err);
      notification.error({
        message: 'Order Failed',
        description: err.message || 'Failed to place order. Please try again.'
      });
    } finally {
      setIsPlacingOrder(false);
    }
  };

  const handleContinueShopping = () => {
    navigate('/home');
  };

  const handleTrackOrder = () => {
    // Redirect to track-order page with the first tracking number if available
    if (orderDetails?.trackingNumbers?.length > 0) {
      navigate(`/track-order/${orderDetails.trackingNumbers[0]}`);
    } else {
      navigate('/track-order');
    }
  };

  if (loading || cartLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
        <Footer />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Error Loading Checkout</h2>
            <p className="text-red-600 mb-4">{error}</p>
            <button 
              onClick={() => navigate('/cart')}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors"
            >
              Back to Cart
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (!cart?.items?.length) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">Your cart is empty</h2>
            <p className="text-gray-600 mb-6">Add some products to proceed with checkout</p>
            <button 
              onClick={() => navigate('/home')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
            >
              Continue Shopping
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // Show order success page if order was placed successfully
  if (orderSuccess) {
    return (
      <OrderSuccess 
        orderDetails={orderDetails}
        onContinueShopping={handleContinueShopping}
        onTrackOrder={handleTrackOrder}
      />
    );
  }

  const totals = calculateTotals();
  
  // Fix address selection logic to properly handle profile address
  const selectedAddress = (() => {
    if (!user || !selectedAddressId) return null;
    
    if (selectedAddressId === 'profile' || selectedAddressId === user.id) {
      // Return user with proper address structure for validation
      const addressData = {
        ...user,
        // Ensure all address fields are available
        address: user.address || '',
        city: user.city || '',
        state: user.state || '',
        zipCode: user.zipCode || '',
        country: user.country || 'India'
      };
      
      // Debug logging
      console.log('CheckoutPage - selectedAddress:', {
        selectedAddressId,
        userId: user.id,
        addressFields: {
          address: addressData.address,
          city: addressData.city,
          state: addressData.state,
          zipCode: addressData.zipCode,
          hasAll: !!(addressData.address && addressData.city && addressData.state && addressData.zipCode)
        }
      });
      
      return addressData;
    }
    
    return null;
  })();

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-6">
        {/* Progress Indicator */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                1
              </div>
              <span className="ml-2 text-sm font-medium text-blue-600">LOGIN</span>
            </div>
            <div className="w-8 h-px bg-blue-600"></div>
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                2
              </div>
              <span className="ml-2 text-sm font-medium text-blue-600">DELIVERY ADDRESS</span>
            </div>
            <div className="w-8 h-px bg-gray-300"></div>
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-semibold">
                3
              </div>
              <span className="ml-2 text-sm font-medium text-gray-600">ORDER SUMMARY</span>
            </div>
            <div className="w-8 h-px bg-gray-300"></div>
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-semibold">
                4
              </div>
              <span className="ml-2 text-sm font-medium text-gray-600">PAYMENT</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Address and Payment */}
          <div className="lg:col-span-2 space-y-6">
            {/* Address Selection */}
            <AddressSelector
              selectedAddressId={selectedAddressId}
              onAddressSelect={handleAddressSelect}
            />

            {/* Order Items Preview */}
            <div className="bg-white rounded-lg shadow-sm">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">
                  Order Items ({cart.totalItems} items)
                </h2>
              </div>
              <div className="px-6 py-4 max-h-96 overflow-y-auto">
                {cart.items.map((item, index) => {
                  const productImage = getPrimaryProductImage(item.product) || 
                    'https://via.placeholder.com/60x60?text=No+Image';
                  const itemPrice = item.selectedVariant?.price || item.priceAtAdd;
                  
                  return (
                    <div key={`${item.product._id}-${index}`} className="flex items-center py-3 border-b border-gray-100 last:border-b-0">
                      <img 
                        src={productImage} 
                        alt={item.product.name}
                        className="w-16 h-16 object-cover rounded"
                      />
                      <div className="ml-4 flex-grow">
                        <h4 className="text-sm font-medium text-gray-900">
                          {item.product.name}
                        </h4>
                        <p className="text-sm text-gray-600">
                          Qty: {item.quantity} × ₹{itemPrice}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-semibold text-gray-900">
                          ₹{(itemPrice * item.quantity).toFixed(0)}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Payment Options */}
            <PaymentOptions
              selectedMethod={selectedPaymentMethod}
              onMethodSelect={handlePaymentMethodSelect}
              totalAmount={totals.total}
            />
          </div>

          {/* Right Column - Order Summary */}
          <div className="lg:col-span-1">
            <OrderSummary
              cart={cart}
              totals={totals}
              selectedAddress={selectedAddress}
              selectedPaymentMethod={selectedPaymentMethod}
              onPlaceOrder={handlePlaceOrder}
              isPlacingOrder={isPlacingOrder}
            />
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default CheckoutPage;
