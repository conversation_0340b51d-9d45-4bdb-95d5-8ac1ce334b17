const HomepageSettings = require('../../models/HomepageSettings');
const Category = require('../../models/Category');

// Get public homepage settings
const getHomepageData = async (req, res) => {
  try {
    let settings = await HomepageSettings.getSettings();
    settings = await HomepageSettings.populate(settings, [
      {
        path: 'featuredCategories.category',
        select: 'name slug image'
      },
      {
        path: 'featuredCategoryIds',
        select: 'name slug image'
      }
    ]);

    // Filter only active and current items
    const now = new Date();

    const activeCarouselImages = settings.carouselImages
      .filter(img => img.isActive)
      .sort((a, b) => a.sortOrder - b.sortOrder)
      .map(img => ({
        _id: img._id,
        title: img.title,
        description: img.description,
        imageUrl: img.imageUrl,
        linkUrl: img.linkUrl
      }));

    // Prioritize featuredCategoryIds over featuredCategories for dynamic category management
    let activeFeaturedCategories = [];

    if (settings.featuredCategoryIds && settings.featuredCategoryIds.length > 0) {
      // Use the simple featuredCategoryIds system (admin panel selection)
      activeFeaturedCategories = settings.featuredCategoryIds
        .filter(category => category) // Filter out null/undefined categories
        .slice(0, 5) // Limit to 5 categories
        .map((category, index) => ({
          _id: `featured-${category._id}`,
          displayName: category.name,
          imageUrl: category.image || 'https://via.placeholder.com/200x150?text=Category',
          category: {
            _id: category._id,
            name: category.name,
            slug: category.slug
          },
          sortOrder: index
        }));
    } else {
      // Fallback to the complex featuredCategories system
      activeFeaturedCategories = settings.featuredCategories
        .filter(cat => cat.isActive && cat.category)
        .sort((a, b) => a.sortOrder - b.sortOrder)
        .map(cat => ({
          _id: cat._id,
          displayName: cat.displayName || cat.category.name,
          imageUrl: cat.imageUrl,
          category: {
            _id: cat.category._id,
            name: cat.category.name,
            slug: cat.category.slug
          }
        }));
    }
    
    const activePromotions = settings.promotionImages
      .filter(promo => {
        if (!promo.isActive) return false;
        if (promo.startDate && promo.startDate > now) return false;
        if (promo.endDate && promo.endDate < now) return false;
        return true;
      })
      .reduce((acc, promo) => {
        if (!acc[promo.position]) acc[promo.position] = [];
        acc[promo.position].push({
          _id: promo._id,
          title: promo.title,
          description: promo.description,
          imageUrl: promo.imageUrl,
          linkUrl: promo.linkUrl,
          position: promo.position
        });
        return acc;
      }, {});
    
    // Sort promotions by sortOrder within each position
    Object.keys(activePromotions).forEach(position => {
      activePromotions[position].sort((a, b) => a.sortOrder - b.sortOrder);
    });
    
    const responseData = {
      carousel: {
        images: activeCarouselImages,
        settings: {
          autoPlay: settings.settings.autoPlayCarousel,
          speed: settings.settings.carouselSpeed
        }
      },
      featuredCategories: activeFeaturedCategories,
      promotions: activePromotions,
      showPromotions: settings.settings.showPromotions
    };
    
    res.status(200).json({
      success: true,
      data: responseData
    });
  } catch (error) {
    console.error('Error fetching homepage data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch homepage data',
      error: error.message
    });
  }
};

// Get featured categories for navigation
const getFeaturedCategories = async (req, res) => {
  try {
    let settings = await HomepageSettings.getSettings();
    settings = await HomepageSettings.populate(settings, [
      {
        path: 'featuredCategories.category',
        select: 'name slug image'
      },
      {
        path: 'featuredCategoryIds',
        select: 'name slug image'
      }
    ]);

    let categories = [];

    if (settings.featuredCategoryIds && settings.featuredCategoryIds.length > 0) {
      // Use the simple featuredCategoryIds system (admin panel selection)
      categories = settings.featuredCategoryIds
        .filter(category => category) // Filter out null/undefined categories
        .slice(0, 5) // Limit to 5 categories
        .map(category => ({
          _id: category._id,
          name: category.name,
          slug: category.slug,
          imageUrl: category.image || 'https://via.placeholder.com/200x150?text=Category'
        }));
    } else {
      // Fallback to the complex featuredCategories system
      categories = settings.featuredCategories
        .filter(cat => cat.isActive && cat.category)
        .sort((a, b) => a.sortOrder - b.sortOrder)
        .map(cat => ({
          _id: cat.category._id,
          name: cat.displayName || cat.category.name,
          slug: cat.category.slug,
          imageUrl: cat.imageUrl || cat.category.image
        }));
    }

    res.status(200).json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Error fetching featured categories:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch featured categories',
      error: error.message
    });
  }
};

// Get all categories modal data for public use
const getAllCategoriesModalData = async (req, res) => {
  try {
    console.log('🔍 Fetching all categories modal data...');
    
    let settings = await HomepageSettings.getSettings();
    console.log('📊 Settings found:', !!settings);
    
    // Initialize default categories if not exists
    if (!settings.allCategoriesModal || 
        !settings.allCategoriesModal.mainCategories || 
        settings.allCategoriesModal.mainCategories.length === 0) {
      console.log('🔄 Initializing default categories...');
      await settings.initializeDefaultCategories();
      // Refresh settings after initialization
      settings = await settings.save();
      console.log('✅ Default categories initialized');
    }

    // Get active categories only - with error handling
    let mainCategories = [];
    let popularCategories = [];

    try {
      // Check if virtual method exists and works
      if (settings.activeMainCategories) {
        const activeMain = settings.activeMainCategories;
        mainCategories = activeMain.map(cat => ({
          _id: cat._id,
          name: cat.name,
          icon: cat.icon,
          linkUrl: cat.linkUrl || ''
        }));
      } else {
        // Fallback: manually filter active main categories
        if (settings.allCategoriesModal && settings.allCategoriesModal.mainCategories) {
          mainCategories = settings.allCategoriesModal.mainCategories
            .filter(cat => cat.isActive)
            .sort((a, b) => a.sortOrder - b.sortOrder)
            .map(cat => ({
              _id: cat._id,
              name: cat.name,
              icon: cat.icon,
              linkUrl: cat.linkUrl || ''
            }));
        }
      }
      console.log(`📋 Main categories: ${mainCategories.length}`);
    } catch (error) {
      console.error('❌ Error processing main categories:', error);
      mainCategories = [];
    }

    try {
      // Check if virtual method exists and works
      if (settings.activePopularCategories) {
        const activePopular = settings.activePopularCategories;
        popularCategories = activePopular.map(cat => ({
          _id: cat._id,
          name: cat.name,
          linkUrl: cat.linkUrl || ''
        }));
      } else {
        // Fallback: manually filter active popular categories
        if (settings.allCategoriesModal && settings.allCategoriesModal.popularCategories) {
          popularCategories = settings.allCategoriesModal.popularCategories
            .filter(cat => cat.isActive)
            .sort((a, b) => a.sortOrder - b.sortOrder)
            .map(cat => ({
              _id: cat._id,
              name: cat.name,
              linkUrl: cat.linkUrl || ''
            }));
        }
      }
      console.log(`📋 Popular categories: ${popularCategories.length}`);
    } catch (error) {
      console.error('❌ Error processing popular categories:', error);
      popularCategories = [];
    }

    res.status(200).json({
      success: true,
      data: {
        mainCategories,
        popularCategories
      }
    });
  } catch (error) {
    console.error('❌ Error fetching all categories modal data:', error);
    console.error('Stack trace:', error.stack);
    
    // Return fallback data if everything fails
    res.status(200).json({
      success: true,
      data: {
        mainCategories: [
          { _id: '1', name: 'Apparel & Accessories', icon: 'ShopOutlined', linkUrl: '' },
          { _id: '2', name: 'Consumer Electronics', icon: 'MobileOutlined', linkUrl: '' },
          { _id: '3', name: 'Sports & Entertainment', icon: 'CrownOutlined', linkUrl: '' },
          { _id: '4', name: 'Jewelry, Eyewear & Watches', icon: 'HeartOutlined', linkUrl: '' },
          { _id: '5', name: 'Shoes & Accessories', icon: 'CarOutlined', linkUrl: '' },
          { _id: '6', name: 'Home & Garden', icon: 'HomeOutlined', linkUrl: '' },
          { _id: '7', name: 'Beauty', icon: 'SkinOutlined', linkUrl: '' },
          { _id: '8', name: 'Luggage, Bags & Cases', icon: 'ShoppingOutlined', linkUrl: '' },
          { _id: '9', name: 'Packaging & Printing', icon: 'PrinterOutlined', linkUrl: '' },
          { _id: '10', name: 'Parents, Kids & Toys', icon: 'BugOutlined', linkUrl: '' },
          { _id: '11', name: 'Personal Care & Home Care', icon: 'SmileOutlined', linkUrl: '' },
          { _id: '12', name: 'Health & Medical', icon: 'MedicineBoxOutlined', linkUrl: '' },
          { _id: '13', name: 'Gifts & Crafts', icon: 'GiftOutlined', linkUrl: '' },
          { _id: '14', name: 'Furniture', icon: 'TableOutlined', linkUrl: '' },
          { _id: '15', name: 'Lights & Lighting', icon: 'BulbOutlined', linkUrl: '' },
          { _id: '16', name: 'Home Appliances', icon: 'SettingOutlined', linkUrl: '' },
          { _id: '17', name: 'Safety & Security', icon: 'SafetyOutlined', linkUrl: '' },
          { _id: '18', name: 'View All', icon: 'AppstoreOutlined', linkUrl: '' }
        ],
        popularCategories: [
          { _id: '1', name: 'Consumer Electronics', linkUrl: '' },
          { _id: '2', name: 'Apparel & Accessories', linkUrl: '' },
          { _id: '3', name: 'Home & Garden', linkUrl: '' },
          { _id: '4', name: 'Sports & Entertainment', linkUrl: '' },
          { _id: '5', name: 'Beauty', linkUrl: '' },
          { _id: '6', name: 'Jewelry, Eyewear & Watches', linkUrl: '' },
          { _id: '7', name: 'Shoes & Accessories', linkUrl: '' },
          { _id: '8', name: 'Health & Medical', linkUrl: '' },
          { _id: '9', name: 'Furniture', linkUrl: '' },
          { _id: '10', name: 'Home Appliances', linkUrl: '' }
        ]
      }
    });
  }
};

module.exports = {
  getHomepageData,
  getFeaturedCategories,
  getAllCategoriesModalData
};