import React, { createContext, useContext, useState } from 'react';

const NavbarContext = createContext();

export const useNavbar = () => {
  const context = useContext(NavbarContext);
  if (!context) {
    throw new Error('useNavbar must be used within a NavbarProvider');
  }
  return context;
};

export const NavbarProvider = ({ children }) => {
  const [isAnyModalHovered, setIsAnyModalHovered] = useState(false);
  const [isMainMenuHovered, setIsMainMenuHovered] = useState(false);

  const setModalHovered = (isHovered) => {
    setIsAnyModalHovered(isHovered);
  };

  return (
    <NavbarContext.Provider value={{
      isAnyModalHovered,
      setModalHovered,
      isMainMenuHovered,
      setIsMainMenuHovered
    }}>
      {children}
    </NavbarContext.Provider>
  );
};
