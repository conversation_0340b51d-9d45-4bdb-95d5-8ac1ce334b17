import React from 'react';
import { 
  UserOutlined, 
  PhoneOutlined
} from '@ant-design/icons';
import ProfileForm<PERSON>ield from './ProfileFormField';

const PersonalInfoSection = ({ user, userType, editData, isEditing, onInputChange }) => {

  if (userType === 'vendor') {
    return null; // Vendors don't have personal info section
  }
  
  if (userType === 'admin') {
    // Admin accounts have a simplified view
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900 flex items-center space-x-2">
            <UserOutlined className="text-red-500" />
            <span>Admin Information</span>
          </h2>
        </div>
        <div className="px-6 py-4 space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <ProfileFormField
              label="First Name"
              value={editData.firstName}
              displayValue={user.firstName || 'Super'}
              isEditing={isEditing}
              onChange={(value) => onInputChange('firstName', value)}
              icon={UserOutlined}
              required
            />
            <ProfileFormField
              label="Last Name"
              value={editData.lastName}
              displayValue={user.lastName || 'Admin'}
              isEditing={isEditing}
              onChange={(value) => onInputChange('lastName', value)}
              icon={UserOutlined}
              required
            />
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <ProfileFormField
              label="Email"
              value={editData.email}
              displayValue={user.email}
              isEditing={false} // Email should not be editable for admin
              onChange={(value) => onInputChange('email', value)}
              type="email"
              icon={UserOutlined}
              required
            />
            <ProfileFormField
              label="Mobile Number"
              value={editData.phone}
              displayValue={user.phone}
              isEditing={isEditing}
              onChange={(value) => onInputChange('phone', value)}
              type="tel"
              icon={PhoneOutlined}
              placeholder="Enter mobile number"
            />
          </div>
          
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center space-x-2 text-red-700">
              <UserOutlined />
              <span className="font-medium">Administrator Account</span>
            </div>
            <p className="text-sm text-red-600 mt-1">
              This is an administrative account with full system access and management privileges.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-900 flex items-center space-x-2">
          <UserOutlined className="text-orange-500" />
          <span>Personal Information</span>
        </h2>
      </div>
      <div className="px-6 py-4 space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <ProfileFormField
            label="First Name"
            value={editData.firstName}
            displayValue={user.firstName}
            isEditing={isEditing}
            onChange={(value) => onInputChange('firstName', value)}
            icon={UserOutlined}
            required
          />
          <ProfileFormField
            label="Last Name"
            value={editData.lastName}
            displayValue={user.lastName}
            isEditing={isEditing}
            onChange={(value) => onInputChange('lastName', value)}
            icon={UserOutlined}
            required
          />
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <ProfileFormField
            label="Email"
            value={editData.email}
            displayValue={user.email}
            isEditing={false} // Email should not be editable
            onChange={(value) => onInputChange('email', value)}
            type="email"
            icon={UserOutlined}
            required
          />
          <ProfileFormField
            label="Mobile Number"
            value={editData.phone}
            displayValue={user.phone}
            isEditing={isEditing}
            onChange={(value) => onInputChange('phone', value)}
            type="tel"
            icon={PhoneOutlined}
            placeholder="Enter mobile number"
          />
        </div>
      </div>
    </div>
  );
};

export default PersonalInfoSection;
